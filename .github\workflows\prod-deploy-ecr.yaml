name: Production Build and Deploy AWS

on:
  workflow_dispatch:

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: prod-main-nextjs-prod
  ECS_CLUSTER: prod-main-nextjs-prod-cluster
  ECS_SERVICE: prod-main-nextjs-prod-service

jobs:
  deploy:
    runs-on: ubuntu-24.04
    environment: production
    steps:
      - uses: actions/checkout@v4

      # Add .next cache clearing
      - name: Clear .next cache
        run: rm -rf .next

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      # Add build time generation
      - name: Set build time
        id: build-time
        run: echo "BUILD_TIME=$(date +%s)" >> $GITHUB_ENV

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
          NEXT_PUBLIC_BUILD_TIME: ${{ github.sha }}-${{ github.run_number }}-${{ env.BUILD_TIME }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
          NEXT_PUBLIC_DASHBOARD_URL: ${{ secrets.NEXT_PUBLIC_DASHBOARD_URL }}
          NEXT_PUBLIC_WEB_SOCKET_URL: ${{ secrets.NEXT_PUBLIC_WEB_SOCKET_URL }}
          NEXT_PUBLIC_POSTHOG_KEY: ${{ secrets.NEXT_PUBLIC_POSTHOG_KEY }}
          NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}
          NEXT_PUBLIC_POSTHOG_HOST: ${{ secrets.NEXT_PUBLIC_POSTHOG_HOST }}
          NEXT_PUBLIC_SENTRY_DSN: ${{ secrets.NEXT_PUBLIC_SENTRY_DSN }}
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          STRAPI_URL: ${{ secrets.STRAPI_URL }}
          STRAPI_TOKEN: ${{ secrets.STRAPI_TOKEN }}
          S3_BUCKET_NAME: ${{ vars.S3_BUCKET_NAME }}
          S3_STATIC_ASSETS_PREFIX: ${{ vars.S3_STATIC_ASSETS_PREFIX }}
        run: |
          docker build \
            --build-arg NEXT_PUBLIC_API_URL="${NEXT_PUBLIC_API_URL}" \
            --build-arg NEXT_PUBLIC_BUILD_TIME="${NEXT_PUBLIC_BUILD_TIME}" \
            --build-arg NEXTAUTH_SECRET="${NEXTAUTH_SECRET}" \
            --build-arg NEXTAUTH_URL="${NEXTAUTH_URL}" \
            --build-arg NEXT_PUBLIC_DASHBOARD_URL="${NEXT_PUBLIC_DASHBOARD_URL}" \
            --build-arg NEXT_PUBLIC_WEB_SOCKET_URL="${NEXT_PUBLIC_WEB_SOCKET_URL}" \
            --build-arg NEXT_PUBLIC_POSTHOG_KEY="${NEXT_PUBLIC_POSTHOG_KEY}" \
            --build-arg NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="${NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}" \
            --build-arg NEXT_PUBLIC_POSTHOG_HOST="${NEXT_PUBLIC_POSTHOG_HOST}" \
            --build-arg NEXT_PUBLIC_SENTRY_DSN="${NEXT_PUBLIC_SENTRY_DSN}" \
            --build-arg SENTRY_DSN="${SENTRY_DSN}" \
            --build-arg SENTRY_AUTH_TOKEN="${SENTRY_AUTH_TOKEN}" \
            --build-arg STRAPI_URL="${STRAPI_URL}" \
            --build-arg STRAPI_TOKEN="${STRAPI_TOKEN}" \
            --build-arg S3_BUCKET_NAME="${S3_BUCKET_NAME}" \
            --build-arg S3_STATIC_ASSETS_PREFIX="${S3_STATIC_ASSETS_PREFIX}" \
            -t ${ECR_REGISTRY}/${ECR_REPOSITORY}:latest .
          docker push ${ECR_REGISTRY}/${ECR_REPOSITORY}:latest

      - name: Trigger CodeDeploy Deployment
        run: |
          aws deploy create-deployment \
            --application-name AppECS-prod-main-nextjs-prod-cluster-nextjs-prod-service \
            --deployment-group-name DgpECS-prod-main-nextjs-prod \
            --revision "revisionType=S3,s3Location={bucket=${{ vars.S3_DEPLOY_ASSETS_BUCKET }},key=prod-appspec.yml,bundleType=YAML}" \
            --deployment-config-name CodeDeployDefault.ECSAllAtOnce
