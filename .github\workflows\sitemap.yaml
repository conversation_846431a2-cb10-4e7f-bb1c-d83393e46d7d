name: Generate and Deploy Sitemap

on:
  schedule:
    - cron: '0 * * * *'  # Runs every hour at minute 0
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select environment'
        required: true
        default: 'dev'
        type: choice
        options:
          - production
          - dev
          
permissions:
  contents: read

jobs:
  generate-sitemap:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'production' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Generate Sitemap
        run: node scripts/generate-sitemap.js
        env:
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
          STRAPI_URL: ${{ secrets.STRAPI_URL }}
          STRAPI_TOKEN: ${{ secrets.STRAPI_TOKEN }}
          NEXT_PUBLIC_URL: ${{ vars.NEXT_PUBLIC_URL }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Deploy sitemap.xml to S3
        run: |
          aws s3 cp ./public/sitemap.xml s3://${{ vars.S3_BUCKET_NAME }}/${{ vars.S3_STATIC_ASSETS_PREFIX }}sitemap.xml --content-type application/xml

      - name: Upload sitemap.xml as artifact
        uses: actions/upload-artifact@v4
        with:
          name: sitemap-output
          path: ./public/sitemap.xml
          retention-days: 2
