# Whitelabel Subdomain Solution

This document outlines the comprehensive whitelabel solution implemented for the RevvdUp Events platform, allowing organizers to have their own branded subdomains.

## Overview

The whitelabel solution provides:

- **Subdomain-based access**: `organizerslug.yourdomain.com`
- **Completely separated routes**: `/events/` (clean, main domain) vs `/event/` (whitelabel only)
- **Limited route access**: Only `/event` listing, `/event/{eventSlug}` pages, and `/resend` for subdomains
- **API context awareness**: API calls automatically include organizer context
- **Isolated branding**: Clean, minimal UI without main platform navigation
- **Zero conflicts**: Normal and whitelabel flows are completely isolated - no shared code

## Architecture

### Core Components

1. **Subdomain Detection (`src/lib/utils/subdomainUtils.ts`)**

   - Extracts organizer slug from hostname
   - Validates subdomain format
   - Determines allowed paths for whitelabel access

2. **Context Provider (`src/lib/Providers/SubdomainProvider.tsx`)**

   - Provides subdomain state throughout the application
   - Hooks for easy access to organizer slug
   - Client-side subdomain detection

3. **Middleware (`src/middleware.ts`)**

   - Routes whitelabel traffic appropriately
   - Blocks access to unauthorized routes
   - Passes subdomain context via headers

4. **Enhanced API Layer (`src/lib/utils/baseQuery.ts`)**
   - Integrated whitelabel support into existing base queries
   - Automatically includes `X-Organizer-Slug` header on whitelabel subdomains
   - Timezone headers for proper date/time handling
   - No client-side URL modifications - headers provide server-side context

### Route Structure

#### Available for Whitelabel Subdomains:

- `organizerslug.domain.com/events` - Redirects to `/event` (whitelabel events listing)
- `organizerslug.domain.com/events/{eventSlug}` - Rewrites to `/event/{eventSlug}` (individual events)
- `organizerslug.domain.com/resend` - Password reset functionality
- Static assets (CSS, JS, images, fonts)
- API endpoints

#### Internal Route Handling:

- **Normal flow (main domain)**:
  - `/events` → Normal events page with full navigation and features
  - `/events/{eventSlug}` → Normal event page with full navigation
- **Whitelabel flow (subdomains)**:
  - `/events` → Redirects to `/event` (whitelabel events listing with minimal layout)
  - `/events/{eventSlug}` → Rewrites to `/event/{eventSlug}` (whitelabel event page)

#### Completely Separated Routes:

- `/events/` routes: **Clean, main domain only** - no whitelabel logic
- `/event/` routes: **Whitelabel only** - not accessible on main domain

#### Blocked on Whitelabel Subdomains:

- All other platform routes redirect to `/event`

## Implementation Details

### 1. Subdomain Detection

```typescript
// Hostname: organizerslug.yourdomain.com
const subdomainInfo = getSubdomainInfo(hostname);
// Result: { isSubdomain: true, organizerSlug: "organizerslug", hostname: "..." }
```

### 2. Context Usage

```typescript
// In any component
const { isWhitelabel, organizerSlug } = useSubdomain();
const slug = useOrganizerSlug(); // Shorthand for organizer slug only
```

### 3. API Integration

The enhanced base query automatically:

- Adds `X-Organizer-Slug` header to all requests on whitelabel subdomains
- Maintains timezone information
- Server-side APIs use the organizer header to filter/scope data appropriately

### 4. Layout Adaptation

Components automatically adapt based on whitelabel context:

- **Events Layout**: Shows minimal header with organizer branding
- **Event Pages**: Adjusted margins for whitelabel navigation
- **Resend Page**: Standalone password reset interface

## Setup Instructions

### 1. DNS Configuration

Configure DNS to point subdomains to your application:

```
*.yourdomain.com CNAME yourdomain.com
```

### 2. Environment Variables

Ensure the following environment variables are set:

```env
NEXT_PUBLIC_API_URL=https://your-api-url.com/
```

### 3. Organizer Setup

1. Organizers need to have a unique slug in the system
2. DNS wildcards should be configured
3. Events should be associated with the organizer

## Usage Examples

### For Organizers

1. **Access organizer's event listing**:

   ```
   https://myorganizer.yourdomain.com/events
   ```

2. **View specific event**:

   ```
   https://myorganizer.yourdomain.com/events/awesome-car-show
   ```

3. **Password reset**:
   ```
   https://myorganizer.yourdomain.com/resend
   ```

### For Developers

1. **Check if in whitelabel mode**:

   ```typescript
   const { isWhitelabel } = useSubdomain();
   if (isWhitelabel) {
     // Show whitelabel-specific UI
   }
   ```

2. **Get organizer slug for API calls**:

   ```typescript
   const organizerSlug = useOrganizerSlug();
   // Use in API calls or components
   ```

3. **Conditional rendering**:
   ```typescript
   {
     isWhitelabel ? <WhitelabelHeader /> : <MainHeader />;
   }
   ```

## Security Considerations

1. **Route Protection**: Non-whitelabel routes are automatically blocked
2. **API Context**: Organizer slug is validated server-side
3. **Static Assets**: Only necessary assets are accessible
4. **CORS**: Subdomain requests are handled appropriately

## Performance Considerations

1. **Caching**: Event data is cached with appropriate revalidation
2. **Static Assets**: Shared across main domain and subdomains
3. **API Calls**: Optimized with context-aware base queries
4. **Code Splitting**: Whitelabel components are loaded as needed

## Troubleshooting

### Common Issues

1. **Subdomain not detected**:

   - Check DNS configuration
   - Verify hostname parsing in development

2. **API calls failing**:

   - Ensure organizer slug is valid
   - Check API endpoint configuration

3. **Assets not loading**:
   - Verify static asset paths in `isWhitelabelAllowedPath`
   - Check middleware configuration

### Development Testing

1. **Local Development**:

   ```bash
   # Add to /etc/hosts (Mac/Linux) or C:\Windows\System32\drivers\etc\hosts (Windows)
   127.0.0.1 testorg.localhost
   ```

2. **Test subdomain detection**:
   ```
   http://testorg.localhost:3000/events
   ```

## Future Enhancements

1. **Custom Domain Support**: Allow organizers to use their own domains
2. **Theme Customization**: Organizer-specific branding and colors
3. **Additional Routes**: Expand available routes based on needs
4. **Analytics**: Subdomain-specific tracking and analytics
5. **SEO Optimization**: Organizer-specific meta tags and schema

## API Integration

All API requests on whitelabel subdomains automatically include:

- `X-Organizer-Slug` header with the subdomain organizer slug
- `X-Timezone` header for proper date/time handling
- Server-side APIs use these headers to scope data to the appropriate organizer
- No client-side URL modifications - APIs handle organizer context server-side

## File Structure

```
src/
├── lib/
│   ├── utils/
│   │   ├── subdomainUtils.ts          # Subdomain detection utilities
│   │   └── baseQuery.ts               # Enhanced with whitelabel support
│   └── Providers/
│       └── SubdomainProvider.tsx      # Context provider
├── app/
│   ├── events/                        # CLEAN - Main domain only
│   │   ├── layout.tsx                 # Original layout (no whitelabel logic)
│   │   ├── page.tsx                   # Original events page (no whitelabel logic)
│   │   ├── [slug]/
│   │   │   ├── page.tsx               # Original event page (no whitelabel logic)
│   │   │   └── components/
│   │   │       └── [event components] # Shared event components
│   │   └── components/
│   │       └── [original components]  # Original event components
│   ├── event/                         # WHITELABEL ONLY - Not accessible on main domain
│   │   ├── layout.tsx                 # Whitelabel layout with minimal header
│   │   ├── page.tsx                   # Whitelabel events listing page
│   │   ├── components/
│   │   │   └── WhitelabelEventsPage.tsx # Whitelabel events listing component
│   │   └── [slug]/
│   │       ├── layout.tsx             # Whitelabel event layout (inherits from parent)
│   │       ├── page.tsx               # Whitelabel event page
│   │       ├── loading.tsx            # Whitelabel loading component
│   │       └── components/
│   │           └── ClientRedirect.tsx # Redirect component
│   ├── resend/
│   │   ├── layout.tsx                 # Minimal layout
│   │   └── page.tsx                   # Password reset page
│   ├── layout.tsx                     # Root layout with providers
│   └── middleware.ts                  # Route handling and rewriting
└── WHITELABEL_SETUP.md               # This documentation
```

This whitelabel solution provides a clean, branded experience for organizers while maintaining the full functionality of the main platform.
