version: "3.8"

services:
  nextapp:
    container_name: nextapp
    image: nextapp:${TAG:-latest}
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
        - NEXT_PUBLIC_WEB_SOCKET_URL=${NEXT_PUBLIC_WEB_SOCKET_URL}
        - AUTH_SECRET=${AUTH_SECRET}
        - NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY}
        - NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}
        - NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST}
        - NEXT_PUBLIC_SENTRY_DSN=${NEXT_PUBLIC_SENTRY_DSN}
        - SENTRY_DSN=${SENTRY_DSN}
        - SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}
        - STRAPI_URL=${STRAPI_URL}
        - STRAPI_TOKEN=${STRAPI_TOKEN}
        - S3_BUCKET_NAME=${S3_BUCKET_NAME}
        - S3_STATIC_ASSETS_PREFIX=${S3_STATIC_ASSETS_PREFIX}
    ports:
      - "3000:3000"
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXT_PUBLIC_WEB_SOCKET_URL=${NEXT_PUBLIC_WEB_SOCKET_URL}
      - AUTH_SECRET=${AUTH_SECRET}
      - NEXTAUTH_SECRET=${AUTH_SECRET}
      - NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY}
      - NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}
      - NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST}
      - NEXT_PUBLIC_SENTRY_DSN=${NEXT_PUBLIC_SENTRY_DSN}
      - SENTRY_DSN=${SENTRY_DSN}
      - SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}
      - STRAPI_URL=${STRAPI_URL}
      - STRAPI_TOKEN=${STRAPI_TOKEN}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_STATIC_ASSETS_PREFIX=${S3_STATIC_ASSETS_PREFIX}
    env_file:
      - .env.local
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
