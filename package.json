{"name": "nextjs-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "fast": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@hookform/resolvers": "^3.9.1", "@mui/material": "^6.1.10", "@nextui-org/react": "^2.4.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@reduxjs/toolkit": "^2.2.6", "@sentry/nextjs": "8", "@shopify/polaris-icons": "^9.3.0", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.4.0", "@types/js-cookie": "^3.0.6", "@types/tinycolor2": "^1.4.6", "@typescript-eslint/eslint-plugin": "^8.0.1", "@typescript-eslint/parser": "^8.0.1", "async-mutex": "^0.5.0", "browser-image-compression": "^2.0.2", "camel-case": "^5.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-state-city": "^3.2.1", "dayjs": "^1.11.12", "embla-carousel": "^8.2.1", "embla-carousel-react": "^8.2.1", "framer-motion": "^11.3.2", "heic2any": "^0.0.4", "html-react-parser": "^5.1.12", "input-otp": "^1.4.2", "isomorphic-dompurify": "^2.19.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.479.0", "next": "14.2.5", "next-auth": "5.0.0-beta.20", "next-qrcode": "^2.5.1", "pdfjs-dist": "4.4.168", "photoswipe": "^5.4.4", "posthog-js": "^1.166.1", "quill": "^2.0.2", "react": "^18", "react-device-detect": "^2.2.3", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.2.0", "react-google-autocomplete": "^2.7.5", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.1", "react-markdown": "^9.0.1", "react-pdf": "^9.1.0", "react-photoswipe-gallery": "^3.0.2", "react-qr-code": "^2.0.15", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-select": "^5.8.0", "redux-persist": "^6.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "snake-case": "^4.0.0", "swiper": "^11.1.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tinycolor2": "^1.6.0", "uuid": "^10.0.0", "zod": "^3.24.1"}, "devDependencies": {"@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-easy-crop": "^2.0.0", "@types/react-select": "^5.0.1", "@types/uuid": "^10.0.0", "critters": "^0.0.25", "cssnano": "^7.0.6", "eslint": "^9.8.0", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.1", "typescript": "^5"}, "pnpm": {"supportedArchitectures": {"os": ["current"], "cpu": ["x64", "arm64"]}}, "packageManager": "pnpm@9.4.0+sha512.f549b8a52c9d2b8536762f99c0722205efc5af913e77835dbccc3b0b0b2ca9e7dc8022b78062c17291c48e88749c70ce88eb5a74f1fa8c4bf5e18bb46c8bd83a", "resolutions": {"eslint": "^8.56.0"}}