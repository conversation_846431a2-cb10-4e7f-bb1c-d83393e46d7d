const { writeFileSync, mkdirSync } = require('fs');

const BASE_URL = process.env.NEXT_PUBLIC_URL ?? 'https://www.devautolnk.com';
const BASE_URL_API = process.env.NEXT_PUBLIC_API_URL;
const STRAPI_URL = process.env.STRAPI_URL;
const STRAPI_TOKEN = process.env.STRAPI_TOKEN;


async function getEvents(isFeaturedEvents = false) {
  let url = `${BASE_URL_API}api/events/list/?&order_by=start_date&date=${isFeaturedEvents? 12: 2}_months`;
  if (isFeaturedEvents) url += "&has_paid_tickets=true&status=active";
  try {
    const response = await fetch(url, {
      next: {
        revalidate: 60, // Revalidate every 1 minute
      },
      headers: {
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.results;
  } catch (error) {
    console.error("Failed to fetch events:", error);
    return { results: [] };
  }
}


async function fetchAllEvents() {
  let cursor= null;
  let hasMore = true;
  const fetchedEvents = [];

  while (hasMore) {
    try {
      const url = `${BASE_URL_API}api/events/list?status=active&per_page=100${cursor ? `&cursor=${cursor}` : ''}`;
      const response = await fetch(url, {
        method: 'GET',
        cache: 'no-store',
      });

      if (!response.ok) {
        hasMore = false;
        continue;
      }

      const data = await response.json();
      fetchedEvents.push(...data.results);

      if (data.nextPageResults && data.nextCursor) {
        cursor = data.nextCursor;
      } else {
        hasMore = false;
      }
    } catch (e) {
      hasMore = false;
    }
  }
  return fetchedEvents;
}

async function fetchAllArticles() {
  try {
    const response = await fetch(`${STRAPI_URL}/articles?pagination[page]=100`, {
      cache: 'no-store',
      headers: {
        Authorization: `Bearer ${STRAPI_TOKEN}`,
      },
    });

    if (!response.ok) return [];

    const data = await response.json();
    return (data.data || []);
  } catch (e) {
    return [];
  }
}

async function generateSitemap() {
  let sitemap = '<?xml version="1.0" encoding="UTF-8"?>\n';
  sitemap += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  // Static Routes
  const staticRoutes = [
    { url: `${BASE_URL}/events`, changeFreq: 'daily', priority: '1' },
    { url: `${BASE_URL}/events/list`, changeFreq: 'daily', priority: '0.8' },
    { url: `${BASE_URL}/about`, changeFreq: 'weekly', priority: '0.7' },
    { url: `${BASE_URL}/terms-of-service`, changeFreq: 'weekly', priority: '0.7' },
    { url: `${BASE_URL}/download`, changeFreq: 'weekly', priority: '0.5' },
    { url: `${BASE_URL}/professional-account-creation`, changeFreq: 'yearly', priority: '0.5' },
  ];

  staticRoutes.forEach(route => {
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${route.url}</loc>\n`;
    // Only include lastmod if not /about
    if (route.url !== `${BASE_URL}/about`) {
      sitemap += `    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n`;
    }
    sitemap += `    <changefreq>${route.changeFreq}</changefreq>\n`;
    sitemap += `    <priority>${route.priority}</priority>\n`;
    sitemap += `  </url>\n`;
  });

  // Featured Events
  const featuredData = await getEvents(true);
  featuredData.forEach((event) => {
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${BASE_URL}/e/${event.slug}</loc>\n`;
    sitemap += `    <changefreq>hourly</changefreq>\n`;
    sitemap += `    <priority>0.9</priority>\n`;
    sitemap += `  </url>\n`;
  });

  // All Events
  const allEvents = await fetchAllEvents();
  allEvents.forEach((event) => {
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${BASE_URL}/e/${event.slug}</loc>\n`;
    sitemap += `    <changefreq>hourly</changefreq>\n`;
    sitemap += `    <priority>0.9</priority>\n`;
    sitemap += `  </url>\n`;
  });

  // Community Routes
  const communityRoutes = [
    { url: `${BASE_URL}/community`, changeFreq: 'hourly', priority: '0.9' },
  ];

  communityRoutes.forEach(route => {
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${route.url}</loc>\n`;
    sitemap += `    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>\n`;
    sitemap += `    <changefreq>${route.changeFreq}</changefreq>\n`;
    sitemap += `    <priority>${route.priority}</priority>\n`;
    sitemap += `  </url>\n`;
  });

  // Article Routes
  const articles = await fetchAllArticles();
  articles.forEach((article) => {
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${BASE_URL}/community/${article.attributes.slug}</loc>\n`;
    sitemap += `    <lastmod>${new Date(article.attributes.updatedAt || article.attributes.publishedAt).toISOString().split('T')[0]}</lastmod>\n`;
    sitemap += `    <changefreq>hourly</changefreq>\n`;
    sitemap += `    <priority>0.8</priority>\n`;
    sitemap += `  </url>\n`;
  });

  sitemap += '</urlset>';

  try {
    mkdirSync('public', { recursive: true });
    writeFileSync('public/sitemap.xml', sitemap);
    console.log('Sitemap generated successfully');
  } catch (error) {
    console.error('Error writing sitemap:', error);
    throw error;
  }
}

generateSitemap().catch(console.error);