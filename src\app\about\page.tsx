import Image from "next/image";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
const AboutPage = () => {
  return (
    <div className="mt-12 pb-24 md:pb-48">
      <div className="relative">
        <Image
          src={IMAGE_LINKS.ABOUT_BANNER}
          width={1440}
          height={400}
          alt="Autolnk"
          className="w-full"
        />
        <div className="absolute inset-0 bg-black opacity-30" />
      </div>
      <div className="p-6 mt-10 md:mt-20 max-w-[1280px] mx-auto grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 items-start md:items-center">
        <div className="w-full md:w-[85%]">
          <h1 className="text-3xl md:text-4xl lg:text-[50px] text-[#3F3F3F] font-semibold border-b border-[#DFDFDF] pb-4">
            About AutoLNK
          </h1>
          <div className="mt-6 md:mt-8 text-base md:text-lg lg:text-[19px] text-[#444444] font-normal tracking-wide flex flex-col gap-6 md:gap-8">
            <p>
              AutoLNK centralizes all areas of the automotive community onto one
              platform, connecting car enthusiasts, mechanics, and automotive
              companies.
            </p>
            <p className="md:whitespace-pre-line">
              AutoLNK allows you to find and <br /> purchase tickets for
              automotive events, connect with friends through drive shares, and
              manage your vehicles in a virtual garage.
            </p>
          </div>
        </div>
        <div className="w-full px-0 md:px-6 mt-8 md:mt-0">
          <Image
            src={IMAGE_LINKS.ABOUT_MAIN}
            width={1440}
            height={400}
            alt="Autolnk"
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
};

export default AboutPage;
