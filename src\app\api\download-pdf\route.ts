import { NextRequest, NextResponse } from 'next/server';

/**
 * API route to proxy PDF downloads from S3 with proper headers
 * This bypasses CORS issues and forces the browser to download the file
 */
export async function GET(request: NextRequest) {
  // Get the PDF URL and filename from URL parameters
  const searchParams = request.nextUrl.searchParams;
  const pdfUrl = searchParams.get('url');
  const filename = searchParams.get('filename') || 'ticket.pdf';

  // Validate input
  if (!pdfUrl) {
    return NextResponse.json(
      { error: 'Missing required parameter: url' },
      { status: 400 }
    );
  }

  try {
    // Fetch the PDF from S3
    const response = await fetch(pdfUrl);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
    }
    
    // Get the PDF as an ArrayBuffer
    const pdfData = await response.arrayBuffer();
    
    // Prepare the response with the proper headers to force download
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    headers.set('Content-Length', pdfData.byteLength.toString());
    
    // Return the PDF with the proper headers
    return new NextResponse(pdfData, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('Error downloading PDF:', error);
    return NextResponse.json(
      { error: 'Failed to download PDF' },
      { status: 500 }
    );
  }
} 