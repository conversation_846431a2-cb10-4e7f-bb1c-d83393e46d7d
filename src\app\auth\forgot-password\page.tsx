"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { TOASTS } from "@/lib/utils/constants";
import { reportError } from "@/lib/utils/sentryErrorLogs";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

// Reusing the components from the modal-based implementation
import EmailVerificationForm from "@/components/Authenticator/forgotpassword/EmailVerificationForm";
import { ResetPasswordModal } from "@/components/Authenticator/forgotpassword/ResetPasswordModal";

const FORGOT_FORM_MODE = {
  EMAIL: "EMAIL",
  EMAIL_SENT: "EMAIL_SENT",
  RESET_PASSWORD: "RESET_PASSWORD",
};

export default function ForgotPasswordPage() {
  const [formMode, setFormMode] = useState<string>(FORGOT_FORM_MODE.EMAIL);
  const [email, setEmail] = useState("");
  const [uidb64, setUidb64] = useState("");
  const [token, setToken] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleToast = (message: string, type: 'success' | 'error') => {
    if (type === 'error') {
      reportError(message, { page: "forgot-password" });
      toast.error(message);
    } else {
      toast.success(message);
    }
  };

  // Check for query parameters
  useEffect(() => {
    const emailParam = searchParams.get('email');
    const uidb64Param = searchParams.get('uidb64');
    const tokenParam = searchParams.get('token');

    if (emailParam && uidb64Param && tokenParam) {
      setEmail(emailParam);
      setUidb64(uidb64Param);
      setToken(tokenParam);
      setFormMode(FORGOT_FORM_MODE.RESET_PASSWORD);
    }
  }, [searchParams]);

  const isEmailActive = useMemo(
    () => formMode === FORGOT_FORM_MODE.EMAIL,
    [formMode]
  );

  const isEmailSentActive = useMemo(
    () => formMode === FORGOT_FORM_MODE.EMAIL_SENT,
    [formMode]
  );
  
  const isResetPasswordActive = useMemo(
    () => formMode === FORGOT_FORM_MODE.RESET_PASSWORD,
    [formMode]
  );

  function onChangeState(state: string) {
    setFormMode(state);
  }

  return (
    <div className="w-full">
      <div className="space-y-5">
        {isEmailActive && (
          <EmailVerificationForm
            onNext={(value) => {
              setEmail(value);
              onChangeState(FORGOT_FORM_MODE.EMAIL_SENT);
            }}
            handleToast={handleToast}
          />
        )}
        
        {isEmailSentActive && (
          <div className="text-center space-y-4">
            <h2 className="text-xl font-semibold">Check Your Email</h2>
            <p className="text-gray-600">
              We've sent a password reset link to <span className="font-medium">{email}</span>. 
              Please check your email and click on the link to reset your password.
            </p>
          </div>
        )}
        
        {isResetPasswordActive && (
          <ResetPasswordModal
            email={email}
            uidb64={uidb64}
            token={token}
            onNext={() => {
              const successMessage = TOASTS.PASSWORD_RESET_SUCCESS;
              toast.success(successMessage);
              router.push("/auth/signin");
            }}
            handleToast={handleToast}
          />
        )}

        {isEmailActive && (
          <div className="mt-4">
            <Button
              variant="outline"
        className="w-full h-11 rounded-[7.83px] text-base text-[#367AFF] font-medium border-gray-300 hover:text-[#367AFF]"
              onClick={() => router.push("/auth/signin")}
            >
              Back to log in
            </Button>
          </div>
        )}
      </div>
    </div>
  );
} 