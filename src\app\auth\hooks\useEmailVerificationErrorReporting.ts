import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { FieldErrors } from "react-hook-form";
import { z } from "zod";
import { forgotEmailSchema } from "@/lib/utils/validation";

type FormFields = z.infer<typeof forgotEmailSchema>;

/**
 * Custom hook to handle error reporting for email verification form
 */
export function useEmailVerificationErrorReporting(errors: FieldErrors<FormFields>) {
  // Report email validation errors to Sentry
  useEffect(() => {
    const emailError = errors?.email?.message;
    if (emailError) {
      reportError(emailError, { 
        page: "forgot-password", 
        field: "email",
        type: "validation_error" 
      });
    }
  }, [errors?.email?.message]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page: "forgot-password", 
      field: "email",
      ...context
    });
  };

  return { reportApiError };
} 