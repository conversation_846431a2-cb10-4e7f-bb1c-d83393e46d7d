import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { AUTH_ERRORS } from "../constants/errorMessages";

interface UseOtpErrorReportingProps {
  otp: string;
  isSubmitting: boolean;
}

/**
 * Custom hook to handle error reporting for OTP verification
 */
export function useOtpErrorReporting({
  otp,
  isSubmitting
}: UseOtpErrorReportingProps) {
  // Report OTP validation errors to Sentry
  useEffect(() => {
    if (otp && otp.length !== 6 && isSubmitting) {
      reportError(AUTH_ERRORS.INVALID_OTP_LENGTH, { 
        page: "signup", 
        field: "otp",
        type: "validation_error",
        otpLength: otp.length.toString()
      });
    }
  }, [otp, isSubmitting]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page: "signup", 
      field: "otp",
      ...context
    });
  };

  return { reportApiError };
} 