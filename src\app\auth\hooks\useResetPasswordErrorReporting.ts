import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { FieldErrors } from "react-hook-form";
import { z } from "zod";
import { resetPasswordSchema } from "@/lib/utils/validation";

type FormFields = z.infer<typeof resetPasswordSchema>;

/**
 * Custom hook to handle error reporting for reset password form
 */
export function useResetPasswordErrorReporting(errors: FieldErrors<FormFields>) {
  // Report password validation errors to Sentry
  useEffect(() => {
    const newPasswordError = errors?.newPassword?.message;
    if (newPasswordError) {
      reportError(newPasswordError, { 
        page: "forgot-password", 
        field: "newPassword",
        type: "validation_error" 
      });
    }
  }, [errors?.newPassword?.message]);

  useEffect(() => {
    const confirmPasswordError = errors?.confirmPassword?.message;
    if (confirmPasswordError) {
      reportError(confirmPasswordError, { 
        page: "forgot-password", 
        field: "confirmPassword",
        type: "validation_error" 
      });
    }
  }, [errors?.confirmPassword?.message]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page: "forgot-password", 
      ...context
    });
  };

  return { reportApiError };
} 