import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { FieldErrors } from "react-hook-form";
import { signInSchema } from "@/lib/utils/validation";
import { z } from "zod";

type FormFields = z.infer<typeof signInSchema>;

/**
 * Custom hook to handle error reporting for signin form
 * @param errors - Form errors from react-hook-form
 */
export function useSigninErrorReporting(errors: FieldErrors<FormFields>) {
  // Report validation errors to Sentry
  useEffect(() => {
    const valueError = errors?.value?.message;
    if (valueError) {
      reportError(valueError, { 
        page: "signin", 
        field: "value",
        type: "validation_error" 
      });
    }
  }, [errors?.value?.message]);

  useEffect(() => {
    const passwordError = errors?.password?.message;
    if (passwordError) {
      reportError(passwordError, { 
        page: "signin", 
        field: "password",
        type: "validation_error" 
      });
    }
  }, [errors?.password?.message]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page: "signin", 
      ...context
    });
  };

  return { reportApiError };
} 