import { reportError } from "@/lib/utils/sentryErrorLogs";

/**
 * Custom hook to handle error reporting for signup page
 */
export function useSignupErrorReporting() {
  // Function to report API/runtime errors during signup process
  const reportSignupError = (errorMessage: string, step: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page: "signup", 
      step,
      ...context
    });
  };

  return { reportSignupError };
} 