import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { UseFormReturn } from "react-hook-form";
import { AUTH_ERRORS } from "../constants/errorMessages";

interface UseUserDetailsErrorReportingProps {
  form: UseFormReturn<any>;
  email: string;
  username: string;
  isCheckingEmail: boolean;
  isCheckingUsername: boolean;
  isEmailAvailable: boolean | null;
  isUsernameAvailable: boolean | null;
}

/**
 * Custom hook to handle error reporting for user details form
 */
export function useUserDetailsErrorReporting({
  form,
  email,
  username,
  isCheckingEmail,
  isCheckingUsername,
  isEmailAvailable,
  isUsernameAvailable
}: UseUserDetailsErrorReportingProps) {
  const { formState } = form;

  // Report validation errors to Sentry
  useEffect(() => {
    const emailError = formState?.errors?.email?.message;
    if (emailError) {
      reportError(String(emailError), { 
        page: "signup", 
        field: "email",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.email?.message]);

  useEffect(() => {
    const passwordError = formState?.errors?.password?.message;
    if (passwordError) {
      reportError(String(passwordError), { 
        page: "signup", 
        field: "password",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.password?.message]);

  useEffect(() => {
    const nameError = formState?.errors?.name?.message;
    if (nameError) {
      reportError(String(nameError), { 
        page: "signup", 
        field: "name",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.name?.message]);

  useEffect(() => {
    const usernameError = formState?.errors?.username?.message;
    if (usernameError) {
      reportError(String(usernameError), { 
        page: "signup", 
        field: "username",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.username?.message]);

  // Report availability errors to Sentry
  useEffect(() => {
    if (email && !formState?.errors?.email && !isCheckingEmail && isEmailAvailable === false) {
      reportError(AUTH_ERRORS.EMAIL_AVAILABILITY_ERROR, { 
        page: "signup", 
        field: "email",
        type: "availability_error" 
      });
    }
  }, [email, formState?.errors?.email, isCheckingEmail, isEmailAvailable]);

  useEffect(() => {
    if (username && !formState?.errors?.username && !isCheckingUsername && isUsernameAvailable === false) {
      reportError(AUTH_ERRORS.USERNAME_AVAILABILITY_ERROR, { 
        page: "signup", 
        field: "username",
        type: "availability_error" 
      });
    }
  }, [username, formState?.errors?.username, isCheckingUsername, isUsernameAvailable]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page: "signup", 
      ...context
    });
  };

  return { reportApiError };
} 