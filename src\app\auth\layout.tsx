'use client'
import Image from "next/image";
import { Inter } from "next/font/google";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { useSessionData } from "@/lib/hooks/useSession";
import { useRouter } from "next/navigation";
import { AUTH_STATUS } from "@/lib/utils/constants";
import Loading from "../loading";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

const inter = Inter({ subsets: ["latin"] });

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { data: session, status } = useSessionData();
  if (session?.user) {
    router.replace("/");
  }

  if (status === AUTH_STATUS.LOADING) {
    return <Loading />;
  }

  return (
    <div className={`min-h-screen w-full bg-white flex flex-col ${inter.className}`}>
      <div className="flex flex-1 items-center justify-center p-4 md:p-8">
        <div className="flex flex-col md:flex-row w-full max-w-6xl items-center">
          {/* Left side with app mockup */}
          <div className="hidden md:flex md:w-1/2 items-center justify-center">
            <div className="w-full max-w-[700px]">
              <Image
                src={IMAGE_LINKS.SIGN_UP_SCREENSHOTS}
                alt="Autolnk App Screenshots"
                width={700}
                height={800}
                quality={100}
                className="w-full h-auto object-contain"
                priority
              />
            </div>
          </div>
          
          {/* Right side with auth card */}
          <div className="w-full md:w-1/2 flex flex-col items-center">
            <Card className="w-full max-w-[390px] p-6 rounded-[7.83px] sm:border-[#E8E8E8] shadow-none sm:border-2 border-0">
              <div className="text-center mb-12">
                <Image
                  src={IMAGE_LINKS.LOGO_TRANSPARENT}
                  alt="AutoLNK"
                  width={120}
                  height={40}
                  className="mx-auto"
                  priority
                />
              </div>
              
              {/* Auth form content */}
              {children}
            </Card>
            
            {/* Download app section */}
            <div className="w-full max-w-[390px] mt-5 text-center">
              <p className="text-sm text-gray-600 mb-3">Download the app:</p>
              <div className="flex justify-center space-x-4">
                <Link href="https://apps.apple.com/app/autolnk/id1234567890" target="_blank">
                  <Image
                    src={IMAGE_LINKS.DOWNLOAD_ON_APPSTORE}
                    alt="Autolnk Download on App Store"
                    width={120}
                    height={40}
                    className="h-10 w-auto object-contain"
                    priority
                  />
                </Link>
                <Link href="https://play.google.com/store/apps/details?id=com.xcelerate.xcelerate" target="_blank">
                  <Image
                    src={IMAGE_LINKS.DOWNLOAD_ON_GOOGLE}
                    alt="Autolnk Download on Google Play"
                    width={135}
                    height={40}
                    className="h-10 w-auto object-contain"
                    priority
                  />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Footer */}
      <div className="p-4 flex flex-col-reverse sm:flex-row sm:w-[70%] w-full gap-2 mx-auto justify-between items-center text-xs text-[#737373]">
        <p>© {new Date().getFullYear()} AutoLNK | All rights reserved.</p>
        <p>
            Designed by AutoLNK
        </p>
      </div>
    </div>
  );
} 