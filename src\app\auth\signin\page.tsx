"use client";

import { signInSchema } from "@/lib/utils/validation";
import { zodResolver } from "@hookform/resolvers/zod";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { z } from "zod";
import { Login } from "@/lib/actions/auth/Login";
import { FormError } from "@/components/FormStatus/Form-error";
import { useAppDispatch } from "@/lib/redux/hooks";
import { setUser } from "@/lib/redux/slices/auth/authSlice";
import { getSession } from "next-auth/react";
import { mapToSignupResponse } from "@/lib/utils/modification";
import { usePostHog } from 'posthog-js/react';
import { isValidEmail } from "@/lib/utils/string";
import Link from "next/link";
import { useSigninErrorReporting } from "../hooks/useSigninErrorReporting";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type FormFields = z.infer<typeof signInSchema>;

export default function SignInPage() {
  const form = useForm<FormFields>({
    resolver: zodResolver(signInSchema),
  });

  const router = useRouter();
  const dispatch = useAppDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const posthog = usePostHog();

  const { watch, formState } = form;
  const email = watch("value");
  const password = watch("password");
  const isFormFilled = email && password;

  const { reportApiError } = useSigninErrorReporting(formState.errors);

  const onSubmit: SubmitHandler<FormFields> = async (data) => {
    setIsSubmitting(true);
    setError(null);
    try {
      const result = await Login({
        value: data?.value,
        field: isValidEmail(data?.value) ? "email" : "username",
        password: data?.password,
        redirect: false,
      });
      if (result?.error) {
        const errorMessage = result.error;
        setError(errorMessage);
        reportApiError(errorMessage);
      } else {
        const session = await getSession();
        const mappedResponse = mapToSignupResponse(session);
        dispatch(setUser(mappedResponse));

        if (!posthog?._isIdentified()) {
          posthog?.identify(mappedResponse.email);
        }
        window.location.href = "/";
      }
    } catch (error) {
      console.log("ERROR ", error);
      const errorMessage = (error as Error).message;
      setError(errorMessage);
      reportApiError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full">
      <form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
        <div>
          <Input
            id="value"
            type="text"
            placeholder="Email / Username"
            {...form.register("value", {
              onChange: (e) => {
                e.target.value = e.target.value.toLowerCase();
              },
            })}
            className="h-12"
          />
          {formState.errors?.value?.message && (
            <p className="text-xs text-red-500 mt-1">
              {formState.errors.value.message}
            </p>
          )}
        </div>

        <div className="">
          <div className="relative">
            <Input
              id="password"
              type="password"
              placeholder="Password"
              {...form.register("password")}
              className="h-12"
            />
          </div>
          {formState.errors?.password?.message && (
            <p className="text-xs text-red-500 mt-1">
              {formState.errors.password.message}
            </p>
          )}
          <div className="flex justify-end pr-3">
            <Link
              href="/auth/forgot-password"
              className="text-xs hover:underline whitespace-nowrap text-black mt-1"
            >
              Forgot password?
            </Link>
          </div>
        </div>

        {error && <FormError message={error} />}

        <Button
          type="submit"
          disabled={isSubmitting || !isFormFilled}
          loading={isSubmitting}
          className="w-full h-11 rounded-[7.83px] text-base text-white font-medium bg-[#367AFF] hover:bg-[#3679ffd7]"
        >
          Log in
        </Button>
      </form>

      <div className="mt-3 mb-3 text-center relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300"></div>
        </div>
        <div className="relative">
          <span className="px-2 bg-white text-sm text-gray-500">or</span>
        </div>
      </div>

      <Button
        variant="outline"
        className="w-full h-11 rounded-[7.83px] text-base text-[#367AFF] font-medium border-gray-300 hover:text-[#367AFF]"
        onClick={() => router.push("/auth/signup")}
      >
        Create account
      </Button>
    </div>
  );
}
