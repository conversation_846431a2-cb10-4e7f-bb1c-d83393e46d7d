"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { useOtpErrorReporting } from "../../hooks/useOtpErrorReporting";

interface OtpVerificationFormProps {
  otp: string;
  setOtp: (value: string) => void;
  isSubmitting: boolean;
  isFormValid: boolean;
  handleResendOtp: () => Promise<void>;
  canResendOtp: boolean;
  timeLeft: number;
}

const OtpVerificationForm = ({
  otp,
  setOtp,
  isSubmitting,
  isFormValid,
  handleResendOtp,
  canResendOtp,
  timeLeft,
}: OtpVerificationFormProps) => {
  // Use the custom hook for error reporting
  useOtpErrorReporting({
    otp,
    isSubmitting
  });

  return (
    <div className="flex justify-center items-center flex-col gap-4">
      <p className="text-sm text-gray-500 mb-2">
        Enter the code sent to your email
      </p>
      <InputOTP
        maxLength={6}
        pattern={REGEXP_ONLY_DIGITS}
        autoFocus
        value={otp}
        onChange={(value) => setOtp?.(value)}
      >
        <InputOTPGroup>
          <InputOTPSlot index={0} />
          <InputOTPSlot index={1} />
          <InputOTPSlot index={2} />
          <InputOTPSlot index={3} />
          <InputOTPSlot index={4} />
          <InputOTPSlot index={5} />
        </InputOTPGroup>
      </InputOTP>
      
      <div className="mt-2 text-center">
        {canResendOtp ? (
          <Button
            type="button"
            variant="ghost"
            className="text-sm text-blue-500"
            onClick={handleResendOtp}
            disabled={isSubmitting}
          >
            Resend code
          </Button>
        ) : (
          <p className="text-xs text-gray-500">
            Resend code in {timeLeft} seconds
          </p>
        )}
      </div>

      <Button
        type="submit"
        disabled={isSubmitting || !isFormValid || otp?.length !== 6}
        className="w-full h-12 text-base font-medium mt-2"
        loading={isSubmitting}
      >
        Create account
      </Button>
    </div>
  );
};

export default OtpVerificationForm; 