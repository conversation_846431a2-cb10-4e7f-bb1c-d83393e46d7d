"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";
import { useUserDetailsErrorReporting } from "../../hooks/useUserDetailsErrorReporting";
import { AUTH_ERRORS } from "../../constants/errorMessages";

interface UserDetailsFormProps {
  form: UseFormReturn<any>;
  isEmailAvailable: boolean | null;
  isUsernameAvailable: boolean | null;
  isCheckingEmail: boolean;
  isCheckingUsername: boolean;
  isSubmitting: boolean;
  isGeneratingOtp: boolean;
  handleNext: {
    onNext: () => Promise<void>;
    isFormValid: boolean;
  };
}

const UserDetailsForm = ({
  form,
  isEmailAvailable,
  isUsernameAvailable,
  isCheckingEmail,
  isCheckingUsername,
  isSubmitting,
  isGeneratingOtp,
  handleNext,
}: UserDetailsFormProps) => {
  const { formState, register } = form;
  const email = form.watch("email");
  const username = form.watch("username");
  const usernamePattern = /^[a-z0-9_.]*$/;

  // Use the custom hook for error reporting
  useUserDetailsErrorReporting({
    form,
    email,
    username,
    isCheckingEmail,
    isCheckingUsername,
    isEmailAvailable,
    isUsernameAvailable
  });

  return (
    <>
      <div>
        <Input
          id="email"
          type="email"
          placeholder="Email"
          {...register("email", {
            onChange: (e) => {
              // Convert to lowercase
              e.target.value = e.target.value.toLowerCase();
              // Filter out emoji and invalid email characters
              if (e.target.value) {
                // Only keep valid email characters
                const filteredValue = e.target.value.replace(/[^\x00-\x7F]+|[^a-z0-9._%+\-@]/g, '');
                if (filteredValue !== e.target.value) {
                  e.target.value = filteredValue;
                  form.setValue("email", filteredValue);
                }
              }
            },
          })}
          className="h-12"
        />
        {formState?.errors?.email?.message && (
          <p className="text-xs text-red-500 mt-1">
            {String(formState.errors.email.message)}
          </p>
        )}
        {email && !formState?.errors?.email && (
          <>
            {!isCheckingEmail && isEmailAvailable === false && (
              <p className="text-xs text-red-500 mt-1">
                {AUTH_ERRORS.EMAIL_ALREADY_REGISTERED}
              </p>
            )}
          </>
        )}
      </div>

      <div>
        <Input
          id="password"
          type="password"
          placeholder="Password"
          {...register("password")}
          className="h-12"
        />
        {formState?.errors?.password?.message && (
          <p className="text-xs text-red-500 mt-1">
            {String(formState.errors.password.message)}
          </p>
        )}
      </div>

      <div>
        <Input
          id="name"
          type="text"
          placeholder="Full Name"
          {...register("name", {
            onChange: (e) => {
              // Filter out everything except letters, and spaces
              if (e.target.value) {
                const filteredValue = e.target.value.replace(/[^a-zA-Z\s]/g, '');
                if (filteredValue !== e.target.value) {
                  e.target.value = filteredValue;
                  form.setValue("name", filteredValue);
                }
              }
            }
          })}
          className="h-12"
        />
        {formState?.errors?.name?.message && (
          <p className="text-xs text-red-500 mt-1">
            {String(formState.errors.name.message)}
          </p>
        )}
      </div>

      <div>
        <Input
          id="username"
          type="text"
          placeholder="Username"
          {...register("username", {
            onChange: (e) => {
              // Replace any characters that don't match our pattern
              const value = e.target.value;
              if (!usernamePattern.test(value)) {
                e.target.value = value.replace(/[^a-z0-9_.]/g, '');
                form.setValue("username", e.target.value);
              }
            }
          })}
          className="h-12"
        />
        {formState?.errors?.username?.message && (
          <p className="text-xs text-red-500 mt-1">
            {String(formState.errors.username.message)}
          </p>
        )}
        {username && !formState?.errors?.username && (
          <>
            {!isCheckingUsername && isUsernameAvailable === false && (
              <p className="text-xs text-red-500 mt-1">
                {AUTH_ERRORS.USERNAME_ALREADY_TAKEN}
              </p>
            )}
          </>
        )}
      </div>

      <Button
        type="submit"
        disabled={isSubmitting || !handleNext?.isFormValid}
        className="w-full h-12 text-base font-medium"
        loading={isSubmitting || isGeneratingOtp}
      >
        Next
      </Button>
    </>
  );
};

export default UserDetailsForm; 