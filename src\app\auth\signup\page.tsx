"use client";

import { signUpSchema } from "@/lib/utils/validation";
import { zodResolver } from "@hookform/resolvers/zod";
import { SubmitHandler, useForm } from "react-hook-form";
import { useState, useCallback, useEffect } from "react";
import { z } from "zod";
import { SignUp } from "@/lib/actions/auth/SignUp";
import { FormError } from "@/components/FormStatus/Form-error";
import { getSession } from "next-auth/react";
import { mapToSignupResponse } from "@/lib/utils/modification";
import { useAppDispatch } from "@/lib/redux/hooks";
import { setUser } from "@/lib/redux/slices/auth/authSlice";
import { usePostHog } from 'posthog-js/react';
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSignupErrorReporting } from "../hooks/useSignupErrorReporting";
import { AUTH_ERRORS } from "../constants/errorMessages";

// Import component files
import UserDetailsForm from "./components/UserDetailsForm";
import OtpVerificationForm from "./components/OtpVerificationForm";

import {
  useEmailCheckMutation,
  useGenerateOtpMutation,
} from "@/lib/redux/slices/auth/authApi";
import { useCheckUsernameMutation } from "@/lib/redux/slices/user/userApi";
import { isEmailValid, isUsernameFormatValid } from "@/lib/utils/string";

type FormFields = z.infer<typeof signUpSchema>;

export default function SignUpPage() {
  const form = useForm<FormFields>({
    resolver: zodResolver(signUpSchema),
  });
  const [formStep, setFormStep] = useState<number>(1);
  const [generateOtp, { isLoading: isGeneratingOtp }] =
    useGenerateOtpMutation();

  const router = useRouter();
  const dispatch = useAppDispatch();
  const posthog = usePostHog();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [otp, setOtp] = useState<string>("");
  const [checkEmail] = useEmailCheckMutation();
  const [checkUsername] = useCheckUsernameMutation();

  // Use custom hook for error reporting
  const { reportSignupError } = useSignupErrorReporting();

  // Add states for email and username availability
  const [isEmailAvailable, setIsEmailAvailable] = useState<boolean | null>(
    null
  );
  const [isUsernameAvailable, setIsUsernameAvailable] = useState<
    boolean | null
  >(null);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  // Add states for OTP resend functionality
  const [canResendOtp, setCanResendOtp] = useState<boolean>(true);
  const [timeLeft, setTimeLeft] = useState<number>(0);

  const { watch } = form;
  const email = watch("email");
  const password = watch("password");
  const name = watch("name");
  const username = watch("username");

  // Update form validation to include availability checks
  const isFormFilled = Boolean(email && password && name && username);
  const isFormValid = isFormFilled && isEmailAvailable === true && isUsernameAvailable === true;

  // OTP resend timer handler
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && !canResendOtp) {
      setCanResendOtp(true);
    }
  }, [timeLeft, canResendOtp]);

  // Check email availability with debounce
  useEffect(() => {
    const timer = setTimeout(async () => {
      const isValidEmail = isEmailValid(email);
      if (email && !form.formState?.errors?.email && isValidEmail) {
        setIsCheckingEmail(true);
        try {
          const result = await checkEmail({ email }).unwrap();
          setIsEmailAvailable(!result?.existing);
        } catch (err) {
          console.error("Failed to check email:", err);
          setIsEmailAvailable(null);
        } finally {
          setIsCheckingEmail(false);
        }
      }
    }, 600);

    return () => clearTimeout(timer);
  }, [email, checkEmail, form.formState?.errors?.email]);

  // Check username availability with debounce
  useEffect(() => {
    const timer = setTimeout(async () => {
      const isValidUsername = isUsernameFormatValid(username);
      if (username && !form.formState?.errors?.username && isValidUsername) {
        setIsCheckingUsername(true);
        try {
          const result = await checkUsername({ username }).unwrap();
          setIsUsernameAvailable(false);
        } catch (err) {
          console.error("Failed to check username:", err);
          setIsUsernameAvailable(true);
        } finally {
          setIsCheckingUsername(false);
        }
      }
    }, 600);

    return () => clearTimeout(timer);
  }, [username, checkUsername, form.formState?.errors?.username]);

  const handleGenerateOtp = useCallback(async () => {
    try {
      const result = await generateOtp({ email: email }).unwrap();
      setFormStep(2);
      // Set resend cooldown
      setCanResendOtp(false);
      setTimeLeft(30);
    } catch (err) {
      const errorMessage = (err as Error)?.message || "An error occurred";
      setError(errorMessage);
      // Report visible error to Sentry
      reportSignupError(errorMessage, "generateOtp");
    }
  }, [generateOtp, email, reportSignupError]);

  const handleResendOtp = useCallback(async () => {
    if (!canResendOtp) return;
    try {
      await generateOtp({ email: email }).unwrap();
      // Start cooldown timer
      setCanResendOtp(false);
      setTimeLeft(30);
    } catch (err) {
      const errorMessage = (err as Error)?.message || "An error occurred";
      setError(errorMessage);
      // Report visible error to Sentry
      reportSignupError(errorMessage, "resendOtp");
    }
  }, [generateOtp, email, canResendOtp, reportSignupError]);

  const onSubmit: SubmitHandler<FormFields> =
    async (data) => {
      if (formStep === 1) {
        handleGenerateOtp();
        return;
      }
      setIsSubmitting(true);
      setError(null);

      try {
        const [firstName, lastName] = data.name.split(" ");
        const result = await SignUp({
          email: data.email,
          password: data.password,
          name: data.name,
          first_name: firstName || "",
          last_name: lastName || "",
          username: data.username,
          code: otp,
          redirect: false,
        });

        if (result?.error) {
          const errorMessage = result.error;
          setError(errorMessage);
          // Report visible error to Sentry
          reportSignupError(errorMessage, "signUp");
          return;
        }

        const session = await getSession();
        const mappedResponse = mapToSignupResponse(session);
        dispatch(setUser(mappedResponse));

        if (!posthog?._isIdentified()) {
          posthog?.identify(mappedResponse?.email || "");
        }
        window.location.href = "/";
      } catch (err) {
        console.log(err);
        const errorMessage = (err as Error)?.message || "An error occurred";
        setError(errorMessage);
        // Report visible error to Sentry
        reportSignupError(errorMessage, "signUp");
      } finally {
        setIsSubmitting(false);
      }
    }
    

  return (
    <div className="w-full">
      <form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
        {formStep === 1 && (
          <UserDetailsForm
            form={form}
            isEmailAvailable={isEmailAvailable}
            isUsernameAvailable={isUsernameAvailable}
            isCheckingEmail={isCheckingEmail}
            isCheckingUsername={isCheckingUsername}
            isSubmitting={isSubmitting}
            isGeneratingOtp={isGeneratingOtp}
            handleNext={{
              onNext: handleGenerateOtp,
              isFormValid: isFormValid,
            }}
          />
        )}

        {formStep === 2 && (
          <OtpVerificationForm
            otp={otp}
            setOtp={setOtp}
            isSubmitting={isSubmitting}
            isFormValid={isFormValid}
            handleResendOtp={handleResendOtp}
            canResendOtp={canResendOtp}
            timeLeft={timeLeft}
          />
        )}

        {error && <FormError message={error} />}
      </form>

      <div className="mt-4 mb-2 text-center">
        <p className="text-sm text-gray-500">
          Already have an account?{" "}
          <Link href="/auth/signin" className="text-blue-500 hover:underline">
            Sign in
          </Link>
        </p>
      </div>
    </div>
  );
}
