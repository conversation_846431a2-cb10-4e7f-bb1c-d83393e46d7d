"use client";

import { hasTicketFormProblems } from "@/app/cart/utils/cartUtils";
import type { CartFooterProps } from "../types";

import WaiverModal from "./WaiverModal";
import { CartSummary } from "./CartSummary";
import { useWaiverHandling } from "../hooks/useWaiverHandling";
import { useCheckoutHandling } from "../hooks/useCheckoutHandling";

export default function CartFooter({
  cartData,
  refetchCart,
  hasProblems,
}: CartFooterProps) {
  const hasFormProblems = hasTicketFormProblems(cartData?.problems);

  const {
    showWaiver,
    currentWaiverIndex,
    allWaivers,
    handleWaiverAccept,
    handleWaiverClose,
    startWaiverProcess,
  } = useWaiverHandling(cartData);

  const { loading, isSubmitting, handleCheckout } = useCheckoutHandling(
    cartData,
    refetchCart
  );

  const handleCheckoutClick = () => {
    if (allWaivers.length > 0) {
      startWaiverProcess();
    } else {
      handleCheckout();
    }
  };

  const handleWaiverAcceptWithCheckout = async (signature: string) => {
    const newSignatures = await handleWaiverAccept(signature);
    if (newSignatures) {
      handleCheckout(newSignatures);
    }
  };

  if (!cartData) return null;

  return (
    <>
      <CartSummary
        cartData={cartData}
        refetchCart={refetchCart}
        loading={loading}
        isSubmitting={isSubmitting}
        hasProblems={hasProblems}
        onCheckout={handleCheckoutClick}
      />

      {hasProblems && hasFormProblems && (
        <div className="mt-4 flex justify-end">
          <div className="w-full md:w-[300px] mb-2 text-red-500 text-sm">
            <div className="flex flex-col gap-2">
              <p>
                There was an issue with your ticket form. Please remove the
                event and try to add it again.
              </p>
            </div>
          </div>
        </div>
      )}

      <WaiverModal
        isOpen={showWaiver}
        onClose={handleWaiverClose}
        onSave={handleWaiverAcceptWithCheckout}
        pdfUrl={allWaivers[currentWaiverIndex]?.waiverUrl}
      />
    </>
  );
}
