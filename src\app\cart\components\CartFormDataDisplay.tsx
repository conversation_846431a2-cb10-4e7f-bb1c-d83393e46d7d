import React from "react";
import Image from "next/image";
import VehicleInfo from "./VehicleInfo";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

// Add this interface to match the actual vehicle data structure
interface VehicleImage {
  image: string;
}

interface VehicleData {
  customMakeName: string;
  vehicleType: string;
  year: string;
  make: string;
  modelName: string;
  modificationText: string;
  vehicleImages: VehicleImage[];
}

interface FormData {
  data: {
    vehicle?: VehicleData;
    instagram?: string;
    teamClubName?: string;
    vendorName?: string;
    merchandiseYouPlanToSell?: string;
    website?: string;
    phoneNumberICanReachYouAtOnEventDay?: string;
    profileImageForTheCompany?: {
      image: string;
    };
  };
  formId: string;
  formName: string;
  formType: string;
  templateId: string;
}

interface CartFormDataDisplayProps {
  formData: FormData[];
  openFormModal: () => void;
}

const CartFormDataDisplay: React.FC<CartFormDataDisplayProps> = ({
  formData,
  openFormModal,
}) => {
  if (!formData?.length) return null;

  const EditButton = () => {
    return (
      <p
        onClick={openFormModal}
        className="text-xs text-blue-500 cursor-pointer"
      >
        Edit
      </p>
    );
  };
  const renderFormData = (form: FormData) => {
    const { formType, formName, data } = form;

    if (formType === "vehicle_registration") {
      const vehicleData = data.vehicle;

      if (vehicleData) {
        return (
          <div
            key={form.formId}
            className="mb-2 bg-[#F2F2F2] p-2 rounded-lg last:mb-0 w-full md:w-[300px] flex justify-between"
          >
            <VehicleInfo
              vehicleData={{
                customMakeName: vehicleData.customMakeName,
                vehicleType: vehicleData.vehicleType,
                year: vehicleData.year,
                make: vehicleData.make,
                modelName: vehicleData.modelName,
                modificationText: vehicleData.modificationText,
                teamName: data.teamClubName,
                vehicleImages: vehicleData.vehicleImages,
              }}
            />
            <EditButton />
          </div>
        );
      }
    }

    // Add Vendor form type handling
    if (formType === "vendor_booth") {
      if (!data) return null;

      const {
        vendorName = "",
        merchandiseYouPlanToSell = "",
        website = "",
        profileImageForTheCompany = {
          image: "",
        },
      } = data;

      return (
        <div
          key={form.formId}
          className="mb-2 bg-[#F2F2F2] p-2 rounded-lg last:mb-0 w-full md:w-[300px] flex flex-col sm:flex-row justify-between gap-2"
        >
          <div className="flex items-start gap-2">
            <Image
              src={profileImageForTheCompany?.image || IMAGE_LINKS.VENDOR}
              alt="Autolnk Vendor"
              width={85}
              height={85}
              className={`rounded-lg ${
                profileImageForTheCompany?.image
                  ? "w-[60px] h-[60px] sm:w-[75px] sm:h-[75px]"
                  : "w-[70px] h-[70px] sm:w-[85px] sm:h-[85px]"
              }`}
            />
            <div className="flex flex-col gap-1 sm:gap-2">
              <p className="font-medium text-sm sm:mb-2">{vendorName}</p>
              <div className="flex flex-col gap-0.5 sm:gap-1">
                <p className="text-xs text-gray-600 line-clamp-2">
                  {merchandiseYouPlanToSell}
                </p>
                <p className="text-xs text-gray-600 truncate">{website}</p>
              </div>
            </div>
          </div>
          <div className="self-end sm:self-center">
            <EditButton />
          </div>
        </div>
      );
    }

    return (
      <div
        key={form.formId}
        className="mb-2 bg-[#F2F2F2] p-2 rounded-lg last:mb-0 w-full md:w-[300px] flex justify-between"
      >
        <div className="flex items-center gap-2">
          <Image src="/file.svg" alt="Form" width={20} height={20} />
          <p className="font-medium text-sm">{formName}</p>
        </div>
        <EditButton />
      </div>
    );
  };

  return (
    <div className="mt-2">{formData.map((form) => renderFormData(form))}</div>
  );
};

export default CartFormDataDisplay;
