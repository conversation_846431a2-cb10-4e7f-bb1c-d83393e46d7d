"use client";

import React, { useCallback, memo, useState } from "react";
import { useSessionData } from "@/lib/hooks/useSession";

// Components
import QuantityButton from "./QuantityButton";
import DeleteButton from "./DeleteButton";
import Image from "next/image";
import {
  useRemoveFromCartMutation,
  useRemoveVoucherMutation,
  useUpdateQuantityMutation,
} from "@/lib/redux/slices/cart/cartApi";
import CartFormDataDisplay from "./CartFormDataDisplay";
import TicketForm from "@/app/e/[slug]/components/Form/TicketForm";
import { useDispatch } from "react-redux";
import { useCart } from "@/hooks/useCart";
import { isZero } from "@/lib/utils/numberUtil";

// Types
interface VehicleFormData {
  vehicle?: {
    vehicle_images?: { image: string }[];
    name?: string;
    year?: string;
  };
  team_name?: string;
}

interface CartProduct {
  selectedVariants: any;
  unitPrice: number;
  id: string;
  title: string;
  img: string;
  eventTotal: number;
  uid: string;
  quantity: number;
  eventTicket: string;
  totalPrice: number;
  eventDetails?: {
    eventName: string;
    eventPhoto: { photo: string }[];
    ticketName: string;
    ticketApprovalRequired: boolean;
  };
  ticket: {
    ticketId: string;
    quantity: number;
    price: number;
    ticketType: string;
    total: number;
  };
  vehicleFormData?: VehicleFormData;
  vehicle?: any;
  requiresVehicle?: boolean;
}

interface CartProductOverviewProps {
  product: CartProduct;
  teamName?: string;
  cartData: CartProduct[];
  refetchCart: () => void;
  formData?: any;
  vehicleImages?: any;
}

interface ViewProps {
  product: CartProduct;
  handleQuantityDecrease: () => void;
  handleQuantityIncrease: () => void;
  handleRemoveTicket: () => void;
  handleVehicleEditModalOpen: () => void;
  teamName?: string;
  isUpdatingQuantity: boolean;
  isRemovingFromCart: boolean;
  formData?: any;
  vehicleImages?: any;
  openFormModal: () => void;
  isRemovingVoucher: boolean;
  selectedVariantsInfoArray: string[];
  isVehicleFormInFormData: boolean;
}

const CartProductOverview: React.FC<CartProductOverviewProps> = memo(
  ({ product, teamName, refetchCart, cartData, formData, vehicleImages }) => {
    const [modalState, setModalState] = useState(false);
    const [removeFromCart, { isLoading: isRemovingFromCart }] =
      useRemoveFromCartMutation();
    const [updateCart, { isLoading: isUpdatingQuantity }] =
      useUpdateQuantityMutation();
    const [removeVoucher, { isLoading: isRemovingVoucher }] =
      useRemoveVoucherMutation();

    const { data: snakeCasedCartData } = useCart();
    const snakeCasedProduct = snakeCasedCartData?.checkout?.lines?.find(
      (line: any) => line.id === product.id
    );

    const isFreeTicket = isZero(product?.unitPrice * product?.quantity);
    const isContainedForms =
      Array.isArray(formData) && formData?.some((form: any) => form.formId);
    const isFreeTicketNonForm = isFreeTicket && !isContainedForms;

    const isApprovalRequired = product?.eventDetails?.ticketApprovalRequired;

    const isVehicleFormInFormData =
      Array.isArray(formData) &&
      formData?.some((form: any) => form.formType === "vehicle_registration");

    const selectedVariantsInfoArray = product?.selectedVariants?.map(
      (selectedVariant: any) => selectedVariant?.variant?.name
    );

    const handleQuantityDecrease = useCallback(async () => {
      if (!product?.quantity || product.quantity <= 1) {
        if (cartData?.length === 1) {
          await removeVoucher();
        }
        await removeFromCart({
          id: product?.id,
          type: "event_ticket",
        });
        await refetchCart();
        return;
      }

      const newQuantity = product.quantity - 1;
      await updateCart({
        id: product?.id,
        type: "event_ticket",
        quantity: newQuantity,
      });
      await refetchCart();
    }, [product, removeFromCart, updateCart, cartData?.length, removeVoucher]);

    const handleQuantityIncrease = useCallback(async () => {
      if (!product?.quantity || product.vehicleFormData) return;

      const newQuantity = product.quantity + 1;
      await updateCart({
        id: product?.id,
        type: "event_ticket",
        quantity: newQuantity,
      });
      await refetchCart();
    }, [product, updateCart]);

    const handleRemoveTicket = useCallback(async () => {
      if (cartData?.length === 1) {
        await removeVoucher();
      }
      await removeFromCart({ id: product?.id, type: "event_ticket" });
      await refetchCart();
    }, [product?.id, cartData?.length, removeVoucher]);

    const openFormModal = () => {
      setModalState(true);
    };

    const canIncreaseQuantity =
      isVehicleFormInFormData ||
      isFreeTicketNonForm ||
      isContainedForms ||
      isApprovalRequired;

    return (
      <div className="my-8">
        <MobileView
          product={product}
          handleQuantityDecrease={handleQuantityDecrease}
          handleQuantityIncrease={handleQuantityIncrease}
          handleRemoveTicket={handleRemoveTicket}
          handleVehicleEditModalOpen={() => {}}
          teamName={teamName}
          isUpdatingQuantity={isUpdatingQuantity}
          isRemovingFromCart={isRemovingFromCart}
          isRemovingVoucher={isRemovingVoucher}
          formData={formData}
          vehicleImages={vehicleImages}
          openFormModal={openFormModal}
          selectedVariantsInfoArray={selectedVariantsInfoArray}
          isVehicleFormInFormData={canIncreaseQuantity || false}
        />
        <DesktopView
          product={product}
          handleQuantityDecrease={handleQuantityDecrease}
          handleQuantityIncrease={handleQuantityIncrease}
          handleRemoveTicket={handleRemoveTicket}
          handleVehicleEditModalOpen={() => {}}
          teamName={teamName}
          isUpdatingQuantity={isUpdatingQuantity}
          isRemovingFromCart={isRemovingFromCart}
          isRemovingVoucher={isRemovingVoucher}
          formData={formData}
          vehicleImages={vehicleImages}
          openFormModal={openFormModal}
          isVehicleFormInFormData={canIncreaseQuantity || false}
          selectedVariantsInfoArray={selectedVariantsInfoArray}
        />
        <TicketForm
          open={modalState}
          onClose={() => setModalState(false)}
          isEdit={true}
          ticketFormData={snakeCasedProduct}
          eventId={product?.eventDetails?.eventId}
          ticketId={product?.eventTicket}
          lineId={product?.id}
        />
      </div>
    );
  }
);

const MobileView: React.FC<ViewProps> = memo(
  ({
    product,
    handleQuantityDecrease,
    handleQuantityIncrease,
    handleRemoveTicket,
    handleVehicleEditModalOpen,
    isUpdatingQuantity,
    isRemovingFromCart,
    isRemovingVoucher,
    selectedVariantsInfoArray,
    formData,
    openFormModal,
    isVehicleFormInFormData,
  }) => {
    const itemImage =
      product?.eventDetails?.eventPhoto?.find((photo: any) => photo.type === 1)
        ?.photo || product.img;

    const productName =
      product?.selectedVariants?.[0]?.variant?.productDetails?.name;

    return (
      <div className="flex gap-4 justify-between md:hidden">
        <Image
          width={120}
          height={100}
          alt="event-image"
          src={itemImage}
          className="object-cover h-[100px] w-[120px]"
        />
        <div className="flex flex-col w-full justify-between">
          <div>
            <a
              href={`/e/${product?.eventDetails?.eventId}`}
              className="text-md font-semibold hover:underline"
            >
              {product?.eventDetails?.eventName || productName}
            </a>
            <p className="text-xs">
              {product?.eventDetails?.ticketName || product?.ticket?.ticketType}
            </p>
            <div className="mt-2">
              {selectedVariantsInfoArray?.length > 0 &&
                selectedVariantsInfoArray?.map((item) => (
                  <p className="text-xs" key={item}>
                    {productName} - {item}
                  </p>
                ))}
            </div>
            <p className="text-xs">${Number(product?.unitPrice).toFixed(2)}</p>
          </div>

          <div className="mt-2">
            <QuantityButton
              quantity={product.quantity || product.ticket.quantity}
              onDecrease={handleQuantityDecrease}
              onIncrease={handleQuantityIncrease}
              isDisabled={isVehicleFormInFormData}
              isLoading={
                isUpdatingQuantity || isRemovingFromCart || isRemovingVoucher
              }
            />
          </div>
          {formData && (
            <CartFormDataDisplay
              formData={formData}
              openFormModal={openFormModal}
            />
          )}
        </div>
        <div className="flex flex-col justify-between">
          <p>${Number(product?.totalPrice).toFixed(2)}</p>
          <DeleteButton onDelete={handleRemoveTicket} />
        </div>
      </div>
    );
  }
);

const DesktopView: React.FC<ViewProps> = memo(
  ({
    product,
    handleQuantityDecrease,
    handleQuantityIncrease,
    handleRemoveTicket,
    handleVehicleEditModalOpen,
    isUpdatingQuantity,
    isRemovingVoucher,
    isRemovingFromCart,
    selectedVariantsInfoArray,
    formData,
    openFormModal,
    isVehicleFormInFormData,
  }) => {
    const itemImage =
      product?.eventDetails?.eventPhoto?.find((photo: any) => photo.type === 1)
        ?.photo || product.img;

    const productName =
      product?.selectedVariants?.[0]?.variant?.productDetails?.name;

    return (
      <div className="hidden md:flex gap-8 justify-center items-center">
        <div className="flex justify-between flex-1 items-start">
          <div className="w-full md:flex md:gap-3 md:min-w-[500px]">
            <Image
              width={100}
              height={100}
              alt="event-image"
              src={itemImage}
              className="object-cover rounded-md h-[100px] w-[100px]"
            />
            <div className="hidden md:flex flex-col">
              <a
                href={`/e/${product?.eventDetails?.eventId}`}
                className="text-md font-semibold hover:underline"
              >
                {product?.eventDetails?.eventName || productName}
              </a>
              <p className="text-xs my-1">
                {product?.eventDetails?.ticketName ||
                  product?.ticket?.ticketType}
              </p>
              <p className="text-xs">
                ${Number(product?.unitPrice).toFixed(2)}
              </p>
              <div className="mt-2">
                {selectedVariantsInfoArray?.length > 0 &&
                  selectedVariantsInfoArray?.map((item) => (
                    <p className="text-xs" key={item}>
                      {productName} - {item}
                    </p>
                  ))}
              </div>
              {formData && (
                <CartFormDataDisplay
                  formData={formData}
                  openFormModal={openFormModal}
                />
              )}
            </div>
          </div>
        </div>
        <div className="flex justify-between flex-1 sm:mt-5">
          <div className="flex w-full flex-col">
            <QuantityButton
              quantity={product.quantity || product.ticket.quantity}
              onDecrease={handleQuantityDecrease}
              onIncrease={handleQuantityIncrease}
              isDisabled={isVehicleFormInFormData}
              isLoading={
                isUpdatingQuantity || isRemovingFromCart || isRemovingVoucher
              }
            />

            <DeleteButton onDelete={handleRemoveTicket} version="text" />
          </div>
          <p>${Number(product?.totalPrice).toFixed(2)}</p>
        </div>
      </div>
    );
  }
);

export default CartProductOverview;
