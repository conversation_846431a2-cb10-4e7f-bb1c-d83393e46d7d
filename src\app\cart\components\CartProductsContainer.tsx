interface CartProductContainerProps {
  children: React.ReactNode;
  type: "ticket" | "merch";
}

const CartProductContainer: React.FC<CartProductContainerProps> = ({
  children,
  type,
}) => {
  return (
    <div>
      <div className="border-b-1 pb-2 md:flex md:justify-between md:gap-12 md:items-center">
        <div className="flex justify-between md:flex-1 md:min-w-[490px]">
          <span>{type === "ticket" ? "Ticket" : "Product"}</span>
        </div>
        <div className="hidden md:flex justify-between md:flex-1">
          <span>Quantity</span>
          <span>Total</span>
        </div>
      </div>
      <div className="py-6">{children}</div>
      <div className="w-full border-b-1"></div>
    </div>
  );
};

export default CartProductContainer;
