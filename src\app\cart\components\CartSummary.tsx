import { Button } from "@nextui-org/react";
import Discount from "./Discount";
import type { CartData } from "../types";

type CartSummaryProps = {
  cartData: CartData;
  refetchCart: () => void;
  loading: boolean;
  isSubmitting: boolean;
  hasProblems: boolean;
  onCheckout: () => void;
};

export const CartSummary = ({
  cartData,
  refetchCart,
  loading,
  isSubmitting,
  hasProblems,
  onCheckout,
}: CartSummaryProps) => {
  if (!cartData) return null;

  const shippingPrice = cartData?.checkout?.shippingPrice || 0;
  const discountAmount = cartData?.checkout?.discountAmount || 0;
  const discounts = cartData?.checkout?.discounts || [];
  const processingFee = Number(cartData?.checkout?.processingFee || 0);
  const platformFee = Number(cartData?.checkout?.platformFee || 0);
  const serviceCharge = Number(platformFee - processingFee || 0);
  const subtotal = Number(cartData?.checkout?.subtotal || 0);

  return (
    <div className="flex justify-end">
      <div className="w-full md:w-[300px]">
        <Discount
          refetchCart={refetchCart}
          discounts={discounts}
          voucherCode={cartData?.checkout?.voucherCode}
        />
        <div className="my-4 flex flex-col gap-2">
          <div className="flex justify-between items-center gap-2">
            <p className="text-[14px]">Subtotal</p>
            <p className="text-[14px]">${Number(subtotal).toFixed(2)}</p>
          </div>
          <div className="flex justify-between gap-2">
            <div className="flex flex-col">
              <p className="text-[14px]">Platform & Service Fee</p>
              <span className="text-[10px] text-[#A9A9A9]">
                (card processing ${processingFee?.toFixed(2)}, service charge $
                {serviceCharge?.toFixed(2)})
              </span>
            </div>
            <p className="text-[14px]">${platformFee?.toFixed(2)}</p>
          </div>
          {shippingPrice > 0 && (
            <div className="flex justify-between items-center gap-2">
              <p className="text-[14px]">Shipping Charges</p>
              <p className="text-[14px]">${Number(shippingPrice).toFixed(2)}</p>
            </div>
          )}
          {discountAmount > 0 && (
            <div className="flex justify-between items-center gap-2">
              <p className="text-[14px]">Discount</p>
              <p className="text-[14px] font-italic">
                - ${Number(discountAmount).toFixed(2)}
              </p>
            </div>
          )}
          <div className="flex justify-between items-center gap-2">
            <p className="text-[17px] font-medium">Total</p>
            <p className="text-[17px] font-medium">
              ${Number(cartData?.checkout?.total || 0).toFixed(2)}
            </p>
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <Button
            className="w-full bg-black text-white text-md rounded-none h-12 cursor-pointer sm:w-[200px]"
            onPress={onCheckout}
            disabled={loading || isSubmitting || hasProblems}
            isDisabled={loading || isSubmitting || hasProblems}
            isLoading={loading || isSubmitting}
          >
            {loading || isSubmitting ? "Processing..." : "Checkout"}
          </Button>
        </div>
      </div>
    </div>
  );
};
