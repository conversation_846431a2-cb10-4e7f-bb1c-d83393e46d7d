// CartTickets.tsx
"use client";
import CartProductContainer from "./CartProductsContainer";
import CartProductOverview from "./CartProductOverview";
import { MerchProductOverview } from "./MerchProductOverview";

interface CartTicketsProps {
  cartData: any[];
  customerInfo: any;
  refetchCart: () => void;
}

export const CartTickets: React.FC<CartTicketsProps> = ({
  cartData,
  customerInfo,
  refetchCart,
}) => {
  return (
    <div className="my-9">
      <CartProductContainer type="ticket">
        {cartData &&
          cartData?.map((ticket: any) => {
            if (ticket?.type === "product") {
              return (
                <MerchProductOverview
                  key={`${ticket?.type}-${ticket?.uid}`}
                  product={ticket}
                />
              );
            }

            return (
              <CartProductOverview
                key={`${ticket?.type || "ticket"}-${ticket?.uid}`}
                product={ticket}
                cartData={cartData}
                refetchCart={refetchCart}
                vehicleImages={ticket?.vehicleImages || []}
                formData={ticket?.formResponseData || null}
              />
            );
          })}
      </CartProductContainer>
    </div>
  );
};
