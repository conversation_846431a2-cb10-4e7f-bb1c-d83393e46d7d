import React, { memo } from "react";
import { RiDeleteBin6Line } from "react-icons/ri";

interface DeleteButtonProps {
  onDelete: () => void;
  version?: 'icon' | 'text';
}

const DeleteButton: React.FC<DeleteButtonProps> = memo(
  ({ onDelete, version = "icon" }) =>
    version === "text" ? (
      <button className="cursor-pointer underline items-start p-0 h-10 w-full md:w-1/3" onClick={onDelete}>
        Remove
      </button>
    ) : (
      <RiDeleteBin6Line
        size={36}
        className="cursor-pointer p-2 w-10 h-10"
        onClick={onDelete}
      />
    )
);

export default DeleteButton;
