"use client";

import {
  useApplyVoucherMutation,
  useRemoveVoucherMutation,
} from "@/lib/redux/slices/cart/cartApi";
import { TOASTS } from "@/lib/utils/constants";
import { Button, Input, Spinner } from "@nextui-org/react";
import Image from "next/image";
import { useState } from "react";
import toast from "react-hot-toast";

export default function Discount({
  discounts,
  refetchCart,
  voucherCode,
}: {
  discounts: string[];
  refetchCart: () => void;
  voucherCode: any;
}) {
  const [discountInput, setDiscountInput] = useState("");

  const [applyVoucher, { isLoading: isApplyingDiscount }] =
    useApplyVoucherMutation();
  const [removeVoucher, { isLoading: isRemovingVoucher }] =
    useRemoveVoucherMutation();

  const handleApplyDiscount = async () => {
    try {
      if (discounts?.length > 0) {
        await removeVoucher().unwrap();
      }

      await applyVoucher({
        code: discountInput?.trim()?.toLowerCase(),
      }).unwrap();
      await refetchCart();
      setDiscountInput("");
      toast.success(TOASTS?.DISCOUNT_APPLIED);
    } catch (error: any) {
      if (error?.data?.error === TOASTS?.DISCOUNT_USAGE_LIMIT_REACHED) {
        toast(TOASTS?.DISCOUNT_USAGE_LIMIT_REACHED);
      } else {
        toast.error(
          error?.data?.error ||
            error?.message ||
            error?.data?.message ||
            TOASTS?.DISCOUNT_ERROR
        );
      }
    }
  };

  const removeDiscount = async (code: string) => {
    try {
      await removeVoucher({
        code: code?.toLowerCase(),
      }).unwrap();
      await refetchCart();
      toast.success("Discount removed");
    } catch (error) {
      toast.error("Error removing discount");
      toast.error(error.message);
    }
  };

  const isLoading = isApplyingDiscount || isRemovingVoucher;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center">
        <Spinner />
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-between items-center gap-2">
        <Input
          variant="bordered"
          label="Discount code"
          labelPlacement="inside"
          value={discountInput}
          onChange={(e) => setDiscountInput(e.target.value)}
          classNames={{
            base: "max-w-full",
            mainWrapper: "h-[52px]",
            input: "text-sm",
            inputWrapper:
              "h-[52px] border border-default-200 rounded-[4px] px-3",
            innerWrapper: "h-full",
            label: "text-sm",
            description: "text-sm",
            errorMessage: "text-sm",
            helperWrapper: "hidden",
          }}
          isDisabled={isApplyingDiscount || isRemovingVoucher}
        />
        <Button
          size="md"
          className="h-[52px] border-1 px-4 bg-[#1773B0] font-medium text-[#fff] disabled:bg-default-100 disabled:text-[#707070] font-medium rounded-[4px]"
          onPress={handleApplyDiscount}
          disabled={discountInput.length === 0}
          isDisabled={discountInput.length === 0}
        >
          Apply
        </Button>
      </div>

      {voucherCode && (
        <div className="bg-[#F5F5F5] w-fit px-2 py-1 rounded-[4px] mt-4 flex items-center gap-4">
          <Image
            src="/discount.svg"
            alt="Discount success"
            width={15}
            height={15}
          />
          <p className="text-[14px]">{voucherCode}</p>
          <Image
            src="/close.svg"
            alt="remove discount"
            width={15}
            height={15}
            className="cursor-pointer"
            onClick={() => removeDiscount(voucherCode)}
          />
        </div>
      )}
    </>
  );
}
