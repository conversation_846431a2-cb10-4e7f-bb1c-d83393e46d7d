import { memo } from "react";
import dynamic from "next/dynamic";

const WaiverPdfViewer = dynamic(
  () => import("@/components/WaiverPdfViewer/WaiverPdfViewer"),
  {
    ssr: false,
    loading: () => (
      <div className="w-full h-[300px] bg-gray-200 animate-pulse">
        Loading PDF...
      </div>
    ),
  }
);

interface MemoizedWaiverPdfViewerProps {
  pdfUrl: string;
}

const MemoizedWaiverPdfViewer = memo(function MemoizedWaiverPdfViewer({
  pdfUrl,
}: MemoizedWaiverPdfViewerProps) {
  return (
    <div className="w-full h-full">
      <WaiverPdfViewer pdfUrl={pdfUrl} />
      <div className="w-full h-[100px] bg-transparent"></div>
    </div>
  );
});

export default MemoizedWaiverPdfViewer;
