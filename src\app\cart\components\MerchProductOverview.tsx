import {
  useUpdateQuantityMutation,
  useRemoveFromCartMutation,
} from "@/lib/redux/slices/cart/cartApi";
import QuantityButton from "./QuantityButton";
import DeleteButton from "./DeleteButton";
import { CART_STORAGE_KEYS } from "@/lib/utils/constants";
import Image from "next/image";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

interface MerchProductProps {
  product: {
    id: string;
    variant: {
      productDetails: any;
      id: number;
      sku: string;
      name: string;
      price: string;
      media: Array<{
        id: number;
        image: string;
        alt: string;
        type: string;
      }>;
      attributes: Array<{
        attribute: {
          id: number;
          name: string;
          slug: string;
          input_type: string;
        };
        values: Array<{
          id: number;
          name: string;
          value: string;
        }>;
      }>;
    };
    quantity: number;
    unitPrice: string;
    totalPrice: string;
    deliveryCharges: number;
  };
}

export const MerchProductOverview: React.FC<MerchProductProps> = ({
  product,
}) => {
  const [updateQuantity, { isLoading: isUpdatingQuantity }] =
    useUpdateQuantityMutation();
  const [removeFromCart, { isLoading: isRemovingFromCart }] =
    useRemoveFromCartMutation();

  const getLocalVariants = localStorage.getItem(
    CART_STORAGE_KEYS.CART_VARIANTS
  );

  const localVariants = JSON.parse(getLocalVariants || "{}");

  const handleQuantityChange = async (quantity: number) => {
    try {
      await updateQuantity({
        id: product?.id,
        type: "product",
        quantity,
      });
    } catch (error) {
      console.error("Failed to update quantity:", error);
    }
  };

  const handleRemove = async () => {
    try {
      await removeFromCart({ id: product?.id, type: "product" });
    } catch (error) {
      console.error("Failed to remove item:", error);
    }
  };

  const handleDecrease = async () => {
    if (product.quantity <= 1) {
      await handleRemove();
    } else {
      await handleQuantityChange(product.quantity - 1);
    }
  };

  const productImage =
    product.variant.media?.[0]?.image ||
    localVariants[product.variant.id]?.image ||
    IMAGE_LINKS.NO_IMG;
  const attributes = product.variant.attributes.reduce((acc, attr) => {
    acc[attr.attribute.name] = attr.values[0]?.name || "";
    return acc;
  }, {} as Record<string, string>);

  return (
    <div className="my-8">
      {/* Mobile View */}
      <div className="flex gap-4 justify-between md:hidden">
        <Image
          width={120}
          height={100}
          alt={product.variant.media?.[0]?.alt || "product-image"}
          src={productImage}
          className="object-cover h-[100px] w-[120px]"
        />
        <div className="flex flex-col w-full justify-between">
          <div>
            <h2>
              {product?.variant?.productDetails?.name || ""} -{" "}
              {product?.variant?.name || ""}
            </h2>
            <p className="text-xs">${product.unitPrice}</p>
            <div className="text-xs mt-1">
              {Object.entries(attributes).map(([key, value]) => (
                <p key={key} className="text-gray-500 capitalize">
                  {key}: {value}
                </p>
              ))}
            </div>
          </div>
          <QuantityButton
            quantity={product.quantity}
            onDecrease={handleDecrease}
            onIncrease={() => handleQuantityChange(product.quantity + 1)}
            isDisabled={false}
            isLoading={isUpdatingQuantity || isRemovingFromCart}
          />
        </div>
        <div className="flex flex-col justify-between">
          <p>${product.totalPrice}</p>
          <DeleteButton onDelete={handleRemove} />
        </div>
      </div>

      {/* Desktop View */}
      <div className="hidden md:flex gap-8 justify-center items-center">
        <div className="flex justify-between flex-1 items-start">
          <div className="w-full md:flex md:gap-3 md:min-w-[500px]">
            <Image
              width={100}
              height={100}
              alt={product.variant.media?.[0]?.alt || "product-image"}
              src={productImage}
              className="object-cover rounded-md h-[100px] w-[100px]"
            />
            <div className="hidden md:flex flex-col">
              <h2 className="font-semibold truncate">{product.variant.name}</h2>
              <p className="text-xs">${product.unitPrice}</p>
              <div className="text-xs mt-1">
                {Object.entries(attributes).map(([key, value]) => (
                  <p key={key} className="text-gray-500">
                    {key}: {value}
                  </p>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-between flex-1 sm:mt-5">
          <div className="flex w-full flex-col">
            <QuantityButton
              quantity={product.quantity}
              onDecrease={handleDecrease}
              onIncrease={() => handleQuantityChange(product.quantity + 1)}
              isDisabled={false}
              isLoading={isUpdatingQuantity || isRemovingFromCart}
            />
            <DeleteButton onDelete={handleRemove} version="text" />
          </div>

          <p>${Number(product.totalPrice).toFixed(2)}</p>
        </div>
      </div>
    </div>
  );
};
