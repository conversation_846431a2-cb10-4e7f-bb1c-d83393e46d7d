import Image from "next/image";
import { useRouter } from "next/navigation";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
const Nothing = () => {
  const router = useRouter();

  return (
    <div className="flex flex-col justify-center items-center h-[70vh] min-h-screen">
      <Image src={IMAGE_LINKS.EMPTY_CART} alt="Autolnk Empty Cart" width={220} height={300} />
      <p className="text-gray-400">No items in cart</p>
      <button
        onClick={() => router.push("/events")}
        className="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none bg-primary text-primary-foreground data-[hover=true]:opacity-hover w-[220px] mt-8"
      >
        Browse Events
      </button>
    </div>
  );
};

export default Nothing;
