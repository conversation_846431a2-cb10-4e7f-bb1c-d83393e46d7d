import { cn } from "@/lib/utils";
import React, { memo } from "react";
import { IoAdd, IoR<PERSON>ove } from "react-icons/io5";

interface QuantityButtonProps {
  quantity: number;
  onDecrease: () => void;
  onIncrease: () => void;
  isDisabled: boolean;
  isLoading?: boolean;
  isWhitelabel?: boolean;
}

const QuantityButton: React.FC<QuantityButtonProps> = memo(
  ({
    quantity,
    onDecrease,
    onIncrease,
    isDisabled,
    isLoading = false,
    isWhitelabel = false,
  }) => (
    <div
      className={cn(
        "flex justify-around items-center border border-black py-1 h-10",
        isWhitelabel ? "w-full" : "w-full  md:w-1/3"
      )}
    >
      <IoRemove
        className={`${
          isLoading ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
        }`}
        onClick={isLoading ? undefined : onDecrease}
      />
      <span>
        {isLoading ? (
          <div className="h-4 w-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
        ) : (
          quantity
        )}
      </span>
      <IoAdd
        className={`${
          isDisabled || isLoading
            ? "opacity-50 cursor-not-allowed"
            : "cursor-pointer hover:opacity-80"
        }`}
        onClick={isDisabled || isLoading ? undefined : onIncrease}
      />
    </div>
  )
);

export default QuantityButton;
