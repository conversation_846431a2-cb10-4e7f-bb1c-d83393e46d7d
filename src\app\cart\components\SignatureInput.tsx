import { memo, useCallback } from "react";
import { Input } from "@/components/ui/input";

interface SignatureInputProps {
  signature: string;
  onSignatureChange: (value: string) => void;
}

const SignatureInput = memo(
  function SignatureInput({
    signature,
    onSignatureChange,
  }: SignatureInputProps) {
    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        onSignatureChange(e.target.value);
      },
      [onSignatureChange]
    );

    return (
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-8 md:gap-12 lg:gap-16">
        <div className="flex-1">
          <label className="block text-[12px] text-[#303030] font-normal mb-1 sm:mb-2">
            Enter your signature
          </label>
          <Input
            value={signature}
            onChange={handleChange}
            tabIndex={-1}
            className="w-full text-sm border-gray-300 h-[35px] rounded-lg"
          />
        </div>
        <div className="flex-1 mt-2 sm:mt-0">
          <p className="text-[12px] text-[#303030] font-normal mb-1 sm:mb-2">
            Preview Signature
          </p>
          <div
            className="text-xl text-center border min-h-[35px] rounded-lg border-gray-300 px-2 py-1"
            style={{ fontFamily: "var(--font-dancing-script)" }}
          >
            {signature}
          </div>
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.signature === nextProps.signature;
  }
);

export default SignatureInput;
