import { memo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import SignatureInput from "./SignatureInput";

interface SignatureSectionProps {
  signature: string;
  onSignatureChange: (value: string) => void;
  onSave: () => void;
  onClose: () => void;
}

const SignatureSection = memo(function SignatureSection({
  signature,
  onSignatureChange,
  onSave,
  onClose,
}: SignatureSectionProps) {
  return (
    <div className="absolute bottom-0 left-0 right-0 px-3 sm:px-6 md:px-12 lg:px-16 py-3 sm:py-4 bg-[#FCFCFC] border-t">
      <SignatureInput
        signature={signature}
        onSignatureChange={onSignatureChange}
      />

      <div className="mt-3 sm:mt-4">
        <p className="text-[10px] sm:text-[12px] text-center text-[#303030]">
          By clicking 'Agree,' you confirm that you have read, understood, and
          agree to be bound by the Waiver, conditions, and responsibilities
          outlined in this document, as set forth by the organizer.
        </p>
      </div>

      <div className="mt-3 sm:mt-4 flex justify-center items-center gap-3 sm:gap-4">
        <Button
          className="h-[28px] rounded-lg text-xs sm:text-[12px] px-3 sm:px-4"
          size="sm"
          variant="outline"
          onClick={onClose}
        >
          Cancel
        </Button>
        <Button
          className="h-[28px] rounded-lg text-xs sm:text-[12px] px-3 sm:px-4"
          size="sm"
          onClick={onSave}
          disabled={!signature.trim()}
        >
          Agree
        </Button>
      </div>
    </div>
  );
});

export default SignatureSection;
