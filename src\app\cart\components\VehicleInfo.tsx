import Image from "next/image";
import React, { memo } from "react";

interface VehicleImage {
  image: string;
}

interface VehicleData {
  customMakeName: string;
  vehicleType: string;
  year: string;
  make: string;
  modelName: string;
  modificationText: string;
  teamName?: string;
  vehicleImages: VehicleImage[];
}

interface VehicleInfoProps {
  vehicleData: VehicleData;
  onEdit?: () => void;
}

const VehicleInfo: React.FC<VehicleInfoProps> = memo(
  ({ vehicleData, onEdit }) => {
    if (!vehicleData) return null;

    const imgSrc = vehicleData?.vehicleImages[0]?.image;

    return (
      <div className="relative flex gap-2 sm:gap-3 w-full">
        {imgSrc && (
          <Image
            src={imgSrc}
            alt="Vehicle"
            width={65}
            height={65}
            className="object-cover rounded-md h-[50px] w-[50px] sm:h-[65px] sm:w-[65px]"
          />
        )}
        <div className="flex flex-col justify-between flex-1 min-w-0">
          <div>
            <p className="text-[12px] sm:text-[13px] font-semibold break-words pr-8">
              {vehicleData.customMakeName} {vehicleData.modelName}
            </p>
            <p className="text-[10px] sm:text-[11px]">
              {vehicleData.year || "N/A"}
            </p>
          </div>
          <div>
            <p className="text-[11px] sm:text-xs font-medium truncate">
              Team / Club: {vehicleData.teamName || "N/A"}
            </p>
          </div>
        </div>
        {onEdit && (
          <button
            className="absolute top-0 sm:top-1 right-0 sm:right-2 text-[11px] sm:text-[12px] cursor-pointer text-[#007AFF] p-1"
            onClick={onEdit}
          >
            Edit
          </button>
        )}
      </div>
    );
  }
);

VehicleInfo.displayName = "VehicleInfo";

export default VehicleInfo;
