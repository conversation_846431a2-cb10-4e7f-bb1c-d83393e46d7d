"use client";

import { useState, useEffect, useCallback, memo } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import MemoizedWaiverPdfViewer from "./MemoizedWaiverPdfViewer";
import SignatureSection from "./SignatureSection";

interface WaiverModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (signature: string) => void;
  pdfUrl: string;
}

const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 0,
    height: typeof window !== "undefined" ? window.innerHeight : 0,
  });

  useEffect(() => {
    if (typeof window === "undefined") return;

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return windowSize;
};

const WaiverModal = memo(function WaiverModal({
  isOpen,
  onClose,
  onSave,
  pdfUrl,
}: WaiverModalProps) {
  const [signature, setSignature] = useState("");
  const { height } = useWindowSize();
  const [currentPdfUrl, setCurrentPdfUrl] = useState(pdfUrl);

  // Update currentPdfUrl when pdfUrl prop changes
  useEffect(() => {
    if (pdfUrl) {
      setCurrentPdfUrl(pdfUrl);
    }
  }, [pdfUrl]);

  // Reset signature when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSignature("");
    }
  }, [isOpen]);

  const handleSave = useCallback(() => {
    if (signature.trim()) {
      onSave(signature);
      setSignature("");
    }
  }, [signature, onSave]);

  const handleClose = useCallback(() => {
    setSignature("");
    onClose();
  }, [onClose]);

  const handleSignatureChange = useCallback((value: string) => {
    setSignature(value);
  }, []);

  if (!currentPdfUrl) return null;

  // Calculate appropriate heights based on viewport
  const getModalHeight = () => {
    if (height < 600) return "h-[95vh]";
    if (height < 900) return "h-[85vh]";
    return "h-[80vh]";
  };

  const getPdfViewerHeight = () => {
    if (height < 600) return "h-[85vh]";
    if (height < 900) return "h-[75vh]";
    return "h-[70vh]";
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className={`max-w-4xl p-0 ${getModalHeight()} max-h-[850px] overflow-hidden w-[95vw] sm:w-[90vw] md:w-[676px] [&>button]:hidden rounded-lg`}
      >
        <div className="flex flex-col h-full">
          <div
            className={`w-full bg-gray-100 overflow-y-auto ${getPdfViewerHeight()}`}
          >
            <MemoizedWaiverPdfViewer pdfUrl={currentPdfUrl} />
          </div>

          <SignatureSection
            signature={signature}
            onSignatureChange={handleSignatureChange}
            onSave={handleSave}
            onClose={handleClose}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
});

export default WaiverModal;
