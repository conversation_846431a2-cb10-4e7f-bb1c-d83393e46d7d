import { useEffect, useRef } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { CartProblem } from "@/lib/types";


/**
 * Custom hook to handle error reporting for cart page
 */
export function useCartErrorReporting(serverCartData) {
  const reportedRef = useRef({
    loading: false,
    empty: false,
    fallback: false,
  });

  // Report fallback error state
  useEffect(() => {
    if (serverCartData || !serverCartData?.checkout) {
      reportError("Loading, empty, or error...", { 
        state: "fallback_error", 
      });
      reportedRef.current.fallback = true;
    }
  }, [serverCartData]);

} 