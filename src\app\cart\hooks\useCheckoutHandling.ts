import { useState } from "react";
import { useRouter } from "next/navigation";
import { useUpdateQuantityMutation } from "@/lib/redux/slices/cart/cartApi";
import { useMetaPixel } from "@/lib/hooks/useMetaPixel";
import { useThirdPartyPixelTracking } from "@/lib/hooks/useThirdPartyPixelTracking";
import { extractTrackingData } from "@/lib/utils/extractTrackingData";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import { isZero } from "@/lib/utils/numberUtil";
import toast from "react-hot-toast";
import { getErrorMessage } from "@/lib/utils/errorUtils";
import { STORAGE_KEYS } from "@/lib/constants/storage";

type WaiverData = {
  waiverUrl: string;
  waiverSignature: string;
  waiverSignedDateTime: string;
  timezone: string;
};

export const useCheckoutHandling = (cartData: any, refetchCart: () => void) => {
  const router = useRouter();
  const { trackInitiateCheckout: trackMetaInitiateCheckout } = useMetaPixel();
  const { trackInitiateCheckout: trackThirdPartyInitiateCheckout } = useThirdPartyPixelTracking();
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [updateQuantity] = useUpdateQuantityMutation();

  const handleCheckout = async (waiverSignatures: any = {}) => {
    if (loading || isSubmitting) return;

    const isZeroPrice = isZero(cartData?.checkout?.total || 0);

    setLoading(true);
    setIsSubmitting(true);

    try {
      if (cartData?.checkout?.token) {
        const linesWithWaivers = cartData?.checkout?.lines?.filter((line: any) => {
          if (line.ticketWaiverUrl && waiverSignatures[line.ticketWaiverUrl.id]) {
            return true;
          }
          if (line.waiverResponseData?.length > 0) {
            return line.waiverResponseData.some(
              (waiver: any) => waiverSignatures[`${line.id}-${waiver.waiverUrl}`]
            );
          }
          return false;
        }) || [];

        if (linesWithWaivers.length > 0) {
          const updatePromises = linesWithWaivers.map((line: any) => {
            const waiverData: WaiverData[] = [];

            if (line.ticketWaiverUrl && waiverSignatures[line.ticketWaiverUrl.id]) {
              const waiverInfo = waiverSignatures[line.ticketWaiverUrl.id];
              waiverData.push({
                waiverUrl: line.ticketWaiverUrl.waiverUrl,
                waiverSignature: waiverInfo.signature,
                waiverSignedDateTime: waiverInfo.signedDateTime,
                timezone: waiverInfo.timezone,
              });
            }

            if (line.waiverResponseData?.length > 0) {
              line.waiverResponseData.forEach((waiver: any) => {
                const waiverInfo = waiverSignatures[`${line.id}-${waiver.waiverUrl}`];
                if (waiverInfo) {
                  waiverData.push({
                    waiverUrl: waiver.waiverUrl,
                    waiverSignature: waiverInfo.signature,
                    waiverSignedDateTime: waiverInfo.signedDateTime,
                    timezone: waiverInfo.timezone,
                  });
                }
              });
            }

            const payload = keysToSnake({
              id: line.eventTicket,
              type: "event_ticket",
              quantity: line.quantity,
              waiverResponseData: waiverData,
            });

            return updateQuantity(payload).unwrap();
          });

          await Promise.all(updatePromises);
        }

        const trackingData = extractTrackingData(cartData?.checkout);
        if (trackingData) {
          try {
            // Get pixel data from the event
            const eventPixels = trackingData.event.pixels || {};
            
            // Track Meta pixel checkout initiation
            if (eventPixels.meta) {
              trackMetaInitiateCheckout(trackingData.event, trackingData.tickets);
            }

            // Track third-party pixel checkout initiation
            const hasThirdPartyPixels = eventPixels.ga4 || eventPixels.snap || eventPixels.tiktok;
            if (hasThirdPartyPixels) {
              trackThirdPartyInitiateCheckout(trackingData.event, trackingData.tickets);
            }
          } catch (trackingError) {
            console.error("Failed to track checkout:", trackingError);
          }
        }
        const checkoutToken = cartData.checkout.token;
        localStorage.setItem(STORAGE_KEYS.CART_TOKEN, checkoutToken);
        localStorage.removeItem(STORAGE_KEYS.CLIENT_SECRET);
        localStorage.removeItem(STORAGE_KEYS.CONNECT_ID);
        localStorage.removeItem(STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT);
        sessionStorage.removeItem(STORAGE_KEYS.CHECKOUT_FORM_DATA);
        
        if (isZeroPrice) {
          router.push("/checkout?isFree=true");
          return;
        }
        
        router.push(`/checkout`);
      }
    } catch (error) {
      const errMessage = getErrorMessage(error);
      toast.error(errMessage || "Checkout failed. Please try again.");
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  return {
    loading,
    isSubmitting,
    handleCheckout,
  };
}; 