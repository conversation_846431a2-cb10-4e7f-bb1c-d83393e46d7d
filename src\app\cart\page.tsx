"use client";
import CartFooter from "./components/CartFooter";
import { CartTickets } from "./components/CartTickets";
import Title from "./components/Title";
import Nothing from "./components/Nothing";
import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";
import { useSessionData } from "@/lib/hooks/useSession";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { CART_STORAGE_KEYS } from "@/lib/utils/constants";
import {
  getProblemMessage,
  getProblemToastId,
  QUANTITY_ERROR_TOAST_ID,
  FORM_ERROR_TOAST_ID,
  PRICE_ERROR_TOAST_ID,
  VARIANT_ERROR_TOAST_ID,
} from "@/app/cart/utils/cartUtils";
import { CartProblem, ExtendedCartResponse } from "@/lib/types";
import { CART_ERROR_MESSAGES } from "./utils/cartErrorMessages";
import { useCartErrorReporting } from "./hooks/useCartErrorReporting";

const POLLING_INTERVAL = 600000;

const Cart = () => {
  const { data: sessionData, status: sessionStatus } = useSessionData();

  const [cartToken, setCartToken] = useState<string | null>(null);
  const [initialFetchAttempted, setInitialFetchAttempted] = useState(false);

  const {
    data: serverCartData,
    isLoading,
    isFetching,
    isUninitialized,
    refetch: refetchCart,
  } = useGetCartQuery(undefined, {
    refetchOnFocus: true,
    refetchOnReconnect: true,
    refetchOnMountOrArgChange: true,
    pollingInterval: POLLING_INTERVAL,
    skip: !cartToken && !sessionData?.user,
  }) as {
    data: ExtendedCartResponse | undefined;
    isLoading: boolean;
    isFetching: boolean;
    isUninitialized: boolean;
    refetch: () => Promise<any>;
  };

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetchCart = useCallback(() => {
    if (!isUninitialized) {
      refetchCart();
    }
  }, [isUninitialized, refetchCart]);

  useEffect(() => {
    if (cartToken && !serverCartData && !isLoading && !isUninitialized) {
      safeRefetchCart();
    }
  }, [cartToken, serverCartData, isLoading, isUninitialized, safeRefetchCart]);

  useCartErrorReporting(serverCartData);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const token = localStorage.getItem(CART_STORAGE_KEYS.CART_TOKEN);
      setCartToken(token);
    }
  }, []);

  useEffect(() => {
    // Don't show or dismiss toasts while loading
    if (isLoading || isFetching) return;

    // Dismiss existing toasts if no problems
    if (!serverCartData?.problems?.length) {
      toast.dismiss(QUANTITY_ERROR_TOAST_ID);
      toast.dismiss(FORM_ERROR_TOAST_ID);
      toast.dismiss(PRICE_ERROR_TOAST_ID);
      toast.dismiss(VARIANT_ERROR_TOAST_ID);
      return;
    }

    // Add delay before showing problems toast
    const timeoutId = setTimeout(() => {
      // Show or update toast for problems
      serverCartData?.problems?.forEach((problem: CartProblem) => {
        const ticket = serverCartData?.checkout?.lines?.find(
          (line: any) => line.id === problem.lineId
        );

        if (!ticket) return;

        const message = getProblemMessage(problem, ticket);
        const toastId = getProblemToastId(problem.type);

        if (message) {
          toast.error(message, {
            id: toastId,
            duration: Infinity,
          });
        }
      });
    }, 4000); // 4 second delay

    return () => clearTimeout(timeoutId);
  }, [serverCartData?.problems]);

  useEffect(() => {
    if (
      !isLoading &&
      !serverCartData &&
      !initialFetchAttempted &&
      !isUninitialized &&
      (cartToken || sessionData?.user)
    ) {
      const timer = setTimeout(() => {
        safeRefetchCart();
        setInitialFetchAttempted(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [
    isLoading,
    serverCartData,
    initialFetchAttempted,
    isUninitialized,
    safeRefetchCart,
    cartToken,
    sessionData?.user,
  ]);

  if (
    isLoading ||
    (isFetching && !serverCartData) ||
    sessionStatus === "loading"
  ) {
    const loadingMessage = CART_ERROR_MESSAGES.LOADING_CART;

    return (
      <div className="min-h-[calc(100vh-100px)] p-6 mt-12 max-w-[900px] mx-auto flex justify-center items-center">
        <div className="animate-pulse">{loadingMessage}</div>
      </div>
    );
  }

  if (
    (!serverCartData?.checkout ||
      serverCartData.checkout.lines?.length === 0) &&
    !isFetching
  ) {
    return (
      <div className="p-6 mt-12 max-w-[900px] mx-auto">
        <Nothing />
      </div>
    );
  }

  // Guard to ensure serverCartData and serverCartData.checkout are defined before rendering cart items
  if (!serverCartData || !serverCartData.checkout) {
    // This should ideally not be reached if the above logic is complete,
    // but it ensures type safety for the rendering block below.
    // It might indicate an edge case not covered by loading/empty states or an unexpected data structure.
    const errorMessage = CART_ERROR_MESSAGES.LOADING_ERROR;

    return (
      <div className="min-h-[calc(100vh-100px)] p-6 mt-12 max-w-[900px] mx-auto flex justify-center items-center">
        <div>{errorMessage}</div>
      </div>
    );
  }

  return (
    <div className="p-6 mt-12 max-w-[900px] mx-auto min-h-screen">
      <Title />
      <div className="mb-12">
        <CartTickets
          cartData={serverCartData.checkout.lines ?? []}
          customerInfo={serverCartData.checkout.customerMetadata ?? {}}
          refetchCart={safeRefetchCart}
        />
        <CartFooter
          cartData={serverCartData}
          refetchCart={safeRefetchCart}
          hasProblems={Boolean(
            serverCartData?.problems && serverCartData.problems.length > 0
          )}
        />
      </div>
    </div>
  );
};

export default Cart;
