export type CheckoutData = {
  lines?: any[];
  customerMetadata?: Record<string, any>;
  discounts?: any[];
  initialSubtotal?: number;
  platformFee?: number;
  processingFee?: number;
  shippingPrice?: number;
  discountAmount?: number;
  total?: number;
  token?: string;
  voucherCode?: string;
  isShippingRequired?: boolean;
};

export type CartData = {
  checkout?: CheckoutData;
  problems?: any[];
};

export type CartFooterProps = {
  cartData: CartData;
  refetchCart: () => void;
  hasProblems: boolean;
}; 