export const CART_ERROR_MESSAGES = {
  QUANTITY_UNAVAILABLE: (ticketName: string) => 
    `"${ticketName}" is no longer available. Please remove this item.`,
  
  QUANTITY_LIMITED: (ticketName: string, availableQuantity: number) => 
    `Only ${availableQuantity} tickets available for "${ticketName}". Please adjust quantity.`,
  
  VARIANT_NOT_PUBLISHED: (ticketName: string) => 
    `"${ticketName}" is not published yet.`,
  
  VARIANT_NO_PRICE: (ticketName: string) => 
    `"${ticketName}" has no price set.`,
  
  VARIANT_NOT_AVAILABLE: (ticketName: string) => 
    `"${ticketName}" is not available.`,
  
  PRICE_CHANGED: (ticketName: string, oldPrice: number, newPrice: number) => 
    `The price for "${ticketName}" has changed from $${oldPrice} to $${newPrice}. Please review your cart.`,
  
  FORMS_INCOMPLETE: (ticketName: string, expectedFormsCount: number, actualFormsCount: number) => 
    `Please complete the required forms for "${ticketName}". ${expectedFormsCount} forms required, but only ${actualFormsCount} completed.`,
  
  GENERIC_PROBLEM: (ticketName: string) => 
    `There is an issue with "${ticketName}". Please check your cart.`,
  
  LOADING_CART: "Loading cart...",
  LOADING_ERROR: "Loading, empty, or error..."
}; 