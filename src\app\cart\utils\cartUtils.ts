import { CartResponse } from "@/lib/redux/types";
import { CART_STORAGE_KEYS } from "@/lib/utils/constants";
import { CART_ERROR_MESSAGES } from "@/app/cart/utils/cartErrorMessages";
import { reportError } from "@/lib/utils/sentryErrorLogs";

export const QUANTITY_ERROR_TOAST_ID = "quantity-error";
export const FORM_ERROR_TOAST_ID = "form-error";
export const PRICE_ERROR_TOAST_ID = "price-error";
export const VARIANT_ERROR_TOAST_ID = "variant-error";

export const PROBLEM_TYPES = {
  INSUFFICIENT_STOCK: "CheckoutLineProblemInsufficientStock",
  TICKET_FORM: "CheckoutLineProblemTicketForm",
  VARIANT_NOT_AVAILABLE: "CheckoutLineProblemVariantNotAvailable",
  PRICE_CHANGED: "CheckoutLineProblemPriceChanged"
};

/**
 * Get the appropriate error message for a cart problem
 */
export const getProblemMessage = (problem: any, ticket: any) => {
  const ticketName = ticket?.eventDetails?.ticketName;
  let message = '';
  
  switch (problem.type) {
    case PROBLEM_TYPES.INSUFFICIENT_STOCK:
      message = problem?.availableQuantity === 0
        ? CART_ERROR_MESSAGES.QUANTITY_UNAVAILABLE(ticketName)
        : CART_ERROR_MESSAGES.QUANTITY_LIMITED(ticketName, problem?.availableQuantity);
      break;
    
    case PROBLEM_TYPES.VARIANT_NOT_AVAILABLE:
      if (problem.reason === 'not_published') {
        message = CART_ERROR_MESSAGES.VARIANT_NOT_PUBLISHED(ticketName);
      } else if (problem.reason === 'no_price') {
        message = CART_ERROR_MESSAGES.VARIANT_NO_PRICE(ticketName);
      } else {
        message = CART_ERROR_MESSAGES.VARIANT_NOT_AVAILABLE(ticketName);
      }
      break;
    
    case PROBLEM_TYPES.PRICE_CHANGED:
      message = CART_ERROR_MESSAGES.PRICE_CHANGED(ticketName, problem.oldPrice, problem.newPrice);
      break;
    
    case PROBLEM_TYPES.TICKET_FORM:
      message = CART_ERROR_MESSAGES.FORMS_INCOMPLETE(
        ticketName, 
        problem.expectedFormsCount, 
        problem.actualFormsCount
      );
      break;
    
    default:
      message = CART_ERROR_MESSAGES.GENERIC_PROBLEM(ticketName);
  }
  
  // Log the message being shown to the user
  reportError(message, { 
    problem_type: problem.type,
    ticket_id: ticket?.id,
    ticket_name: ticketName
  });
  
  return message;
};

/**
 * Get the appropriate toast ID for a problem type
 */
export const getProblemToastId = (problemType: string) => {
  switch (problemType) {
    case PROBLEM_TYPES.INSUFFICIENT_STOCK:
      return QUANTITY_ERROR_TOAST_ID;
    case PROBLEM_TYPES.TICKET_FORM:
      return FORM_ERROR_TOAST_ID;
    case PROBLEM_TYPES.PRICE_CHANGED:
      return PRICE_ERROR_TOAST_ID;
    case PROBLEM_TYPES.VARIANT_NOT_AVAILABLE:
      return VARIANT_ERROR_TOAST_ID;
    default:
      return "cart-problem";
  }
};

/**
 * Check if the ticket has form-related problems
 */
export const hasTicketFormProblems = (problems?: any[]) => {
  return problems?.some(problem => problem.type === PROBLEM_TYPES.TICKET_FORM) || false;
};

/**
 * Get the first form problem ticket to enable navigation
 */
export const getFormProblemTicket = (problems?: any[]) => {
  if (!problems) return null;
  return problems.find(problem => problem.type === PROBLEM_TYPES.TICKET_FORM) || null;
};

export const handleInvalidCartToken = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(CART_STORAGE_KEYS.CART_TOKEN);
  }
};

/**
 * Check if the cart has approval required tickets
 */
export const checkIfCartHasApprovalRequiredTickets = (lines: any[]) => {
  return lines?.some((item: any) => item?.eventDetails?.ticketApprovalRequired);
};
