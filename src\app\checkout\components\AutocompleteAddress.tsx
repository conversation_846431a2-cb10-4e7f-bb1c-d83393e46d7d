import React from "react";
import { Control, Controller, UseFormSetValue } from "react-hook-form";
import Autocomplete from "react-google-autocomplete";
import { CheckoutFormData } from "../types";
interface AutocompleteAddressProps {
  control: Control<CheckoutFormData>;
  name: "addressLine1" | "billingAddressLine1";
  label: string;
  errorMessage?: string;
  isDisabled?: boolean;
  setValue: UseFormSetValue<CheckoutFormData>;
  prefix?: "billing" | "";
  isRequired?: boolean;
}

export function AutocompleteAddress({
  control,
  name,
  label,
  errorMessage,
  isDisabled,
  setValue,
  prefix = "",
  isRequired = false,
}: AutocompleteAddressProps) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <div className="relative">
          <Autocomplete
            placeholder="Address"
            autoComplete="new-password"
            apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}
            inputAutocompleteValue={field.value}
            className="w-full px-3 py-2 rounded-sm bg-white shadow-none border border-gray-200 hover:border-gray-300 h-[60px]  focus:border-gray-300 focus:outline-none"
            style={{
              width: "100%",
              fontSize: "14px",
            }}
            value={field.value}
            onPlaceSelected={(place) => {
              if (place && place?.address_components) {
                // Extract address components
                let streetNumber = "";
                let route = "";
                let city = "";
                let state = "";
                let stateLong = "";
                let postalCode = "";
                let country = "";

                place?.address_components?.forEach((component) => {
                  const types = component?.types;
                  if (types?.includes("street_number")) {
                    streetNumber = component?.long_name;
                  } else if (types?.includes("route")) {
                    route = component?.long_name;
                  } else if (
                    types?.includes("locality") ||
                    types?.includes("sublocality")
                  ) {
                    city = component?.long_name;
                  } else if (types?.includes("administrative_area_level_1")) {
                    state = component?.short_name;
                    stateLong = component?.long_name;
                  } else if (types?.includes("postal_code")) {
                    postalCode = component?.long_name;
                  } else if (types?.includes("country")) {
                    country = component?.short_name;
                  }
                });

                const formattedStreetAddress =
                  streetNumber && route
                    ? `${streetNumber} ${route}`.trim()
                    : place?.formatted_address;

                setValue(`${prefix}${name}` as any, formattedStreetAddress);
                field.onChange(formattedStreetAddress);

                if (country)
                  setValue(
                    (prefix ? `${prefix}Country` : "country") as any,
                    country
                  );
                if (city)
                  setValue((prefix ? `${prefix}City` : "city") as any, city);
                if (state) {
                  const stateValue = stateLong || state;
                  setValue(
                    (prefix ? `${prefix}State` : "state") as any,
                    stateValue
                  );
                }
                if (postalCode)
                  setValue(
                    (prefix ? `${prefix}PostalCode` : "postalCode") as any,
                    postalCode
                  );
              }
            }}
            options={{
              types: "locality",
              fields: ["address_components", "formatted_address"],
            }}
            onChange={field.onChange}
            onBlur={field.onBlur}
            disabled={isDisabled}
            required={isRequired}
          />
          {isRequired && !field.value && (
            <span className="absolute left-[75px] top-1/2 transform -translate-y-1/2 text-red-500 pointer-events-none">
              *
            </span>
          )}
          {errorMessage && (
            <div className="text-danger text-xs mt-1">{errorMessage}</div>
          )}
        </div>
      )}
    />
  );
}
