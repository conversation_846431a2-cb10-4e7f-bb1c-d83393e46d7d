import {
  Control,
  FieldErrors,
  UseFormSetValue,
  Controller,
} from "react-hook-form";
import { FormInput, FormSelect } from "./Form";
import { FormCheckbox } from "./Form/FormCheckbox";
import {
  SelectItem,
  RadioGroup,
  Radio,
  Accordion,
  AccordionItem,
} from "@nextui-org/react";
import { CheckoutFormData } from "../types";
import { State } from "../types";
import { ICountry } from "country-state-city";
import React from "react";
import { AutocompleteAddress } from "./AutocompleteAddress";
import { PhoneInput } from "./Form/PhoneInput";

interface BillingSectionProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  countries: ICountry[];
  states: State[];
  isProductInCart: boolean;
  billingAddressType: "same" | "different";
  setBillingAddressType: (value: "same" | "different") => void;
  isDisabled?: boolean;
  setValue: UseFormSetValue<CheckoutFormData>;
  isLoggedIn: boolean;
  isWhitelabel: boolean;
  fieldErrors?: Record<string, string>;
}

export function BillingSection({
  control,
  errors,
  countries,
  states,
  isProductInCart,
  billingAddressType,
  setBillingAddressType,
  isDisabled,
  setValue,
  isLoggedIn,
  isWhitelabel,
  fieldErrors = {},
}: BillingSectionProps) {
  const BillingForm = React.useMemo(
    () => () =>
      (
        <div className="grid grid-cols-1 gap-y-4">
          {isLoggedIn && (
            <FormCheckbox
              name="saveBillingAddress"
              control={control}
              label="Save billing address"
              isDisabled={isDisabled}
            />
          )}

          <FormSelect
            name="billingCountry"
            control={control}
            label="Country"
            errorMessage={errors.billingCountry?.message}
            defaultSelectedKeys={["US"]}
            isDisabled={isDisabled}
            isRequired={true}
          >
            {countries.map((country) => (
              <SelectItem key={country.isoCode} value={country.isoCode}>
                {country.name}
              </SelectItem>
            ))}
          </FormSelect>

          {!isWhitelabel && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormInput
                name={"billingFirstName"}
                control={control}
                type="text"
                label="First name"
                errorMessage={errors.billingFirstName?.message}
                isDisabled={isDisabled}
                isRequired={true}
              />
              <FormInput
                name={"billingLastName"}
                control={control}
                type="text"
                label="Last name"
                errorMessage={errors.billingLastName?.message}
                isDisabled={isDisabled}
                isRequired={true}
              />
            </div>
          )}

          <FormInput
            name="billingCompany"
            control={control}
            type="text"
            label="Company (optional)"
            errorMessage={errors.billingCompany?.message}
            isDisabled={isDisabled}
          />

          <AutocompleteAddress
            name="billingAddressLine1"
            control={control}
            label="Address"
            errorMessage={errors.billingAddressLine1?.message}
            isDisabled={isDisabled}
            setValue={setValue}
            prefix="billing"
            isRequired={true}
          />

          <FormInput
            name="billingAddressLine2"
            control={control}
            type="text"
            label="Apartment, suite, etc. (optional)"
            errorMessage={errors.billingAddressLine2?.message}
            isDisabled={isDisabled}
          />

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <FormInput
              name="billingCity"
              control={control}
              type="text"
              label="City"
              errorMessage={errors.billingCity?.message}
              isDisabled={isDisabled}
              isRequired={true}
            />

            <FormSelect
              name="billingState"
              control={control}
              label="State/Province"
              errorMessage={errors.billingState?.message}
              isDisabled={isDisabled}
              isRequired={true}
            >
              {states.map((state) => (
                <SelectItem key={state.name} value={state.name}>
                  {state.name}
                </SelectItem>
              ))}
            </FormSelect>
            <FormInput
              name="billingPostalCode"
              control={control}
              type="text"
              label="Postal code"
              errorMessage={errors.billingPostalCode?.message}
              isDisabled={isDisabled}
              isRequired={true}
            />
          </div>

          {!isWhitelabel && (
            <>
              <PhoneInput
                name="billingPhone"
                control={control}
                label="Phone number"
                errorMessage={errors.billingPhone?.message}
                isDisabled={isDisabled}
                fieldErrors={fieldErrors}
                isRequired={true}
              />
            </>
          )}
        </div>
      ),
    [
      control,
      errors,
      countries,
      states,
      isProductInCart,
      isDisabled,
      setValue,
      isWhitelabel,
      fieldErrors,
    ]
  );

  return (
    <div>
      <h2 className="text-[24px] font-semibold text-gray-900 mb-4">
        Billing address
      </h2>
      {isProductInCart ? (
        <div className="space-y-4">
          <RadioGroup
            defaultValue="same"
            value={billingAddressType}
            onValueChange={(value) =>
              setBillingAddressType(value as "same" | "different")
            }
            classNames={{
              wrapper: "gap-4 w-full",
            }}
          >
            <Radio
              value="same"
              classNames={{
                base: "max-w-full border border-gray-200 rounded-md px-4 py-3 hover:bg-gray-50 data-[selected=true]:bg-[#1773B020] data-[selected=true]:border-[#1773B0] transition-colors rounded-bl-none rounded-br-none mx-0 ",
                labelWrapper: "w-full flex flex-col",
              }}
            >
              <div className="w-full text-sm font-medium">
                Same as shipping address
              </div>
            </Radio>
            <Radio
              value="different"
              classNames={{
                base: "max-w-full border border-gray-200 rounded-md px-4 py-3 hover:bg-gray-50 data-[selected=true]:bg-[#1773B020] data-[selected=true]:border-[#1773B0] transition-colors rounded-tl-none rounded-tr-none data-[selected=true]:rounded-bl-none data-[selected=true]:rounded-br-none mx-0",
                labelWrapper: "w-full flex flex-col",
              }}
            >
              <div className="w-full text-sm font-medium">
                Use a different billing address
              </div>
            </Radio>
          </RadioGroup>

          <Accordion
            selectedKeys={
              billingAddressType === "different"
                ? new Set(["different-billing"])
                : new Set([])
            }
          >
            <AccordionItem
              key="different-billing"
              aria-label="Different Billing Address"
              classNames={{
                content: "py-8 px-4 bg-[#F5F5F5] rounded-br-md rounded-bl-md",
                base: "m-[-0.5rem] p-0",
                title: "hidden",
                trigger: "hidden",
              }}
            >
              <BillingForm />
            </AccordionItem>
          </Accordion>
        </div>
      ) : (
        <BillingForm />
      )}
    </div>
  );
}
