"use client";

import {
  PaymentElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { Button } from "./ui";
import { useCheckoutForm } from "../hooks/useCheckoutForm";
import { ContactSection } from "./ContactSection";
import { DeliverySection } from "./DeliverySection";
import { BillingSection } from "./BillingSection";
import { ShippingMethods } from "./ShippingMethods";
import { useEffect, useCallback, useState, useMemo } from "react";
import { useWatch } from "react-hook-form";
import { CHECKOUT_ERROR_MESSAGES } from "../utils/errorMessages";
import { useUserMessageReporting } from "../hooks/useUserMessageReporting";
import { useEmailCartClaim } from "../hooks/useEmailCartClaim";
import { useAddressCartPatch } from "../hooks/useAddressCartPatch";
import { Spinner } from "@nextui-org/react";
import { ADDRESS_TYPES } from "../constants/addressConstants";

/**
 * Determines the button text based on checkout state
 */
function getCheckoutButtonText({
  isProcessing,
  isFreeCheckout,
  isPaymentLinkMethod,
  isEmailValid,
  areBillingFieldsFilled,
  isBillingPatched,
}: {
  isProcessing: boolean;
  isFreeCheckout: boolean;
  isPaymentLinkMethod: boolean;
  isEmailValid: boolean;
  areBillingFieldsFilled: boolean;
  isBillingPatched: boolean;
}): string {
  if (isProcessing) {
    return "Processing...";
  }

  if ((isFreeCheckout || isPaymentLinkMethod) && !isEmailValid) {
    return "Please enter a valid email address";
  }

  if ((isFreeCheckout || isPaymentLinkMethod) && !areBillingFieldsFilled) {
    return "Please complete all billing fields";
  }

  if ((isFreeCheckout || isPaymentLinkMethod) && !isBillingPatched) {
    return "Validating billing information...";
  }

  if (isPaymentLinkMethod) {
    return "Complete registration";
  }

  return "Complete order";
}

interface BasicUserInfo {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  smsUpdates: boolean;
  newsletter: boolean;
}

interface CartCheckout {
  total: string;
  currency: string;
  email: string;
  lines: Array<any>;
  platform_fee: string;
  subtotal: string;
  token: string;
}

interface CartData {
  checkout: CartCheckout;
  problems: Array<any>;
}

interface CheckoutFormProps {
  isFreeCheckout?: boolean;
  refreshCartData?: () => void;
  onCreatePaymentIntent?: () => Promise<any>;
  isCreatingPayment?: boolean;
  hasPaymentIntent?: boolean;
  paymentError?: string | null;
  stripePromise?: Promise<any> | null;
  appearance?: object;
  clientSecret?: string | null;
  stripeMode?: "payment" | "deferred";
  basicUserInfo?: BasicUserInfo;
  isWhitelabel?: boolean;
  slug?: string;
  isPaymentLinkMethod?: boolean;
}

export default function CheckoutForm({
  isFreeCheckout = false,
  refreshCartData,
  onCreatePaymentIntent,
  isCreatingPayment = false,
  hasPaymentIntent = false,
  basicUserInfo,
  isWhitelabel = false,
  slug,
  isPaymentLinkMethod = false,
}: CheckoutFormProps) {
  const stripe = !isFreeCheckout && !isPaymentLinkMethod ? useStripe() : null;
  const elements =
    !isFreeCheckout && !isPaymentLinkMethod ? useElements() : null;

  const {
    form: {
      control,
      handleSubmit,
      formState: { errors },
      setValue,
      // watch,
    },
    billingAddressType,
    setBillingAddressType,
    isProcessing,
    isCartLoading,
    cartData: typedCartData,
    countries,
    shippingStates,
    billingStates,
    isShippingRequired,
    sessionData,
    onSubmit,
    resetAddressFields,
  } = useCheckoutForm(
    stripe,
    elements,
    isWhitelabel,
    slug,
    isPaymentLinkMethod
  );

  const approvalRequiredLines =
    typedCartData?.checkout?.lines?.filter(
      (line) => line?.eventDetails?.ticketApprovalRequired
    ) || [];

  useEmailCartClaim({
    control,
    initialEmail: sessionData?.user?.email,
  });

  useEmailCartClaim({
    control,
    initialEmail: sessionData?.user?.email,
    field: "applePayEmail",
  });

  const cartDataTyped = typedCartData as unknown as CartData;
  const { reportUserMessage } = useUserMessageReporting();

  const [shouldFetchShippingMethods, setShouldFetchShippingMethods] =
    useState(false);
  const [shippingMethodsRefetchTrigger, setShippingMethodsRefetchTrigger] =
    useState(0);
  const [isShippingMethodSelected, setIsShippingMethodSelected] = useState(
    !isShippingRequired
  );
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [isBillingPatched, setIsBillingPatched] = useState(false);
  const [isEmailValid, setIsEmailValid] = useState(false);

  const handleFieldError = useCallback(
    (fieldName: string, errorMessage: string) => {
      setFieldErrors((prev) => ({
        ...prev,
        [fieldName]: errorMessage,
      }));
    },
    []
  );

  const handleFieldErrorClear = useCallback((fieldName: string) => {
    setFieldErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  useEffect(() => {
    if (isWhitelabel) {
      setValue("email", basicUserInfo?.email || "");
      setValue("firstName", basicUserInfo?.firstName || "");
      setValue("lastName", basicUserInfo?.lastName || "");
      setValue("phone", basicUserInfo?.phone || "");
      setValue("billingPhone", basicUserInfo?.phone || "");
      setValue("billingFirstName", basicUserInfo?.firstName || "");
      setValue("billingLastName", basicUserInfo?.lastName || "");
      setValue("smsUpdates", basicUserInfo?.smsUpdates || true);
      setValue("newsletter", basicUserInfo?.newsletter || true);
    }
  }, [basicUserInfo, isWhitelabel, setValue]);

  useEffect(() => {
    if (isShippingRequired) {
      setIsShippingMethodSelected(false);
    } else {
      setIsShippingMethodSelected(true);
    }
  }, [isShippingRequired]);

  const handleShippingAddressSuccess = useCallback(() => {
    setShouldFetchShippingMethods(true);
    setShippingMethodsRefetchTrigger((prev) => prev + 1);
  }, []);

  const handleShippingMethodSet = useCallback(() => {
    setIsShippingMethodSelected(true);
    if (refreshCartData) {
      refreshCartData();
    }

    if (onCreatePaymentIntent && !hasPaymentIntent && !isCreatingPayment) {
      onCreatePaymentIntent();
    }
  }, [
    onCreatePaymentIntent,
    hasPaymentIntent,
    isCreatingPayment,
    refreshCartData,
    setIsShippingMethodSelected,
  ]);

  const handleAddressPatched = useCallback(() => {
    if (!isShippingRequired) {
      if (onCreatePaymentIntent && !hasPaymentIntent && !isCreatingPayment) {
        onCreatePaymentIntent();
      }
    }
  }, [
    onCreatePaymentIntent,
    hasPaymentIntent,
    isCreatingPayment,
    isShippingRequired,
  ]);

  const handleBillingPatched = useCallback(() => {
    setIsBillingPatched(true);
    handleAddressPatched();
  }, [handleAddressPatched]);

  useAddressCartPatch({
    control,
    type: ADDRESS_TYPES.SHIPPING,
    token: cartDataTyped?.checkout?.token,
    onAddressPatched: handleAddressPatched,
    onShippingAddressSuccess: handleShippingAddressSuccess,
    billingAddressType,
    onFieldError: handleFieldError,
    onFieldErrorClear: handleFieldErrorClear,
    isWhitelabel,
    slug,
    onResetAddressFields: resetAddressFields,
  });

  useAddressCartPatch({
    control,
    type: ADDRESS_TYPES.BILLING,
    token: cartDataTyped?.checkout?.token,
    onAddressPatched: handleBillingPatched,
    billingAddressType,
    onFieldError: handleFieldError,
    onFieldErrorClear: handleFieldErrorClear,
    isWhitelabel,
    slug,
    onResetAddressFields: resetAddressFields,
  });

  useEffect(() => {
    if (cartDataTyped?.checkout?.lines?.length) {
      reportUserMessage(CHECKOUT_ERROR_MESSAGES.EMPTY_CART, {
        context: "checkout_form",
      });
    }
  }, [cartDataTyped, reportUserMessage]);

  // Watch billing fields to reset patch state when they change
  const watchedBillingFields = useWatch({
    control,
    name: [
      "billingFirstName",
      "billingLastName",
      "billingCompany",
      "billingAddressLine1",
      "billingAddressLine2",
      "billingCity",
      "billingState",
      "billingCountry",
      "billingPostalCode",
      "billingPhone",
      "saveBillingAddress",
    ],
  });

  // Watch email field for validation
  const emailValue = useWatch({
    control,
    name: "email",
  });

  // Watch essential billing fields to ensure they're filled
  const billingEssentialFields = useWatch({
    control,
    name: [
      "billingFirstName",
      "billingLastName",
      "billingAddressLine1",
      "billingCity",
      "billingState",
      "billingCountry",
      "billingPostalCode",
      "billingPhone",
    ],
  });

  // Reset billing patch state when component mounts, cart data changes, or billing fields change
  useEffect(() => {
    setIsBillingPatched(false);
  }, [cartDataTyped?.checkout?.token, watchedBillingFields]);

  // Reset email validation when cart token changes
  useEffect(() => {
    setIsEmailValid(false);
  }, [cartDataTyped?.checkout?.token]);

  // Validate email field
  useEffect(() => {
    // For whitelabel, use basicUserInfo email if emailValue is not available
    const effectiveEmail =
      emailValue || (isWhitelabel ? basicUserInfo?.email : "");
    const isValidEmail =
      effectiveEmail &&
      effectiveEmail.trim() !== "" &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(effectiveEmail.trim());
    setIsEmailValid(!!isValidEmail);
  }, [emailValue, isWhitelabel, basicUserInfo?.email]);

  // Check if essential billing fields are filled
  const areBillingFieldsFilled = useMemo(() => {
    if (!billingEssentialFields || !Array.isArray(billingEssentialFields)) {
      return false;
    }

    // Check each essential field
    const [
      billingFirstName,
      billingLastName,
      billingAddressLine1,
      billingCity,
      billingState,
      billingCountry,
      billingPostalCode,
      billingPhone,
    ] = billingEssentialFields;

    // For whitelabel with payment link or free checkout, only require name and phone from basicUserInfo
    if (isWhitelabel && (isPaymentLinkMethod || isFreeCheckout)) {
      const effectiveFirstName =
        billingFirstName?.trim() || basicUserInfo?.firstName?.trim();
      const effectiveLastName =
        billingLastName?.trim() || basicUserInfo?.lastName?.trim();
      const effectivePhone =
        billingPhone?.trim() || basicUserInfo?.phone?.trim();

      return !!(effectiveFirstName && effectiveLastName && effectivePhone);
    }

    return (
      billingFirstName?.trim() &&
      billingLastName?.trim() &&
      billingAddressLine1?.trim() &&
      billingCity?.trim() &&
      billingState?.trim() &&
      billingCountry?.trim() &&
      billingPostalCode?.trim() &&
      billingPhone?.trim()
    );
  }, [
    billingEssentialFields,
    isWhitelabel,
    isPaymentLinkMethod,
    isFreeCheckout,
    basicUserInfo,
  ]);

  if (!cartDataTyped?.checkout?.lines?.length) {
    const emptyCartMessage = CHECKOUT_ERROR_MESSAGES.EMPTY_CART;
    return <div>{emptyCartMessage}</div>;
  }

  const isPaymentButtonDisabled =
    isProcessing ||
    isCartLoading ||
    isCreatingPayment ||
    (!isFreeCheckout && !isPaymentLinkMethod && !hasPaymentIntent) ||
    ((isFreeCheckout || isPaymentLinkMethod) &&
      isShippingRequired &&
      !isShippingMethodSelected) ||
    ((isFreeCheckout || isPaymentLinkMethod) &&
      (!isBillingPatched || !isEmailValid || !areBillingFieldsFilled));

  const isPaymentButtonLoading =
    isProcessing || isCartLoading || isCreatingPayment;

  const renderPaymentElement = () => {
    if (isFreeCheckout) {
      return (
        <div>
          <h2 className="text-[24px] font-semibold text-gray-900">Payment</h2>
          <p className="mt-4 bg-gray-100 p-4 rounded-md text-gray-500">
            Your order is free. No payment is required.
          </p>
        </div>
      );
    }

    if (isPaymentLinkMethod) {
      return (
        <div>
          <h2 className="text-[24px] font-semibold text-gray-900">Payment</h2>
          <p className="mt-4 bg-gray-100 p-4 rounded-md text-gray-500">
            You won't be charged now. Your registration will be in process. If
            the event organizer accepts your request, you will receive an email
            with a payment link to complete your payment and receive your
            tickets.
          </p>
        </div>
      );
    }

    return (
      <div className="relative">
        <div>
          <h2 className="text-[24px] font-semibold text-gray-900">Payment</h2>
          {isCreatingPayment && (
            <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
              <Spinner />
            </div>
          )}

          <div className="mt-1 relative">
            <PaymentElement
              id="payment-element"
              options={{
                layout: "tabs",
                paymentMethodOrder: ["card", "apple_pay"],
                wallets: {
                  googlePay: "never",
                  applePay: "auto",
                },
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 gap-8">
        {!isWhitelabel && (
          <ContactSection
            control={control as any}
            errors={errors}
            isDisabled={Boolean(sessionData?.user)}
          />
        )}

        {isShippingRequired && (
          <DeliverySection
            control={control as any}
            errors={errors}
            countries={countries}
            states={shippingStates}
            setValue={setValue as any}
            isLoggedIn={Boolean(sessionData?.user)}
            isWhitelabel={isWhitelabel}
            fieldErrors={fieldErrors}
          />
        )}

        <BillingSection
          control={control as any}
          errors={errors}
          countries={countries}
          states={billingStates}
          isProductInCart={isShippingRequired ?? false}
          billingAddressType={billingAddressType}
          setBillingAddressType={setBillingAddressType}
          setValue={setValue as any}
          isLoggedIn={Boolean(sessionData?.user)}
          isWhitelabel={isWhitelabel}
          fieldErrors={fieldErrors}
        />

        {isShippingRequired && (
          <ShippingMethods
            control={control as any}
            errors={errors}
            cartToken={cartDataTyped?.checkout?.token}
            onShippingMethodSet={handleShippingMethodSet}
            shouldFetchMethods={shouldFetchShippingMethods}
            refetchTrigger={shippingMethodsRefetchTrigger}
          />
        )}

        {renderPaymentElement()}
        <Button
          type="submit"
          color="primary"
          isDisabled={isPaymentButtonDisabled}
          isLoading={isPaymentButtonLoading}
          className="w-full bg-[#1773B0] mt-0 mb-8"
          size="lg"
        >
          {getCheckoutButtonText({
            isProcessing,
            isFreeCheckout,
            isPaymentLinkMethod,
            isEmailValid,
            areBillingFieldsFilled: !!areBillingFieldsFilled,
            isBillingPatched,
          })}
        </Button>
      </div>
    </form>
  );
}
