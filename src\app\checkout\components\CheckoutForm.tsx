"use client";

import {
  PaymentElement,
  useStripe,
  useElements,
  // ExpressCheckoutElement,
} from "@stripe/react-stripe-js";
import { Button } from "./ui";
import { useCheckoutForm } from "../hooks/useCheckoutForm";
import { ContactSection } from "./ContactSection";
import { DeliverySection } from "./DeliverySection";
import { BillingSection } from "./BillingSection";
import { ShippingMethods } from "./ShippingMethods";
import { useEffect, useCallback, useState } from "react";
// import { useApplePaySupport } from "../hooks/useApplePaySupport";
import { CHECKOUT_ERROR_MESSAGES } from "../utils/errorMessages";
import { useUserMessageReporting } from "../hooks/useUserMessageReporting";
// import { FormInput } from "./Form/FormInput";
import { useEmailCartClaim } from "../hooks/useEmailCartClaim";
import { useAddressCartPatch } from "../hooks/useAddressCartPatch";
import { Spinner } from "@nextui-org/react";
import { ADDRESS_TYPES } from "../constants/addressConstants";
import { PAYMENT_OPTIONS } from "@/lib/constants/storage";

interface BasicUserInfo {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  smsUpdates: boolean;
  newsletter: boolean;
}

interface CartCheckout {
  total: string;
  currency: string;
  email: string;
  lines: Array<any>;
  platform_fee: string;
  subtotal: string;
  token: string;
}

interface CartData {
  checkout: CartCheckout;
  problems: Array<any>;
}

interface CheckoutFormProps {
  isFreeCheckout?: boolean;
  refreshCartData?: () => void;
  onCreatePaymentIntent?: () => Promise<any>;
  isCreatingPayment?: boolean;
  hasPaymentIntent?: boolean;
  paymentError?: string | null;
  stripePromise?: Promise<any> | null;
  appearance?: object;
  clientSecret?: string | null;
  stripeMode?: "payment" | "deferred";
  basicUserInfo?: BasicUserInfo;
  isWhitelabel?: boolean;
  slug?: string;
}

export default function CheckoutForm({
  isFreeCheckout = false,
  refreshCartData,
  onCreatePaymentIntent,
  isCreatingPayment = false,
  hasPaymentIntent = false,
  basicUserInfo,
  isWhitelabel = false,
  slug,
}: CheckoutFormProps) {
  const stripe = !isFreeCheckout ? useStripe() : null;
  const elements = !isFreeCheckout ? useElements() : null;
  // const isApplePaySupported = useApplePaySupport();

  const {
    form: {
      control,
      handleSubmit,
      formState: { errors },
      setValue,
      // watch,
    },
    billingAddressType,
    setBillingAddressType,
    isProcessing,
    isCartLoading,
    cartData: typedCartData,
    countries,
    shippingStates,
    billingStates,
    isShippingRequired,
    sessionData,
    onSubmit,
    resetAddressFields,
    // handleExpressCheckout,
    // handleExpressCheckoutClick,
  } = useCheckoutForm(stripe, elements, isWhitelabel, slug);

  const approvalRequiredLines =
    typedCartData?.checkout?.lines?.filter(
      (line) => line?.eventDetails?.ticketApprovalRequired
    ) || [];

  const isSavePaymentMethod = approvalRequiredLines?.some(
    (line) =>
      line?.eventDetails?.paymentOption === PAYMENT_OPTIONS.SAVE_PAYMENT_METHOD
  );

  useEmailCartClaim({
    control,
    initialEmail: sessionData?.user?.email,
  });

  useEmailCartClaim({
    control,
    initialEmail: sessionData?.user?.email,
    field: "applePayEmail",
  });

  const cartDataTyped = typedCartData as unknown as CartData;
  const { reportUserMessage } = useUserMessageReporting();

  const [shouldFetchShippingMethods, setShouldFetchShippingMethods] =
    useState(false);
  const [shippingMethodsRefetchTrigger, setShippingMethodsRefetchTrigger] =
    useState(0);
  const [isShippingMethodSelected, setIsShippingMethodSelected] = useState(
    !isShippingRequired
  );
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const handleFieldError = useCallback(
    (fieldName: string, errorMessage: string) => {
      setFieldErrors((prev) => ({
        ...prev,
        [fieldName]: errorMessage,
      }));
    },
    []
  );

  const handleFieldErrorClear = useCallback((fieldName: string) => {
    setFieldErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  useEffect(() => {
    if (isWhitelabel) {
      setValue("email", basicUserInfo?.email || "");
      setValue("firstName", basicUserInfo?.firstName || "");
      setValue("lastName", basicUserInfo?.lastName || "");
      setValue("phone", basicUserInfo?.phone || "");
      setValue("billingPhone", basicUserInfo?.phone || "");
      setValue("billingFirstName", basicUserInfo?.firstName || "");
      setValue("billingLastName", basicUserInfo?.lastName || "");
      setValue("smsUpdates", basicUserInfo?.smsUpdates || true);
      setValue("newsletter", basicUserInfo?.newsletter || true);
    }
  }, [basicUserInfo, isWhitelabel, setValue]);

  useEffect(() => {
    if (isShippingRequired) {
      setIsShippingMethodSelected(false);
    } else {
      setIsShippingMethodSelected(true);
    }
  }, [isShippingRequired]);

  const handleShippingAddressSuccess = useCallback(() => {
    setShouldFetchShippingMethods(true);
    setShippingMethodsRefetchTrigger((prev) => prev + 1);
  }, []);

  const handleShippingMethodSet = useCallback(() => {
    setIsShippingMethodSelected(true);
    if (refreshCartData) {
      refreshCartData();
    }

    if (onCreatePaymentIntent && !hasPaymentIntent && !isCreatingPayment) {
      onCreatePaymentIntent();
    }
  }, [
    onCreatePaymentIntent,
    hasPaymentIntent,
    isCreatingPayment,
    refreshCartData,
    setIsShippingMethodSelected,
  ]);

  const handleAddressPatched = useCallback(() => {
    if (!isShippingRequired) {
      if (onCreatePaymentIntent && !hasPaymentIntent && !isCreatingPayment) {
        onCreatePaymentIntent();
      }
    }
  }, [
    onCreatePaymentIntent,
    hasPaymentIntent,
    isCreatingPayment,
    isShippingRequired,
  ]);

  useAddressCartPatch({
    control,
    type: ADDRESS_TYPES.SHIPPING,
    token: cartDataTyped?.checkout?.token,
    onAddressPatched: handleAddressPatched,
    onShippingAddressSuccess: handleShippingAddressSuccess,
    billingAddressType,
    onFieldError: handleFieldError,
    onFieldErrorClear: handleFieldErrorClear,
    isWhitelabel,
    slug,
    onResetAddressFields: resetAddressFields,
  });

  useAddressCartPatch({
    control,
    type: ADDRESS_TYPES.BILLING,
    token: cartDataTyped?.checkout?.token,
    onAddressPatched: handleAddressPatched,
    onFieldError: handleFieldError,
    onFieldErrorClear: handleFieldErrorClear,
    isWhitelabel,
    slug,
    onResetAddressFields: resetAddressFields,
  });

  useEffect(() => {
    if (cartDataTyped?.checkout?.lines?.length) {
      reportUserMessage(CHECKOUT_ERROR_MESSAGES.EMPTY_CART, {
        context: "checkout_form",
      });
    }
  }, [cartDataTyped, reportUserMessage]);

  if (!cartDataTyped?.checkout?.lines?.length) {
    const emptyCartMessage = CHECKOUT_ERROR_MESSAGES.EMPTY_CART;
    return <div>{emptyCartMessage}</div>;
  }

  const isPaymentButtonDisabled =
    isProcessing ||
    isCartLoading ||
    isCreatingPayment ||
    (!isFreeCheckout && !hasPaymentIntent) ||
    (isFreeCheckout && isShippingRequired && !isShippingMethodSelected);

  const isPaymentButtonLoading =
    isProcessing || isCartLoading || isCreatingPayment;

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 gap-8">
        {/* {!isFreeCheckout && isApplePaySupported && (
          <>
            <div className="mb-6 mt-4">
              <FormInput
                name="applePayEmail"
                control={control as any}
                type="email"
                label="Email for Apple Pay"
                errorMessage={errors.applePayEmail?.message}
                isDisabled={isFreeCheckout}
              />
              <div className="mt-4">
                <ExpressCheckoutElement
                  options={{
                    buttonType: {
                      applePay: "buy",
                    },
                    buttonHeight: 55,
                    paymentMethodOrder: ["apple_pay"],
                    paymentMethods: {
                      applePay: "always",
                      googlePay: "never",
                    },
                  }}
                  onConfirm={handleExpressCheckout}
                  onClick={handleExpressCheckoutClick}
                />
                {!watch("applePayEmail") && (
                  <div className="text-sm text-gray-500 mt-2">
                    Please enter your email to use Apple Pay
                  </div>
                )}
              </div>
            </div>
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center">
                <span className="bg-white px-4 text-sm text-gray-500">OR</span>
              </div>
            </div>
          </>
        )} */}

        {!isWhitelabel && (
          <ContactSection
            control={control as any}
            errors={errors}
            isDisabled={Boolean(sessionData?.user)}
          />
        )}

        {isShippingRequired && (
          <DeliverySection
            control={control as any}
            errors={errors}
            countries={countries}
            states={shippingStates}
            setValue={setValue as any}
            isLoggedIn={Boolean(sessionData?.user)}
            isWhitelabel={isWhitelabel}
            fieldErrors={fieldErrors}
          />
        )}

        <BillingSection
          control={control as any}
          errors={errors}
          countries={countries}
          states={billingStates}
          isProductInCart={isShippingRequired ?? false}
          billingAddressType={billingAddressType}
          setBillingAddressType={setBillingAddressType}
          setValue={setValue as any}
          isLoggedIn={Boolean(sessionData?.user)}
          isWhitelabel={isWhitelabel}
          fieldErrors={fieldErrors}
        />

        {isShippingRequired && (
          <ShippingMethods
            control={control as any}
            errors={errors}
            cartToken={cartDataTyped?.checkout?.token}
            onShippingMethodSet={handleShippingMethodSet}
            shouldFetchMethods={shouldFetchShippingMethods}
            refetchTrigger={shippingMethodsRefetchTrigger}
          />
        )}

        {!isFreeCheckout ? (
          <div className="relative">
            <div>
              <h2 className="text-[24px] font-semibold text-gray-900">
                Payment
              </h2>
              {isCreatingPayment && (
                <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
                  <Spinner />
                </div>
              )}
              {isSavePaymentMethod && (
                <div className="mt-2 mb-4 shadow-sm bg-white border border-[#E6E6E6] rounded-[4px] pt-[32px] pb-[12px] pl-[14px] w-[50%]">
                  <p className="text-[#6D6E78] text-[14px] font-semibold">
                    Pre Register
                  </p>
                </div>
              )}

              <div className="mt-1 relative">
                <PaymentElement
                  id="payment-element"
                  options={{
                    layout: "tabs",
                    paymentMethodOrder: isSavePaymentMethod
                      ? ["card"]
                      : ["card", "apple_pay"],
                    wallets: {
                      googlePay: "never",
                      applePay: isSavePaymentMethod ? "never" : "auto",
                    },
                  }}
                />
              </div>
            </div>
          </div>
        ) : (
          <div>
            <h2 className="text-[24px] font-semibold text-gray-900">Payment</h2>
            <p className="mt-4 bg-gray-100 p-4 rounded-md text-gray-500">
              Your order is free. No payment is required.
            </p>
          </div>
        )}
        <Button
          type="submit"
          color="primary"
          isDisabled={isPaymentButtonDisabled}
          isLoading={isPaymentButtonLoading}
          className="w-full bg-[#1773B0] mt-0 mb-8"
          size="lg"
        >
          {isProcessing ? "Processing..." : "Complete order"}
        </Button>
      </div>
    </form>
  );
}
