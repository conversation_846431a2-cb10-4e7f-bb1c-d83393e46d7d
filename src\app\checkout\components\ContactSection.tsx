import { Control, FieldErrors } from "react-hook-form";
import { FormInput } from "./Form/FormInput";
import { FormCheckbox } from "./Form/FormCheckbox";
import { CheckoutFormData } from "../types";
import Link from "next/link";

interface ContactSectionProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  isDisabled?: boolean;
}

export function ContactSection({
  control,
  errors,
  isDisabled,
}: ContactSectionProps) {
  return (
    <div>
      <div className="flex justify-between items-end">
        <h2 className="text-[24px] font-semibold text-gray-900">Contact</h2>
        {!isDisabled && (
          <Link
            href="/auth/signin"
            className="text-[15px] text-blue-600 underline"
          >
            Log in
          </Link>
        )}
      </div>
      <div className="mt-4 grid grid-cols-1 gap-y-4">
        <FormInput
          name="email"
          control={control}
          type="text"
          label="Email"
          errorMessage={errors.email?.message}
          isDisabled={isDisabled}
          isRequired={true}
        />
        {!isDisabled && (
          <FormCheckbox
            name="newsletter"
            control={control}
            label="Email me with news and offers"
            isDisabled={isDisabled}
          />
        )}
      </div>
    </div>
  );
}
