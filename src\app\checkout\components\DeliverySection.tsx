import { Control, FieldErrors, UseFormSetValue } from "react-hook-form";
import { FormInput, FormSelect } from "./Form";
import { SelectItem } from "@nextui-org/react";
import { CheckoutFormData } from "../types";
import { State } from "../types";
import { ICountry } from "country-state-city";
import { AutocompleteAddress } from "./AutocompleteAddress";
import { FormCheckbox } from "./Form/FormCheckbox";
import { PhoneInput } from "./Form/PhoneInput";

interface DeliverySectionProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  countries: ICountry[];
  states: State[];
  isDisabled?: boolean;
  setValue: UseFormSetValue<CheckoutFormData>;
  isLoggedIn: boolean;
  isWhitelabel: boolean;
  fieldErrors?: Record<string, string>;
}

export function DeliverySection({
  control,
  errors,
  countries,
  states,
  isDisabled,
  setValue,
  isLoggedIn,
  isWhitelabel,
  fieldErrors = {},
}: DeliverySectionProps) {
  return (
    <div>
      <h2 className="text-[24px] font-semibold text-gray-900">Delivery</h2>
      <div className="mt-4 grid grid-cols-1 gap-y-4">
        <FormSelect
          name="country"
          control={control}
          label="Country"
          errorMessage={errors.country?.message}
          defaultSelectedKeys={["US"]}
          isDisabled={isDisabled}
        >
          {countries.map((country) => (
            <SelectItem key={country.isoCode} value={country.isoCode}>
              {country.name}
            </SelectItem>
          ))}
        </FormSelect>

        {!isWhitelabel && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormInput
              name="firstName"
              control={control}
              type="text"
              label="First name"
              errorMessage={errors.firstName?.message}
              isDisabled={isDisabled}
            />
            <FormInput
              name="lastName"
              control={control}
              type="text"
              label="Last name"
              errorMessage={errors.lastName?.message}
              isDisabled={isDisabled}
            />
          </div>
        )}

        <FormInput
          name="company"
          control={control}
          type="text"
          label="Company (optional)"
          errorMessage={errors.company?.message}
          isDisabled={isDisabled}
        />

        <AutocompleteAddress
          name="addressLine1"
          control={control}
          label="Address"
          errorMessage={errors.addressLine1?.message}
          isDisabled={isDisabled}
          setValue={setValue}
          prefix=""
        />

        <FormInput
          name="addressLine2"
          control={control}
          type="text"
          label="Apartment, suite, etc. (optional)"
          errorMessage={errors.addressLine2?.message}
          isDisabled={isDisabled}
        />

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <FormInput
            name="city"
            control={control}
            type="text"
            label="City"
            errorMessage={errors.city?.message}
            isDisabled={isDisabled}
          />

          <FormSelect
            name="state"
            control={control}
            label="State/Province"
            errorMessage={errors.state?.message}
            isDisabled={isDisabled}
          >
            {states.map((state) => (
              <SelectItem key={state.name} value={state.name}>
                {state.name}
              </SelectItem>
            ))}
          </FormSelect>
          <FormInput
            name="postalCode"
            control={control}
            type="text"
            label="Postal code"
            errorMessage={errors.postalCode?.message}
            isDisabled={isDisabled}
          />
        </div>

        {!isWhitelabel && (
          <PhoneInput
            name="phone"
            control={control}
            label="Phone number"
            errorMessage={errors.phone?.message}
            isDisabled={isDisabled}
            fieldErrors={fieldErrors}
          />
        )}
        {isLoggedIn && (
          <FormCheckbox
            name="saveShippingAddress"
            control={control}
            label="Save shipping address"
            isDisabled={isDisabled}
          />
        )}
      </div>
    </div>
  );
}
