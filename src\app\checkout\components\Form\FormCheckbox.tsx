import { Controller, Control } from "react-hook-form";
import { Checkbox } from "@nextui-org/react";

interface FormCheckboxProps {
  name: string;
  control: Control<any>;
  label: string;
  defaultSelected?: boolean;
  color?:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";
  className?: string;
  isDisabled?: boolean;
}

export function FormCheckbox({
  name,
  control,
  label,
  defaultSelected = false,
  color = "primary",
  className = "text-sm text-gray-600",
  isDisabled = false,
}: FormCheckboxProps) {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultSelected}
      render={({ field: { onChange, value } }) => (
        <Checkbox
          isSelected={Boolean(value)}
          onValueChange={onChange}
          color={color}
          className={className}
          isDisabled={isDisabled}
        >
          {label}
        </Checkbox>
      )}
    />
  );
}
