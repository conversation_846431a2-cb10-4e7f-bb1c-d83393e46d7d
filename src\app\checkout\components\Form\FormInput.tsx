import { Controller, Control } from "react-hook-form";
import { Input, CustomInputProps } from "../ui/Input";

interface FormInputProps extends Omit<CustomInputProps, "value" | "onChange"> {
  name: string;
  control: Control<any>;
  errorMessage?: string;
  inputMode?:
    | "none"
    | "text"
    | "tel"
    | "url"
    | "email"
    | "numeric"
    | "decimal"
    | "search";
  pattern?: string;
}

export function FormInput({
  name,
  control,
  errorMessage,
  inputMode,
  pattern,
  ...props
}: FormInputProps) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Input
          {...props}
          value={field.value}
          onValueChange={field.onChange}
          errorMessage={errorMessage}
          isInvalid={!!errorMessage}
          inputMode={inputMode}
          pattern={pattern}
        />
      )}
    />
  );
}
