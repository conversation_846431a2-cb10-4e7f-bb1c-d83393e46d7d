import { Controller, Control } from "react-hook-form";
import { Select, CustomSelectProps } from "../ui/Select";

interface FormSelectProps
  extends Omit<CustomSelectProps, "value" | "onChange"> {
  name: string;
  control: Control<any>;
  errorMessage?: string;
}

export function FormSelect({
  name,
  control,
  errorMessage,
  ...props
}: FormSelectProps) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Select
          {...props}
          selectedKeys={field.value ? [field.value] : []}
          onSelectionChange={(keys) => field.onChange(Array.from(keys)[0])}
          errorMessage={errorMessage}
          isInvalid={!!errorMessage}
        />
      )}
    />
  );
}
