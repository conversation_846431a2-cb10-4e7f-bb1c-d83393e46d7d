import { Control, Controller, Path } from "react-hook-form";
import { Input } from "../ui/Input";
import { useState, useEffect, useRef } from "react";

interface PhoneInputProps<T extends Record<string, any>> {
  control: Control<T>;
  name: Path<T>;
  label: string;
  errorMessage?: string;
  isDisabled?: boolean;
  fieldErrors?: Record<string, string>;
  isRequired?: boolean;
}

export function PhoneInput<T extends Record<string, any>>({
  control,
  name,
  label,
  errorMessage,
  isDisabled,
  fieldErrors = {},
  isRequired = false,
}: PhoneInputProps<T>) {
  const [validationError, setValidationError] = useState<string>("");
  const [currentValue, setCurrentValue] = useState<string>("");
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const formatPhoneNumber = (value: string) => {
    const digits = value.replace(/[^\d]/g, "");

    if (digits.length <= 3) {
      return digits;
    } else if (digits.length <= 6) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    } else {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(
        6,
        10
      )}`;
    }
  };

  const validatePhoneNumber = (value: string) => {
    if (!value || value.trim() === "") {
      setValidationError("");
      return;
    }

    if (value.trim().startsWith("+")) {
      setValidationError(
        "Please don't include the country code. It will be added automatically based on your selected country."
      );
      return;
    }

    const digitCount = value.replace(/[^\d]/g, "").length;

    if (digitCount > 0 && digitCount !== 10) {
      setValidationError("Please enter a valid 10-digit phone number");
      return;
    }

    setValidationError("");
  };

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (currentValue) {
      timeoutRef.current = setTimeout(() => {
        validatePhoneNumber(currentValue);
      }, 2000);
    } else {
      setValidationError("");
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentValue]);

  const apiError = fieldErrors[name as string];
  const currentError = apiError || validationError || errorMessage;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <div className="relative">
          <Input
            {...field}
            type="tel"
            inputMode="tel"
            label={label}
            errorMessage={currentError}
            isInvalid={!!currentError}
            isDisabled={isDisabled}
            isRequired={isRequired}
            onChange={(e) => {
              const value = e.target.value;

              setValidationError("");

              if (value.trim().startsWith("+")) {
                setValidationError(
                  "Please don't include the country code. It will be added automatically based on your selected country."
                );
                return;
              }

              const digitsOnly = value.replace(/[^\d]/g, "");

              const limitedDigits = digitsOnly.slice(0, 10);

              const formattedValue = formatPhoneNumber(limitedDigits);

              field.onChange(formattedValue);

              setCurrentValue(formattedValue);
            }}
            value={field.value as string}
          />
        </div>
      )}
    />
  );
}
