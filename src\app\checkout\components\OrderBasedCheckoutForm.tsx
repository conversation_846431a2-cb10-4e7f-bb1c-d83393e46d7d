"use client";

import { useEffect, useState, useCallback, useMemo } from "react";
import { useForm, useWatch } from "react-hook-form";
import {
  PaymentElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { Button } from "./ui";
import { BillingSection } from "./BillingSection";
import { Spinner } from "@nextui-org/react";
import { useSessionData } from "@/lib/hooks/useSession";
import {
  useGetOrderByIdQuery,
  useUpdateOrderBillingAddressMutation,
  useGetOrderPaymentClientSecretQuery,
} from "@/lib/redux/slices/orders/ordersApi";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { Country, State, ICountry } from "country-state-city";
import { State as StateType } from "../types";
import { STORAGE_KEYS } from "@/lib/constants/storage";
import { handleApiErrorWithRouter } from "@/lib/utils/errorUtils";

interface ExtendedOrder {
  id: string;
  status: string;
  number: number;
  total: {
    amount: string;
    currency: string;
  };
  organization: {
    id: string;
    slug: string;
    [key: string]: any;
  };
  payment: {
    id: number;
    chargeStatus: string;
    paymentLinkExpiresAt: number;
  };
  lines: Array<{
    id: string;
    event: {
      id: string;
      name: string;
      timezone: string;
      [key: string]: any;
    };
    variantName: string;
    productName: string;
    totalPrice: {
      amount: string;
      currency: string;
    };
    [key: string]: any;
  }>;
  createdAt: string;
  linesCount: number;
}

interface OrderBasedCheckoutFormProps {
  orderId: string;
  refreshCartData?: () => void;
  onBillingPatched?: () => void;
}

// Helper functions for form persistence
const getStoredFormValues = (orderId: string) => {
  if (typeof window === "undefined") return null;
  try {
    const stored = sessionStorage.getItem(
      `${STORAGE_KEYS.CHECKOUT_FORM_DATA}_order_${orderId}`
    );
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (err) {
    console.error("Failed to parse stored order checkout form data", err);
  }
  return null;
};

const storeFormValues = (orderId: string, values: any) => {
  if (typeof window === "undefined") return;
  try {
    sessionStorage.setItem(
      `${STORAGE_KEYS.CHECKOUT_FORM_DATA}_order_${orderId}`,
      JSON.stringify(values)
    );
  } catch (err) {
    console.error("Failed to store order checkout form data", err);
  }
};

const clearStoredFormValues = (orderId: string) => {
  if (typeof window === "undefined") return;
  try {
    sessionStorage.removeItem(
      `${STORAGE_KEYS.CHECKOUT_FORM_DATA}_order_${orderId}`
    );
  } catch (err) {
    console.error("Failed to clear stored order checkout form data", err);
  }
};

export default function OrderBasedCheckoutForm({
  orderId,
  refreshCartData,
  onBillingPatched,
}: OrderBasedCheckoutFormProps) {
  const router = useRouter();
  const stripe = useStripe();
  const elements = useElements();
  const { data: session } = useSessionData();

  // Schema for order-based checkout (billing only)
  const orderCheckoutSchema = z.object({
    billingFirstName: z.string().min(1, "First name is required"),
    billingLastName: z.string().min(1, "Last name is required"),
    billingCompany: z.string().optional(),
    billingAddressLine1: z.string().min(1, "Address is required"),
    billingAddressLine2: z.string().optional(),
    billingCity: z.string().min(1, "City is required"),
    billingState: z.string().min(1, "State is required"),
    billingCountry: z.string().min(1, "Country is required"),
    billingPostalCode: z.string().min(1, "Postal code is required"),
    billingPhone: z.string().min(1, "Phone is required"),
    saveBillingAddress: z.boolean().optional(),
  });

  type OrderCheckoutFormData = z.infer<typeof orderCheckoutSchema>;

  // Get default values for form, prioritizing stored values
  const getDefaultValues = () => {
    const storedValues = getStoredFormValues(orderId);
    const defaultSessionValues = {
      billingFirstName:
        session?.user?.firstName || session?.user?.name?.split(" ")[0] || "",
      billingLastName:
        session?.user?.lastName ||
        session?.user?.name?.split(" ").slice(1).join(" ") ||
        "",
      billingCompany: "",
      billingAddressLine1: "",
      billingAddressLine2: "",
      billingCity: "",
      billingState: "",
      billingCountry: "US",
      billingPostalCode: "",
      billingPhone: "",
      saveBillingAddress: false,
    };

    // Merge stored values with defaults, prioritizing stored values
    return storedValues
      ? { ...defaultSessionValues, ...storedValues }
      : defaultSessionValues;
  };

  // Form setup
  const form = useForm<OrderCheckoutFormData>({
    resolver: zodResolver(orderCheckoutSchema),
    defaultValues: getDefaultValues(),
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = form;

  // Watch all form values for persistence
  const allFormValues = useWatch({ control });

  // Store form values in sessionStorage whenever they change
  useEffect(() => {
    if (allFormValues && Object.keys(allFormValues).length > 0) {
      storeFormValues(orderId, allFormValues);
    }
  }, [allFormValues, orderId]);

  // State management
  const [isProcessing, setIsProcessing] = useState(false);
  const [billingAddressType, setBillingAddressType] = useState<
    "same" | "different"
  >("different");
  const [isBillingPatched, setIsBillingPatched] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [billingUpdateAttempted, setBillingUpdateAttempted] = useState(false);
  const [billingUpdateError, setBillingUpdateError] = useState<string | null>(
    null
  );
  const [billingUpdateErrors, setBillingUpdateErrors] = useState<string[]>([]);

  // Fetch order data
  const {
    data: orderData,
    isLoading: isOrderLoading,
    error: orderError,
  } = useGetOrderByIdQuery({ orderId });

  // Cast to our extended type to access orderDetails
  const extendedOrderData = orderData as ExtendedOrder | undefined;

  // Get org identifier from order data
  const orgIdentifier = extendedOrderData?.organization?.slug;

  // Mutations
  const [updateBillingAddress, { isLoading: isUpdatingBilling }] =
    useUpdateOrderBillingAddressMutation();

  // Watch billing country for states
  const watchBillingCountry = useWatch({ control, name: "billingCountry" });

  // Generate states based on selected country
  const billingStates = useMemo(() => {
    if (watchBillingCountry && countries.length > 0) {
      const selectedCountry = countries.find(
        (c) => c.isoCode === watchBillingCountry
      );
      return selectedCountry
        ? State.getStatesOfCountry(selectedCountry.isoCode)
        : [];
    }
    return [];
  }, [watchBillingCountry, countries]);

  // Load countries on mount
  useEffect(() => {
    setCountries(Country.getAllCountries());
  }, []);

  // Watch billing fields
  const billingEssentialFields = useWatch({
    control,
    name: [
      "billingFirstName",
      "billingLastName",
      "billingAddressLine1",
      "billingCity",
      "billingState",
      "billingCountry",
      "billingPostalCode",
      "billingPhone",
    ],
  });

  // Reset attempt flag when billing fields change (to allow retry)
  useEffect(() => {
    if (billingUpdateAttempted) {
      setBillingUpdateAttempted(false);
      setBillingUpdateError(null);
      setBillingUpdateErrors([]);
    }
  }, [billingEssentialFields, billingUpdateAttempted]);

  // Check if essential billing fields are filled
  const areBillingFieldsFilled = useMemo(() => {
    if (!billingEssentialFields || !Array.isArray(billingEssentialFields)) {
      return false;
    }

    const [
      billingFirstName,
      billingLastName,
      billingAddressLine1,
      billingCity,
      billingState,
      billingCountry,
      billingPostalCode,
      billingPhone,
    ] = billingEssentialFields;

    return (
      billingFirstName?.trim() &&
      billingLastName?.trim() &&
      billingAddressLine1?.trim() &&
      billingCity?.trim() &&
      billingState?.trim() &&
      billingCountry?.trim() &&
      billingPostalCode?.trim() &&
      billingPhone?.trim()
    );
  }, [billingEssentialFields]);

  const handleFieldError = useCallback(
    (fieldName: string, errorMessage: string) => {
      setFieldErrors((prev) => ({
        ...prev,
        [fieldName]: errorMessage,
      }));
    },
    []
  );

  const handleFieldErrorClear = useCallback((fieldName: string) => {
    setFieldErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  // Extract errors from API response
  const extractApiErrors = useCallback((error: any): string[] => {
    const errors: string[] = [];

    if (error && typeof error === "object" && "data" in error) {
      const apiError = error.data;

      // Handle multiple errors
      if (
        apiError?.type === "multiple" &&
        apiError?.list &&
        Array.isArray(apiError.list)
      ) {
        apiError.list.forEach((individualError: any) => {
          if (individualError.detail) {
            let errorMessage = individualError.detail;
            // Prefix address validation errors with the attr field
            if (
              errorMessage.includes(
                "This value is not valid for the address."
              ) &&
              individualError.attr
            ) {
              errorMessage = `${individualError.attr}: ${errorMessage}`;
            }
            errors.push(errorMessage);
          }
        });
      }
      // Handle single validation error
      else if (apiError?.type === "validation_error" && apiError?.detail) {
        let errorMessage = apiError.detail;
        // Prefix address validation errors with the attr field
        if (
          errorMessage.includes("This value is not valid for the address.") &&
          apiError.attr
        ) {
          errorMessage = `${apiError.attr}: ${errorMessage}`;
        }
        errors.push(errorMessage);
      }
      // Fallback for other error formats
      else if (apiError?.detail) {
        errors.push(apiError.detail);
      }
    }

    // If no specific errors found, use generic message
    if (errors.length === 0) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to update billing address";
      errors.push(errorMessage);
    }

    return errors;
  }, []);

  // Handle billing address update
  const handleUpdateBillingAddress = useCallback(async () => {
    if (!orgIdentifier || !areBillingFieldsFilled || billingUpdateAttempted)
      return;

    setBillingUpdateAttempted(true);
    setBillingUpdateError(null);
    setBillingUpdateErrors([]);

    try {
      const addressLine2 = form.getValues("billingAddressLine2");
      const company = form.getValues("billingCompany");

      const billingData = {
        firstName: form.getValues("billingFirstName"),
        lastName: form.getValues("billingLastName"),
        ...(company && company.trim() && { company: company }),
        street_address_1: form.getValues("billingAddressLine1"),
        ...(addressLine2 &&
          addressLine2.trim() && { street_address_2: addressLine2 }),
        city: form.getValues("billingCity"),
        country_area: form.getValues("billingState"),
        country: form.getValues("billingCountry"),
        postalCode: form.getValues("billingPostalCode"),
        phone: form.getValues("billingPhone"),
      };

      await updateBillingAddress({
        orgIdentifier,
        orderId,
        billingAddress: billingData,
      }).unwrap();

      setIsBillingPatched(true);
      onBillingPatched?.(); // Notify parent component
    } catch (error) {
      console.error("Failed to update billing address:", error);

      // Extract and store the specific errors
      const extractedErrors = extractApiErrors(error);
      setBillingUpdateErrors(extractedErrors);

      // Use enhanced error handling with toast and potential redirect
      handleApiErrorWithRouter(
        error,
        router,
        "Failed to update billing address"
      );

      setBillingUpdateError(
        error instanceof Error
          ? error.message
          : "Failed to update billing address"
      );
      // Reset the attempted flag to allow retry on form change
      setBillingUpdateAttempted(false);
    }
  }, [
    orgIdentifier,
    orderId,
    areBillingFieldsFilled,
    billingUpdateAttempted,
    updateBillingAddress,
    form,
    onBillingPatched,
    extractApiErrors,
    router,
  ]);

  // Auto-update billing address when fields are filled (with debounce)
  useEffect(() => {
    if (
      areBillingFieldsFilled &&
      !isBillingPatched &&
      !isUpdatingBilling &&
      !billingUpdateError
    ) {
      const timeoutId = setTimeout(() => {
        handleUpdateBillingAddress();
      }, 500); // 500ms debounce

      return () => clearTimeout(timeoutId);
    }
  }, [
    areBillingFieldsFilled,
    isBillingPatched,
    isUpdatingBilling,
    billingUpdateError,
    handleUpdateBillingAddress,
  ]);

  // Handle form submission
  const onSubmit = async (data: OrderCheckoutFormData) => {
    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/order/success?orderId=${orderId}`,
        },
      });

      if (error) {
        console.error("Payment failed:", error);
        setIsProcessing(false);
      } else {
        // Payment successful - clear stored form data
        clearStoredFormValues(orderId);
      }
    } catch (error) {
      console.error("Payment processing error:", error);

      // Use enhanced error handling with toast and potential redirect
      handleApiErrorWithRouter(error, router, "Payment processing failed");

      setIsProcessing(false);
    }
  };

  if (isOrderLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  if (orderError || !extendedOrderData) {
    // Handle order loading error with enhanced error handling
    if (orderError) {
      handleApiErrorWithRouter(orderError, router, "Failed to load order data");
    }

    return (
      <div className="text-center text-red-600">
        Failed to load order data. Please try again.
      </div>
    );
  }

  if (!orgIdentifier) {
    return (
      <div className="text-center text-red-600">
        Invalid order data. Missing organization information.
      </div>
    );
  }

  const isPaymentButtonDisabled =
    isProcessing ||
    isUpdatingBilling ||
    !areBillingFieldsFilled ||
    !isBillingPatched ||
    !!billingUpdateError ||
    billingUpdateErrors.length > 0;

  const isPaymentButtonLoading = isProcessing || isUpdatingBilling;

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 gap-8">
        <BillingSection
          control={control as any}
          errors={errors}
          countries={countries}
          states={billingStates}
          isProductInCart={false}
          billingAddressType={billingAddressType}
          setBillingAddressType={setBillingAddressType}
          setValue={setValue as any}
          isLoggedIn={Boolean(session?.user)}
          isWhitelabel={false}
          fieldErrors={fieldErrors}
        />

        {/* Display billing update errors */}
        {(billingUpdateErrors.length > 0 || billingUpdateError) && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Billing Address Update Failed
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  {billingUpdateErrors.length > 0 ? (
                    <ul className="list-disc list-inside space-y-1">
                      {billingUpdateErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  ) : (
                    <p>{billingUpdateError}</p>
                  )}
                </div>
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setBillingUpdateAttempted(false);
                      setBillingUpdateError(null);
                      setBillingUpdateErrors([]);
                      handleUpdateBillingAddress();
                    }}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 text-sm rounded-md"
                  >
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="relative">
          <div>
            <h2 className="text-[24px] font-semibold text-gray-900">Payment</h2>
            <div className="mt-1 relative">
              {stripe ? (
                <PaymentElement
                  id="payment-element"
                  options={{
                    layout: "tabs",
                    paymentMethodOrder: ["card", "apple_pay"],
                    wallets: {
                      googlePay: "never",
                      applePay: "auto",
                    },
                  }}
                />
              ) : (
                <div className="bg-gray-100 p-4 rounded-md">
                  <div className="text-gray-500 text-center">
                    Loading payment form...
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <Button
          type="submit"
          color="primary"
          isDisabled={isPaymentButtonDisabled}
          isLoading={isPaymentButtonLoading}
          className="w-full bg-[#1773B0] mt-0 mb-8"
          size="lg"
        >
          {isProcessing
            ? "Processing payment..."
            : isUpdatingBilling
            ? "Updating billing address..."
            : billingUpdateError || billingUpdateErrors.length > 0
            ? "Fix billing address errors above"
            : !areBillingFieldsFilled
            ? "Please complete billing address"
            : !isBillingPatched
            ? "Validating billing information..."
            : "Complete payment"}
        </Button>
      </div>
    </form>
  );
}
