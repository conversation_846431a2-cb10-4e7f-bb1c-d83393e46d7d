"use client";

import React, { useCallback } from "react";
import { Badge } from "@nextui-org/react";
import Image from "next/image";
import {
  useGetOrderByIdQuery,
  useUpdateOrderLineMutation,
  useDeleteOrderLineMutation,
} from "@/lib/redux/slices/orders/ordersApi";
import QuantityButton from "@/app/cart/components/QuantityButton";
import toast from "react-hot-toast";
import { useOrderDataExtraction } from "../hooks/useOrderDataExtraction";
import {
  LoadingDisplay,
  EmptyOrderDisplay,
  InvalidOrderIdDisplay,
  OrderDataErrorDisplay,
  ProcessingErrorDisplay,
  FallbackItemDisplay,
} from "./OrderSummaryFallbacks";

interface OrderBasedOrderSummaryProps {
  orderId: string;
  refetchOrder?: () => void;
}

export default function OrderBasedOrderSummary({
  orderId,
  refetchOrder,
}: OrderBasedOrderSummaryProps) {
  const {
    hasError,
    errorMessage,
    extractOrderData,
    safeExtractOrganization,
    safeExtractEvent,
    safeExtractFormData,
    safeExtractPrivateMetadata,
    safeExtractPrice,
    getItemQuantity,
    getItemImage,
    clearError,
    setHasError,
    setErrorMessage,
  } = useOrderDataExtraction();

  const {
    data: orderData,
    isLoading: isOrderLoading,
    error: orderError,
    isUninitialized,
    refetch: refetchOrderData,
  } = useGetOrderByIdQuery({ orderId });

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetchOrderData = useCallback(() => {
    if (!isUninitialized) {
      refetchOrderData();
    }
  }, [isUninitialized, refetchOrderData]);

  const [updateOrderLine, { isLoading: isUpdatingQuantity }] =
    useUpdateOrderLineMutation();
  const [deleteOrderLine, { isLoading: isRemovingFromOrder }] =
    useDeleteOrderLineMutation();

  // Get organization slug from order data with fallbacks
  const orderOrgSlug = safeExtractOrganization(orderData)?.slug;

  // Error boundary for the entire component
  if (hasError) {
    return (
      <ProcessingErrorDisplay
        errorMessage={errorMessage}
        onRetry={() => {
          clearError();
          safeRefetchOrderData();
        }}
      />
    );
  }

  // Validate required props
  if (!orderId) {
    return <InvalidOrderIdDisplay />;
  }

  if (isOrderLoading) {
    return <LoadingDisplay />;
  }

  if (orderError || !orderData) {
    return <OrderDataErrorDisplay onRetry={safeRefetchOrderData} />;
  }

  try {
    // Extract order data using the custom hook
    const extractedData = extractOrderData(orderData);
    if (!extractedData) {
      return (
        <ProcessingErrorDisplay
          errorMessage="Failed to process order data"
          onRetry={() => {
            clearError();
            safeRefetchOrderData();
          }}
        />
      );
    }

    const {
      lines,
      totalAmount,
      currency,
      subtotalAmount,
      processingFeeAmount,
      platformFeeAmount,
      serviceFee,
    } = extractedData;

    // Check if order has no items
    if (!lines || lines.length === 0) {
      return <EmptyOrderDisplay />;
    }

    // Handle quantity decrease
    const handleQuantityDecrease = async (item: any) => {
      try {
        if (!orderOrgSlug) {
          toast.error("Organization not found");
          return;
        }

        const itemQuantity = getItemQuantity(item);

        if (itemQuantity <= 1) {
          // Remove item from order
          await deleteOrderLine({
            orgIdentifier: orderOrgSlug,
            lineId: item.id,
          }).unwrap();

          toast.success("Item removed from order");

          // Check if this was the last item
          if (lines.length === 1) {
            toast.success("All items removed from order");
          }
        } else {
          // Decrease quantity
          const newQuantity = itemQuantity - 1;

          await updateOrderLine({
            orgIdentifier: orderOrgSlug,
            lineId: item.id,
            quantity: newQuantity,
          }).unwrap();

          toast.success("Quantity updated");
        }

        // Refetch order data to update UI
        if (refetchOrder) {
          refetchOrder();
        } else {
          safeRefetchOrderData();
        }
      } catch (error) {
        console.error("Error updating quantity:", error);
        toast.error("Error updating quantity");
      }
    };

    // Handle quantity increase
    const handleQuantityIncrease = async (item: any) => {
      try {
        if (!orderOrgSlug) {
          toast.error("Organization not found");
          return;
        }

        const itemQuantity = getItemQuantity(item);

        // Add reasonable limit for quantity increase
        if (itemQuantity >= 99) {
          toast.error("Maximum quantity limit reached");
          return;
        }

        // Increase quantity
        const newQuantity = itemQuantity + 1;

        await updateOrderLine({
          orgIdentifier: orderOrgSlug,
          lineId: item.id,
          quantity: newQuantity,
        }).unwrap();

        toast.success("Quantity updated");

        // Refetch order data to update UI
        if (refetchOrder) {
          refetchOrder();
        } else {
          safeRefetchOrderData();
        }
      } catch (error) {
        console.error("Error updating quantity:", error);
        toast.error("Error updating quantity");
      }
    };

    return (
      <div className="p-4 md:p-6">
        <div className="mt-6 space-y-4">
          {lines.map((item, index) => {
            try {
              const itemQuantity = getItemQuantity(item);
              const privateMetadata = safeExtractPrivateMetadata(item);
              const isAddedAfterOrder =
                privateMetadata?.addedAfterOrder === true;
              const event = safeExtractEvent(item);
              const formResponseData = safeExtractFormData(item);

              return (
                <div
                  key={item.id || `item-${index}`}
                  className="flex items-center gap-2 md:gap-4"
                >
                  <Badge
                    content={itemQuantity}
                    color="secondary"
                    className="rounded-full"
                    classNames={{
                      badge: "text-xs text-white bg-gray-700/70 border-0",
                    }}
                  >
                    <Image
                      src={getItemImage(item)}
                      alt={
                        event?.name || item.productName || item.name || "Event"
                      }
                      width={64}
                      height={64}
                      className="h-12 w-12 md:h-16 md:w-16 rounded object-cover border border-gray-400"
                      onError={(e) => {
                        // Fallback to placeholder if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.src = "/placeholder.jpg";
                      }}
                    />
                  </Badge>
                  <div className="ml-4 flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {event?.name ||
                        item.productName ||
                        item.name ||
                        "Unknown Event"}
                    </h3>
                    <p className="text-xs md:text-sm text-gray-500">
                      {item.variantName ||
                        item.ticketName ||
                        item.type ||
                        "General"}
                    </p>
                    {/* Show vehicle information if available */}
                    {formResponseData &&
                      Array.isArray(formResponseData) &&
                      formResponseData.length > 0 &&
                      formResponseData[0]?.data?.vehicle && (
                        <div className="mt-1 text-xs md:text-sm text-gray-500">
                          Vehicle:{" "}
                          {formResponseData[0].data.vehicle.customMakeName ||
                            formResponseData[0].data.vehicle.makeName ||
                            "N/A"}{" "}
                          {formResponseData[0].data.vehicle.modelName || ""}
                        </div>
                      )}
                  </div>
                  {isAddedAfterOrder && (
                    <div className="min-w-[20%] md:min-w-[15%] flex justify-center">
                      <QuantityButton
                        quantity={itemQuantity}
                        onDecrease={() => handleQuantityDecrease(item)}
                        onIncrease={() => handleQuantityIncrease(item)}
                        isDisabled={false}
                        isLoading={isUpdatingQuantity || isRemovingFromOrder}
                        isWhitelabel={true}
                      />
                    </div>
                  )}
                  <p className="text-sm font-medium text-gray-900 ml-2">
                    $
                    {parseFloat(safeExtractPrice(item, "total").amount).toFixed(
                      2
                    )}
                  </p>
                </div>
              );
            } catch (error) {
              console.error(`Error rendering order line item ${index}:`, error);
              // Return a fallback item display
              return (
                <FallbackItemDisplay key={`fallback-${index}`} index={index} />
              );
            }
          })}
        </div>

        <dl className="mt-6 space-y-4">
          <div className="flex items-center justify-between">
            <dt className="text-sm text-gray-600">
              Subtotal &#8226;{" "}
              {`${lines?.length || 0} item${
                (lines?.length || 0) > 1 ? "s" : ""
              }`}
            </dt>
            <dd className="text-sm font-medium text-gray-900">
              ${subtotalAmount}
            </dd>
          </div>

          {/* Service Fee */}
          {serviceFee > 0 && (
            <div className="flex items-center justify-between">
              <dt className="text-sm text-gray-600">Service Fee</dt>
              <dd className="text-sm font-medium text-gray-900">
                ${serviceFee.toFixed(2)}
              </dd>
            </div>
          )}

          {/* Processing Fee */}
          {processingFeeAmount > 0 && (
            <div className="flex items-center justify-between">
              <dt className="text-sm text-gray-600">Processing Fee</dt>
              <dd className="text-sm font-medium text-gray-900">
                ${processingFeeAmount.toFixed(2)}
              </dd>
            </div>
          )}

          <div className="flex items-center justify-between border-t border-gray-200 pt-4">
            <dt className="text-md font-semibold text-gray-900">Total</dt>
            <dd className="flex gap-2 items-center text-md font-semibold text-gray-900">
              <span className="text-xs font-medium text-gray-500">
                {currency}
              </span>
              ${parseFloat(totalAmount).toFixed(2)}
            </dd>
          </div>
        </dl>
      </div>
    );
  } catch (error) {
    console.error("Error in OrderBasedOrderSummary:", error);
    setHasError(true);
    setErrorMessage("Failed to process order data. Please try again.");
    return (
      <ProcessingErrorDisplay
        errorMessage="Failed to process order data. Please try again."
        onRetry={() => {
          clearError();
          safeRefetchOrderData();
        }}
      />
    );
  }
}
