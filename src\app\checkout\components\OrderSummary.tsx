"use client";

import { Badge, Input, Button } from "@nextui-org/react";
import { CART_STORAGE_KEYS } from "@/lib/utils/constants";
import Image from "next/image";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { CHECKOUT_ERROR_MESSAGES } from "../utils/errorMessages";
import { useUserMessageReporting } from "../hooks/useUserMessageReporting";
import {
  useApplyVoucherMutation,
  useRemoveVoucherMutation,
  useUpdateQuantityMutation,
  useRemoveFromCartMutation,
} from "@/lib/redux/slices/cart/cartApi";
import QuantityButton from "@/app/cart/components/QuantityButton";
import { useRouter } from "next/navigation";
import { useSubdomain } from "@/lib/Providers/SubdomainProvider";

interface EventPhoto {
  id: string;
  photo: string;
}

interface Vehicle {
  name: string;
  year: number;
  makeName: string;
  modelName: string;
}

interface EventDetails {
  eventId: string;
  eventName: string;
  eventPhoto: EventPhoto[];
  ticketName: string;
  organizationName: string;
}

interface ProductVariant {
  id: number;
  sku: string;
  name: string;
  price: string;
  media: Array<{
    image: string;
    alt: string;
  }>;
  productDetails: {
    name: string;
  };
}

interface CartLine {
  id: string;
  variant?: ProductVariant;
  eventTicket?: string;
  type: string;
  quantity: number;
  totalPrice: string;
  eventDetails?: EventDetails;
  vehicle?: Vehicle;
  deliveryCharges?: number;
}

export default function OrderSummary({
  cartData,
  isCartLoading,
  isProcessingPayment,
  refetchCart,
  showDiscountSection = false,
}: {
  cartData: any;
  isCartLoading: boolean;
  isProcessingPayment?: boolean;
  refetchCart: () => void;
  showDiscountSection?: boolean;
}) {
  const { reportUserMessage } = useUserMessageReporting();
  const router = useRouter();

  // Safety check for useSubdomain hook
  let isWhitelabel = false;
  try {
    const subdomainContext = useSubdomain();
    isWhitelabel = subdomainContext?.isWhitelabel || false;
  } catch (error) {
    console.warn("useSubdomain hook not available in this context:", error);
    isWhitelabel = false;
  }

  const [discountInput, setDiscountInput] = useState("");
  const [applyVoucher, { isLoading: isApplyingDiscount }] =
    useApplyVoucherMutation();
  const [removeVoucher, { isLoading: isRemovingDiscount }] =
    useRemoveVoucherMutation();
  const [updateQuantity, { isLoading: isUpdatingQuantity }] =
    useUpdateQuantityMutation();
  const [removeFromCart, { isLoading: isRemovingFromCart }] =
    useRemoveFromCartMutation();

  // Combined loading state
  const isLoading =
    isApplyingDiscount ||
    isRemovingDiscount ||
    isProcessingPayment ||
    isUpdatingQuantity ||
    isRemovingFromCart;

  // Handle quantity decrease
  const handleQuantityDecrease = async (item: any) => {
    try {
      // Safety check for required properties
      if (!item || typeof item.quantity !== "number") {
        console.error("Invalid item data for quantity decrease:", item);
        return;
      }

      if (item.quantity <= 1) {
        // Check if this is the last item in cart before removing
        const isLastItem = cartData?.checkout?.lines?.length === 1;

        // If last item in cart, remove voucher first
        if (isLastItem) {
          try {
            await removeVoucher(undefined as any).unwrap();
          } catch (voucherError) {
            console.warn("Failed to remove voucher:", voucherError);
            // Continue with item removal even if voucher removal fails
          }
        }

        // Get the correct ID for the item - FIXED
        const itemId =
          item.type === "product" ? item.variant?.id : item.eventTicket;
        if (!itemId) {
          console.error("Cannot determine item ID for removal:", item);
          toast.error("Error: Unable to identify item for removal");
          return;
        }

        // Remove item from cart
        await removeFromCart({
          id: itemId,
          type: item.type === "product" ? "product" : "event_ticket",
        }).unwrap();

        await refetchCart();

        // Check if cart is now empty and we're in whitelabel
        if (isWhitelabel && isLastItem) {
          // Small delay to allow refetch to complete
          setTimeout(() => {
            const currentPath = window.location.pathname;
            const pathParts = currentPath.split("/");
            if (pathParts[1] === "event" && pathParts[2]) {
              const slug = pathParts[2];
              router.push(`/event/${slug}?oid=event`);
            }
          }, 100);
        }
      } else {
        // Get the correct ID for the item - FIXED
        const itemId =
          item.type === "product" ? item.variant?.id : item.eventTicket;
        if (!itemId) {
          console.error("Cannot determine item ID for quantity update:", item);
          toast.error("Error: Unable to identify item for update");
          return;
        }

        // Decrease quantity
        await updateQuantity({
          id: itemId,
          type: item.type === "product" ? "product" : "event_ticket",
          quantity: item.quantity - 1,
        }).unwrap();

        await refetchCart();
      }
    } catch (error) {
      console.error("Error decreasing quantity:", error);
      toast.error("Error updating quantity");
    }
  };

  // Handle quantity increase
  const handleQuantityIncrease = async (item: any) => {
    try {
      // Safety check for required properties
      if (!item || typeof item.quantity !== "number") {
        console.error("Invalid item data for quantity increase:", item);
        return;
      }

      // Get the correct ID for the item - FIXED
      const itemId =
        item.type === "product" ? item.variant?.id : item.eventTicket;
      if (!itemId) {
        console.error("Cannot determine item ID for quantity update:", item);
        toast.error("Error: Unable to identify item for update");
        return;
      }

      // Update quantity
      await updateQuantity({
        id: itemId,
        type: item.type === "product" ? "product" : "event_ticket",
        quantity: item.quantity + 1,
      }).unwrap();

      await refetchCart();
    } catch (error) {
      console.error("Error increasing quantity:", error);
      toast.error("Error updating quantity");
    }
  };

  const handleApplyDiscount = async () => {
    try {
      if (cartData?.checkout?.voucherCode) {
        await removeVoucher(undefined as any).unwrap();
      }

      await applyVoucher({
        code: discountInput?.trim()?.toLowerCase(),
      } as any).unwrap();
      await refetchCart();
      setDiscountInput("");
      toast.success("Discount applied");
    } catch (error) {
      toast.error("Error applying discount");
    }
  };

  const handleRemoveVoucher = async () => {
    try {
      await removeVoucher(undefined as any).unwrap();
      await refetchCart();
      toast.success("Discount code removed");
    } catch (error) {
      toast.error("Error removing discount code");
    }
  };

  const getLocalVariants = localStorage.getItem(
    CART_STORAGE_KEYS.CART_VARIANTS
  );

  const localVariants = JSON.parse(getLocalVariants || "{}");

  useEffect(() => {
    if (!cartData?.checkout?.lines?.length) {
      const emptyCartMessage = CHECKOUT_ERROR_MESSAGES.EMPTY_CART;
      reportUserMessage(emptyCartMessage, { component: "OrderSummary" });
    }
  }, [cartData, reportUserMessage]);

  if (isCartLoading || !cartData) {
    return <div>Loading cart data...</div>;
  }

  if (!cartData?.checkout?.lines?.length) {
    const emptyCartMessage = CHECKOUT_ERROR_MESSAGES.EMPTY_CART;
    return <div>{emptyCartMessage}</div>;
  }

  const subtotal = cartData?.checkout?.lines?.reduce(
    (acc, line) => acc + line.quantity * line.unitPrice,
    0
  );
  const platformFee = parseFloat(cartData.checkout.platformFee);
  const processingFee = parseFloat(cartData.checkout.processingFee);
  const shippingPrice = parseFloat(cartData.checkout.shippingPrice);
  const total = parseFloat(cartData.checkout.total);
  const discountAmount = parseFloat(cartData.checkout.discountAmount);
  const serviceFee = platformFee - processingFee;

  const itemImage = (product: any) => {
    return (
      product?.eventDetails?.eventPhoto?.find((photo: any) => photo.type === 1)
        ?.photo ||
      product.img ||
      "/placeholder.jpg"
    );
  };

  const itemVariantImage = (item: any) => {
    return (
      item.variant.media[0]?.image ||
      localVariants[item.variant.id]?.image ||
      "/placeholder.jpg"
    );
  };

  return (
    <div className="p-4 md:p-6">
      <div className="mt-6 space-y-4">
        {cartData.checkout.lines.map((item) => {
          const isFormResponseAvailable = item?.formResponseData?.length > 0;
          const isApprovalRequired = item?.eventDetails?.ticketApprovalRequired;
          const isIncreaseDisabled =
            isFormResponseAvailable || isApprovalRequired;

          return (
            <div key={item.id} className="flex items-center gap-2 md:gap-4">
              <Badge
                content={item.quantity}
                color="secondary"
                className="rounded-full"
                classNames={{
                  badge: "text-xs text-white bg-gray-700/70 border-0",
                }}
              >
                {item.type === "product" && item.variant ? (
                  <Image
                    src={itemVariantImage(item)}
                    alt={
                      item.variant.media[0]?.alt ||
                      item.variant.productDetails.name
                    }
                    width={64}
                    height={64}
                    className="h-12 w-12 md:h-16 md:w-16 rounded object-cover border border-gray-400"
                  />
                ) : (
                  <Image
                    src={itemImage(item)}
                    alt={item.eventDetails?.eventName || ""}
                    width={64}
                    height={64}
                    className="h-12 w-12 md:h-16 md:w-16 rounded object-cover border border-gray-400"
                  />
                )}
              </Badge>
              <div className="ml-4 flex-1 min-w-0">
                {item.type === "product" && item.variant ? (
                  <>
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {item.variant.productDetails.name}
                    </h3>
                    <p className="text-xs md:text-sm text-gray-500">
                      Variant: {item.variant.name}
                    </p>
                  </>
                ) : (
                  <>
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {item.eventDetails?.eventName}
                    </h3>
                    <p className="text-xs md:text-sm text-gray-500">
                      {item.eventDetails?.ticketName}
                    </p>
                    {item.vehicle && (
                      <div className="mt-1 text-xs md:text-sm text-gray-500">
                        Vehicle: {item.vehicle.makeName}{" "}
                        {item.vehicle.modelName}
                      </div>
                    )}
                    {item.selectedVariants &&
                      item.selectedVariants.length > 0 && (
                        <div className="mt-1 text-xs md:text-sm text-gray-500">
                          {item.selectedVariants.map((selectedVariant: any) => (
                            <p
                              key={selectedVariant.id}
                              className="text-xs md:text-sm text-gray-600"
                            >
                              {selectedVariant.variant.productDetails.name} -{" "}
                              {selectedVariant.variant.name}
                            </p>
                          ))}
                        </div>
                      )}
                  </>
                )}
              </div>
              {isWhitelabel && (
                <div className="min-w-[20%] md:min-w-[15%]">
                  {item.quantity && (
                    <QuantityButton
                      quantity={item.quantity}
                      onDecrease={() => handleQuantityDecrease(item)}
                      onIncrease={() => handleQuantityIncrease(item)}
                      isDisabled={isIncreaseDisabled}
                      isLoading={isUpdatingQuantity || isRemovingFromCart}
                      isWhitelabel={isWhitelabel}
                    />
                  )}
                </div>
              )}
              <p className="text-sm font-medium text-gray-900 ml-2">
                ${parseFloat(item.totalPrice).toFixed(2)}
              </p>
            </div>
          );
        })}
      </div>
      {showDiscountSection && (
        <div className="flex gap-2 my-6">
          <Input
            variant="bordered"
            placeholder="Discount code"
            value={discountInput}
            onChange={(e) => setDiscountInput(e.target.value)}
            classNames={{
              base: "max-w-full",
              mainWrapper: "h-[50px]",
              input: "text-sm",
              inputWrapper:
                "h-[50px] bg-white border border-default-200 rounded-[4px] px-3",
            }}
            isDisabled={isLoading}
          />
          <Button
            size="md"
            className="h-[50px] px-4 bg-gray-200 font-medium rounded-[4px] border border-gray-300"
            onPress={handleApplyDiscount}
            isDisabled={discountInput.length === 0 || isLoading}
            isLoading={isApplyingDiscount || isProcessingPayment}
          >
            Apply
          </Button>
        </div>
      )}
      {showDiscountSection && cartData.checkout.voucherCode && (
        <div className="bg-gray-200 w-fit px-2 py-1 rounded-[4px] mt-4 flex items-center gap-4">
          <Image
            src="/discount.svg"
            alt="Discount success"
            width={15}
            height={15}
          />
          <p className="text-[14px]">
            {cartData.checkout.voucherCode.toUpperCase()}
          </p>
          <button
            disabled={isLoading}
            className={
              isLoading ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
            }
            onClick={isLoading ? undefined : handleRemoveVoucher}
          >
            <Image
              src="/close.svg"
              alt="remove discount"
              width={15}
              height={15}
            />
          </button>
        </div>
      )}
      <dl className="mt-6 space-y-4">
        <div className="flex items-center justify-between">
          <dt className="text-sm text-gray-600">
            Subtotal &#8226;{" "}
            {`${cartData?.checkout?.lines?.length} item${
              cartData?.checkout?.lines?.length > 1 ? "s" : ""
            }`}
          </dt>
          <dd className="text-sm font-medium text-gray-900">
            ${subtotal.toFixed(2)}
          </dd>
        </div>
        {discountAmount > 0 && (
          <div>
            <div className="flex items-center justify-between">
              <dt className="text-sm">Order Discount</dt>
            </div>
            <div className="mt-1 flex items-center justify-between">
              <dt className="text-sm">
                <div className="flex items-center gap-2 text-gray-600 font-medium">
                  <Image
                    src="/discount.svg"
                    alt="Discount success"
                    width={15}
                    height={15}
                  />
                  {cartData.checkout.voucherCode?.toUpperCase()}
                </div>
              </dt>
              <dd className="text-sm font-medium">
                -${discountAmount.toFixed(2)}
              </dd>
            </div>
          </div>
        )}
        <div className="flex items-center justify-between">
          <dt className="text-sm text-gray-600">Service Fee</dt>
          <dd className="text-sm font-medium text-gray-900">
            ${serviceFee.toFixed(2)}
          </dd>
        </div>
        <div className="flex items-center justify-between">
          <dt className="text-sm text-gray-600">Processing Fee</dt>
          <dd className="text-sm font-medium text-gray-900">
            ${processingFee.toFixed(2)}
          </dd>
        </div>
        {shippingPrice > 0 && (
          <div className="flex items-center justify-between">
            <dt className="text-sm text-gray-600">Shipping</dt>
            <dd className="text-sm font-medium text-gray-900">
              ${shippingPrice.toFixed(2)}
            </dd>
          </div>
        )}
        <div className="flex items-center justify-between border-t border-gray-200 pt-4">
          <dt className="text-md font-semibold text-gray-900">Total</dt>
          <dd className="flex gap-2 items-center text-md font-semibold text-gray-900">
            <span className="text-xs font-medium text-gray-500">USD</span>$
            {total.toFixed(2)}
          </dd>
        </div>
      </dl>
    </div>
  );
}
