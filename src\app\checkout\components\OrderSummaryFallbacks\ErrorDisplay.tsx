import React from "react";

interface ErrorDisplayProps {
  title: string;
  message: string;
  onRetry?: () => void;
  retryButtonText?: string;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title,
  message,
  onRetry,
  retryButtonText = "Try Again",
}) => (
  <div className="p-4 md:p-6">
    <div className="text-center py-8">
      <p className="text-red-500 text-sm">{title}</p>
      <p className="text-gray-500 text-xs mt-2">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
        >
          {retryButtonText}
        </button>
      )}
    </div>
  </div>
);
