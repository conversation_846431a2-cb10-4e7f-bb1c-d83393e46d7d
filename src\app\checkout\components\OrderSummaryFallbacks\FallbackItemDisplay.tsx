import React from "react";

interface FallbackItemDisplayProps {
  index: number;
}

export const FallbackItemDisplay: React.FC<FallbackItemDisplayProps> = ({
  index,
}) => (
  <div
    key={`fallback-${index}`}
    className="flex items-center gap-2 md:gap-4 p-3 border border-red-200 bg-red-50 rounded"
  >
    <div className="h-12 w-12 md:h-16 md:w-16 rounded bg-gray-200 flex items-center justify-center">
      <span className="text-xs text-gray-500">?</span>
    </div>
    <div className="ml-4 flex-1">
      <h3 className="text-sm font-medium text-gray-900">
        Item {index + 1} (Error displaying)
      </h3>
      <p className="text-xs text-red-500">Unable to display item details</p>
    </div>
    <p className="text-sm font-medium text-gray-900 ml-2">$0.00</p>
  </div>
);
