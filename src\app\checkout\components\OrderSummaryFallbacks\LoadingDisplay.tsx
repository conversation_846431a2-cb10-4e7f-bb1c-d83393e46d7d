import React from "react";
import { Spinner } from "@nextui-org/react";

interface LoadingDisplayProps {
  message?: string;
}

export const LoadingDisplay: React.FC<LoadingDisplayProps> = ({
  message = "Loading order data...",
}) => (
  <div className="p-4 md:p-6">
    <div className="text-center py-8">
      <Spinner size="lg" />
      <p className="mt-4 text-gray-600">{message}</p>
    </div>
  </div>
);
