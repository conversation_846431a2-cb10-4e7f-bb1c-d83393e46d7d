import React from "react";

interface OrderDataErrorDisplayProps {
  onRetry: () => void;
}

export const OrderDataErrorDisplay: React.FC<OrderDataErrorDisplayProps> = ({
  onRetry,
}) => (
  <div className="p-4 md:p-6">
    <div className="text-center py-8">
      <p className="text-red-500 text-sm">Failed to load order data</p>
      <p className="text-gray-500 text-xs mt-2">
        Please try refreshing the page
      </p>
      <button
        onClick={onRetry}
        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
      >
        Retry
      </button>
    </div>
  </div>
);
