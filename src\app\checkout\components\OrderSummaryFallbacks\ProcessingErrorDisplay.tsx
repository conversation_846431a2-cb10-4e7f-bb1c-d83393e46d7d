import React from "react";

interface ProcessingErrorDisplayProps {
  errorMessage: string;
  onRetry: () => void;
}

export const ProcessingErrorDisplay: React.FC<ProcessingErrorDisplayProps> = ({
  errorMessage,
  onRetry,
}) => (
  <div className="p-4 md:p-6">
    <div className="text-center py-8">
      <p className="text-red-500 text-sm">Something went wrong</p>
      <p className="text-gray-500 text-xs mt-2">{errorMessage}</p>
      <button
        onClick={onRetry}
        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
      >
        Try Again
      </button>
    </div>
  </div>
);
