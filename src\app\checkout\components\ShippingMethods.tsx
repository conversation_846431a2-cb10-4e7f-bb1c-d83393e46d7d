import { Control, FieldErrors, Controller } from "react-hook-form";
import { RadioGroup, Radio, Spinner } from "@nextui-org/react";
import { CheckoutFormData } from "../types";
import {
  useShippingMethodsQuery,
  useSetShippingMethodMutation,
} from "@/lib/redux/slices/cart/cartApi";
import { useCallback, useEffect } from "react";
import { showCheckoutErrorToast } from "../utils/toastUtils";
import {
  SHIPPING_METHOD_SET_FAILED,
  SHIPPING_METHOD_SET_ERROR_LOG,
} from "../constants/formMessages";

interface ShippingMethodsProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  cartToken: string | undefined;
  onShippingMethodSet?: () => void;
  shouldFetchMethods?: boolean;
  refetchTrigger?: number;
}

export function ShippingMethods({
  control,
  errors,
  cartToken,
  onShippingMethodSet,
  shouldFetchMethods = false,
  refetchTrigger = 0,
}: ShippingMethodsProps) {
  const {
    data: shippingMethods,
    isLoading: isLoadingMethods,
    error: methodsError,
    isUninitialized,
    refetch: refetchShippingMethods,
  } = useShippingMethodsQuery(
    { cartToken },
    {
      skip: !cartToken || !shouldFetchMethods,
    }
  );

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetchShippingMethods = useCallback(() => {
    if (!isUninitialized) {
      refetchShippingMethods();
    }
  }, [isUninitialized, refetchShippingMethods]);

  const [setShippingMethod, { isLoading: isSettingMethod }] =
    useSetShippingMethodMutation();

  const cheapestMethods = shippingMethods
    ? [...shippingMethods]
        .sort((a: any, b: any) => (a.price || 0) - (b.price || 0))
        .slice(0, 5)
    : [];

  useEffect(() => {
    if (shouldFetchMethods && refetchTrigger > 0) {
      safeRefetchShippingMethods();
    }
  }, [refetchTrigger, shouldFetchMethods, safeRefetchShippingMethods]);

  const handleShippingMethodChange = useCallback(
    async (uniqueMethodId: string) => {
      if (!cartToken || !uniqueMethodId || !cheapestMethods.length) return;

      const methodIndex = parseInt(uniqueMethodId);
      const selectedMethod = cheapestMethods[methodIndex];
      if (!selectedMethod) return;

      try {
        await setShippingMethod({
          token: cartToken,
          shippingMethodId: selectedMethod.id,
          type: selectedMethod.type,
        }).unwrap();

        if (onShippingMethodSet) {
          onShippingMethodSet();
        }
      } catch (error: any) {
        console.error(SHIPPING_METHOD_SET_ERROR_LOG, error);
        showCheckoutErrorToast(SHIPPING_METHOD_SET_FAILED);
      }
    },
    [cartToken, setShippingMethod, onShippingMethodSet, cheapestMethods]
  );

  if (!shouldFetchMethods) {
    return (
      <div>
        <h2 className="text-[24px] font-semibold text-gray-900 mb-4">
          Shipping Method
        </h2>
        <div className="text-gray-500 text-sm">
          Please complete your shipping address to see available shipping
          methods.
        </div>
      </div>
    );
  }

  if (isLoadingMethods) {
    return (
      <div>
        <h2 className="text-[24px] font-semibold text-gray-900 mb-4">
          Shipping Method
        </h2>
        <div className="flex justify-center items-center py-8">
          <Spinner />
        </div>
      </div>
    );
  }

  if (methodsError) {
    return (
      <div>
        <h2 className="text-[24px] font-semibold text-gray-900 mb-4">
          Shipping Method
        </h2>
        <div className="text-red-500 text-sm">
          Failed to load shipping methods. Please try again.
        </div>
      </div>
    );
  }

  if (!shippingMethods || shippingMethods.length === 0) {
    return (
      <div>
        <h2 className="text-[24px] font-semibold text-gray-900 mb-4">
          Shipping Method
        </h2>
        <div className="text-gray-500 text-sm">
          No shipping methods available.
        </div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-[24px] font-semibold text-gray-900 mb-4">
        Shipping Method
      </h2>
      <Controller
        name="shippingMethod"
        control={control}
        render={({ field }) => (
          <RadioGroup
            value={field.value || ""}
            onValueChange={(value) => {
              field.onChange(value);
              handleShippingMethodChange(value);
            }}
            isDisabled={isSettingMethod}
            classNames={{
              wrapper: "gap-4 w-full",
            }}
          >
            {cheapestMethods.map((method: any, index: number) => (
              <Radio
                key={`${method.id}-${index}`}
                value={index.toString()}
                classNames={{
                  base: "max-w-full border border-gray-200 mt-1 rounded-md px-4 py-3 hover:bg-gray-50 data-[selected=true]:bg-[#1773B020] data-[selected=true]:border-[#1773B0] transition-colors mx-0",
                  labelWrapper: "w-full flex flex-col",
                }}
              >
                <div className="w-full flex justify-between items-center">
                  <div>
                    <div className="text-sm font-medium">{method.name}</div>
                    {method.description && (
                      <div className="text-xs text-gray-500 mt-1">
                        {method.description}
                      </div>
                    )}
                  </div>
                  <div className="text-sm font-semibold">
                    {method.price ? `$${method.price}` : "Free"}
                  </div>
                </div>
              </Radio>
            ))}
          </RadioGroup>
        )}
      />
      {errors.shippingMethod && (
        <div className="text-red-500 text-sm mt-2">
          {errors.shippingMethod.message}
        </div>
      )}
      {isSettingMethod && (
        <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
          <Spinner size="sm" />
          <span>Updating shipping method...</span>
        </div>
      )}
    </div>
  );
}
