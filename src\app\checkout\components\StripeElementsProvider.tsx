import { Elements } from "@stripe/react-stripe-js";
import { ReactNode, useEffect } from "react";
import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";

interface StripeElementsProviderProps {
  clientSecret?: string | null;
  stripePromise: Promise<any> | null;
  appearance: object;
  children: ReactNode;
  mode?: "payment" | "deferred";
  orderData?: any; // For order-based deferred mode
}

/**
 * A wrapper component that manages Stripe Elements and ensures it's re-mounted
 * when clientSecret or stripePromise changes. Supports both regular and deferred payment modes.
 */
export default function StripeElementsProvider({
  clientSecret,
  stripePromise,
  appearance,
  children,
  mode = "payment",
  orderData,
}: StripeElementsProviderProps) {
  const { data: cartData } = useGetCartQuery();

  if (!stripePromise) {
    return null;
  }

  // For deferred mode, we don't need clientSecret initially
  if (mode === "deferred") {
    // Use order data if available, otherwise fall back to cart data
    const amount = orderData?.total?.amount
      ? Math.round(parseFloat(orderData.total.amount) * 100)
      : (cartData as any)?.checkout?.total
      ? Math.round(parseFloat((cartData as any).checkout.total) * 100)
      : 1099;
    const currency =
      orderData?.total?.currency?.toLowerCase() ||
      (cartData as any)?.checkout?.currency?.toLowerCase() ||
      "usd";

    return (
      <Elements
        key={`stripe-elements-deferred-${amount}-${currency}`}
        stripe={stripePromise}
        options={{
          mode: "payment",
          amount,
          currency,
          appearance,
          locale: "en",
          paymentMethodTypes: ["card"],
        }}
      >
        {children}
      </Elements>
    );
  }

  // Regular mode requires clientSecret
  if (!clientSecret) {
    return null;
  }

  // The key prop ensures that the Elements component is completely re-mounted
  // when the clientSecret changes, forcing Stripe to reinitialize with the new intent
  return (
    <Elements
      key={`stripe-elements-${clientSecret}`}
      stripe={stripePromise}
      options={{
        appearance,
        clientSecret,
        locale: "en",
      }}
    >
      {children}
    </Elements>
  );
}
