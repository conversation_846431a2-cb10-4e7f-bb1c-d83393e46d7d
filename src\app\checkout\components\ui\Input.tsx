import { Input as NextUIInput, InputProps } from "@nextui-org/react";

export interface CustomInputProps extends InputProps {
  // Add any additional props here if needed
}

export function Input(props: CustomInputProps) {
  return (
    <NextUIInput
      {...props}
      prefix={props.prefix}
      classNames={{
        base: "bg-white",
        input: "text-[14px]",
        label: "text-[14px]",
        inputWrapper:
          "rounded-sm bg-white shadow-none border border-gray-200 hover:border-gray-300 h-[60px]",
        innerWrapper: "bg-transparent",
        ...props.classNames,
      }}
    />
  );
}
