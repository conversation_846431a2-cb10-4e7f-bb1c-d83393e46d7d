import { Select as NextUISelect, SelectProps } from "@nextui-org/react";

export interface CustomSelectProps extends SelectProps {
  // Add any additional props here if needed
}

export function Select(props: CustomSelectProps) {
  return (
    <NextUISelect
      {...props}
      classNames={{
        base: "bg-white",
        trigger:
          "rounded-sm bg-white shadow-none border border-gray-200 hover:border-gray-300 h-[60px]",
        value: "text-[14px]",
        label: "text-[14px]",
        listbox: "bg-white rounded-sm text-[14px]",
        popoverContent: "rounded-sm",
        innerWrapper: "bg-transparent",
        selectorIcon: "text-gray-500",
        ...props.classNames,
      }}
    />
  );
}
