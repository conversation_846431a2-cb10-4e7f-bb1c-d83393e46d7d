export const CHECKOUT_ERROR_CODES = {
  PAYMENT_INTENT_UNEXPECTED_STATE: 'payment_intent_unexpected_state',
} as const;

export const CHECKOUT_PAYMENT_STATUS = {
  CANCELED: 'canceled',
} as const;

export const CHECKOUT_ERROR_TYPES = {
  VALIDATION_ERROR: 'validation_error',
} as const;

export const CHECKOUT_ERROR_STATUS = {
  BAD_REQUEST: 400,
} as const;

export const CHECKOUT_FORM_TYPES = {
  SHIPPING: 'shipping',
  BILLING: 'billing',
} as const;

export const CHECKOUT_ERROR_CONTEXTS = {
  FORM_VALIDATION: 'form_validation',
  PAYMENT_METHOD: 'express',
} as const;

export const CHECKOUT_DEFAULT_COUNTRY = 'US';

export const CHECKOUT_STORAGE_KEYS = {
  PIXEL_PURCHASE_DATA: 'pixelPurchaseData',
  IS_APPROVAL_REQUIRED_CHECKOUT: 'isApprovalRequiredCheckout',
} as const;

export const CHECKOUT_TRANSACTION_PREFIXES = {
  FREE: 'free',
  ORDER: 'order',
  EXPRESS: 'express',
} as const;
