export const APPLE_PAY_EMAIL_REQUIRED = "Please enter your email to proceed with Apple Pay";
export const INVALID_EMAIL_ADDRESS = "Please enter a valid email address";
export const INVALID_APPLE_PAY_EMAIL_FORMAT = "Invalid Apple Pay email format";
export const INVALID_EMAIL_TLD = "Please enter an email with a valid domain extension (e.g., .com, .org, .net)";
export const INVALID_EMAIL_DOMAIN = "Please enter an email with a valid domain name";
export const SUSPICIOUS_EMAIL_DOMAIN = "Did you mean a different email provider? Please check your email address";
export const DISPOSABLE_EMAIL_NOT_ALLOWED = "Temporary or disposable email addresses are not allowed";
export const FILL_ALL_REQUIRED_DELIVERY_ADDRESS_FIELDS = "Please fill in all required address fields for delivery";
export const FILL_ALL_REQUIRED_BILLING_ADDRESS_FIELDS = "Please fill in all required address fields for billing";
export const NAME_ONLY_LETTERS_SPACES = "First and last name can only contain letters, and spaces";
export const PAYMENT_PROCESSING_NOT_READY = "Payment processing is not ready yet. Please try again.";
export const PAYMENT_CANCELED_REDIRECT_TO_CART = "Payment has been canceled. Redirecting to cart page, please try again.";
export const PAYMENT_FAILED = "Payment failed";
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
export const SHIPPING_METHOD_SET_FAILED = "Failed to set shipping method. Please try again.";
export const SHIPPING_METHOD_SET_ERROR_LOG = "Failed to set shipping method:";

// Common TLDs for validation
export const VALID_TLDS = [
  // Generic TLDs
  'com', 'org', 'net', 'edu', 'gov', 'mil', 'int',
  
  // Country code TLDs (major countries)
  'us', 'uk', 'ca', 'au', 'de', 'fr', 'jp', 'cn', 'in', 'br', 'ru', 'it', 'es', 'nl', 'se', 'no', 'dk', 'fi', 'pl', 'cz', 'hu', 'ro', 'bg', 'hr', 'si', 'sk', 'ee', 'lv', 'lt', 'mt', 'cy', 'gr', 'pt', 'ie', 'be', 'lu', 'at', 'ch', 'li', 'mc', 'ad', 'sm', 'va', 'mx', 'ar', 'cl', 'pe', 'co', 've', 'ec', 'bo', 'py', 'uy', 'gy', 'sr', 'gf', 'fk', 'gs', 'io', 'sh', 'ac', 'tc', 'vg', 'ai', 'ag', 'aw', 'bb', 'bs', 'bz', 'bm', 'ky', 'dm', 'do', 'gd', 'ht', 'jm', 'kn', 'lc', 'vc', 'tt', 'cr', 'sv', 'gt', 'hn', 'ni', 'pa',
  
  // New gTLDs (popular ones)
  'app', 'dev', 'io', 'ai', 'co', 'me', 'tv', 'cc', 'biz', 'info', 'name', 'pro', 'aero', 'coop', 'museum', 'jobs', 'mobi', 'travel', 'xxx', 'post', 'tel', 'asia', 'cat', 'eu', 'online', 'site', 'web', 'blog', 'shop', 'store', 'tech', 'digital', 'cloud', 'data', 'media', 'news', 'video', 'music', 'photo', 'art', 'design', 'creative', 'agency', 'consulting', 'services', 'solutions', 'systems', 'network', 'hosting', 'server', 'domain', 'website', 'webapp', 'mobile', 'api', 'tools', 'software', 'platform', 'marketplace', 'community', 'social', 'forum', 'chat', 'messaging', 'email', 'mail', 'contact', 'support', 'help', 'faq', 'docs', 'guide', 'tutorial', 'learn', 'course', 'training', 'education', 'school', 'university', 'college', 'academy', 'institute', 'research', 'science', 'technology', 'engineering', 'medical', 'health', 'fitness', 'wellness', 'beauty', 'fashion', 'style', 'luxury', 'premium', 'exclusive', 'vip', 'elite', 'gold', 'silver', 'platinum', 'diamond'
];

// Popular email domains for typo detection
export const POPULAR_DOMAINS = [
  // Gmail variations
  'gmail.com', 'googlemail.com',
  // Yahoo variations  
  'yahoo.com', 'yahoo.co.uk', 'yahoo.ca', 'yahoo.com.au', 'yahoo.fr', 'yahoo.de', 'yahoo.es', 'yahoo.it', 'yahoo.co.jp', 'yahoo.co.in',
  // Microsoft/Outlook variations
  'outlook.com', 'hotmail.com', 'live.com', 'msn.com', 'hotmail.co.uk', 'hotmail.fr', 'hotmail.de', 'hotmail.it', 'hotmail.es',
  // Apple
  'icloud.com', 'me.com', 'mac.com',
  // Other popular providers
  'aol.com', 'protonmail.com', 'mail.com', 'yandex.com', 'mail.ru', 'qq.com', '163.com', '126.com', 'sina.com',
  // Business/Professional
  'company.com', 'business.com', 'work.com', 'office.com'
];

// Common domain typos and their corrections
export const DOMAIN_TYPOS = {
  // Gmail typos - comprehensive list
  'gnail.com': 'gmail.com',
  'ganil.com': 'gmail.com',
  'gamil.com': 'gmail.com',
  'gmial.com': 'gmail.com', 
  'gmai.com': 'gmail.com',
  'gmail.co': 'gmail.com',
  'gmaill.com': 'gmail.com',
  'gmeil.com': 'gmail.com',
  'gmill.com': 'gmail.com',
  'gmial.co': 'gmail.com',
  'gmal.com': 'gmail.com',
  'gmali.com': 'gmail.com',
  'gmai.co': 'gmail.com',
  'gmail.cm': 'gmail.com',
  'gmail.om': 'gmail.com',
  'gmaail.com': 'gmail.com',
  'gmail.con': 'gmail.com',
  'gmaiil.com': 'gmail.com',
  'gmasil.com': 'gmail.com',
  'gmawil.com': 'gmail.com',
  'gmazil.com': 'gmail.com',
  'gmaxil.com': 'gmail.com',
  'gmaikl.com': 'gmail.com',
  'gmailk.com': 'gmail.com',
  'gmaikm.com': 'gmail.com',
  'gmailm.com': 'gmail.com',
  'gmain.com': 'gmail.com',
  'gmaik.com': 'gmail.com',
  'gmial.vom': 'gmail.com',
  'gmail.vom': 'gmail.com',
  'gmial.xom': 'gmail.com',
  'gmail.xom': 'gmail.com',
  'gmai.vom': 'gmail.com',
  'gmai.xom': 'gmail.com',
  'gimial.com': 'gmail.com',
  'gmaial.com': 'gmail.com',
  'gmailc.om': 'gmail.com',
  'gmailc.com': 'gmail.com',
  'gmailv.com': 'gmail.com',
  'gimial.co': 'gmail.com',
  'gmcial.com': 'gmail.com',
  'gmvial.com': 'gmail.com',
  'gmxial.com': 'gmail.com',
  'gmbial.com': 'gmail.com',
  'gnmial.com': 'gmail.com',
  'ghmial.com': 'gmail.com',
  'gymial.com': 'gmail.com',
  'gtmial.com': 'gmail.com',
  'grmial.com': 'gmail.com',
  'gfmial.com': 'gmail.com',
  'gdmial.com': 'gmail.com',
  'gsmial.com': 'gmail.com',
  'gwmial.com': 'gmail.com',
  'gqmial.com': 'gmail.com',
  'gemial.com': 'gmail.com',
  'gimails.com': 'gmail.com',
  'gmaiol.com': 'gmail.com',
  'gmaiul.com': 'gmail.com',
  'gmaijl.com': 'gmail.com',
  'gmaill.co': 'gmail.com',
  'gmial.net': 'gmail.com',
  'gmail.net': 'gmail.com',
  'gmial.org': 'gmail.com',
  'gmail.org': 'gmail.com',
  
  // Yahoo typos - comprehensive list
  'yahooo.com': 'yahoo.com',
  'yaho.com': 'yahoo.com',
  'yahoo.co': 'yahoo.com',
  'yhaoo.com': 'yahoo.com',
  'yahoo.cm': 'yahoo.com',
  'yahho.com': 'yahoo.com',
  'yahoo.om': 'yahoo.com',
  'yaahoo.com': 'yahoo.com',
  'yahool.com': 'yahoo.com',
  'yahoo.con': 'yahoo.com',
  'yaoo.com': 'yahoo.com',
  'yahop.com': 'yahoo.com',
  'yahooo.co': 'yahoo.com',
  'yahpoo.com': 'yahoo.com',
  'yanhoo.com': 'yahoo.com',
  'yasoo.com': 'yahoo.com',
  'yanoo.com': 'yahoo.com',
  'yaboo.com': 'yahoo.com',
  'yagoo.com': 'yahoo.com',
  'yadhoo.com': 'yahoo.com',
  'yawhoo.com': 'yahoo.com',
  'yazhoo.com': 'yahoo.com',
  'yaxhoo.com': 'yahoo.com',
  'yadoo.com': 'yahoo.com',
  'yahoop.com': 'yahoo.com',
  'yahooi.com': 'yahoo.com',
  'yahooo.vom': 'yahoo.com',
  'yahoo.vom': 'yahoo.com',
  'yahoo.xom': 'yahoo.com',
  'yaoo.co': 'yahoo.com',
  'yhoo.com': 'yahoo.com',
  'yaho.co': 'yahoo.com',
  'yhoo.co': 'yahoo.com',
  'yaahoo.co': 'yahoo.com',
  'yahho.co': 'yahoo.com',
  'yajoo.com': 'yahoo.com',
  'yahkoo.com': 'yahoo.com',
  'yahoom.com': 'yahoo.com',
  'yahooc.om': 'yahoo.com',
  'yahooc.com': 'yahoo.com',
  'yahoov.com': 'yahoo.com',
  'yqhoo.com': 'yahoo.com',
  'ywhoo.com': 'yahoo.com',
  'yshoo.com': 'yahoo.com',
  'yzhoo.com': 'yahoo.com',
  'yahoo.net': 'yahoo.com',
  'yahoo.org': 'yahoo.com',
  
  // Outlook typos - comprehensive list
  'outlok.com': 'outlook.com',
  'outlook.co': 'outlook.com',
  'outlokk.com': 'outlook.com',
  'outlook.om': 'outlook.com',
  'outlook.cm': 'outlook.com',
  'outlool.com': 'outlook.com',
  'outlooks.com': 'outlook.com',
  'outlook.con': 'outlook.com',
  'outllok.com': 'outlook.com',
  'outlpok.com': 'outlook.com',
  'outlokc.om': 'outlook.com',
  'outlokc.com': 'outlook.com',
  'outlokv.com': 'outlook.com',
  'outlook.vom': 'outlook.com',
  'outlook.xom': 'outlook.com',
  'outlok.co': 'outlook.com',
  'outlokk.co': 'outlook.com',
  'outlool.co': 'outlook.com',
  'outook.com': 'outlook.com',
  'outlok.net': 'outlook.com',
  'outlook.net': 'outlook.com',
  'outlok.org': 'outlook.com',
  'outlook.org': 'outlook.com',
  'otlook.com': 'outlook.com',
  'outloook.com': 'outlook.com',
  'oitlook.com': 'outlook.com',
  'oultook.com': 'outlook.com',
  'outtook.com': 'outlook.com',
  'ouflook.com': 'outlook.com',
  'outhook.com': 'outlook.com',
  'outloko.com': 'outlook.com',
  'outloki.com': 'outlook.com',
  'outlokm.com': 'outlook.com',
  'outlokn.com': 'outlook.com',
  'outlokj.com': 'outlook.com',
  'outlokh.com': 'outlook.com',
  'outlouk.com': 'outlook.com',
  'outloik.com': 'outlook.com',
  'outloyk.com': 'outlook.com',
  'outlojk.com': 'outlook.com',
  'outlokl.com': 'outlook.com',
  'outlpook.com': 'outlook.com',
  'outliook.com': 'outlook.com',
  'ouylook.com': 'outlook.com',
  'ohtlook.com': 'outlook.com',
  'putlook.com': 'outlook.com',
  'iutlook.com': 'outlook.com',
  '0utlook.com': 'outlook.com',
  '9utlook.com': 'outlook.com',
  'kutlook.com': 'outlook.com',
  'lutlook.com': 'outlook.com',
  
  // Hotmail typos - comprehensive list
  'hotmial.com': 'hotmail.com',
  'hotmai.com': 'hotmail.com',
  'hotmil.com': 'hotmail.com',
  'hotmial.co': 'hotmail.com',
  'hotmali.com': 'hotmail.com',
  'hotmaill.com': 'hotmail.com',
  'hotmail.co': 'hotmail.com',
  'hotmail.cm': 'hotmail.com',
  'hotmail.om': 'hotmail.com',
  'hotmial.vom': 'hotmail.com',
  'hotmail.vom': 'hotmail.com',
  'hotmail.xom': 'hotmail.com',
  'hotmai.co': 'hotmail.com',
  'hotmil.co': 'hotmail.com',
  'hotmali.co': 'hotmail.com',
  'hotmaill.co': 'hotmail.com',
  'hotmail.con': 'hotmail.com',
  'hotmails.com': 'hotmail.com',
  'hotmaal.com': 'hotmail.com',
  'hotnail.com': 'hotmail.com',
  'hotiail.com': 'hotmail.com',
  'hotmall.com': 'hotmail.com',
  'hotmaikl.com': 'hotmail.com',
  'hotmailk.com': 'hotmail.com',
  'hotmaik.com': 'hotmail.com',
  'hotmain.com': 'hotmail.com',
  'hotmailc.om': 'hotmail.com',
  'hotmailc.com': 'hotmail.com',
  'hotmailv.com': 'hotmail.com',
  'hotmial.net': 'hotmail.com',
  'hotmail.net': 'hotmail.com',
  'hotmial.org': 'hotmail.com',
  'hotmail.org': 'hotmail.com',
  'hmotail.com': 'hotmail.com',
  'hoitail.com': 'hotmail.com',
  'hottail.com': 'hotmail.com',
  'hoftail.com': 'hotmail.com',
  'hohtail.com': 'hotmail.com',
  'hoymail.com': 'hotmail.com',
  'hoymail.co': 'hotmail.com',
  'homail.com': 'hotmail.com',
  'homail.co': 'hotmail.com',
  'hotmaiil.com': 'hotmail.com',
  'hotmaail.com': 'hotmail.com',
  'hotmaiol.com': 'hotmail.com',
  'hotmaiul.com': 'hotmail.com',
  'hotmaipl.com': 'hotmail.com',
  'hotmaijl.com': 'hotmail.com',
  'htomail.com': 'hotmail.com',
  'ghotmail.com': 'hotmail.com',
  'jhotmail.com': 'hotmail.com',
  'nhotmail.com': 'hotmail.com',
  'hhotmail.com': 'hotmail.com',
  'bhotmail.com': 'hotmail.com',
  'yhotmail.com': 'hotmail.com',
  'uhotmail.com': 'hotmail.com',
  'piotmail.com': 'hotmail.com',
  'h0tmail.com': 'hotmail.com',
  'h9tmail.com': 'hotmail.com',
  'hktmail.com': 'hotmail.com',
  'hltmail.com': 'hotmail.com',
  'hptmail.com': 'hotmail.com',
  
  // Live.com typos
  'live.co': 'live.com',
  'live.cm': 'live.com',
  'live.om': 'live.com',
  'live.con': 'live.com',
  'live.vom': 'live.com',
  'live.xom': 'live.com',
  'livee.com': 'live.com',
  'liive.com': 'live.com',
  'livev.com': 'live.com',
  'livec.om': 'live.com',
  'livec.com': 'live.com',
  'livve.com': 'live.com',
  'luve.com': 'live.com',
  'lvie.com': 'live.com',
  'lice.com': 'live.com',
  'lide.com': 'live.com',
  'lise.com': 'live.com',
  'lire.com': 'live.com',
  'lige.com': 'live.com',
  'lije.com': 'live.com',
  'live.net': 'live.com',
  'live.org': 'live.com',
  'kive.com': 'live.com',
  'oive.com': 'live.com',
  'pive.com': 'live.com',
  ';ive.com': 'live.com',
  'mive.com': 'live.com',
  'ljve.com': 'live.com',
  'lkve.com': 'live.com',
  'love.com': 'live.com',
  'l8ve.com': 'live.com',
  'l9ve.com': 'live.com',
  'luke.com': 'live.com',
  'lite.com': 'live.com',
  'lime.com': 'live.com',
  'line.com': 'live.com',
  'lipe.com': 'live.com',
  
  // MSN.com typos
  'msn.co': 'msn.com',
  'msn.cm': 'msn.com',
  'msn.om': 'msn.com',
  'msn.con': 'msn.com',
  'msn.vom': 'msn.com',
  'msn.xom': 'msn.com',
  'mssn.com': 'msn.com',
  'msnn.com': 'msn.com',
  'mmsn.com': 'msn.com',
  'msm.com': 'msn.com',
  'msb.com': 'msn.com',
  'msj.com': 'msn.com',
  'msh.com': 'msn.com',
  'mskn.com': 'msn.com',
  'msln.com': 'msn.com',
  'mspn.com': 'msn.com',
  'mson.com': 'msn.com',
  'msin.com': 'msn.com',
  'msun.com': 'msn.com',
  'msyn.com': 'msn.com',
  'nsn.com': 'msn.com',
  'msn.net': 'msn.com',
  'msn.org': 'msn.com',
  
  // iCloud typos - comprehensive list
  'iclod.com': 'icloud.com',
  'icloud.co': 'icloud.com',
  'icoud.com': 'icloud.com',
  'iclould.com': 'icloud.com',
  'icloud.cm': 'icloud.com',
  'icloud.om': 'icloud.com',
  'icloud.con': 'icloud.com',
  'icloud.vom': 'icloud.com',
  'icloud.xom': 'icloud.com',
  'icloude.com': 'icloud.com',
  'iclod.co': 'icloud.com',
  'icoud.co': 'icloud.com',
  'iclould.co': 'icloud.com',
  'iclud.com': 'icloud.com',
  'iclowd.com': 'icloud.com',
  'icloowd.com': 'icloud.com',
  'iclaud.com': 'icloud.com',
  'icloaud.com': 'icloud.com',
  'icluod.com': 'icloud.com',
  'icliud.com': 'icloud.com',
  'icloid.com': 'icloud.com',
  'icould.com': 'icloud.com',
  'iclound.com': 'icloud.com',
  'ickoud.com': 'icloud.com',
  'iclkud.com': 'icloud.com',
  'iclpud.com': 'icloud.com',
  'icl0ud.com': 'icloud.com',
  'icl9ud.com': 'icloud.com',
  'icloyud.com': 'icloud.com',
  'iclohud.com': 'icloud.com',
  'iclogud.com': 'icloud.com',
  'iclofud.com': 'icloud.com',
  'iclosud.com': 'icloud.com',
  'iclotud.com': 'icloud.com',
  'iclorud.com': 'icloud.com',
  'iclored.com': 'icloud.com',
  'iclpoud.com': 'icloud.com',
  'iclioud.com': 'icloud.com',
  'iucloud.com': 'icloud.com',
  'ixcloud.com': 'icloud.com',
  'ivcloud.com': 'icloud.com',
  'idcloud.com': 'icloud.com',
  'ifcloud.com': 'icloud.com',
  'ucloud.com': 'icloud.com',
  'ocloud.com': 'icloud.com',
  'kcloud.com': 'icloud.com',
  'jcloud.com': 'icloud.com',
  '8cloud.com': 'icloud.com',
  '9cloud.com': 'icloud.com',
  'icloud.net': 'icloud.com',
  'icloud.org': 'icloud.com',
  
  // me.com typos
  'me.co': 'me.com',
  'me.cm': 'me.com',
  'me.om': 'me.com',
  'me.con': 'me.com',
  'me.vom': 'me.com',
  'me.xom': 'me.com',
  'mee.com': 'me.com',
  'mme.com': 'me.com',
  'mec.om': 'me.com',
  'mec.com': 'me.com',
  'mev.com': 'me.com',
  'nme.com': 'me.com',
  'ne.com': 'me.com',
  'je.com': 'me.com',
  'ke.com': 'me.com',
  'mw.com': 'me.com',
  'ms.com': 'me.com',
  'md.com': 'me.com',
  'mr.com': 'me.com',
  'mf.com': 'me.com',
  'mv.com': 'me.com',
  'mc.com': 'me.com',
  'mx.com': 'me.com',
  'mz.com': 'me.com',
  'ma.com': 'me.com',
  'mi.com': 'me.com',
  'mo.com': 'me.com',
  'mu.com': 'me.com',
  'my.com': 'me.com',
  'mt.com': 'me.com',
  'mg.com': 'me.com',
  'mh.com': 'me.com',
  'mj.com': 'me.com',
  'mk.com': 'me.com',
  'ml.com': 'me.com',
  'mn.com': 'me.com',
  'mp.com': 'me.com',
  'mq.com': 'me.com',
  'me.net': 'me.com',
  'me.org': 'me.com',
  
  // mac.com typos  
  'mac.co': 'mac.com',
  'mac.cm': 'mac.com',
  'mac.om': 'mac.com',
  'mac.con': 'mac.com',
  'mac.vom': 'mac.com',
  'mac.xom': 'mac.com',
  'macc.com': 'mac.com',
  'mmac.com': 'mac.com',
  'maacc.com': 'mac.com',
  'maac.com': 'mac.com',
  'macc.om': 'mac.com',
  'macc.co': 'mac.com',
  'maxc.com': 'mac.com',
  'mavc.com': 'mac.com',
  'madc.com': 'mac.com',
  'mafc.com': 'mac.com',
  'magc.com': 'mac.com',
  'mahc.com': 'mac.com',
  'majc.com': 'mac.com',
  'makc.com': 'mac.com',
  'malc.com': 'mac.com',
  'manc.com': 'mac.com',
  'mapc.com': 'mac.com',
  'maqc.com': 'mac.com',
  'marc.com': 'mac.com',
  'masc.com': 'mac.com',
  'matc.com': 'mac.com',
  'mauc.com': 'mac.com',
  'mawc.com': 'mac.com',
  'mayc.com': 'mac.com',
  'mazc.com': 'mac.com',
  'nac.com': 'mac.com',
  'jac.com': 'mac.com',
  'kac.com': 'mac.com',
  'lac.com': 'mac.com',
  'mac.net': 'mac.com',
  'mac.org': 'mac.com',
  
  // AOL typos
  'aol.co': 'aol.com',
  'aol.cm': 'aol.com',
  'aol.om': 'aol.com',
  'aol.con': 'aol.com',
  'aol.vom': 'aol.com',
  'aol.xom': 'aol.com',
  'aoll.com': 'aol.com',
  'aaol.com': 'aol.com',
  'aooll.com': 'aol.com',
  'aoolc.om': 'aol.com',
  'aoolc.com': 'aol.com',
  'aoolv.com': 'aol.com',
  'ail.com': 'aol.com',
  'alol.com': 'aol.com',
  'awol.com': 'aol.com',
  'asol.com': 'aol.com',
  'adol.com': 'aol.com',
  'afol.com': 'aol.com',
  'agol.com': 'aol.com',
  'ahol.com': 'aol.com',
  'ajol.com': 'aol.com',
  'akol.com': 'aol.com',
  'amol.com': 'aol.com',
  'anol.com': 'aol.com',
  'apol.com': 'aol.com',
  'aqol.com': 'aol.com',
  'arol.com': 'aol.com',
  'atol.com': 'aol.com',
  'auol.com': 'aol.com',
  'avol.com': 'aol.com',
  'ayol.com': 'aol.com',
  'azol.com': 'aol.com',
  'aokl.com': 'aol.com',
  'aopl.com': 'aol.com',
  'ao0l.com': 'aol.com',
  'ao9l.com': 'aol.com',
  'aolk.com': 'aol.com',
  'aolo.com': 'aol.com',
  'aolp.com': 'aol.com',
  'aol.net': 'aol.com',
  'aol.org': 'aol.com',
  
  // ProtonMail typos
  'protonmial.com': 'protonmail.com',
  'protonmai.com': 'protonmail.com',
  'protonmail.co': 'protonmail.com',
  'protonmail.cm': 'protonmail.com',
  'protonmail.om': 'protonmail.com',
  'protonmail.con': 'protonmail.com',
  'protonmail.vom': 'protonmail.com',
  'protonmail.xom': 'protonmail.com',
  'protonmails.com': 'protonmail.com',
  'protinmail.com': 'protonmail.com',
  'protanmail.com': 'protonmail.com',
  'protenmail.com': 'protonmail.com',
  'protonmial.co': 'protonmail.com',
  'protonmai.co': 'protonmail.com',
  'protommail.com': 'protonmail.com',
  'protonmall.com': 'protonmail.com',
  'protonmaikl.com': 'protonmail.com',
  'protonmailk.com': 'protonmail.com',
  'protonmaik.com': 'protonmail.com',
  'protonmain.com': 'protonmail.com',
  'protonmailc.om': 'protonmail.com',
  'protonmailc.com': 'protonmail.com',
  'protonmailv.com': 'protonmail.com',
  'protonmial.net': 'protonmail.com',
  'protonmail.net': 'protonmail.com',
  'protonmial.org': 'protonmail.com',
  'protonmail.org': 'protonmail.com',
  'protomail.com': 'protonmail.com',
  'protonmaiil.com': 'protonmail.com',
  'protonmaail.com': 'protonmail.com',
  'protonmaiol.com': 'protonmail.com',
  'protonmaiul.com': 'protonmail.com',
  'protonmaipl.com': 'protonmail.com',
  'protonmaijl.com': 'protonmail.com',
  'protnomail.com': 'protonmail.com',
  'prtonmail.com': 'protonmail.com',
  'prortonmail.com': 'protonmail.com',
  'prootonmail.com': 'protonmail.com',
  'prottonmail.com': 'protonmail.com',
  'proftonmail.com': 'protonmail.com',
  'prohtonmail.com': 'protonmail.com',
  'proytonmail.com': 'protonmail.com',
  'proutonmail.com': 'protonmail.com',
  'proitonmail.com': 'protonmail.com',
  'proktonmail.com': 'protonmail.com',
  'proltonmail.com': 'protonmail.com',
  'proptonmail.com': 'protonmail.com',
  'protimail.com': 'protonmail.com',
  'protonamail.com': 'protonmail.com',
  'protonemail.com': 'protonmail.com',
  'protonimail.com': 'protonmail.com',
  'protonomail.com': 'protonmail.com',
  'protonumail.com': 'protonmail.com',
  'protonymail.com': 'protonmail.com',
  'protontmail.com': 'protonmail.com',
  'protonfmail.com': 'protonmail.com',
  'protongmail.com': 'protonmail.com',
  'protonhmail.com': 'protonmail.com',
  'protonjmail.com': 'protonmail.com',
  'protonkmail.com': 'protonmail.com',
  'protonlmail.com': 'protonmail.com',
  'protonpmail.com': 'protonmail.com',
  'protonqmail.com': 'protonmail.com',
  'protonrmail.com': 'protonmail.com',
  'protonsmail.com': 'protonmail.com',
  'protonvmail.com': 'protonmail.com',
  'protonwmail.com': 'protonmail.com',
  'protonxmail.com': 'protonmail.com',
  'protonzmail.com': 'protonmail.com',
  

};

// Import email validation packages
let validator: any;
let disposableDomains: string[] = [];

try {
  validator = require('email-validator');
  disposableDomains = require('disposable-email-domains');
} catch (error) {
  console.warn('Email validation packages not available:', error);
}

// Email validation function with comprehensive checks
export const validateEmailWithTLD = (email: string): { isValid: boolean; error?: string; suggestion?: string } => {
  if (!email) {
    return { isValid: false, error: INVALID_EMAIL_ADDRESS };
  }

  const trimmedEmail = email.trim().toLowerCase();

  // Basic email format check using our regex first
  if (!EMAIL_REGEX.test(trimmedEmail)) {
    return { isValid: false, error: INVALID_EMAIL_ADDRESS };
  }

  // Use email-validator library if available for more robust validation
  if (validator && !validator.validate(trimmedEmail)) {
    return { isValid: false, error: INVALID_EMAIL_ADDRESS };
  }

  // Extract domain and TLD
  const domainPart = trimmedEmail.split('@')[1];
  if (!domainPart) {
    return { isValid: false, error: INVALID_EMAIL_DOMAIN };
  }

  // Check for disposable email domains
  if (disposableDomains.length > 0 && disposableDomains.includes(domainPart)) {
    return { isValid: false, error: DISPOSABLE_EMAIL_NOT_ALLOWED };
  }

  const domainParts = domainPart.split('.');
  if (domainParts.length < 2) {
    return { isValid: false, error: INVALID_EMAIL_DOMAIN };
  }

  const tld = domainParts[domainParts.length - 1].toLowerCase();
  
  // Check if TLD is valid
  if (!VALID_TLDS.includes(tld)) {
    return { isValid: false, error: INVALID_EMAIL_TLD };
  }

  // Check if domain is already a popular domain (valid case)
  if (POPULAR_DOMAINS.includes(domainPart)) {
    return { isValid: true };
  }

  // Check for common domain typos
  if (DOMAIN_TYPOS[domainPart]) {
    return { 
      isValid: false, 
      error: `Did you mean ${DOMAIN_TYPOS[domainPart]}?`,
      suggestion: DOMAIN_TYPOS[domainPart]
    };
  }

  // Additional domain validation
  const domainName = domainParts.slice(0, -1).join('.');
  if (!domainName || domainName.length < 1) {
    return { isValid: false, error: INVALID_EMAIL_DOMAIN };
  }

  // Check for suspicious domains (simple Levenshtein distance check for popular domains)
  // Only check if it's NOT already a popular domain
  const checkForSuspiciousDomain = (inputDomain: string): string | null => {
    for (const popularDomain of POPULAR_DOMAINS) {
      if (inputDomain !== popularDomain && getLevenshteinDistance(inputDomain, popularDomain) === 1) {
        return popularDomain;
      }
    }
    return null;
  };

  const suspiciousDomain = checkForSuspiciousDomain(domainPart);
  if (suspiciousDomain) {
    return { 
      isValid: false, 
      error: `Did you mean ${suspiciousDomain}?`,
      suggestion: suspiciousDomain
    };
  }

  return { isValid: true };
};

// Simple Levenshtein distance calculation for typo detection
function getLevenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
} 
