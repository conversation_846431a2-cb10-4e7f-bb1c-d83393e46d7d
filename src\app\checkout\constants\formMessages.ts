export const APPLE_PAY_EMAIL_REQUIRED = "Please enter your email to proceed with Apple Pay";
export const INVALID_EMAIL_ADDRESS = "Please enter a valid email address";
export const INVALID_APPLE_PAY_EMAIL_FORMAT = "Invalid Apple Pay email format";
export const INVALID_EMAIL_TLD = "Please enter an email with a valid domain extension (e.g., .com, .org, .net)";
export const INVALID_EMAIL_DOMAIN = "Please enter an email with a valid domain name";
export const FILL_ALL_REQUIRED_DELIVERY_ADDRESS_FIELDS = "Please fill in all required address fields for delivery";
export const FILL_ALL_REQUIRED_BILLING_ADDRESS_FIELDS = "Please fill in all required address fields for billing";
export const NAME_ONLY_LETTERS_SPACES = "First and last name can only contain letters, and spaces";
export const PAYMENT_PROCESSING_NOT_READY = "Payment processing is not ready yet. Please try again.";
export const PAYMENT_CANCELED_REDIRECT_TO_CART = "Payment has been canceled. Redirecting to cart page, please try again.";
export const PAYMENT_FAILED = "Payment failed";
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
export const SHIPPING_METHOD_SET_FAILED = "Failed to set shipping method. Please try again.";
export const SHIPPING_METHOD_SET_ERROR_LOG = "Failed to set shipping method:";

// Common TLDs for validation
export const VALID_TLDS = [
  // Generic TLDs
  'com', 'org', 'net', 'edu', 'gov', 'mil', 'int',
  
  // Country code TLDs (major countries)
  'us', 'uk', 'ca', 'au', 'de', 'fr', 'jp', 'cn', 'in', 'br', 'ru', 'it', 'es', 'nl', 'se', 'no', 'dk', 'fi', 'pl', 'cz', 'hu', 'ro', 'bg', 'hr', 'si', 'sk', 'ee', 'lv', 'lt', 'mt', 'cy', 'gr', 'pt', 'ie', 'be', 'lu', 'at', 'ch', 'li', 'mc', 'ad', 'sm', 'va', 'mx', 'ar', 'cl', 'pe', 'co', 've', 'ec', 'bo', 'py', 'uy', 'gy', 'sr', 'gf', 'fk', 'gs', 'io', 'sh', 'ac', 'tc', 'vg', 'ai', 'ag', 'aw', 'bb', 'bs', 'bz', 'bm', 'ky', 'dm', 'do', 'gd', 'ht', 'jm', 'kn', 'lc', 'vc', 'tt', 'cr', 'sv', 'gt', 'hn', 'ni', 'pa',
  
  // New gTLDs (popular ones)
  'app', 'dev', 'io', 'ai', 'co', 'me', 'tv', 'cc', 'biz', 'info', 'name', 'pro', 'aero', 'coop', 'museum', 'jobs', 'mobi', 'travel', 'xxx', 'post', 'tel', 'asia', 'cat', 'eu', 'online', 'site', 'web', 'blog', 'shop', 'store', 'tech', 'digital', 'cloud', 'data', 'media', 'news', 'video', 'music', 'photo', 'art', 'design', 'creative', 'agency', 'consulting', 'services', 'solutions', 'systems', 'network', 'hosting', 'server', 'domain', 'website', 'webapp', 'mobile', 'api', 'tools', 'software', 'platform', 'marketplace', 'community', 'social', 'forum', 'chat', 'messaging', 'email', 'mail', 'contact', 'support', 'help', 'faq', 'docs', 'guide', 'tutorial', 'learn', 'course', 'training', 'education', 'school', 'university', 'college', 'academy', 'institute', 'research', 'science', 'technology', 'engineering', 'medical', 'health', 'fitness', 'wellness', 'beauty', 'fashion', 'style', 'luxury', 'premium', 'exclusive', 'vip', 'elite', 'gold', 'silver', 'platinum', 'diamond'
];

// Email validation function with TLD check
export const validateEmailWithTLD = (email: string): { isValid: boolean; error?: string } => {
  if (!email) {
    return { isValid: false, error: INVALID_EMAIL_ADDRESS };
  }

  // Basic email format check
  if (!EMAIL_REGEX.test(email)) {
    return { isValid: false, error: INVALID_EMAIL_ADDRESS };
  }

  // Extract domain and TLD
  const domainPart = email.split('@')[1];
  if (!domainPart) {
    return { isValid: false, error: INVALID_EMAIL_DOMAIN };
  }

  const domainParts = domainPart.split('.');
  if (domainParts.length < 2) {
    return { isValid: false, error: INVALID_EMAIL_DOMAIN };
  }

  const tld = domainParts[domainParts.length - 1].toLowerCase();
  
  // Check if TLD is valid
  if (!VALID_TLDS.includes(tld)) {
    return { isValid: false, error: INVALID_EMAIL_TLD };
  }

  // Additional domain validation
  const domainName = domainParts.slice(0, -1).join('.');
  if (!domainName || domainName.length < 1) {
    return { isValid: false, error: INVALID_EMAIL_DOMAIN };
  }

  return { isValid: true };
}; 
