export const PAYMENT_MODE = {
  PAYMENT: "payment",
  DEFERRED: "deferred",
} as const;

export type PaymentMode = typeof PAYMENT_MODE[keyof typeof PAYMENT_MODE];

export const PAYMENT_CREATION_STATE = {
  IDLE: 'idle',
  CREATING: 'creating',
  COMPLETED: 'completed',
} as const;

export const PAYMENT_ERROR_MESSAGES = {
  NO_CHECKOUT_TOKEN: 'No checkout token available',
  INCOMPLETE_CONFIRMATION: 'Checkout confirmation data is incomplete',
} as const;

export const PAYMENT_CONTEXT = {
  DEFERRED_PAYMENT_CREATION: 'deferred_payment_creation',
} as const;
