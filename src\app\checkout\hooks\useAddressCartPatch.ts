import { useEffect, useRef, useCallback } from "react";
import { useWatch } from "react-hook-form";
import { useRouter } from "next/navigation";
import { debounce, isEqual } from "lodash";
import {toast} from "react-hot-toast";
import { useShippingAddressMutation, useBillingAddressMutation } from "@/lib/redux/slices/cart/cartApi";
import { CART_CLAIM_DEBOUNCE_TIME } from "../constants/cartConstants";
import { formatAddressValidationError } from "../utils/errorMessages";
import { showCheckoutErrorToast } from "../utils/toastUtils";
import { 
  SHIPPING_FIELDS, 
  BILLING_FIELDS, 
  ADDRESS_UPDATE_MAX_RETRIES,
  ADDRESS_UPDATE_RETRY_MESSAGE,
  ADDRESS_UPDATE_RETRY_ERROR_MESSAGE,
  ADDRESS_FIELD_NAMES,
  ADDRESS_TYPES,
  BILLING_ADDRESS_TYPES
} from "../constants/addressConstants";
import { CHECKOUT_ERROR_STATUS, CHECKOUT_ERROR_TYPES } from "../constants/checkoutConstants";
import { validateInternationalPhoneNumber } from "@/lib/utils/phoneValidation";
import { Country } from "country-state-city";


interface UseAddressCartPatchProps {
  control: any;
  type: typeof ADDRESS_TYPES[keyof typeof ADDRESS_TYPES];
  token: string | undefined;
  onAddressPatched?: () => void;
  onShippingAddressSuccess?: () => void;
  billingAddressType?: typeof BILLING_ADDRESS_TYPES[keyof typeof BILLING_ADDRESS_TYPES];
  onFieldError?: (fieldName: string, errorMessage: string) => void;
  onFieldErrorClear?: (fieldName: string) => void;
  isWhitelabel?: boolean;
  slug?: string;
  onResetAddressFields?: () => void;
}

function getAddressFromFields(fields: any, type: typeof ADDRESS_TYPES[keyof typeof ADDRESS_TYPES]) {
  const formatPhoneWithCountryCode = (phone: string, countryIsoCode: string): string => {
    if (!phone || !countryIsoCode) return phone;
    
    if (phone.startsWith('+')) {
      return phone;
    }
    
    const countries = Country.getAllCountries();
    const country = countries.find(c => c.isoCode === countryIsoCode);
    if (!country?.phonecode) {
      return phone;
    }
    
    const cleanPhone = phone.replace(/[^\d]/g, "");
    
    return `+${country.phonecode}${cleanPhone}`;
  };

  if (type === ADDRESS_TYPES.SHIPPING) {
    const address: any = {
      firstName: fields.firstName,
      lastName: fields.lastName,
      streetAddress_1: fields.addressLine1,
      city: fields.city,
      postalCode: fields.postalCode,
      country: fields.country,
      countryArea: fields.state,
      phone: formatPhoneWithCountryCode(fields.phone, fields.country),
      skipValidation: false,
    };

    if (fields.company && fields.company.toString().trim() !== "") {
      address.companyName = fields.company;
    }

    if (fields.addressLine2 && fields.addressLine2.toString().trim() !== "") {
      address.streetAddress_2 = fields.addressLine2;
    }

    return address;
  } else {
    const address: any = {
      firstName: fields.billingFirstName,
      lastName: fields.billingLastName,
      streetAddress_1: fields.billingAddressLine1,
      city: fields.billingCity,
      postalCode: fields.billingPostalCode,
      country: fields.billingCountry,
      countryArea: fields.billingState,
      phone: formatPhoneWithCountryCode(fields.billingPhone, fields.billingCountry),
      skipValidation: false,
    };

    if (fields.billingCompany && fields.billingCompany.toString().trim() !== "") {
      address.companyName = fields.billingCompany;
    }

    if (fields.billingAddressLine2 && fields.billingAddressLine2.toString().trim() !== "") {
      address.streetAddress_2 = fields.billingAddressLine2;
    }

    return address;
  }
}

function areAllFieldsFilled(fields: any, type: typeof ADDRESS_TYPES[keyof typeof ADDRESS_TYPES]) {
  const required = type === ADDRESS_TYPES.SHIPPING ? SHIPPING_FIELDS : BILLING_FIELDS;
  return required.every((key) => {
    if (key === "company" || key === "addressLine2" || key === "saveShippingAddress" ||
        key === "billingCompany" || key === "billingAddressLine2" || key === "saveBillingAddress") {
      return true;
    }
    
    if (key === "phone" || key === "billingPhone") {
      const phoneValue = fields[key];
      if (!phoneValue || !phoneValue.toString().trim()) {
        return false;
      }
      
      // Get the country for phone validation
      const countryIsoCode = type === ADDRESS_TYPES.SHIPPING ? fields.country : fields.billingCountry;
      if (!countryIsoCode) {
        return false; // Country is required for international phone validation
      }
      
      // Get the country's phone code
      const countries = Country.getAllCountries();
      const country = countries.find(c => c.isoCode === countryIsoCode);
      if (!country?.phonecode) {
        return false; // Unable to validate without phone code
      }
      
      const cleanPhone = phoneValue.toString().trim().replace(/[^\d]/g, "");
      const fullPhone = `+${country.phonecode}${cleanPhone}`;
      
      return validateInternationalPhoneNumber(fullPhone, country.phonecode);
    }
    
    return fields[key] && fields[key].toString().trim() !== "";
  });
}

export function useAddressCartPatch({ control, type, token, onAddressPatched, onShippingAddressSuccess, billingAddressType, onFieldError, onFieldErrorClear, isWhitelabel, slug, onResetAddressFields  }: UseAddressCartPatchProps) {
  const router = useRouter();
  const [patchShipping] = useShippingAddressMutation();
  const [patchBilling] = useBillingAddressMutation();
  const lastPatchedRef = useRef<any>({});
  const errorRef = useRef<any>(null);
  const lastCallbackTimeRef = useRef<number>(0);
  const isPatchingRef = useRef<boolean>(false);
  const lastSaveAddressRef = useRef<boolean | undefined>(undefined);
  const retryStatusRef = useRef<{ addressKey: string | null; attempts: number }>({ addressKey: null, attempts: 0 });

  const watchedFields = useWatch({
    control,
    name: type === ADDRESS_TYPES.SHIPPING ? SHIPPING_FIELDS : BILLING_FIELDS,
  });

  const fieldsObj = (type === ADDRESS_TYPES.SHIPPING
    ? SHIPPING_FIELDS
    : BILLING_FIELDS
  ).reduce((acc, key, idx) => {
    acc[key] = watchedFields[idx];
    return acc;
  }, {} as Record<string, any>);

  const currentSaveAddress = type === ADDRESS_TYPES.SHIPPING ? fieldsObj.saveShippingAddress : fieldsObj.saveBillingAddress;

  const debouncedCallback = useCallback(
    debounce(() => {
      const now = Date.now();
      const timeSinceLastCall = now - lastCallbackTimeRef.current;
      
      if (timeSinceLastCall > 1000) {
        lastCallbackTimeRef.current = now;
        if (onAddressPatched) {
          onAddressPatched();
        }
      }
    }, 100),
    [onAddressPatched, type]
  );

  const debouncedPatch = useCallback(
    debounce(async (fields) => {
      if (!token) return;
      
      if (isPatchingRef.current) {
        return;
      }
      
      const address = getAddressFromFields(fields, type);
      const saveAddress = type === ADDRESS_TYPES.SHIPPING ? fields.saveShippingAddress : fields.saveBillingAddress;
      const currentAddressKey = JSON.stringify(address);
      
      if (errorRef.current?.status === CHECKOUT_ERROR_STATUS.BAD_REQUEST && isEqual(address, errorRef.current.address)) {
        return;
      }

      if (retryStatusRef.current.addressKey === currentAddressKey && retryStatusRef.current.attempts >= ADDRESS_UPDATE_MAX_RETRIES) {
        console.warn(`Max retries (non-400) reached for address: ${currentAddressKey}. Skipping patch.`);
        showCheckoutErrorToast(ADDRESS_UPDATE_RETRY_MESSAGE);
        return;
      }

      const addressWithSave = { ...address, saveAddress: saveAddress || false };
      const lastPatchedWithSave = { ...lastPatchedRef.current, saveAddress: lastSaveAddressRef.current || false };

      if (lastPatchedRef.current && Object.keys(lastPatchedRef.current).length > 0 && isEqual(addressWithSave, lastPatchedWithSave)) {
        return;
      }
      
      isPatchingRef.current = true;
      
      try {
        if (type === ADDRESS_TYPES.SHIPPING) {
          await patchShipping({ token, shippingAddress: address, saveAddress: saveAddress || false }).unwrap();
          toast.dismiss();
          if (onShippingAddressSuccess) {
            onShippingAddressSuccess();
          }
          if (billingAddressType === BILLING_ADDRESS_TYPES.SAME) {
            await patchBilling({ token, billingAddress: address, saveAddress: saveAddress || false }).unwrap();
          }
        } else {
          await patchBilling({ token, billingAddress: address, saveAddress: saveAddress || false }).unwrap();
          toast.dismiss();
        }
        lastPatchedRef.current = address;
        lastSaveAddressRef.current = saveAddress || false;
        errorRef.current = null;
        
        if (onFieldErrorClear) {
          const phoneFieldName = type === ADDRESS_TYPES.SHIPPING ? "phone" : "billingPhone";
          onFieldErrorClear(phoneFieldName);
        }
        
        if (retryStatusRef.current.addressKey === currentAddressKey) {
          retryStatusRef.current.addressKey = null;
          retryStatusRef.current.attempts = 0;
        }
        
        debouncedCallback();
      } catch (error: any) {
        errorRef.current = {
          status: error?.status || error?.data?.status,
          address,
          timestamp: Date.now()
        };
        
        const errorStatus = error?.status || error?.data?.status;
        const errorType = error?.data?.type;

        if (errorStatus === CHECKOUT_ERROR_STATUS.BAD_REQUEST || errorType === CHECKOUT_ERROR_TYPES.VALIDATION_ERROR) {
          const phoneErrorPatterns = [
            'phone number is not valid',
            'invalid phone',
            'phone format',
            'telephone',
            'mobile number',
            'contact number'
          ];
          
          const isPhoneError = error?.data?.attr === "phone" || 
                              (error?.data?.detail && phoneErrorPatterns.some(pattern => 
                                error.data.detail.toLowerCase().includes(pattern.toLowerCase())
                              ));
          
          if (isPhoneError) {
            if (isWhitelabel && slug) {
              const phoneErrorMessage = error?.data?.detail || "Phone number is not valid.";
              const storedBasicInfo = JSON.parse(localStorage.getItem("whitelabel_basic_info") || "{}");

              showCheckoutErrorToast(phoneErrorMessage);

              localStorage.setItem("whitelabel_basic_info", JSON.stringify({
                ...storedBasicInfo,
                phone: ""
              }));
              
              if (onResetAddressFields) {
                onResetAddressFields();
              }
              
              setTimeout(() => {
                
                router.push(`/event/${slug}?oid=contact`);
              }, 2000);
              return;
            } else if (onFieldError) {
              const phoneFieldName = type === ADDRESS_TYPES.SHIPPING ? "phone" : "billingPhone";
              const phoneErrorMessage = error?.data?.detail || "Phone number is not valid.";
              onFieldError(phoneFieldName, phoneErrorMessage);
            } else {
              const phoneErrorMessage = error?.data?.detail || "Phone number is not valid.";
              showCheckoutErrorToast(phoneErrorMessage);
            }
          } else {
            const errorMessage = formatAddressValidationError(error, type);
            showCheckoutErrorToast(errorMessage);
          }

          if (retryStatusRef.current.addressKey === currentAddressKey) {
            retryStatusRef.current.addressKey = null;
            retryStatusRef.current.attempts = 0;
          }
        } else {
          if (retryStatusRef.current.addressKey === currentAddressKey) {
            retryStatusRef.current.attempts += 1;
          } else {
            retryStatusRef.current.addressKey = currentAddressKey;
            retryStatusRef.current.attempts = 1;
          }
          console.log(`Patch attempt ${retryStatusRef.current.attempts} (non-400 error: ${errorStatus}) failed for address: ${currentAddressKey}`);

          if (retryStatusRef.current.attempts >= ADDRESS_UPDATE_MAX_RETRIES) {
            showCheckoutErrorToast(ADDRESS_UPDATE_RETRY_ERROR_MESSAGE);
          }
        }
        
        if (type === ADDRESS_TYPES.SHIPPING && billingAddressType !== BILLING_ADDRESS_TYPES.SAME) {
          debouncedCallback();
        }
      } finally {
        isPatchingRef.current = false;
      }
    }, CART_CLAIM_DEBOUNCE_TIME),
    [patchShipping, patchBilling, token, type, debouncedCallback, billingAddressType]
  );

  useEffect(() => {
    const allFieldsFilled = areAllFieldsFilled(fieldsObj, type);
    const saveAddressChanged = lastSaveAddressRef.current !== undefined && lastSaveAddressRef.current !== currentSaveAddress;
    const hasPreviouslyPatchedAddress = lastPatchedRef.current && Object.keys(lastPatchedRef.current).length > 0;
    
    if (allFieldsFilled || (saveAddressChanged && hasPreviouslyPatchedAddress)) {
      debouncedPatch(fieldsObj);
    }
    
    return () => {
      debouncedPatch.cancel();
    };
  }, [fieldsObj, debouncedPatch, debouncedCallback, type, currentSaveAddress]);
} 
