import { useEffect, useState } from 'react';

declare global {
  interface Window {
    ApplePaySession?: {
      canMakePayments: () => boolean;
    };
  }
}

export const useApplePaySupport = () => {
  const [isApplePaySupported, setIsApplePaySupported] = useState(false);

  useEffect(() => {
    const checkApplePaySupport = () => {
      const isSecureContext = window.isSecureContext;
      
      if (!isSecureContext) {
        setIsApplePaySupported(false);
        return;
      }

      if (window.ApplePaySession) {
        try {
          const canMakePayments = window.ApplePaySession.canMakePayments();
          setIsApplePaySupported(canMakePayments);
        } catch (error) {
          console.warn('Apple Pay check failed:', error);
          setIsApplePaySupported(false);
        }
      } else {
        setIsApplePaySupported(false);
      }
    };

    checkApplePaySupport();
  }, []);

  return isApplePaySupported;
}; 