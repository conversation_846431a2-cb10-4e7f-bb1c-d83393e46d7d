import { useState, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Country, State, ICountry, IState } from "country-state-city";
import {
  useClaimCartMutation,
  useCreateOrderMutation,
  useCreatePaymentMutation,
  useGetCartQuery,
} from "@/lib/redux/slices/cart/cartApi";
import { useSessionData } from "@/lib/hooks/useSession";
import { isZero } from "@/lib/utils/numberUtil";
import { useRouter } from "next/navigation";
import { DIRECT_ORDER_CREATION_SOURCE } from "@/lib/utils/constants";
import { extractTrackingData } from "@/lib/utils/extractTrackingData";
import { checkIfCartHasApprovalRequiredTickets } from "@/app/cart/utils/cartUtils";
import { CHECKOUT_ERROR_MESSAGES } from "@/app/checkout/utils/errorMessages";
import { useUserMessageReporting } from "./useUserMessageReporting";
import type { CartData } from "@/app/cart/types";
import { showCheckoutErrorToast } from "../utils/toastUtils";
import { 
  APPLE_PAY_EMAIL_REQUIRED,
  INVALID_EMAIL_ADDRESS,
  INVALID_APPLE_PAY_EMAIL_FORMAT,
  FILL_ALL_REQUIRED_BILLING_ADDRESS_FIELDS,
  NAME_ONLY_LETTERS_SPACES,
  PAYMENT_CANCELED_REDIRECT_TO_CART,
  PAYMENT_FAILED,
  EMAIL_REGEX,
  validateEmailWithTLD
} from "../constants/formMessages";
import { validateInternationalPhoneNumber } from "@/lib/utils/phoneValidation";
import { STORAGE_KEYS } from "@/lib/constants/storage";
import { 
  CHECKOUT_ERROR_CODES,
  CHECKOUT_PAYMENT_STATUS,
  CHECKOUT_FORM_TYPES,
  CHECKOUT_ERROR_CONTEXTS,
  CHECKOUT_DEFAULT_COUNTRY,
  CHECKOUT_STORAGE_KEYS,
  CHECKOUT_TRANSACTION_PREFIXES
} from "../constants/checkoutConstants";

const checkoutFormSchema = z.object({
  email: z.string().email({message: INVALID_EMAIL_ADDRESS}).refine((email) => {
    const validation = validateEmailWithTLD(email);
    return validation.isValid;
  }, (email) => {
    const validation = validateEmailWithTLD(email);
    return { 
      message: validation.error || INVALID_EMAIL_ADDRESS,
      suggestion: validation.suggestion 
    };
  }),
  applePayEmail: z.preprocess(
    (val) => (val === "" ? undefined : val),
    z.string().refine((email) => {
      if (!email) return true; // Allow empty for optional field
      const validation = validateEmailWithTLD(email);
      return validation.isValid;
    }, (email) => {
      const validation = validateEmailWithTLD(email);
      return { 
        message: validation.error || INVALID_APPLE_PAY_EMAIL_FORMAT,
        suggestion: validation.suggestion 
      };
    }).optional()
  ),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  addressLine1: z.string().optional(),
  addressLine2: z.string().optional(),
  company: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  phone: z.string().optional(),
  billingCountry: z.string().optional(),
  billingFirstName: z.string().optional(),
  billingLastName: z.string().optional(),
  billingAddressLine1: z.string().optional(),
  billingAddressLine2: z.string().optional(),
  billingCompany: z.string().optional(),
  billingCity: z.string().optional(),
  billingState: z.string().optional(),
  billingPostalCode: z.string().optional(),
  billingPhone: z.string().optional(),
  newsletter: z.boolean().optional(),
  smsUpdates: z.boolean().optional(),
  shippingMethod: z.string().optional(),
  saveBillingAddress: z.boolean().optional(),
  saveShippingAddress: z.boolean().optional(),
});

export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;

export function useCheckoutForm(stripe: any, elements: any, isWhitelabel: boolean, slug?: string, isPaymentLinkMethod?: boolean) {
  const router = useRouter();
  const [createOrder] = useCreateOrderMutation();
  const [createPayment] = useCreatePaymentMutation();
  const [billingAddressType, setBillingAddressType] = useState<
    "same" | "different"
  >("same");
  const { data: sessionData } = useSessionData();
  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);

  const { data: cartData, isFetching: isCartLoading } = useGetCartQuery(undefined, {
    skip: !cartToken && !sessionData?.user,
  });

  const [claimCart] = useClaimCartMutation();
  
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { reportUserMessage } = useUserMessageReporting();

  const firstName = sessionData?.user?.firstName ||
  sessionData?.user?.name?.split(" ")[0] ||
  "";

  const lastName = sessionData?.user?.lastName ||
  sessionData?.user?.name?.split(" ").slice(1).join(" ") ||
  "";

  // Determine initial values from sessionStorage if available
  const getStoredValues = () => {
    if (typeof window === "undefined") return null;
    try {
      const stored = sessionStorage.getItem(STORAGE_KEYS.CHECKOUT_FORM_DATA);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (err) {
      console.error("Failed to parse stored checkout form data", err);
    }
    return null;
  };

  const storedDefaults = getStoredValues();

  const form = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutFormSchema),
    defaultValues: {
      email: sessionData?.user?.email || storedDefaults?.email || "",
      applePayEmail: sessionData?.user?.email || storedDefaults?.applePayEmail || "",
      firstName: storedDefaults?.firstName || firstName,
      lastName: storedDefaults?.lastName || lastName,
      addressLine1: storedDefaults?.addressLine1 || "",
      addressLine2: storedDefaults?.addressLine2 || "",
      city: storedDefaults?.city || "",
      state: storedDefaults?.state || "",
      country: storedDefaults?.country || "US",
      postalCode: storedDefaults?.postalCode || "",
      phone: storedDefaults?.phone || "",
      billingCountry: storedDefaults?.billingCountry || "US",
      billingFirstName: storedDefaults?.billingFirstName || firstName,
      billingLastName: storedDefaults?.billingLastName || lastName,
      billingAddressLine1: storedDefaults?.billingAddressLine1 || "",
      billingAddressLine2: storedDefaults?.billingAddressLine2 || "",
      billingCity: storedDefaults?.billingCity || "",
      billingState: storedDefaults?.billingState || "",
      billingPostalCode: storedDefaults?.billingPostalCode || "",
      newsletter: storedDefaults?.newsletter ?? true,
      company: storedDefaults?.company || "",
      billingCompany: storedDefaults?.billingCompany || "",
      billingPhone: storedDefaults?.billingPhone || "",
      smsUpdates: storedDefaults?.smsUpdates ?? true,
      shippingMethod: storedDefaults?.shippingMethod || "",
      saveBillingAddress: storedDefaults?.saveBillingAddress ?? false,
      saveShippingAddress: storedDefaults?.saveShippingAddress ?? false,
    },
  });

  useEffect(() => {
    const subscription = form.watch((value) => {
      try {
        sessionStorage.setItem(STORAGE_KEYS.CHECKOUT_FORM_DATA, JSON.stringify(value));
      } catch (err) {
        console.error("Failed to store checkout form data", err);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const watchCountry = form.watch("country");
  const watchBillingCountry = form.watch("billingCountry");

  const isShippingRequired = (cartData as CartData)?.checkout?.isShippingRequired;

  const handlePaymentError = (error: any) => {
    console.error("Payment error:", error);
    let userMessage = CHECKOUT_ERROR_MESSAGES.PAYMENT_FAILED;
    
    if (
      error?.code === CHECKOUT_ERROR_CODES.PAYMENT_INTENT_UNEXPECTED_STATE || 
      (error?.payment_intent?.status === CHECKOUT_PAYMENT_STATUS.CANCELED) ||
      (error?.message && error.message.includes("canceled"))
    ) {
      userMessage = CHECKOUT_ERROR_MESSAGES.PAYMENT_CANCELED;
      reportUserMessage(userMessage, { 
        errorCode: error?.code || 'unknown',
        paymentStatus: error?.payment_intent?.status || 'unknown'
      });
      
      showCheckoutErrorToast(userMessage);
      setTimeout(() => {
        router.replace("/cart");
      }, 2000);
    } else {
      userMessage = error?.message || CHECKOUT_ERROR_MESSAGES.PAYMENT_FAILED;
      reportUserMessage(userMessage, { 
        errorCode: error?.code || 'unknown'
      });
      
      showCheckoutErrorToast(error?.message || PAYMENT_FAILED);
    }
  };

  const storeTrackingData = (transactionIdPrefix: string) => {
    try {
      const trackingData = extractTrackingData(cartData?.checkout);
      if (trackingData) {
        const transactionId = `${transactionIdPrefix}-${Date.now()}`;
        
        sessionStorage.setItem(CHECKOUT_STORAGE_KEYS.PIXEL_PURCHASE_DATA, JSON.stringify({
          event: trackingData.event,
          tickets: trackingData.tickets,
          transactionId
        }));
      }
    } catch (error) {
      console.error('Failed to store tracking data:', error);
    }
  };

  const cleanupSessionStorage = () => {
    try {
      sessionStorage.removeItem(STORAGE_KEYS.CHECKOUT_FORM_DATA);
    } catch (error) {
      console.error('Failed to cleanup sessionStorage:', error);
    }
  };

  const resetAddressFields = () => {
    const defaultAddressValues = {
      firstName: firstName,
      lastName: lastName,
      addressLine1: "",
      addressLine2: "",
      company: "",
      city: "",
      state: "",
      country: "US",
      postalCode: "",
      phone: "",
      saveShippingAddress: false,
      billingFirstName: firstName,
      billingLastName: lastName,
      billingAddressLine1: "",
      billingAddressLine2: "",
      billingCompany: "",
      billingCity: "",
      billingState: "",
      billingCountry: "US",
      billingPostalCode: "",
      billingPhone: "",
      saveBillingAddress: false,
    };

    form.reset(defaultAddressValues);
  };
  
  const onSubmit = async (data: CheckoutFormData) => {
    const validatePhoneWithCountryCode = (phone: string, countryIsoCode: string): boolean => {
      if (!phone || !countryIsoCode) return false;
      
      const cleanPhone = phone.replace(/[^\d]/g, "");
      if (cleanPhone.length < 7 || cleanPhone.length > 15) {
        return false;
      }
      
      const country = Country.getAllCountries().find(c => c.isoCode === countryIsoCode);
      if (!country?.phonecode) {
        return false;
      }
      
      const fullPhone = `+${country.phonecode}${cleanPhone}`;
      return validateInternationalPhoneNumber(fullPhone, country.phonecode);
    };

    if (isShippingRequired) {
      if (
        !data.addressLine1 ||
        !data.city ||
        !data.state ||
        !data.country ||
        !data.postalCode ||
        !data.firstName ||
        !data.lastName ||
        !data.phone
      ) {
        const userMessage = CHECKOUT_ERROR_MESSAGES.REQUIRED_SHIPPING_FIELDS;
        reportUserMessage(userMessage, { formType: CHECKOUT_FORM_TYPES.SHIPPING });
        showCheckoutErrorToast(userMessage);
        return;
      }
      if (data.phone) {
        if (!validatePhoneWithCountryCode(data.phone, data.country)) {
          const userMessage = "Please enter a valid phone number for shipping";
          reportUserMessage(userMessage, { formType: CHECKOUT_FORM_TYPES.SHIPPING });
          showCheckoutErrorToast(userMessage);
          return;
        }
      }
    } else {
      if (
        !data.billingAddressLine1 ||
        !data.billingCity ||
        !data.billingState ||
        !data.billingCountry ||
        !data.billingPostalCode ||
        !data.billingFirstName ||
        !data.billingLastName ||
        !data.billingPhone
      ) {  
        const userMessage = CHECKOUT_ERROR_MESSAGES.REQUIRED_BILLING_FIELDS;
        reportUserMessage(userMessage, { formType: CHECKOUT_FORM_TYPES.BILLING });
        showCheckoutErrorToast(userMessage);
        return;
      }
      const trimmedFirstName = (data.billingFirstName || "").trim();
      const trimmedLastName = (data.billingLastName || "").trim();
      
      if (!/^[a-zA-Z0-9\s'-]+$/.test(trimmedFirstName) || !/^[a-zA-Z0-9\s'-]+$/.test(trimmedLastName)) {
        reportUserMessage(CHECKOUT_ERROR_MESSAGES.INVALID_NAME_FORMAT, { formType: CHECKOUT_FORM_TYPES.BILLING, firstName: trimmedFirstName, lastName: trimmedLastName });
        showCheckoutErrorToast(CHECKOUT_ERROR_MESSAGES.INVALID_NAME_FORMAT);
        return;
      }
      if (data.billingPhone) {
        if (!validatePhoneWithCountryCode(data.billingPhone, data.billingCountry)) {
          const userMessage = "Please enter a valid phone number for billing";
          reportUserMessage(userMessage, { formType: CHECKOUT_FORM_TYPES.BILLING });
          showCheckoutErrorToast(userMessage);
          return;
        }
      }
    }

    if (isProcessing) return;

    
    setIsProcessing(true);

    try {
      const userData = {
        email: data.email,
        firstName: data.billingFirstName || data.firstName || "",
        lastName: data.billingLastName || data.lastName || "",
        newsletter: data.newsletter,
        smsUpdates: data.smsUpdates,
      };
      
      if ((isZero((cartData?.checkout as any)?.total as number) || isPaymentLinkMethod) && (cartData?.checkout as any)?.token as string) {
        storeTrackingData(CHECKOUT_TRANSACTION_PREFIXES.FREE);
        
        await claimCart(userData);

        if(isPaymentLinkMethod && !isZero((cartData?.checkout as any)?.total as number)) {
          await createPayment( {
            token: (cartData?.checkout as any)?.token as string,
            metadata: {
              checkoutToken: (cartData?.checkout as any)?.token as string,
            }
          });
        }

        await createOrder({
          checkoutToken: (cartData?.checkout as any)?.token as string,
          source: DIRECT_ORDER_CREATION_SOURCE,
        });
        localStorage.removeItem(STORAGE_KEYS.CART_TOKEN);
        cleanupSessionStorage();
        if (isWhitelabel) {
          router.replace(`/e/${slug}?oid=success&checkoutToken=${(cartData?.checkout as any)?.token}`);
        } else {
          router.replace(`/order/success/?checkoutToken=${(cartData?.checkout as any)?.token}`);
        }
        return;
      }

      const billingDetails = {
        name: `${data.billingFirstName || data.firstName} ${data.billingLastName || data.lastName}`,
        email: data.email,
        address: {
          line1: data?.billingAddressLine1 || data?.addressLine1,
          line2: data?.billingAddressLine2 || data?.addressLine2,
          city: data?.billingCity || data?.city,
          state: data?.billingState || data?.state,
          country: data?.billingCountry || data?.country,
          postal_code: data?.billingPostalCode || data?.postalCode,
        },
        phone: data?.billingPhone || data?.phone,
      };

      storeTrackingData(CHECKOUT_TRANSACTION_PREFIXES.ORDER);

      await claimCart(userData);

      const clientSecret = localStorage.getItem(STORAGE_KEYS.CLIENT_SECRET);

      if (!clientSecret) {
        throw new Error('No client secret available for payment confirmation');
      }

      const { error: submitError } = await elements.submit();
      if (submitError) {
        throw submitError;
      }

      const { error } = await stripe.confirmPayment({
        elements,
        clientSecret,
        confirmParams: {
          return_url: isWhitelabel ? `${window.location.origin}/e/${slug}?oid=success&checkoutToken=${(cartData?.checkout as any)?.token}` : `${window.location.origin}/order/success/?checkoutToken=${(cartData?.checkout as any)?.token}`,
          payment_method_data: {
            billing_details: billingDetails,
          },
        },
      });

      if (error) {
        handlePaymentError(error);
      } else {
        cleanupSessionStorage();
        localStorage.removeItem(STORAGE_KEYS.CART_TOKEN);
      }
    } catch (error) {
      handlePaymentError(error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExpressCheckoutClick = async (event: any) => {
    const formData = form.getValues();
    
    if (!formData.applePayEmail) {
      showCheckoutErrorToast(APPLE_PAY_EMAIL_REQUIRED);
      return;
    }

    const emailValidation = validateEmailWithTLD(formData.applePayEmail);
    if (!emailValidation.isValid) {
      showCheckoutErrorToast(emailValidation.error || INVALID_EMAIL_ADDRESS);
      return;
    }

    const options = {
      emailRequired: true,
      phoneNumberRequired: true,
      billingAddressRequired: true,
      requiredBillingContactFields: ['name', 'email', 'phone'],
      ...(isShippingRequired && {
        shippingAddressRequired: true,
        shippingRates: [
          {
            id: 'standard-shipping',
            displayName: 'Standard shipping',
          }
        ]
      })
    };
    event.resolve(options);
  };

  const handleExpressCheckout = async (event) => {
    if (!stripe || !elements) {
      const userMessage = CHECKOUT_ERROR_MESSAGES.PAYMENT_PROCESSING_NOT_READY;
      reportUserMessage(userMessage, { paymentMethod: CHECKOUT_ERROR_CONTEXTS.PAYMENT_METHOD });
      showCheckoutErrorToast(userMessage);
      return;
    }

    const formData = form.getValues();
    if (!formData.applePayEmail) {
      showCheckoutErrorToast(APPLE_PAY_EMAIL_REQUIRED);
      return;
    }

    const emailValidation = validateEmailWithTLD(formData.applePayEmail);
    if (!emailValidation.isValid) {
      showCheckoutErrorToast(emailValidation.error || INVALID_EMAIL_ADDRESS);
      return;
    }

    setIsProcessing(true);

    try {
      const { shippingAddress, billingDetails } = event;

      const email = formData.applePayEmail || billingDetails?.email || '';
      const fullName = billingDetails?.name || '';
      const nameParts = fullName.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      const userData = {
        email,
        firstName,
        lastName,
        billingAddress: {
          line1: billingDetails?.address?.line1 || '',
          line2: billingDetails?.address?.line2 || '',
          city: billingDetails?.address?.city || '',
          state: billingDetails?.address?.state || '',
          country: billingDetails?.address?.country || '',
          postalCode: billingDetails?.address?.postal_code || '',
          phone: billingDetails?.phone || '',
        },
        ...(isShippingRequired && shippingAddress && {
          shippingAddress: {
            line1: shippingAddress.address?.line1 || billingDetails?.address?.line1 ||'',
            line2: shippingAddress.address?.line2 || billingDetails?.address?.line2 ||'',
            city: shippingAddress.address?.city || billingDetails?.address?.city ||'',
            state: shippingAddress.address?.state || billingDetails?.address?.state ||'',
            country: shippingAddress.address?.country || billingDetails?.address?.country ||'',
            postalCode: shippingAddress.address?.postal_code || billingDetails?.address?.postal_code ||'',
            phone: shippingAddress.phone || billingDetails?.phone ||'',
          },
        }),
      };

        await claimCart(userData);

      storeTrackingData(CHECKOUT_TRANSACTION_PREFIXES.EXPRESS);

      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/order/success/`,
        },
      });

      if (error) {
        handlePaymentError(error);
      }
    } catch (error) {
      handlePaymentError(error);
    } finally {
      setIsProcessing(false);
    }
  };

  const shippingStates = useMemo(() => {
    if (watchCountry && countries.length > 0) {
      const selectedCountry = countries.find(
        (c) => c.isoCode === watchCountry
      );
      return selectedCountry 
        ? State.getStatesOfCountry(selectedCountry.isoCode)
        : [];
    }
    return [];
  }, [watchCountry, countries]);

  const billingStates = useMemo(() => {
    if (watchBillingCountry && countries.length > 0) {
      const selectedCountry = countries.find(
        (c) => c.isoCode === watchBillingCountry
      );
      return selectedCountry 
        ? State.getStatesOfCountry(selectedCountry.isoCode)
        : [];
    }
    return [];
  }, [watchBillingCountry, countries]);

  useEffect(() => {
    setCountries(Country.getAllCountries());
  }, []);

  useEffect(() => {
    const formErrors = form.formState.errors;
    Object.entries(formErrors).forEach(([fieldName, error]) => {
      if (error?.message) {
        reportUserMessage(error.message as string, { 
          errorType: 'form_validation',
          field: fieldName
        });
        showCheckoutErrorToast(error.message);
      }
    });
  }, [form.formState.errors, reportUserMessage]);


  useEffect(() => {
    try {
      if (cartData?.checkout?.lines) {
        const hasApprovalRequiredTickets = checkIfCartHasApprovalRequiredTickets(cartData?.checkout?.lines || []);
        localStorage.setItem(CHECKOUT_STORAGE_KEYS.IS_APPROVAL_REQUIRED_CHECKOUT, hasApprovalRequiredTickets.toString());
      }
    } catch (err) {
      console.error(err);
    }
  }, [cartData]);

  return {
    form,
    billingAddressType,
    setBillingAddressType,
    isProcessing,
    isCartLoading,
    cartData,
    countries,
    shippingStates,
    billingStates,
    isShippingRequired,
    sessionData,
    onSubmit,
    handleExpressCheckout,
    handleExpressCheckoutClick,
    resetAddressFields
  };
}
