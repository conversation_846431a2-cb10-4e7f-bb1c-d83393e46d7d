import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { showCheckoutErrorToast } from '../utils/toastUtils';
import { getErrorMessage } from '@/lib/utils/errorUtils';
import {
  useCheckoutCompleteMutation,
  useCreatePaymentMutation,
} from '@/lib/redux/slices/cart/cartApi';
import { useUserMessageReporting } from './useUserMessageReporting';
import { STORAGE_KEYS } from '@/lib/constants/storage';

// Update interface to match the CartData structure
interface CartData {
  checkout?: {
    token?: string;
    lines?: Array<any>;
    total?: string;
    currency?: string;
    email?: string;
    platform_fee?: string;
    subtotal?: string;
  };
  problems?: Array<any>;
}

interface CheckoutProcessProps {
  cartData: CartData | undefined;
  sessionUser: any | null;
  onSuccess?: (clientSecret: string, connectId: string) => void;
}

export const useCheckoutProcess = ({
  cartData,
  sessionUser,
  onSuccess,
}: CheckoutProcessProps) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { reportUserMessage } = useUserMessageReporting();

  const [createPayment] = useCreatePaymentMutation();
  const [checkoutComplete] = useCheckoutCompleteMutation();

  const initiateCheckout = async () => {
    setIsProcessing(true);
    setError(null);
    
    try {
      if (!cartData?.checkout?.token) {
        throw new Error('No checkout token available');
      }
      
      const checkoutToken = cartData.checkout.token;
      
      // Create payment payload
      const payload = {
        token: checkoutToken,
        metadata: {
          checkoutToken,
        },
      };

      // Complete checkout payload
      const checkoutCompletePayload = {
        checkoutToken,
      };

      // Execute API calls
      await createPayment(payload).unwrap();
      const checkoutResponse = await checkoutComplete(checkoutCompletePayload).unwrap();

      const newClientSecret = checkoutResponse?.confirmationData?.clientSecret;
      const newConnectId = checkoutResponse?.confirmationData?.connectId;

      if (checkoutResponse.confirmationNeeded && 
          checkoutResponse.confirmationData && 
          newClientSecret && 
          newConnectId) {
        
        // Store checkout token
        localStorage.setItem(STORAGE_KEYS.CART_TOKEN, checkoutToken);
        
        // Call success callback if provided
        if (onSuccess) {
          onSuccess(newClientSecret, newConnectId);
        }
        
        return {
          success: true,
          clientSecret: newClientSecret,
          connectId: newConnectId,
        };
      } else {
        throw new Error('Checkout confirmation data is incomplete');
      }
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      
      showCheckoutErrorToast(errorMessage);
      
      reportUserMessage(errorMessage, {
        context: 'checkout_process',
      });
      console.error(errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    initiateCheckout,
    isProcessing,
    error,
  };
}; 