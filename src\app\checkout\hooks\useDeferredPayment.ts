import { useState, useCallback, useRef } from 'react';
import { handleApiError } from '@/lib/utils/errorUtils';
import {
  useCheckoutCompleteMutation,
  useCreatePaymentMutation,
} from '@/lib/redux/slices/cart/cartApi';
import { useUserMessageReporting } from './useUserMessageReporting';
import { PAYMENT_OPTIONS, STORAGE_KEYS } from '@/lib/constants/storage';
import { 
  PAYMENT_CREATION_STATE, 
  PAYMENT_ERROR_MESSAGES,
  PAYMENT_CONTEXT 
} from '../constants/paymentConstants';

interface CartData {
  checkout?: {
    token?: string;
    lines?: Array<any>;
    total?: string;
    currency?: string;
    email?: string;
    platform_fee?: string;
    subtotal?: string;
  };
  problems?: Array<any>;
}

interface DeferredPaymentProps {
  cartData: CartData | undefined;
  sessionUser: any | null;
  onPaymentIntentCreated?: (
    clientSecret: string,
    connectId: string,
    isCollaboratorCheckout: boolean
  ) => void;
}

export const useDeferredPayment = ({
  cartData,
  sessionUser,
  onPaymentIntentCreated,
}: DeferredPaymentProps) => {
  const [isCreatingPayment, setIsCreatingPayment] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [connectId, setConnectId] = useState<string | null>(null);
  const { reportUserMessage } = useUserMessageReporting();
  
  // Use a ref to track if payment creation is in progress or completed
  const paymentCreationStateRef = useRef<typeof PAYMENT_CREATION_STATE[keyof typeof PAYMENT_CREATION_STATE]>(PAYMENT_CREATION_STATE.IDLE);
  const creationPromiseRef = useRef<Promise<any> | null>(null);

  const [createPayment] = useCreatePaymentMutation();
  const [checkoutComplete] = useCheckoutCompleteMutation();

  const approvalRequiredLines = cartData?.checkout?.lines?.filter(
    (line) => line?.eventDetails?.ticketApprovalRequired
  ) || [];

  const isPaymentLinkMethod = approvalRequiredLines?.some(
    (line) =>
      line?.eventDetails?.paymentOption === PAYMENT_OPTIONS?.PAYMENT_LINK
  );

  const createPaymentIntent = useCallback(async () => {
    const callStack = new Error().stack;
    
    // If already completed, return existing values
    if (paymentCreationStateRef.current === PAYMENT_CREATION_STATE.COMPLETED && clientSecret && connectId) {
      return {
        success: true,
        clientSecret,
        connectId,
      };
    }

    // If currently creating, wait for the existing promise
    if (paymentCreationStateRef.current === PAYMENT_CREATION_STATE.CREATING && creationPromiseRef.current) {
      return await creationPromiseRef.current;
    }

    // Start new payment creation
    paymentCreationStateRef.current = PAYMENT_CREATION_STATE.CREATING;
    setIsCreatingPayment(true);
    setError(null);
    
    const creationPromise = (async () => {
      try {
        if (!cartData?.checkout?.token) {
          throw new Error(PAYMENT_ERROR_MESSAGES.NO_CHECKOUT_TOKEN);
        }
        
        const checkoutToken = cartData.checkout.token;
        
        // Create payment payload
        const payload = {
          token: checkoutToken,
          metadata: {
            checkoutToken,
          },
          ...(isPaymentLinkMethod && { storePaymentMethod: "off_session" }),
        };

        // Complete checkout payload
        const checkoutCompletePayload = {
          checkoutToken,
        };

        // Execute API calls
        await createPayment(payload).unwrap();

        const checkoutResponse = await checkoutComplete(checkoutCompletePayload).unwrap();

        const newClientSecret = checkoutResponse?.confirmationData?.clientSecret;
        const newConnectId = checkoutResponse?.confirmationData?.connectId;
        const isCollaboratorCheckout = checkoutResponse?.confirmationData?.isCollaboratorCheckout;

        if (checkoutResponse.confirmationNeeded && 
            checkoutResponse.confirmationData && 
            newClientSecret && 
            newConnectId) {
          localStorage.setItem(STORAGE_KEYS.CART_TOKEN, checkoutToken);
          localStorage.setItem(STORAGE_KEYS.CLIENT_SECRET, newClientSecret);
          localStorage.setItem(STORAGE_KEYS.CONNECT_ID, newConnectId);
          localStorage.setItem(STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT, isCollaboratorCheckout);
          
          // Update state
          setClientSecret(newClientSecret);
          setConnectId(newConnectId);
          paymentCreationStateRef.current = PAYMENT_CREATION_STATE.COMPLETED;
          
          if (onPaymentIntentCreated) {
            onPaymentIntentCreated(newClientSecret, newConnectId, isCollaboratorCheckout);
          }
          
          return {
            success: true,
            clientSecret: newClientSecret,
            connectId: newConnectId,
          };
        } else {
          throw new Error(PAYMENT_ERROR_MESSAGES.INCOMPLETE_CONFIRMATION);
        }
      } catch (err) {
        handleApiError(err, PAYMENT_ERROR_MESSAGES.INCOMPLETE_CONFIRMATION);
        const errorMessage = err?.data?.detail || err?.message || PAYMENT_ERROR_MESSAGES.INCOMPLETE_CONFIRMATION;
        setError(errorMessage);
        paymentCreationStateRef.current = PAYMENT_CREATION_STATE.IDLE; // Reset state on error
        reportUserMessage(errorMessage, {
          context: PAYMENT_CONTEXT.DEFERRED_PAYMENT_CREATION,
        });
        return {
          success: false,
          error: errorMessage,
        };
      } finally {
        setIsCreatingPayment(false);
        creationPromiseRef.current = null;
      }
    })();

    creationPromiseRef.current = creationPromise;
    return await creationPromise;
  }, [
    cartData?.checkout?.token,
    createPayment,
    checkoutComplete,
    onPaymentIntentCreated,
    reportUserMessage,
    clientSecret,
    connectId,
  ]);

  const resetPaymentIntent = useCallback(() => {
    setClientSecret(null);
    setConnectId(null);
    setError(null);
    paymentCreationStateRef.current = PAYMENT_CREATION_STATE.IDLE;
    creationPromiseRef.current = null;
  }, []);

  return {
    createPaymentIntent,
    resetPaymentIntent,
    isCreatingPayment,
    error,
    clientSecret,
    connectId,
    hasPaymentIntent: !!clientSecret,
  };
}; 
