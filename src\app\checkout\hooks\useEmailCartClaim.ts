import { useClaimCartMutation } from "@/lib/redux/slices/cart/cartApi";
import { useEffect, useState, useCallback } from "react";
import { useWatch } from "react-hook-form";
import { debounce } from "lodash";
import { CART_CLAIM_ERROR, CART_CLAIM_DEBOUNCE_TIME, validateEmailWithTLD } from "../constants/cartConstants";
import { useSessionData } from "@/lib/hooks/useSession";

interface UseEmailCartClaimProps {
  control: any;
  initialEmail?: string;
  field?: string;
}

export function useEmailCartClaim({ 
  control, 
  initialEmail, 
  field = "email" 
}: UseEmailCartClaimProps) {
  const [claimCart] = useClaimCartMutation();
  const [hasClaimedCart, setHasClaimedCart] = useState(false);
  const [claimedEmail, setClaimedEmail] = useState<string | null>(null);

  const {data} = useSessionData();
  
  const email = useWatch({
    control,
    name: field,
    defaultValue: initialEmail || "",
  });
  
  useEffect(() => {
    if (claimedEmail && email !== claimedEmail) {
      setHasClaimedCart(false);
    }
  }, [email, claimedEmail]);

  const debouncedClaimCart = useCallback(
    debounce(async (emailValue: string) => {
      const emailValidation = validateEmailWithTLD(emailValue);
      const isUserLoggedIn = !!data?.user;
      
      if (emailValidation.isValid && (!hasClaimedCart || emailValue !== claimedEmail) && !isUserLoggedIn) {
        try {
          await claimCart({
            email: emailValue,
          });
          setHasClaimedCart(true);
          setClaimedEmail(emailValue);
        } catch (error) {
          console.error(CART_CLAIM_ERROR, error);
        }
      }
    }, CART_CLAIM_DEBOUNCE_TIME),
    [claimCart, hasClaimedCart, claimedEmail, data?.user]
  );
  
  useEffect(() => {
    if (email) {
      debouncedClaimCart(email);
    }
    
    return () => {
      debouncedClaimCart.cancel();
    };
  }, [email, debouncedClaimCart]);

  return {
    hasClaimedCart,
    claimedEmail,
    resetClaimedStatus: () => {
      setHasClaimedCart(false);
      setClaimedEmail(null);
    },
  };
} 
