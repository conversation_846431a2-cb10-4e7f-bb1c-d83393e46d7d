import { useState } from "react";

/**
 * Custom hook for safely extracting and processing order data with comprehensive fallbacks
 * 
 * This hook provides utilities to handle various API response formats and data structures
 * without causing crashes. It includes safe extraction functions for all order-related data.
 */
export const useOrderDataExtraction = () => {
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");

  // Safe data extraction utilities with comprehensive fallbacks
  const safeExtractAmount = (moneyField: any): string => {
    if (!moneyField) return "0";
    if (typeof moneyField === "string") {
      const parsed = parseFloat(moneyField);
      return isNaN(parsed) ? "0" : String(parsed);
    }
    if (typeof moneyField === "number") {
      return String(moneyField);
    }
    if (typeof moneyField === "object" && moneyField.amount) {
      const amount = String(moneyField.amount);
      const parsed = parseFloat(amount);
      return isNaN(parsed) ? "0" : amount;
    }
    return "0";
  };

  const safeExtractCurrency = (moneyField: any): string => {
    if (!moneyField) return "USD";
    if (typeof moneyField === "string") {
      // Validate currency format (basic check)
      const currency = moneyField.toUpperCase().trim();
      if (currency.length === 3 && /^[A-Z]{3}$/.test(currency)) {
        return currency;
      }
      return "USD";
    }
    if (typeof moneyField === "object" && moneyField.currency) {
      const currency = String(moneyField.currency).toUpperCase().trim();
      if (currency.length === 3 && /^[A-Z]{3}$/.test(currency)) {
        return currency;
      }
      return "USD";
    }
    return "USD";
  };

  const safeExtractLines = (orderData: any): any[] => {
    if (!orderData) return [];
    if (Array.isArray(orderData.lines)) return orderData.lines;
    if (Array.isArray(orderData.lineItems)) return orderData.lineItems;
    if (Array.isArray(orderData.items)) return orderData.items;
    if (Array.isArray(orderData.products)) return orderData.products;
    if (Array.isArray(orderData.tickets)) return orderData.tickets;
    return [];
  };

  const safeExtractOrganization = (orderData: any): any => {
    if (!orderData) return { slug: "" };
    if (orderData.organization && typeof orderData.organization === "object") {
      return orderData.organization;
    }
    if (orderData.org && typeof orderData.org === "object") {
      return orderData.org;
    }
    if (orderData.organizationId) {
      return { id: orderData.organizationId, slug: "" };
    }
    return { slug: "" };
  };

  const safeExtractEvent = (lineItem: any): any => {
    if (!lineItem) return {};
    if (lineItem.event && typeof lineItem.event === "object") {
      return lineItem.event;
    }
    if (lineItem.eventDetails && typeof lineItem.eventDetails === "object") {
      return lineItem.eventDetails;
    }
    if (lineItem.eventInfo && typeof lineItem.eventInfo === "object") {
      return lineItem.eventInfo;
    }
    if (lineItem.eventData && typeof lineItem.eventData === "object") {
      return lineItem.eventData;
    }
    return {};
  };

  const safeExtractFormData = (lineItem: any): any => {
    if (!lineItem) return [];
    if (Array.isArray(lineItem.formResponseData)) {
      return lineItem.formResponseData;
    }
    if (lineItem.formData && Array.isArray(lineItem.formData)) {
      return lineItem.formData;
    }
    if (lineItem.formResponse && Array.isArray(lineItem.formResponse)) {
      return lineItem.formResponse;
    }
    if (lineItem.vehicleData && Array.isArray(lineItem.vehicleData)) {
      return lineItem.vehicleData;
    }
    if (lineItem.customFields && Array.isArray(lineItem.customFields)) {
      return lineItem.customFields;
    }
    if (lineItem.additionalData && Array.isArray(lineItem.additionalData)) {
      return lineItem.additionalData;
    }
    return [];
  };

  const safeExtractPrivateMetadata = (lineItem: any): any => {
    if (!lineItem) return {};
    if (
      lineItem.privateMetadata &&
      typeof lineItem.privateMetadata === "object"
    ) {
      return lineItem.privateMetadata;
    }
    if (lineItem.metadata && typeof lineItem.metadata === "object") {
      return lineItem.metadata;
    }
    if (lineItem.customData && typeof lineItem.customData === "object") {
      return lineItem.customData;
    }
    if (lineItem.attributes && typeof lineItem.attributes === "object") {
      return lineItem.attributes;
    }
    if (lineItem.properties && typeof lineItem.properties === "object") {
      return lineItem.properties;
    }
    return {};
  };

  const safeExtractPrice = (lineItem: any, priceType: "total" | "unit"): any => {
    if (!lineItem) return { amount: "0", currency: "USD" };

    // Try different possible price field names
    const priceFields = {
      total: [
        "totalPrice",
        "totalAmount",
        "price",
        "totalPriceAmount",
        "amount",
        "cost",
      ],
      unit: [
        "unitPrice",
        "price",
        "unitPriceAmount",
        "basePrice",
        "pricePerUnit",
        "unitCost",
      ],
    };

    const fields = priceFields[priceType];
    for (const field of fields) {
      if (lineItem[field]) {
        const price = lineItem[field];
        if (typeof price === "string") {
          const parsed = parseFloat(price);
          return { amount: isNaN(parsed) ? "0" : price, currency: "USD" };
        }
        if (typeof price === "number") {
          return { amount: String(price), currency: "USD" };
        }
        if (typeof price === "object" && price.amount) {
          const amount = String(price.amount);
          const parsed = parseFloat(amount);
          return {
            amount: isNaN(parsed) ? "0" : amount,
            currency: String(price.currency || "USD"),
          };
        }
      }
    }

    return { amount: "0", currency: "USD" };
  };

  const getItemQuantity = (item: any): number => {
    try {
      // If quantity is explicitly provided, use it
      if (item.quantity !== undefined && item.quantity > 0) {
        return Number(item.quantity);
      }

      // Calculate quantity based on total price and unit price
      const totalPrice = parseFloat(
        safeExtractAmount(safeExtractPrice(item, "total"))
      );
      const unitPrice = parseFloat(
        safeExtractAmount(safeExtractPrice(item, "unit"))
      );

      if (unitPrice > 0) {
        const calculatedQuantity = totalPrice / unitPrice;
        // Round to handle any floating point precision issues
        const roundedQuantity = Math.round(calculatedQuantity);
        return roundedQuantity > 0 ? roundedQuantity : 1;
      }

      return 1; // Default fallback
    } catch (error) {
      console.error("Error calculating quantity for item:", error);
      return 1; // Safe fallback
    }
  };

  const getItemImage = (item: any): string => {
    const event = safeExtractEvent(item);
    if (event.image) return event.image;
    if (
      event.eventImage &&
      Array.isArray(event.eventImage) &&
      event.eventImage[0]?.photo
    ) {
      return event.eventImage[0].photo;
    }
    if (item.image) return item.image;
    if (item.photo) return item.photo;
    if (item.thumbnail) return item.thumbnail;
    return "/placeholder.jpg";
  };

  const extractOrderData = (orderData: any) => {
    try {
      const lines = safeExtractLines(orderData);
      const total = orderData.total || { amount: "0", currency: "USD" };
      const processingFee = orderData.processingFee || {
        amount: "0",
        currency: "USD",
      };
      const platformFee = orderData.platformFee || {
        amount: "0",
        currency: "USD",
      };
      const subtotal = orderData.subtotal || { amount: "0", currency: "USD" };

      const totalAmount = safeExtractAmount(total);
      const currency = safeExtractCurrency(total);

      // Calculate subtotal from line items if not provided
      const subtotalAmount =
        safeExtractAmount(subtotal) ||
        lines
          .reduce((sum, item) => {
            const itemTotal = safeExtractAmount(safeExtractPrice(item, "total"));
            return sum + parseFloat(itemTotal);
          }, 0)
          .toFixed(2);

      // Calculate fees with fallbacks
      const processingFeeAmount = parseFloat(safeExtractAmount(processingFee));
      const platformFeeAmount = parseFloat(safeExtractAmount(platformFee));
      const serviceFee = platformFeeAmount - processingFeeAmount;

      return {
        lines,
        totalAmount,
        currency,
        subtotalAmount,
        processingFeeAmount,
        platformFeeAmount,
        serviceFee,
      };
    } catch (error) {
      console.error("Error extracting order data:", error);
      setHasError(true);
      setErrorMessage("Failed to process order data");
      return null;
    }
  };

  const clearError = () => {
    setHasError(false);
    setErrorMessage("");
  };

  return {
    // State
    hasError,
    errorMessage,
    
    // Utility functions
    safeExtractAmount,
    safeExtractCurrency,
    safeExtractLines,
    safeExtractOrganization,
    safeExtractEvent,
    safeExtractFormData,
    safeExtractPrivateMetadata,
    safeExtractPrice,
    getItemQuantity,
    getItemImage,
    
    // Main extraction function
    extractOrderData,
    
    // Error handling
    clearError,
    setHasError,
    setErrorMessage,
  };
};
