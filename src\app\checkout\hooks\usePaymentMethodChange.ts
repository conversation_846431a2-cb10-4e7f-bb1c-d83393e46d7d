import { useCallback, useRef, useState, useEffect } from 'react';

interface UsePaymentMethodChangeProps {
  updatePlatformFee: (payload: any) => { unwrap: () => Promise<any> };
  refreshCartData?: () => void | Promise<void>;
  initialPaymentMethodType?: string;
}

export function usePaymentMethodChange({
  updatePlatformFee,
  refreshCartData,
  initialPaymentMethodType = 'card',
}: UsePaymentMethodChangeProps) {
  const [paymentMethodType, setPaymentMethodType] = useState(initialPaymentMethodType);
  const [isPaymentTypeChanging, setIsPaymentTypeChanging] = useState(false);
  const lastPaymentMethodRef = useRef<string | null>(null);
  const isProcessingRef = useRef<boolean>(false);
  
  // Reset processing state if component unmounts during processing
  useEffect(() => {
    return () => {
      isProcessingRef.current = false;
    };
  }, []);
  
  // The core function to handle payment method changes
  const handlePaymentMethodChange = useCallback(async (event: any) => {
    // Skip if no value or type
    if (!event?.value?.type) return;

    const methodType = event.value.type;
    
    // Avoid unnecessary updates
    if (paymentMethodType === methodType) return;
    if (lastPaymentMethodRef.current === methodType) return;
    if (isProcessingRef.current) return;

    // Set processing flag
    isProcessingRef.current = true;
    
    // Update state and ref
    setPaymentMethodType(methodType);
    lastPaymentMethodRef.current = methodType;
    setIsPaymentTypeChanging(true);

    try {
      // Get checkout token from the event data
      const token = event.checkout?.token || event.checkoutData?.checkout?.token;
      if (!token) {
        console.error("Missing checkout token");
        isProcessingRef.current = false;
        setIsPaymentTypeChanging(false);
        return;
      }

      const payload = {
        paymentMethod: methodType,
        token: token,
      };

      // Make API call
      await updatePlatformFee(payload).unwrap();
      
      // Refresh cart data if provided
      if (refreshCartData) {
        await refreshCartData();
      }
    } catch (error) {
      console.error("Failed to update platform fee:", error);
      lastPaymentMethodRef.current = null;
    } finally {
      setIsPaymentTypeChanging(false);
      isProcessingRef.current = false;
    }
  }, [paymentMethodType, updatePlatformFee, refreshCartData]);

  return {
    paymentMethodType,
    isPaymentTypeChanging,
    handlePaymentMethodChange
  };
} 