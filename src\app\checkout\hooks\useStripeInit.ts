import { useState, useEffect, useCallback } from 'react';
import { loadStripe } from '@stripe/stripe-js';

interface UseStripeInitProps {
  publishableKey: string | undefined;
  connectId: string | null;
  isCollaboratorCheckout?: boolean;
}

export const useStripeInit = ({ publishableKey, connectId: initialConnectId, isCollaboratorCheckout: initialIsCollaboratorCheckout }: UseStripeInitProps) => {
  const [stripePromise, setStripePromise] = useState<Promise<any> | null>(null);

  const initializeStripe = useCallback((newConnectId?: string, newIsCollaboratorCheckout?: boolean) => {
    if (!publishableKey) return;
    
    const currentIsCollaboratorCheckout = newIsCollaboratorCheckout !== undefined ? newIsCollaboratorCheckout : initialIsCollaboratorCheckout;
    const currentConnectId = newConnectId || initialConnectId;

    const shouldUseConnectId = !currentIsCollaboratorCheckout;
    const connectAccountId = shouldUseConnectId ? currentConnectId : null;
    
    setStripePromise(
      loadStripe(publishableKey, {
        stripeAccount: connectAccountId || undefined,
      })
    );
  }, [publishableKey, initialConnectId, initialIsCollaboratorCheckout]);

  useEffect(() => {
    if (publishableKey) {
      initializeStripe(initialConnectId || undefined, initialIsCollaboratorCheckout);
    }
  }, [publishableKey, initialConnectId, initialIsCollaboratorCheckout, initializeStripe]);

  return {
    stripePromise,
    initializeStripe,
  };
}; 