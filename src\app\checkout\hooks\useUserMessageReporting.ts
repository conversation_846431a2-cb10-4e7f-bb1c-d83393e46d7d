import { useCallback } from 'react';
import { reportError } from '@/lib/utils/sentryErrorLogs';

interface UseUserMessageReportingProps {
  moduleName?: string;
}

export function useUserMessageReporting({ moduleName = 'checkout' }: UseUserMessageReportingProps = {}) {
  /**
   * Reports a user-facing message to Sentry
   * @param message The message shown to the user
   * @param additionalContext Additional context to include in the report
   */
  const reportUserMessage = useCallback((message: string, additionalContext?: Record<string, string>) => {
    reportError(message, {
      module: moduleName,
      context: 'user_message',
      ...additionalContext
    });
  }, [moduleName]);

  return { reportUserMessage };
} 