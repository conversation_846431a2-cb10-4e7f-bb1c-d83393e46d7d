import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { Image } from "@nextui-org/react";
import Link from "next/link";

export default function CheckoutLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col justify-between">
      <nav className="bg-white border-b border-[#DEDEDE] w-full">
        <div className="w-full max-w-[1200px] h-[80px] mx-auto flex items-center justify-between px-4 md:px-6">
          <Link href="/">
            <Image
              src={IMAGE_LINKS.LOGO_TRANSPARENT}
              alt="AutoLNK"
              className="h-6 md:h-8 w-auto"
            />
          </Link>
          <Link href="/cart">
            <Image
              src="/cart-icon.svg"
              width="15"
              height="15"
              alt="cart icon"
              className="rounded-none cursor-pointer md:w-[16px] md:h-[16px]"
            />
          </Link>
        </div>
      </nav>
      <main className="w-full relative h-screen">{children}</main>
    </div>
  );
}
