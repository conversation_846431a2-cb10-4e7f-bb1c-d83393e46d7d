"use client";

import { useRouter, useSearchParams } from "next/navigation";
import CheckoutForm from "./components/CheckoutForm";
import OrderSummary from "./components/OrderSummary";
import OrderBasedCheckoutForm from "./components/OrderBasedCheckoutForm";
import OrderBasedOrderSummary from "./components/OrderBasedOrderSummary";
import {
  useGetCartQuery,
  useInitializePaymentGatewayQuery,
} from "@/lib/redux/slices/cart/cartApi";
import {
  useGetOrderByIdQuery,
  useGetOrderPaymentClientSecretQuery,
} from "@/lib/redux/slices/orders/ordersApi";
import { useEffect, useState, useCallback } from "react";
import { useSessionData } from "@/lib/hooks/useSession";
import Link from "next/link";
import { Spinner } from "@nextui-org/react";
import { useStripeInit } from "./hooks/useStripeInit";
import { useDeferredPayment } from "./hooks/useDeferredPayment";
import StripeElementsProvider from "./components/StripeElementsProvider";
import { PAYMENT_OPTIONS, STORAGE_KEYS } from "@/lib/constants/storage";
import { PAYMENT_MODE } from "./constants/paymentConstants";
import type { Order } from "@/app/order/success/types";

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const isFree = searchParams.get("isFree");
  const orderId = searchParams.get("orderId");

  const [localClientSecret, setLocalClientSecret] = useState<string | null>(
    null
  );
  const [localConnectId, setLocalConnectId] = useState<string | null>(null);
  const [isCollaboratorCheckout, setIsCollaboratorCheckout] =
    useState<boolean>(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedClientSecret = localStorage.getItem(
        STORAGE_KEYS.CLIENT_SECRET
      );
      const storedConnectId = localStorage.getItem(STORAGE_KEYS.CONNECT_ID);
      const storedIsCollaboratorCheckout = localStorage.getItem(
        STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT
      );
      if (storedClientSecret) {
        setLocalClientSecret(storedClientSecret);
      }
      if (storedConnectId) {
        setLocalConnectId(storedConnectId);
      }
      if (storedIsCollaboratorCheckout) {
        setIsCollaboratorCheckout(storedIsCollaboratorCheckout === "true");
      }
    }
  }, []);

  const { data: session } = useSessionData();
  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);

  const {
    data: cartData,
    isLoading: isCartLoading,
    isUninitialized,
    refetch,
  } = useGetCartQuery(undefined, {
    skip: (!cartToken && !session?.user) || !!orderId, // Skip cart query if orderId is present
  });

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetch = useCallback(() => {
    if (!isUninitialized) {
      refetch();
    }
  }, [isUninitialized, refetch]);

  const isPaymentLinkMethod =
    cartData?.checkout?.lines?.some(
      (line) =>
        line?.eventDetails?.paymentOption === PAYMENT_OPTIONS?.PAYMENT_LINK
    ) || false;

  const { data: paymentGatewayData } = useInitializePaymentGatewayQuery();

  // Get order data for order-based checkout
  const { data: orderData } = useGetOrderByIdQuery(
    { orderId: orderId || "" },
    { skip: !orderId }
  );

  // State for order-based checkout
  const [orderClientSecret, setOrderClientSecret] = useState<string | null>(
    null
  );
  const [orderConnectId, setOrderConnectId] = useState<string | null>(null);
  const [orderIsCollaboratorCheckout, setOrderIsCollaboratorCheckout] =
    useState(false);
  const [orderBillingPatched, setOrderBillingPatched] = useState(false);

  // Get org identifier from order data for fetching client secret
  const orgIdentifier = orderData?.organization?.slug;

  // Get payment client secret for order-based checkout (only after billing is patched)
  const { data: orderPaymentData } = useGetOrderPaymentClientSecretQuery(
    {
      orgIdentifier: orgIdentifier || "",
      orderId: orderId || "",
    },
    {
      skip: !orgIdentifier || !orderId || !orderBillingPatched,
    }
  );

  const { stripePromise, initializeStripe } = useStripeInit({
    publishableKey: paymentGatewayData?.data?.publishableKey,
    connectId: localConnectId || orderConnectId || null,
    isCollaboratorCheckout:
      isCollaboratorCheckout || orderIsCollaboratorCheckout,
  });

  // Initialize Stripe for order flow even without connect ID initially
  useEffect(() => {
    if (
      orderId &&
      paymentGatewayData?.data?.publishableKey &&
      !orderConnectId
    ) {
      // Initialize with basic settings for deferred mode
      initializeStripe(undefined, false);
    }
  }, [orderId, paymentGatewayData, orderConnectId, initializeStripe]);

  // Update order client secret and stripe config when payment data is available
  useEffect(() => {
    if (orderPaymentData?.confirmationData) {
      const { clientSecret, connectId, isCollaboratorCheckout } =
        orderPaymentData.confirmationData;
      setOrderClientSecret(clientSecret);
      setOrderConnectId(connectId);
      setOrderIsCollaboratorCheckout(isCollaboratorCheckout);

      // Initialize Stripe with the connect ID
      if (connectId) {
        initializeStripe(connectId, isCollaboratorCheckout);
      }
    }
  }, [orderPaymentData, initializeStripe]);

  const {
    createPaymentIntent,
    isCreatingPayment,
    error: paymentError,
    clientSecret: deferredClientSecret,
    connectId: deferredConnectId,
    hasPaymentIntent,
  } = useDeferredPayment({
    cartData,
    sessionUser: session?.user,
    onPaymentIntentCreated: (
      newClientSecret,
      newConnectId,
      isCollaboratorCheckoutUpdate
    ) => {
      if (typeof window !== "undefined") {
        localStorage.setItem(STORAGE_KEYS.CLIENT_SECRET, newClientSecret);
        localStorage.setItem(STORAGE_KEYS.CONNECT_ID, newConnectId);
        localStorage.setItem(
          STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT,
          String(isCollaboratorCheckoutUpdate)
        );
      }

      setLocalClientSecret(newClientSecret);
      setLocalConnectId(newConnectId);
      setIsCollaboratorCheckout(isCollaboratorCheckoutUpdate);

      initializeStripe(newConnectId, isCollaboratorCheckoutUpdate);
      safeRefetch();
    },
  });

  const currentClientSecret = localClientSecret || deferredClientSecret;

  useEffect(() => {
    const connectIdToUse = localConnectId || deferredConnectId;

    if (connectIdToUse && !stripePromise) {
      initializeStripe(connectIdToUse, isCollaboratorCheckout);
    }
  }, [
    localConnectId,
    deferredConnectId,
    stripePromise,
    initializeStripe,
    isCollaboratorCheckout,
  ]);

  // For order-based checkout, skip cart validation
  if (!orderId) {
    if (isCartLoading) {
      return (
        <div className="flex justify-center items-center h-screen">
          <Spinner />
        </div>
      );
    }

    if (!cartData?.checkout?.lines?.length) {
      router.push("/cart");
      return null;
    }
  }

  const appearance = {
    theme: "stripe" as const,
    variables: {
      colorPrimary: "#0066cc",
      colorBackground: "#ffffff",
      colorText: "#30313d",
      colorDanger: "#df1b41",
      fontFamily: 'system-ui, -apple-system, "Segoe UI", Roboto, sans-serif',
      spacingUnit: "4px",
      borderRadius: "4px",
    },
  };

  const refreshCartData = async () => {
    await safeRefetch();
  };

  const FooterInfo = [
    {
      id: "1",
      name: "Privacy Policy",
      path: "/privacy-policy",
    },
    {
      id: "2",
      name: "Terms of Service",
      path: "/terms-of-service",
    },
  ];

  const RenderFooter = () => (
    <div
      className="mt-16 flex justify-center gap-6 border-t border-gray-200 pt-4"
      aria-label="Legal Information"
    >
      {FooterInfo.map((footer) => (
        <Link
          key={footer.id}
          href={footer.path}
          className="text-[#1773B0] hover:text-[#1773B0] underline text-sm transition-colors"
        >
          {footer.name}
        </Link>
      ))}
    </div>
  );

  // Order-based checkout flow
  if (orderId) {
    return (
      <div className="min-h-screen bg-[#F1F1F1]">
        <div className="flex flex-col-reverse md:flex-row items-start">
          <div className="w-full md:w-[60%] flex justify-end min-h-screen bg-white px-4 md:px-10 py-8">
            <div className="w-full md:w-[85%] md:max-w-[900px] mt-4 md:mt-8">
              {stripePromise ? (
                <StripeElementsProvider
                  clientSecret={orderClientSecret}
                  stripePromise={stripePromise}
                  appearance={appearance}
                  mode={orderClientSecret ? "payment" : "deferred"}
                  orderData={orderData}
                >
                  <OrderBasedCheckoutForm
                    orderId={orderId}
                    onBillingPatched={() => setOrderBillingPatched(true)}
                  />
                </StripeElementsProvider>
              ) : (
                <div className="flex justify-center items-center py-16">
                  <div className="text-center">
                    <Spinner size="lg" />
                    <p className="mt-4 text-gray-600">
                      Initializing payment system...
                    </p>
                  </div>
                </div>
              )}
              <div>
                <RenderFooter />
              </div>
            </div>
          </div>
          <div className="w-full md:w-[40%] flex justify-start px-4 md:px-8 py-8">
            <div className="w-full md:w-[80%] max-w-[900px]">
              <OrderBasedOrderSummary orderId={orderId} />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Regular cart-based checkout flow
  return (
    <div className="min-h-screen bg-[#F1F1F1]">
      <div className="flex flex-col-reverse md:flex-row items-start">
        <div className="w-full md:w-[60%] flex justify-end min-h-screen bg-white px-4 md:px-10 py-8">
          <div className="w-full md:w-[85%] md:max-w-[900px] mt-4 md:mt-8">
            {!isFree && !isPaymentLinkMethod ? (
              <StripeElementsProvider
                clientSecret={currentClientSecret}
                stripePromise={stripePromise}
                appearance={appearance}
                mode={
                  currentClientSecret
                    ? PAYMENT_MODE.PAYMENT
                    : PAYMENT_MODE.DEFERRED
                }
              >
                <CheckoutForm
                  refreshCartData={refreshCartData}
                  onCreatePaymentIntent={createPaymentIntent}
                  isCreatingPayment={isCreatingPayment}
                  hasPaymentIntent={hasPaymentIntent}
                  paymentError={paymentError}
                />
              </StripeElementsProvider>
            ) : (
              <CheckoutForm
                isPaymentLinkMethod={isPaymentLinkMethod}
                isFreeCheckout={isFree === "true"}
                refreshCartData={refreshCartData}
              />
            )}
            <div>
              <RenderFooter />
            </div>
          </div>
        </div>
        <div className="w-full md:w-[40%] flex justify-start px-4 md:px-8 py-8">
          <div className="w-full md:w-[80%] max-w-[900px]">
            <OrderSummary
              cartData={cartData}
              isCartLoading={isCartLoading}
              isProcessingPayment={isCreatingPayment}
              refetchCart={refreshCartData}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
