"use client";

import { useRouter, useSearchParams } from "next/navigation";
import CheckoutForm from "./components/CheckoutForm";
import OrderSummary from "./components/OrderSummary";
import {
  useGetCartQuery,
  useInitializePaymentGatewayQuery,
} from "@/lib/redux/slices/cart/cartApi";
import { useEffect, useState } from "react";
import { useSessionData } from "@/lib/hooks/useSession";
import Link from "next/link";
import { Spinner } from "@nextui-org/react";
import { useStripeInit } from "./hooks/useStripeInit";
import { useDeferredPayment } from "./hooks/useDeferredPayment";
import StripeElementsProvider from "./components/StripeElementsProvider";
import { STORAGE_KEYS } from "@/lib/constants/storage";
import { PAYMENT_MODE } from "./constants/paymentConstants";

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const isFree = searchParams.get("isFree");

  const [localClientSecret, setLocalClientSecret] = useState<string | null>(
    null
  );
  const [localConnectId, setLocalConnectId] = useState<string | null>(null);
  const [isCollaboratorCheckout, setIsCollaboratorCheckout] =
    useState<boolean>(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedClientSecret = localStorage.getItem(
        STORAGE_KEYS.CLIENT_SECRET
      );
      const storedConnectId = localStorage.getItem(STORAGE_KEYS.CONNECT_ID);
      const storedIsCollaboratorCheckout = localStorage.getItem(
        STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT
      );
      if (storedClientSecret) {
        setLocalClientSecret(storedClientSecret);
      }
      if (storedConnectId) {
        setLocalConnectId(storedConnectId);
      }
      if (storedIsCollaboratorCheckout) {
        setIsCollaboratorCheckout(storedIsCollaboratorCheckout === "true");
      }
    }
  }, []);

  const {
    data: cartData,
    isLoading: isCartLoading,
    refetch,
  } = useGetCartQuery();

  const { data: paymentGatewayData } = useInitializePaymentGatewayQuery();

  const { data: session } = useSessionData();

  const { stripePromise, initializeStripe } = useStripeInit({
    publishableKey: paymentGatewayData?.data?.publishableKey,
    connectId: localConnectId || null,
    isCollaboratorCheckout,
  });

  const {
    createPaymentIntent,
    isCreatingPayment,
    error: paymentError,
    clientSecret: deferredClientSecret,
    connectId: deferredConnectId,
    hasPaymentIntent,
  } = useDeferredPayment({
    cartData,
    sessionUser: session?.user,
    onPaymentIntentCreated: (
      newClientSecret,
      newConnectId,
      isCollaboratorCheckoutUpdate
    ) => {
      if (typeof window !== "undefined") {
        localStorage.setItem(STORAGE_KEYS.CLIENT_SECRET, newClientSecret);
        localStorage.setItem(STORAGE_KEYS.CONNECT_ID, newConnectId);
        localStorage.setItem(
          STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT,
          String(isCollaboratorCheckoutUpdate)
        );
      }

      setLocalClientSecret(newClientSecret);
      setLocalConnectId(newConnectId);
      setIsCollaboratorCheckout(isCollaboratorCheckoutUpdate);

      initializeStripe(newConnectId, isCollaboratorCheckoutUpdate);
      refetch();
    },
  });

  const currentClientSecret = localClientSecret || deferredClientSecret;

  useEffect(() => {
    const connectIdToUse = localConnectId || deferredConnectId;

    if (connectIdToUse && !stripePromise) {
      initializeStripe(connectIdToUse, isCollaboratorCheckout);
    }
  }, [
    localConnectId,
    deferredConnectId,
    stripePromise,
    initializeStripe,
    isCollaboratorCheckout,
  ]);

  if (isCartLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spinner />
      </div>
    );
  }

  if (!cartData?.checkout?.lines?.length) {
    router.push("/cart");
    return null;
  }

  const appearance = {
    theme: "stripe" as const,
    variables: {
      colorPrimary: "#0066cc",
      colorBackground: "#ffffff",
      colorText: "#30313d",
      colorDanger: "#df1b41",
      fontFamily: 'system-ui, -apple-system, "Segoe UI", Roboto, sans-serif',
      spacingUnit: "4px",
      borderRadius: "4px",
    },
  };

  const refreshCartData = async () => {
    await refetch();
  };

  const FooterInfo = [
    {
      id: "1",
      name: "Privacy Policy",
      path: "/privacy-policy",
    },
    {
      id: "2",
      name: "Terms of Service",
      path: "/terms-of-service",
    },
  ];

  const RenderFooter = () => (
    <div
      className="mt-16 flex justify-center gap-6 border-t border-gray-200 pt-4"
      aria-label="Legal Information"
    >
      {FooterInfo.map((footer) => (
        <Link
          key={footer.id}
          href={footer.path}
          className="text-[#1773B0] hover:text-[#1773B0] underline text-sm transition-colors"
        >
          {footer.name}
        </Link>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-[#F1F1F1]">
      <div className="flex flex-col-reverse md:flex-row items-start">
        <div className="w-full md:w-[60%] flex justify-end min-h-screen bg-white px-4 md:px-10 py-8">
          <div className="w-full md:w-[85%] md:max-w-[900px] mt-4 md:mt-8">
            {!isFree ? (
              <StripeElementsProvider
                clientSecret={currentClientSecret}
                stripePromise={stripePromise}
                appearance={appearance}
                mode={
                  currentClientSecret
                    ? PAYMENT_MODE.PAYMENT
                    : PAYMENT_MODE.DEFERRED
                }
              >
                <CheckoutForm
                  refreshCartData={refreshCartData}
                  onCreatePaymentIntent={createPaymentIntent}
                  isCreatingPayment={isCreatingPayment}
                  hasPaymentIntent={hasPaymentIntent}
                  paymentError={paymentError}
                />
              </StripeElementsProvider>
            ) : (
              <CheckoutForm
                isFreeCheckout={true}
                refreshCartData={refreshCartData}
              />
            )}
            <div>
              <RenderFooter />
            </div>
          </div>
        </div>
        <div className="w-full md:w-[40%] flex justify-start px-4 md:px-8 py-8">
          <div className="w-full md:w-[80%] max-w-[900px]">
            <OrderSummary
              cartData={cartData}
              isCartLoading={isCartLoading}
              isProcessingPayment={isCreatingPayment}
              refetchCart={refreshCartData}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
