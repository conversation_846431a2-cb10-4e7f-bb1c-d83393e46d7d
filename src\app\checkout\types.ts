export interface Country {
  isoCode: string;
  name: string;
}

export interface State {
  name: string;
  countryCode: string;
}

export interface CheckoutFormData {
  email: string;
  newsletter: boolean;
  smsUpdates?: boolean;
  country: string;
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  phone: string;
  billingCountry: string;
  billingFirstName: string;
  billingLastName: string;
  billingCompany?: string;
  billingAddressLine1: string;
  billingAddressLine2?: string;
  billingCity: string;
  billingState: string;
  billingPostalCode: string;
  billingPhone: string;
  shippingMethod?: string;
}
