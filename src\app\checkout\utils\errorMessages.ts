/**
 * Checkout error messages shown to users
 */
export const CHECKOUT_ERROR_MESSAGES = {
  // Form validation errors
  INVALID_EMAIL: "Please enter a valid email address",
  REQUIRED_SHIPPING_FIELDS: "Please fill in all required address fields for delivery",
  REQUIRED_BILLING_FIELDS: "Please fill in all required address fields for billing",
  INVALID_NAME_FORMAT: "First and last name can only contain letters, and spaces",
  
  // Payment processing errors
  PAYMENT_CANCELED: "Payment has been canceled. Redirecting to cart page, please try again.",
  PAYMENT_FAILED: "Payment failed. Please try again.",
  PAYMENT_PROCESSING_NOT_READY: "Payment processing is not ready yet. Please try again.",
  
  // General errors
  EMPTY_CART: "Your cart is empty",
}

/**
 * Formats address validation error messages from API responses
 * @param error - The error object from the API
 * @param addressType - The type of address ("shipping" | "billing")
 * @returns Formatted user-friendly error message
 */
export const formatAddressValidationError = (error: any, addressType: "shipping" | "billing") => {
  if (error?.data?.type === "validation_error" && error?.data?.attr) {
    const fieldName = error.data.attr;
    const detail = error.data.detail || "Please check the value and try again.";
    
    // Map field names to user-friendly names
    const fieldMap: Record<string, string> = {
      postal_code: "postal code",
      street_address_1: "street address",
      street_address_2: "street address line 2",
      first_name: "first name",
      last_name: "last name",
      company_name: "company name",
      city: "city",
      city_area: "city area",
      country: "country",
      country_area: "state/province",
      state: "state/province",
      phone: "phone number"
    };
    
    const friendlyFieldName = fieldMap[fieldName] || fieldName;
    return `${addressType.charAt(0).toUpperCase() + addressType.slice(1)} ${friendlyFieldName}: ${detail}`;
  }
  
  return error?.data?.detail || error?.message || "Address validation failed. Please check your information.";
}; 
