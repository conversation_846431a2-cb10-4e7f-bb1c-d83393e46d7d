import { Skeleton } from "@nextui-org/react";

export default function Loading() {
  return (
    <div className="mt-12 min-h-[calc(100vh-100px)] max-w-4xl mx-auto px-4 py-8">
      {/* Cover Image Skeleton */}
      <Skeleton className="w-full aspect-[2/1] rounded-xl mb-8" />

      {/* Title and Meta Skeleton */}
      <div className="mb-8">
        <Skeleton className="h-10 w-3/4 rounded-lg mb-4" />
        <div className="flex gap-4">
          <Skeleton className="h-5 w-32 rounded-lg" />
          <Skeleton className="h-5 w-32 rounded-lg" />
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-full rounded-lg" />
        <Skeleton className="h-6 w-5/6 rounded-lg" />
        <Skeleton className="h-6 w-4/6 rounded-lg" />
        <Skeleton className="h-40 w-full rounded-lg" />
        <Skeleton className="h-6 w-full rounded-lg" />
        <Skeleton className="h-6 w-3/4 rounded-lg" />
      </div>
    </div>
  );
} 