import { Metadata } from "next";
import Image from "next/image";
import { notFound } from "next/navigation";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { FiUser, FiCalendar } from "react-icons/fi";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import "@/styles/markdown.css";
import Sidebar from "../components/Sidebar";
import TopNavBar from "@/features/TopNavBar/TopNavBar";

dayjs.extend(relativeTime);

async function getArticle(slug: string) {
  const response = await fetch(
    `${process.env.STRAPI_URL}/articles?filters[slug][$eq]=${slug}&populate=*`,
    {
      next: {
        revalidate: 60,
      },
      headers: {
        Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
      },
    }
  );

  if (!response.ok) {
    return null;
  }

  const data = await response.json();
  return data.data.length > 0 ? { data: data.data[0] } : null;
}

interface Props {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const article = await getArticle(params.slug);

  if (!article) {
    return {
      title: "Article Not Found",
      description: "The requested article could not be found.",
      robots: "noindex, nofollow",
    };
  }

  const seoBlock = article.data.blocks?.find(
    (block) => block.__component === "shared.seo"
  );

  return {
    title: seoBlock?.metaTitle || article.data.title,
    description: seoBlock?.metaDescription || article.data.description,
    alternates: {
      canonical: `/community/${params.slug}`, // Self-referencing canonical for each article
    },
    openGraph: {
      title: seoBlock?.metaTitle || article.data.title,
      description: seoBlock?.metaDescription || article.data.description,
      type: "article",
      publishedTime: article.data.publishedAt,
      modifiedTime: article.data.updatedAt,
      authors: article.data.author ? [article.data.author.name] : undefined,
      images: article.data.cover
        ? [
            {
              url: article.data.cover.url,
              width: article.data.cover.width,
              height: article.data.cover.height,
              alt: article.data.cover.alternativeText || article.data.title,
            },
          ]
        : undefined,
    },
    twitter: {
      card: "summary_large_image",
      title: seoBlock?.metaTitle || article.data.title,
      description: seoBlock?.metaDescription || article.data.description,
      images: article.data.cover ? [article.data.cover.url] : undefined,
    },
  };
}

export default async function ArticlePage({ params }: Props) {
  const article = await getArticle(params.slug);

  if (!article) {
    notFound();
  }

  const richTextBlock = article.data.blocks?.find(
    (block) => block.__component === "shared.rich-text"
  );

  return (
    <TopNavBar hideFooter={true}>
      <div className="flex h-[calc(100vh-48px)] mt-12 overflow-hidden bg-[#F1F1F1]">
        <Sidebar type="none" />
        <main className="flex-1 overflow-y-auto">
          <article className="max-w-4xl mx-auto px-4 py-8 ">
            {article.data.cover && (
              <div className="relative aspect-[2/1] mb-8 rounded-xl overflow-hidden">
                <Image
                  src={article.data.cover.url}
                  alt={article.data.cover.alternativeText || article.data.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
                  priority
                />
              </div>
            )}

            <header className="mb-8">
              <h1 className="text-4xl md:text-4xl font-bold mb-4">
                {article.data.title}
              </h1>

              <div className="flex flex-wrap gap-4 text-sm text-default-500">
                {article.data.author && (
                  <div className="flex items-center gap-2">
                    <FiUser className="h-4 w-4" />
                    <span>{article.data.author.name}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <FiCalendar className="h-4 w-4" />
                  <time dateTime={article.data.publishedAt}>
                    {dayjs(article.data.publishedAt).format("MMMM D, YYYY")}
                  </time>
                </div>
              </div>
            </header>

            {/* Article Content */}
            <div className="markdown">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              >
                {richTextBlock?.body || ""}
              </ReactMarkdown>
            </div>
          </article>
        </main>
      </div>
    </TopNavBar>
  );
}
