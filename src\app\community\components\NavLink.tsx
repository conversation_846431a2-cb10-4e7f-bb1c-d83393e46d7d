import Link from "next/link";

interface NavLinkProps { 
  href: string; 
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>; 
  children: React.ReactNode;
  isActive: boolean;
}

export function NavLink({ href, icon: Icon, children, isActive }: NavLinkProps) {
  return (
    <Link 
      href={href} 
      className={`flex items-center gap-2 rounded-lg p-1 text-[14px] font-semibold max-w-[80%] ${
        isActive 
          ? "text-[#151515] bg-white" 
          : "text-[#4A4A4A] hover:bg-white/50"
      }`}
    >
      <Icon className="h-5 w-5" />
      <span>{children}</span>
    </Link>
  );
} 