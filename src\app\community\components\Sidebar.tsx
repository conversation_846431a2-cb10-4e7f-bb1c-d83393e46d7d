import { BookOpenIcon, BlogIcon, PageClockIcon } from "@shopify/polaris-icons";
import { NavLink } from "./NavLink";

interface SidebarProps {
  type?: string;
}

export default function Sidebar({ type }: SidebarProps) {
  return (
    <aside className="hidden md:block w-64 bg-[#EBEBEB] h-full">
      <nav className="space-y-2 p-4">
        <NavLink 
          href="/community" 
          icon={BookOpenIcon}
          isActive={!type}
        >
          All posts
        </NavLink>
        
        <NavLink 
          href="/community?type=blog" 
          icon={BlogIcon}
          isActive={type === 'blog'}
        >
          Blog
        </NavLink>
        
        <NavLink 
          href="/community?type=updates" 
          icon={PageClockIcon}
          isActive={type === 'updates'}
        >
          Updates
        </NavLink>
      </nav>
    </aside>
  );
} 