import TopNavBar from "@/features/TopNavBar/TopNavBar";
import { Skeleton } from "@nextui-org/react";
import { FiChevronUp } from "react-icons/fi";

export default function Loading() {
  return (
    <TopNavBar>
      <div className="mt-12 min-h-[calc(100vh-100px)] max-w-5xl mx-auto px-4 py-8">
        <Skeleton className="rounded-lg mb-8">
          <div className="h-8 w-48"></div>
        </Skeleton>

        <div className="space-y-4">
          {Array(6).fill(null).map((_, index) => (
            <div key={index} className="flex items-center gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <Skeleton className="rounded-lg">
                    <div className="h-6 w-16"></div>
                  </Skeleton>
                  <Skeleton className="rounded-lg">
                    <div className="h-6 w-24"></div>
                  </Skeleton>
                </div>
                <Skeleton className="rounded-lg">
                  <div className="h-8 w-3/4"></div>
                </Skeleton>
              </div>
            </div>
          ))}
        </div>
      </div>
    </TopNavBar>
  );
} 