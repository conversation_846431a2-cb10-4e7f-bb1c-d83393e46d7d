import { Metadata } from "next";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import Link from "next/link";
import Image from "next/image";

import type { ArticleResponse } from "@/types/article";
import { PAGE_TITLES } from "@/lib/utils/constants";
import Sidebar from "./components/Sidebar";
import TopNavBar from "@/features/TopNavBar/TopNavBar";

dayjs.extend(relativeTime);

export const metadata: Metadata = {
  title: "Community - Blog Posts",
  description: "Latest updates and articles from our community",
  alternates: {
    canonical: "/community", // Explicit canonical for community page
  },
};

async function getArticles(page: number = 1, category?: string) {
  const categoryFilter = category
    ? `&filters[category][name][$eq]=${category}`
    : "";

  const response = await fetch(
    `${process.env.STRAPI_URL}/articles?populate[0]=category&populate[1]=cover&pagination[page]=${page}&pagination[pageSize]=10${categoryFilter}`,
    {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch articles");
  }

  return response.json() as Promise<ArticleResponse>;
}

export default async function CommunityPage({
  searchParams,
}: {
  searchParams: { page?: string; type?: string };
}) {
  const currentPage = Number(searchParams.page) || 1;
  const type = searchParams.type;

  const pageSize = 10;
  const { data: articles, meta } = await getArticles(currentPage, type);
  const { pageCount: totalPages, total: totalItems } = meta.pagination;

  return (
    <TopNavBar>
      <div className="flex mt-12 min-h-[calc(100vh-116px)] flex-1 bg-[#EBEBEB]">
        <Sidebar type={type} />

        {/* Main content */}
        <main className="flex-1 bg-[#F1F1F1] overflow-y-auto px-4 md:px-0">
          <div className="max-w-5xl mx-auto px-8 py-8 flex flex-col min-h-full">
            {/* Content wrapper */}
            <div className="flex-1">
              <h1 className="text-2xl text-[#151515] font-bold mb-8">
                {PAGE_TITLES[type as keyof typeof PAGE_TITLES] ||
                  PAGE_TITLES.default}
              </h1>

              <div className="space-y-4">
                {articles.map((article) => (
                  <div
                    className="flex flex-row gap-x-4 items-center"
                    key={article.id}
                  >
                    <img
                      src="/blog.svg"
                      alt="Autolnk Blog Logo"
                      className="w-5 h-5"
                    />
                    <Link
                      href={`/community/${article.slug}`}
                      key={article.id}
                      className="block"
                    >
                      <article className="flex items-center gap-4 group">
                        {article.cover && (
                          <div className="relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                            <Image
                              src={
                                article.cover.formats?.thumbnail?.url ||
                                article.cover.url
                              }
                              alt={
                                article.cover.alternativeText || article.title
                              }
                              fill
                              className="object-cover"
                            />
                          </div>
                        )}
                        <div className="flex-1">
                          <span className="text-[#151515] capitalize font-medium">
                            {article.category?.name || "Blog"}
                          </span>
                          <h2 className="text-[16px] text-[#151515] font-medium group-hover:text-primary transition-colors">
                            <span>
                              {article.title}
                              <span className="text-[#6C6C6A] pl-2 text-[13px]">
                                {dayjs(article.publishedAt).fromNow()}
                              </span>
                            </span>
                          </h2>
                        </div>
                      </article>
                    </Link>
                  </div>
                ))}
              </div>
            </div>

            {/* Pagination at bottom */}
            {totalItems > pageSize && (
              <div className="mt-8">
                <nav className="flex gap-1 justify-center">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                    (page) => (
                      <Link
                        key={page}
                        href={`/community?page=${page}${
                          type ? `&type=${type}` : ""
                        }`}
                        className={`px-4 py-2 text-sm rounded-lg ${
                          currentPage === page
                            ? "bg-primary text-white"
                            : "text-default-600 hover:bg-default-100"
                        }`}
                      >
                        {page}
                      </Link>
                    )
                  )}
                </nav>
              </div>
            )}
          </div>
        </main>
      </div>
    </TopNavBar>
  );
}
