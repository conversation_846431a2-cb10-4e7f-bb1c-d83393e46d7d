import Image from "next/image";
import clsx from "clsx";
import { FORM_TYPES } from "../../types";
import {
  useUpdateOrderLineMutation,
  useDeleteOrderLineMutation,
} from "@/lib/redux/slices/orders/ordersApi";
import toast from "react-hot-toast";

type Props = {
  orderData: any;
  orgIdentifier: string;
  refetchOrder?: () => void;
  isDeclined?: boolean;
};

const ApprovedItem = ({
  orderData,
  orgIdentifier,
  refetchOrder,
  isDeclined,
}: Props) => {
  const [updateOrderLine, { isLoading: isUpdatingQuantity }] =
    useUpdateOrderLineMutation();
  const [deleteOrderLine, { isLoading: isRemovingFromOrder }] =
    useDeleteOrderLineMutation();

  if (!orderData?.lines || orderData.lines.length === 0) {
    return <div>No order data available</div>;
  }

  const sortedLines = [...orderData.lines].sort((a, b) => {
    const aAddedAfterOrder = a?.privateMetadata?.addedAfterOrder;
    const bAddedAfterOrder = b?.privateMetadata?.addedAfterOrder;

    if (!aAddedAfterOrder && bAddedAfterOrder) return -1;
    if (aAddedAfterOrder && !bAddedAfterOrder) return 1;
    return 0;
  });

  const handleQuantityDecrease = async (line: any) => {
    try {
      if (!orgIdentifier) {
        toast.error("Organization not found");
        return;
      }

      const itemQuantity = line.quantity;

      if (itemQuantity <= 1) {
        await deleteOrderLine({
          orgIdentifier,
          lineId: line.id,
        }).unwrap();

        toast.success("Item removed from order");

        if (sortedLines.length === 1) {
          toast.success("All items removed from order");
        }
      } else {
        const newQuantity = itemQuantity - 1;

        await updateOrderLine({
          orgIdentifier,
          lineId: line.id,
          quantity: newQuantity,
        }).unwrap();

        toast.success("Quantity updated");
      }

      if (refetchOrder) {
        refetchOrder();
      }
    } catch (error) {
      console.error("Error updating quantity:", error);
      toast.error("Error updating quantity");
    }
  };

  const handleQuantityIncrease = async (line: any) => {
    try {
      if (!orgIdentifier) {
        toast.error("Organization not found");
        return;
      }

      const itemQuantity = line.quantity;

      if (itemQuantity >= 99) {
        toast.error("Maximum quantity limit reached");
        return;
      }

      const newQuantity = itemQuantity + 1;

      await updateOrderLine({
        orgIdentifier,
        lineId: line.id,
        quantity: newQuantity,
      }).unwrap();

      toast.success("Quantity updated");

      if (refetchOrder) {
        refetchOrder();
      }
    } catch (error) {
      console.error("Error updating quantity:", error);
      toast.error("Error updating quantity");
    }
  };

  return (
    <div className="px-[20px] sm:px-[44px] mt-6">
      {sortedLines.map((line: any) => {
        const formResponseData = line?.formResponseData;

        return (
          <div key={line.id} className="mb-6">
            {formResponseData?.length > 0 && (
              <h4 className="text-[#000000BD] text-[13px] sm:text-[14px] font-[500] tracking-[0.32px] mb-[5px]">
                {line.variantName}
              </h4>
            )}
            {formResponseData?.length > 0 ? (
              <div className="flex items-center justify-between border border-[#E8E8E8] rounded-[15px] px-[16px] sm:px-[25px] py-[12px] sm:py-[14px]">
                {formResponseData.map((formResponse: any, index: number) => {
                  const getImageSrc = () => {
                    if (formResponse?.formType === FORM_TYPES.VEHICLE) {
                      return formResponse?.data?.vehicle?.vehicleImages?.[0]
                        ?.image;
                    } else if (formResponse?.formType === FORM_TYPES.VENDOR) {
                      return formResponse?.data?.profileImageForTheCompany
                        ?.image;
                    } else if (
                      formResponse?.formType === FORM_TYPES.MEDIA ||
                      formResponse?.formType === FORM_TYPES.MODEL
                    ) {
                      return formResponse?.data
                        ?.profileImageDisplayedOnTheEventDetailsPage[0];
                    }
                    return null;
                  };

                  const getTitle = () => {
                    if (formResponse?.formType === FORM_TYPES.VEHICLE) {
                      const customMakeName =
                        formResponse?.data?.vehicle?.customMakeName || "";
                      const modelName =
                        formResponse?.data?.vehicle?.modelName || "";
                      return (
                        `${customMakeName} ${modelName}`.trim() || "Vehicle"
                      );
                    } else if (formResponse?.formType === FORM_TYPES.VENDOR) {
                      return formResponse?.data?.vendorName || "Vendor";
                    } else if (
                      formResponse?.formType === FORM_TYPES.MEDIA ||
                      formResponse?.formType === FORM_TYPES.MODEL
                    ) {
                      return formResponse?.data?.yourName || "Participant";
                    } else if (
                      formResponse?.formType === FORM_TYPES.GENERAL_ADMISSION
                    ) {
                      return formResponse?.formName;
                    }
                    return "Form Response";
                  };

                  const vehicleYear = formResponse?.data?.vehicle?.year;
                  const imageSrc = getImageSrc();

                  return (
                    <div
                      key={index}
                      className="flex items-center justify-between w-full"
                    >
                      <div className="flex items-center gap-[8px] sm:gap-[10px]">
                        {imageSrc && (
                          <Image
                            src={imageSrc}
                            alt="Form response image"
                            className={clsx(
                              "w-[48px] h-[48px] sm:w-[56px] sm:h-[56px] object-cover",
                              formResponse?.formType === FORM_TYPES.VEHICLE
                                ? "rounded-[10px]"
                                : "rounded-full"
                            )}
                            width={56}
                            height={56}
                          />
                        )}
                        <div>
                          {vehicleYear && (
                            <p className="text-[#6161617A] text-[11px] sm:text-[12px] font-[500]">
                              {vehicleYear}
                            </p>
                          )}
                          <p className="text-[#414141] text-[14px] sm:text-[15px] font-[500]">
                            {getTitle()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-[12px] sm:gap-[15px]">
                        <p className="text-[#1A1A1A] text-[13px] sm:text-[14px] font-[700]">
                          ${line.unitPrice.amount}
                        </p>
                        <p className="text-[#303030] text-[12px] sm:text-[13px] font-[500]">
                          ×
                        </p>
                        <p className="text-[#121727] text-[16px] sm:text-[17px] font-[700]">
                          {line.quantity}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : !line?.privateMetadata?.addedAfterOrder ? (
              <div className="flex items-center justify-between border border-[#E8E8E8] rounded-[15px] px-[16px] sm:px-[25px] py-[12px] sm:py-[14px]">
                <div className="flex flex-col gap-1">
                  <p className="text-[#000000BD] text-[13px] sm:text-[14px] font-[500] tracking-[0.32px]">
                    {line.variantName}
                  </p>
                  {line.selectedVariants &&
                    line.selectedVariants.length > 0 && (
                      <div className="flex flex-col gap-1">
                        {line.selectedVariants.map(
                          (variant: any, index: number) => (
                            <div
                              key={variant.id || index}
                              className="flex items-center gap-2"
                            >
                              <span className="text-[#616161] text-[11px] sm:text-[12px] font-[400]">
                                {variant.productName} - {variant.variantName}
                              </span>
                              {variant.quantity > 1 && (
                                <span className="text-[#616161] text-[11px] sm:text-[12px] font-[400]">
                                  (×{variant.quantity})
                                </span>
                              )}
                            </div>
                          )
                        )}
                      </div>
                    )}
                </div>
                <div className="flex items-center gap-[12px] sm:gap-[15px]">
                  <p className="text-[#1A1A1A] text-[13px] sm:text-[14px] font-[700]">
                    ${line.unitPrice.amount}
                  </p>
                  <p className="text-[#303030] text-[12px] sm:text-[13px] font-[500]">
                    ×
                  </p>
                  <p className="text-[#121727] text-[16px] sm:text-[17px] font-[700]">
                    {line.quantity}
                  </p>
                </div>
              </div>
            ) : (
              <div className="bg-[#fff] border border-[#DDDDDD] rounded-[16px] py-[16px] sm:py-[19px] px-[20px] sm:px-[40px]">
                <div className="flex items-center justify-between gap-3 sm:gap-4">
                  <div className="flex flex-col gap-[2px]">
                    <p className="text-[#1A1A1A] text-[15px] sm:text-[16px] font-[600]">
                      {line.variantName}
                    </p>
                    <p className="text-[#1A1A1A] text-[13px] sm:text-[14px] font-[600]">
                      ${line.unitPrice.amount}
                    </p>
                  </div>

                  <div
                    className="flex items-center gap-3 select-none"
                    aria-label="Quantity selector"
                  >
                    <button
                      type="button"
                      onClick={() => handleQuantityDecrease(line)}
                      disabled={
                        isRemovingFromOrder || isUpdatingQuantity || isDeclined
                      }
                      aria-label="Decrease quantity"
                      className="w-6 h-6 flex items-center justify-center text-[#9CA3AF] hover:text-[#111827] disabled:opacity-40 disabled:cursor-not-allowed"
                    >
                      −
                    </button>
                    <span className="min-w-[1.5rem] text-center text-[#121727] text-[16px] sm:text-[17px] font-[700]">
                      {line.quantity}
                    </span>
                    <button
                      type="button"
                      onClick={() => handleQuantityIncrease(line)}
                      disabled={isUpdatingQuantity || isDeclined}
                      aria-label="Increase quantity"
                      className="w-6 h-6 flex items-center justify-center text-[#9CA3AF] hover:text-[#111827] disabled:opacity-40 disabled:cursor-not-allowed"
                    >
                      +
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ApprovedItem;
