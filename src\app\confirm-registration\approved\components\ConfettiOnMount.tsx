"use client";

import { useEffect } from "react";

/**
 * Triggers a brief confetti celebration on initial mount.
 */
export default function ConfettiOnMount(): null {
  useEffect(() => {
    let isMounted = true;

    (async () => {
      const confettiModule = await import("canvas-confetti");
      if (!isMounted) return;

      const confetti = confettiModule.default;

      const defaults = { origin: { y: 0.7 } } as const;

      type ConfettiOptions = {
        particleCount?: number;
        spread?: number;
        startVelocity?: number;
        decay?: number;
        scalar?: number;
        origin?: { x?: number; y?: number };
      };

      function fire(particleRatio: number, opts: ConfettiOptions) {
        confetti({
          ...defaults,
          ...opts,
          particleCount: Math.floor(200 * particleRatio),
        });
      }

      // A nice, quick celebratory burst
      fire(0.25, { spread: 26, startVelocity: 55 });
      fire(0.2, { spread: 60 });
      fire(0.35, { spread: 100, decay: 0.91, scalar: 0.8 });
      fire(0.1, { spread: 120, startVelocity: 25, decay: 0.92, scalar: 1.2 });
      fire(0.1, { spread: 120, startVelocity: 45 });
    })();

    return () => {
      isMounted = false;
    };
  }, []);

  return null;
}
