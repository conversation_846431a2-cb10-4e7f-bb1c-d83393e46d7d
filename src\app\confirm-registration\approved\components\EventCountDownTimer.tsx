"use client";

import React, { useState, useEffect } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import clsx from "clsx";
import { unixToTimezone } from "@/lib/utils/date";

// Initialize dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);

interface CountdownProps {
  paymentLinkExpiry: number;
  timezone: string;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const EventCountDownTimer = ({
  paymentLinkExpiry,
  timezone,
}: CountdownProps) => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [startedWithValidTime, setStartedWithValidTime] =
    useState<boolean>(false);
  const [hasRefreshed, setHasRefreshed] = useState<boolean>(false);

  useEffect(() => {
    const calculateTimeLeft = () => {
      try {
        const now = dayjs().tz(timezone);
        const expiryDate = unixToTimezone(paymentLinkExpiry, timezone);

        if (!expiryDate.isValid()) {
          throw new Error("Invalid payment link expiry timestamp");
        }

        const difference = expiryDate.diff(now);

        // If the payment link has expired, return zeros
        if (difference <= 0) {
          setError(null);
          return { days: 0, hours: 0, minutes: 0, seconds: 0 };
        }

        setError(null);
        return calculateTimeParts(difference);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Invalid timestamp format"
        );
        return null;
      }
    };

    const calculateTimeParts = (difference: number): TimeLeft => {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      return { days, hours, minutes, seconds };
    };

    // Initial calculation
    const initialTimeLeft = calculateTimeLeft();
    setTimeLeft(initialTimeLeft);

    // Check if we started with a valid time (not all zeros)
    if (initialTimeLeft && !hasRefreshed) {
      const hasValidTime =
        initialTimeLeft.days > 0 ||
        initialTimeLeft.hours > 0 ||
        initialTimeLeft.minutes > 0 ||
        initialTimeLeft.seconds > 0;
      setStartedWithValidTime(hasValidTime);
    }

    // Update every second
    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft();
      setTimeLeft(newTimeLeft);

      // Check if countdown just reached zero and we started with valid time
      if (
        newTimeLeft &&
        newTimeLeft.days === 0 &&
        newTimeLeft.hours === 0 &&
        newTimeLeft.minutes === 0 &&
        newTimeLeft.seconds === 0 &&
        startedWithValidTime &&
        !hasRefreshed
      ) {
        setHasRefreshed(true);
        // Small delay to ensure state updates before refresh
        setTimeout(() => {
          window.location.reload();
        }, 100);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [paymentLinkExpiry, timezone, startedWithValidTime, hasRefreshed]);

  if (!paymentLinkExpiry) {
    return null;
  }

  if (error) {
    return (
      <div className="bg-red-100 rounded-lg p-4 text-center text-xs">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (!timeLeft) {
    return null;
  }

  const calculateWidthClass = (value) =>
    `w-[${Math.max(value?.toString().length, 3)}ch]`;

  return (
    <div
      className={clsx(
        "min-w-[80%] sm:min-w-[55%] lg:min-w-[65%] 2xl:min-w-[50%]"
      )}
    >
      <div className="text-[20px] sm:text-[29px] text-[#F01111] font-thin flex justify-center space-x-1 sm:space-x-2">
        <div className={`inline-block ${calculateWidthClass(timeLeft?.days)}`}>
          <span>{timeLeft?.days?.toString()?.padStart(2, "0")}d</span>
        </div>
        <div
          className={`inline-block ${
            Number(calculateWidthClass(timeLeft?.hours)) - 0.3
          }`}
        >
          <span>{timeLeft?.hours?.toString()?.padStart(2, "0")}h</span>
        </div>
        <div
          className={`inline-block  ${calculateWidthClass(timeLeft?.minutes)}`}
        >
          <span>{timeLeft?.minutes?.toString()?.padStart(2, "0")}m</span>
        </div>
        <div
          className={`inline-block ${calculateWidthClass(timeLeft?.seconds)}`}
        >
          <span>{timeLeft?.seconds?.toString()?.padStart(2, "0")}s</span>
        </div>
      </div>
      <div className="text-[#86868B] text-center text-[12px] sm:text-[14px] font-[400] -mt-[3px] sm:-mt-[5px]">
        Remaining time to complete payment
      </div>
    </div>
  );
};

export default EventCountDownTimer;
