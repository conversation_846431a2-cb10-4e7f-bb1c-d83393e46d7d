import Image from "next/image";

type Props = {
  images: any;
  eventName: string;
};

const EventInfo = ({ images, eventName }: Props) => {
  const eventImageSrc = images?.find((image: any) => image?.type === 1)?.photo;

  return (
    <div className="flex items-center gap-[10px] px-[20px] sm:px-[45px]">
      <Image
        src={eventImageSrc}
        alt="Event Image"
        width={40}
        height={40}
        className="w-[40px] h-[40px] rounded-[7px]"
      />
      <h2 className="text-[#414141] text-[15px] sm:text-[16px] font-[500] tracking-[0.32px]">
        {eventName}
      </h2>
    </div>
  );
};

export default EventInfo;
