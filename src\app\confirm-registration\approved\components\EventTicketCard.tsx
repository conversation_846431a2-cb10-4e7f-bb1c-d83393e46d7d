"use client";

import { useCallback, useMemo } from "react";

type Props = {
  ticket: any;
  quantity?: number;
  onQuantityChange?: (quantity: number) => void;
  min?: number;
  max?: number;
  disabled?: boolean;
  orderData?: any;
};

const EventTicketCard = ({
  ticket,
  quantity = 0,
  onQuantityChange,
  min = 0,
  max,
  disabled = false,
  orderData,
}: Props) => {
  const isTicketAlreadyInOrder = useMemo(() => {
    if (!orderData?.lines || !ticket?.id) return false;

    return orderData.lines.some(
      (line: any) => line.productVariantId === ticket.id
    );
  }, [orderData, ticket]);

  const canDecrement = useMemo(
    () => !disabled && quantity > (min ?? 0),
    [disabled, quantity, min]
  );

  const canIncrement = useMemo(
    () => !disabled && (typeof max === "number" ? quantity < max : true),
    [disabled, quantity, max]
  );

  const handleDecrement = useCallback(() => {
    const next = Math.max(min ?? 0, quantity - 1);
    if (next !== quantity) {
      onQuantityChange?.(next);
    }
  }, [min, onQuantityChange, quantity]);

  const handleIncrement = useCallback(() => {
    const upperBound = typeof max === "number" ? max : Number.POSITIVE_INFINITY;
    const next = Math.min(upperBound, quantity + 1);
    if (next !== quantity) {
      onQuantityChange?.(next);
    }
  }, [max, onQuantityChange, quantity]);

  // Early returns after all hooks
  if (isTicketAlreadyInOrder) {
    return null;
  }

  if (ticket?.approvalRequired || ticket?.forms?.length > 0) {
    return <></>;
  }

  console.log(orderData);

  return (
    <div className="bg-[#fff] border border-[#DDDDDD] rounded-[16px] py-[16px] sm:py-[19px] px-[20px] sm:px-[40px]">
      <div className="flex items-center justify-between gap-3 sm:gap-4">
        <div className="flex flex-col gap-[2px]">
          <p className="text-[#1A1A1A] text-[15px] sm:text-[16px] font-[600]">
            {ticket?.name}
          </p>
          <p className="text-[#1A1A1A] text-[13px] sm:text-[14px] font-[600]">
            ${ticket?.ticketPrice}
          </p>
        </div>

        <div
          className="flex items-center gap-3 select-none"
          aria-label="Quantity selector"
        >
          <button
            type="button"
            onClick={handleDecrement}
            disabled={!canDecrement}
            aria-label="Decrease quantity"
            className="w-6 h-6 flex items-center justify-center text-[#9CA3AF] hover:text-[#111827] disabled:opacity-40 disabled:cursor-not-allowed"
          >
            −
          </button>
          <span className="min-w-[1.5rem] text-center text-[#121727] text-[16px] sm:text-[17px] font-[700]">
            {quantity}
          </span>
          <button
            type="button"
            onClick={handleIncrement}
            disabled={!canIncrement}
            aria-label="Increase quantity"
            className="w-6 h-6 flex items-center justify-center text-[#9CA3AF] hover:text-[#111827] disabled:cursor-not-allowed"
          >
            +
          </button>
        </div>
      </div>
    </div>
  );
};

export default EventTicketCard;
