import React from "react";
import { formatTimeUntilEvent } from "@/lib/utils/date";

type Props = {
  startDate: string;
  timezone: string;
  isDeclined: boolean;
  isOrderCompleted: boolean;
};

const ApprovedConfirmationHeader = ({
  startDate,
  timezone,
  isDeclined,
  isOrderCompleted,
}: Props) => {
  const formattedStartTime = formatTimeUntilEvent(startDate, timezone);

  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-[20px] sm:py-[25px] px-[20px] sm:px-[30px] gap-[12px] sm:gap-0">
      <h1 className="text-[18px] sm:text-[20px] tracking-[0.32px] font-[600] text-black text-center sm:text-left">
        {isDeclined
          ? "Registration was not approved"
          : isOrderCompleted
          ? "Order Was Already Completed"
          : "You're Accepted"}
      </h1>
      <p className="text-[13px] sm:text-[14px] tracking-[0.32px] font-[500] text-[#000000BD] text-center sm:text-right">
        Event starts: {formattedStartTime}
      </p>
    </div>
  );
};

export default ApprovedConfirmationHeader;
