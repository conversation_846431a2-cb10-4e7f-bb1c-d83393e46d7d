import { Skeleton } from "@nextui-org/react";

export default function Loading() {
  return (
    <div className="min-h-[calc(100vh-200px)] pt-[45px]">
      <div className="bg-[#F9F9F9] rounded-[38px] w-[590px] mx-auto">
        {/* Header skeleton */}
        <div className="px-[44px] pt-[42px] pb-[20px]">
          <div className="flex items-center justify-between mb-4">
            <Skeleton className="w-32 h-6 rounded-lg" />
            <Skeleton className="w-24 h-6 rounded-lg" />
          </div>
          <Skeleton className="w-48 h-8 rounded-lg mb-2" />
          <Skeleton className="w-64 h-5 rounded-lg" />
        </div>

        {/* Event info skeleton */}
        <div className="px-[44px] pb-[20px]">
          <Skeleton className="w-full h-32 rounded-lg mb-4" />
          <Skeleton className="w-40 h-6 rounded-lg" />
        </div>

        {/* Approved item skeleton */}
        <div className="px-[44px] pb-[20px]">
          <Skeleton className="w-full h-24 rounded-lg" />
        </div>

        {/* Event tickets skeleton */}
        <div className="mt-[20px] px-[44px] flex flex-col gap-[16px]">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="flex items-center justify-between p-4 bg-white rounded-lg"
            >
              <div className="flex-1">
                <Skeleton className="w-32 h-5 rounded-lg mb-2" />
                <Skeleton className="w-24 h-4 rounded-lg" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="w-8 h-8 rounded-lg" />
                <Skeleton className="w-12 h-8 rounded-lg" />
                <Skeleton className="w-8 h-8 rounded-lg" />
              </div>
            </div>
          ))}
        </div>

        {/* Countdown timer skeleton */}
        <div className="px-[44px] mt-[56px] flex items-center justify-center">
          <Skeleton className="w-48 h-8 rounded-lg" />
        </div>

        {/* Pay now button skeleton */}
        <div className="px-[44px] mt-[22px] pb-[42px]">
          <Skeleton className="w-full h-[52px] rounded-[12px]" />
        </div>
      </div>
    </div>
  );
}
