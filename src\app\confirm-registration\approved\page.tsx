"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import toast from "react-hot-toast";
import { useGetEventByIdQuery } from "@/lib/redux/slices/events/eventsApi";
import {
  useGetOrderByIdQuery,
  useAddOrderLinesMutation,
} from "@/lib/redux/slices/orders/ordersApi";
import ApprovedConfirmationHeader from "./components/Header";
import ConfettiOnMount from "./components/ConfettiOnMount";
import EventInfo from "./components/EventInfo";
import ApprovedItem from "./components/ApprovedItem";
import EventTicketCard from "./components/EventTicketCard";
import { Button } from "@/components/ui/button";
import EventCountDownTimer from "./components/EventCountDownTimer";
import { handleApiError } from "@/lib/utils/errorUtils";
import { TOASTS } from "@/lib/utils/constants";
import { isPaymentLinkExpired } from "@/lib/utils/date";

const RegistrationApprovedPage = () => {
  const searchParams = useSearchParams();
  const orderId = searchParams.get("orderId");
  const router = useRouter();
  // RTK Query hooks
  const {
    data: orderData,
    isLoading: isOrderLoading,
    error: orderError,
    isUninitialized,
    refetch: refetchOrder,
  } = useGetOrderByIdQuery({ orderId: orderId! }, { skip: !orderId });

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetchOrder = useCallback(() => {
    if (!isUninitialized) {
      refetchOrder();
    }
  }, [isUninitialized, refetchOrder]);

  // Handle API errors with toast notifications
  useEffect(() => {
    if (orderError) {
      handleApiError(orderError, TOASTS.ERROR);
    }
  }, [orderError]);

  const eventId = (orderData?.lines[0] as any)?.event?.id;
  const orgIdentifier =
    orderData?.organization?.slug || orderData?.organization?.id || "";

  const {
    data: eventData,
    isLoading: isEventLoading,
    error: eventError,
  } = useGetEventByIdQuery({ id: eventId! }, { skip: !eventId });

  // Handle event API errors with toast notifications
  useEffect(() => {
    if (eventError) {
      handleApiError(eventError, TOASTS.ERROR);
    }
  }, [eventError]);

  const [addOrderLines, { isLoading: isAddingLines }] =
    useAddOrderLinesMutation();

  // State to track selected ticket quantities
  const [selectedTickets, setSelectedTickets] = useState<
    Record<string, number>
  >({});

  const handleQuantityChange = (ticketId: string, quantity: number) => {
    setSelectedTickets((prev) => ({
      ...prev,
      [ticketId]: quantity,
    }));
  };

  const handlePayNow = async () => {
    if (!orderId || !orgIdentifier) {
      toast.error("Missing order information. Please try again.");
      return;
    }

    // Filter out tickets with quantity 0 and prepare lines for API
    const lines = Object.entries(selectedTickets)
      .filter(([ticketId, quantity]) => quantity > 0)
      .map(([ticketId, quantity]) => ({
        eventTicketId: ticketId,
        quantity,
      }));

    try {
      // If tickets are selected, add them to the order first
      if (lines.length > 0) {
        await addOrderLines({
          orgIdentifier,
          orderId,
          lines,
        }).unwrap();

        toast.success(
          "Tickets added successfully! Redirecting to checkout page..."
        );
      } else {
        // No tickets selected, proceed directly to checkout
        toast.success("Proceeding to checkout...");
      }

      router.push(`/checkout?orderId=${orderId}`);
    } catch (error: any) {
      handleApiError(
        error,
        "Failed to add tickets to your order. Please try again."
      );
    }
  };

  // Check if the error is specifically "not_approved"
  const isNotApprovedError =
    orderError &&
    (orderError as any)?.data?.type === "validation_error" &&
    (orderError as any)?.data?.code === "not_approved";

  // Loading state
  if (isOrderLoading || isEventLoading || isAddingLines) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <div className="bg-[#F9F9F9] rounded-[24px] w-full max-w-[640px] mx-auto p-4 sm:p-8 text-center">
          <h1 className="text-lg sm:text-xl font-semibold mb-2">
            {isAddingLines ? "Updating..." : "Loading..."}
          </h1>
          <p className="text-sm text-[#666]">
            {isAddingLines
              ? "Please wait while we update your ticket selection."
              : "Please wait while we load your order details."}
          </p>
        </div>
      </div>
    );
  }

  // Error states
  if (!orderId) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <div className="bg-[#F9F9F9] rounded-[24px] w-full max-w-[640px] mx-auto p-4 sm:p-8 text-center">
          <h1 className="text-lg sm:text-xl font-semibold mb-2">
            Order ID Required
          </h1>
          <p className="text-sm text-[#666]">
            Please provide a valid order ID in the URL.
          </p>
        </div>
      </div>
    );
  }

  // Show specific UI for "not_approved" error
  if (isNotApprovedError) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <div className="bg-[#F9F9F9] rounded-[24px] w-full max-w-[640px] mx-auto p-4 sm:p-8 text-center">
          <div className="mb-6">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h1 className="text-xl sm:text-2xl font-semibold text-red-600 mb-2">
              Not Approved Yet
            </h1>
            <p className="text-base sm:text-lg text-[#666] mb-4">
              {TOASTS.NOT_APPROVED}
            </p>
            <p className="text-sm text-[#888]">
              Your registration is currently under review. You will be notified
              once your approval is complete.
            </p>
          </div>
          <Button
            onClick={() => router.push("/")}
            className="bg-[#007AFF] hover:bg-[#007AFF]/90 text-white px-6 py-2 rounded-lg"
          >
            Return to Home
          </Button>
        </div>
      </div>
    );
  }

  if (orderError || eventError) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <div className="bg-[#F9F9F9] rounded-[24px] w-full max-w-[640px] mx-auto p-4 sm:p-8 text-center">
          <h1 className="text-lg sm:text-xl font-semibold mb-2">
            Error Loading Data
          </h1>
          <p className="text-sm text-[#666]">
            {orderError
              ? "Failed to load order details"
              : "Failed to load event details"}
          </p>
        </div>
      </div>
    );
  }

  if (!orderData) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <div className="bg-[#F9F9F9] rounded-[24px] w-full max-w-[640px] mx-auto p-4 sm:p-8 text-center">
          <h1 className="text-lg sm:text-xl font-semibold mb-2">
            Order Not Found
          </h1>
          <p className="text-sm text-[#666]">
            We could not find the requested order details.
          </p>
        </div>
      </div>
    );
  }

  const paymentLinkExpiry = orderData?.payment?.paymentLinkExpiresAt;
  const timezone = (orderData?.lines[0] as any)?.event?.timezone || "UTC"; // Get timezone from event data in order
  const isDeclined = orderData?.status === "voided";
  const isOrderCompleted = orderData?.status === "fulfilled";
  const isExpired = paymentLinkExpiry
    ? isPaymentLinkExpired(paymentLinkExpiry, timezone)
    : false;

  return (
    <div className="min-h-[calc(100vh-200px)] pt-[45px] px-4 sm:px-0">
      {/* Client-only celebration on first mount */}
      {!isDeclined && <ConfettiOnMount />}
      <div className="bg-[#F9F9F9] rounded-[24px] sm:rounded-[38px] w-full max-w-[590px] mx-auto">
        <ApprovedConfirmationHeader
          startDate={eventData?.startDate || ""}
          timezone={timezone}
          isDeclined={isDeclined}
          isOrderCompleted={isOrderCompleted}
        />
        {eventData && (
          <div>
            <EventInfo images={eventData?.images} eventName={eventData?.name} />
          </div>
        )}
        <div>
          <ApprovedItem
            orderData={orderData}
            orgIdentifier={orgIdentifier}
            refetchOrder={safeRefetchOrder}
            isDeclined={isDeclined || isExpired || isOrderCompleted}
          />
        </div>
        {!isDeclined &&
          !isExpired &&
          !isOrderCompleted &&
          eventData?.tickets &&
          eventData.tickets.length > 0 && (
            <div className="mt-[16px] sm:mt-[20px] px-[20px] sm:px-[44px] flex flex-col gap-[12px] sm:gap-[16px]">
              {eventData.tickets.map((ticket: any) => (
                <EventTicketCard
                  key={ticket?.id}
                  ticket={ticket}
                  quantity={selectedTickets[ticket.id] || 0}
                  onQuantityChange={(quantity: number) =>
                    handleQuantityChange(ticket.id, quantity)
                  }
                  disabled={isAddingLines}
                  orderData={orderData}
                />
              ))}
            </div>
          )}
        {!isDeclined && !isOrderCompleted && paymentLinkExpiry && (
          <div className="px-[20px] sm:px-[44px] mt-[40px] sm:mt-[56px] flex items-center justify-center">
            <EventCountDownTimer
              paymentLinkExpiry={paymentLinkExpiry}
              timezone={timezone}
            />
          </div>
        )}
        <div className="px-[20px] sm:px-[44px] mt-[16px] sm:mt-[22px] pb-[32px] sm:pb-[42px]">
          {!isDeclined && !isOrderCompleted && (
            <Button
              onClick={handlePayNow}
              disabled={isAddingLines || isDeclined || isExpired}
              className="w-full bg-[#007AFF] hover:bg-[#007AFF]/90 rounded-[12px] h-[48px] sm:h-[52px] text-[16px] sm:text-[19px] font-[500] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isAddingLines
                ? "Processing..."
                : isExpired
                ? "Expired"
                : "Pay now"}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default RegistrationApprovedPage;
