"use client";

import { STORAGE_KEYS } from "@/lib/constants/storage";
import { useSessionData } from "@/lib/hooks/useSession";
import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";
import clsx from "clsx";
import Image from "next/image";
import React, { useState } from "react";
import { FORM_TYPES } from "../types";
import TicketForm from "@/app/e/[slug]/components/Form/TicketForm";
import { useCart } from "@/hooks/useCart";

type Props = {};

const CartItems = (props: Props) => {
  const { data: sessionData } = useSessionData();
  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);
  const [modalState, setModalState] = useState(false);
  const [selectedLine, setSelectedLine] = useState<any>(null);

  const { data: cartData, isLoading } = useGetCartQuery(undefined, {
    skip: !cartToken && !sessionData?.user,
  });

  const { data: snakeCasedCartData } = useCart();
  const snakeCasedProduct = snakeCasedCartData?.checkout?.lines?.find(
    (line: any) => line.id === selectedLine?.id
  );

  if (isLoading) return <div>Loading...</div>;

  if (!cartData) return <div>No cart data</div>;

  const handleEditClick = (line: any) => {
    setSelectedLine(line);
    setModalState(true);
  };

  return (
    <div className="flex flex-col gap-[14px]">
      {cartData?.checkout?.lines?.map((line) => {
        const eventImage = line?.eventDetails?.eventPhoto?.find(
          (photo) => photo?.type === 1
        )?.photo;

        const formResponseData = line?.formResponseData;
        return (
          <div
            key={line?.id}
            className="bg-[#FFFFFF] border border-[#d7d7d7] rounded-[17px] px-4 md:px-[30px] pt-4 md:pt-[25px] pb-4 md:pb-[20px]"
          >
            <div className="flex items-center gap-[15px]">
              <Image
                src={eventImage || ""}
                alt={line?.eventDetails?.eventName || "Autolnk Event Image"}
                width={40}
                height={40}
                className="w-[40px] h-[40px] rounded-[7px] object-cover flex-shrink-0"
              />
              <h3 className="text-[14px] md:text-[16px] font-[600] text-black break-words">
                {line?.eventDetails?.eventName}
              </h3>
            </div>
            <div className="mt-[20px] md:mt-[25px] flex items-center justify-between gap-2 sm:gap-0">
              <div className="flex-1">
                <p className="text-[#000000BD] text-[13px] md:text-[14px] font-medium">
                  {line?.eventDetails?.ticketName}
                </p>
                {line?.selectedVariants?.length > 0 && (
                  <div className="mt-[5px]">
                    {line.selectedVariants.map(
                      (selectedVariant: any, index: number) => (
                        <div
                          key={selectedVariant.id}
                          className="flex items-center gap-[8px]"
                        >
                          <span className="text-[#666666] text-[11px] md:text-[12px] font-medium break-words">
                            {selectedVariant.variant.productDetails?.name} -{" "}
                            {selectedVariant.variant.name}
                          </span>
                          {line.selectedVariants.length > 1 && (
                            <span className="text-[#666666] text-[11px] md:text-[12px] font-medium">
                              (x{selectedVariant.quantity})
                            </span>
                          )}
                        </div>
                      )
                    )}
                  </div>
                )}
              </div>
              <p className="text-[#303030] text-[14px] md:text-[15px] font-medium flex-shrink-0">
                <span className="mr-2">x</span> {line?.quantity}
              </p>
            </div>
            {formResponseData?.length > 0 && (
              <div className="mt-[10px] flex items-center justify-between border border-[#E8E8E8] rounded-[15px] py-[10px] px-4 md:px-[24px] gap-2 sm:gap-0">
                {formResponseData?.map((formResponse) => {
                  const getImageSrc = () => {
                    if (formResponse?.formType === FORM_TYPES.VEHICLE) {
                      return formResponse?.data?.vehicle?.vehicleImages?.[0]
                        ?.image;
                    } else if (formResponse?.formType === FORM_TYPES.VENDOR) {
                      return formResponse?.data?.profileImageForTheCompany
                        ?.image;
                    } else if (
                      formResponse?.formType === FORM_TYPES.MEDIA ||
                      formResponse?.formType === FORM_TYPES.MODEL
                    ) {
                      return formResponse?.data
                        ?.profileImageDisplayedOnTheEventDetailsPage[0];
                    }
                  };

                  const getTitle = () => {
                    if (formResponse?.formType === FORM_TYPES.VEHICLE) {
                      const customMakeName =
                        formResponse?.data?.vehicle?.customMakeName || "";
                      const modelName =
                        formResponse?.data?.vehicle?.modelName || "";
                      return (
                        `${customMakeName} ${modelName}`.trim() || "Vehicle"
                      );
                    } else if (formResponse?.formType === FORM_TYPES.VENDOR) {
                      return formResponse?.data?.vendorName || "Vendor";
                    } else if (
                      formResponse?.formType === FORM_TYPES.MEDIA ||
                      formResponse?.formType === FORM_TYPES.MODEL
                    ) {
                      return formResponse?.data?.yourName || "Participant";
                    } else if (
                      formResponse?.formType === FORM_TYPES.GENERAL_ADMISSION
                    ) {
                      return formResponse?.formName;
                    }
                    return "Form Response";
                  };

                  const vehicleYear = formResponse?.data?.vehicle?.year;

                  return (
                    <>
                      <div className="flex items-center gap-[10px] flex-1">
                        <Image
                          src={getImageSrc()}
                          alt="Edit"
                          width={57}
                          height={57}
                          className={clsx(
                            "w-[50px] h-[50px] md:w-[57px] md:h-[57px] object-cover flex-shrink-0",
                            formResponse?.formType === FORM_TYPES.VEHICLE
                              ? "rounded-[12px]"
                              : "rounded-full"
                          )}
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-[#6161617A] text-[11px] md:text-[12px] font-[500]">
                            {vehicleYear}
                          </p>
                          <p className="text-black text-[13px] md:text-[15px] font-normal break-words">
                            {getTitle()}
                          </p>
                        </div>
                      </div>
                      <p
                        className="text-[#303030] text-[11px] md:text-[12px] font-medium underline cursor-pointer flex-shrink-0"
                        onClick={() => handleEditClick(line)}
                      >
                        Edit
                      </p>
                    </>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
      {selectedLine && (
        <TicketForm
          open={modalState}
          onClose={() => {
            setModalState(false);
            setSelectedLine(null);
          }}
          isEdit={true}
          ticketFormData={snakeCasedProduct}
          eventId={selectedLine?.eventDetails?.eventId}
          ticketId={selectedLine?.eventTicket}
          lineId={selectedLine?.id}
        />
      )}
    </div>
  );
};

export default CartItems;
