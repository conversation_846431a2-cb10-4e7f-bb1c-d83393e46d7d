import { Skeleton } from "@nextui-org/react";

export default function Loading() {
  return (
    <div className="min-h-[calc(100vh-200px)] mb-16">
      <div className="mt-[150px] max-w-[1060px] mx-auto bg-[#F9F9F9] rounded-[38px] grid grid-cols-1 md:grid-cols-2 gap-6 p-4 md:p-6">
        {/* Left column - Contact Form */}
        <div className="p-10">
          {/* Title */}
          <Skeleton className="w-48 h-7 rounded-lg mb-2" />

          {/* Description */}
          <Skeleton className="w-full h-4 rounded mb-2" />
          <Skeleton className="w-3/4 h-4 rounded mb-8" />

          {/* Form fields */}
          <div className="space-y-6">
            {/* First Name */}
            <div>
              <Skeleton className="w-20 h-4 rounded mb-2" />
              <Skeleton className="w-full h-12 rounded-lg" />
            </div>

            {/* Last Name */}
            <div>
              <Skeleton className="w-20 h-4 rounded mb-2" />
              <Skeleton className="w-full h-12 rounded-lg" />
            </div>

            {/* Email */}
            <div>
              <Skeleton className="w-16 h-4 rounded mb-2" />
              <Skeleton className="w-full h-12 rounded-lg" />
            </div>

            {/* Phone */}
            <div>
              <Skeleton className="w-20 h-4 rounded mb-2" />
              <Skeleton className="w-full h-12 rounded-lg" />
            </div>

            {/* Submit button */}
            <Skeleton className="w-full h-12 rounded-lg mt-8" />
          </div>
        </div>

        {/* Right column - Cart Items */}
        <div className="p-6">
          {/* Cart header */}
          <Skeleton className="w-32 h-6 rounded mb-4" />

          {/* Cart items */}
          <div className="space-y-4">
            {[1, 2].map((i) => (
              <div key={i} className="space-y-3">
                {/* Item card */}
                <div className="p-4 bg-white rounded-lg">
                  <Skeleton className="w-full h-20 rounded" />
                  <div className="mt-3 space-y-2">
                    <Skeleton className="w-3/4 h-4 rounded" />
                    <Skeleton className="w-1/2 h-4 rounded" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Total section */}
          <div className="mt-6 pt-4 border-t">
            <div className="space-y-2">
              <div className="flex justify-between">
                <Skeleton className="w-16 h-4 rounded" />
                <Skeleton className="w-20 h-4 rounded" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="w-12 h-4 rounded" />
                <Skeleton className="w-16 h-4 rounded" />
              </div>
              <div className="flex justify-between pt-2 border-t">
                <Skeleton className="w-20 h-5 rounded" />
                <Skeleton className="w-24 h-5 rounded" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
