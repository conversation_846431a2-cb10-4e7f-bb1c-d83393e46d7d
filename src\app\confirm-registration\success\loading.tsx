import { Skeleton } from "@nextui-org/react";

export default function Loading() {
  return (
    <div className="flex flex-col lg:flex-row justify-center items-center lg:items-start gap-6 lg:gap-[30px] min-h-screen px-4 py-8 lg:py-0">
      {/* Left column - Success Message and Download Buttons */}
      <div className="mt-8 lg:mt-[200px] w-full max-w-[334px] text-center">
        {/* Checkmark icon skeleton */}
        <div className="flex justify-center items-center mb-6 lg:mb-[25px]">
          <Skeleton className="w-12 h-12 sm:w-16 sm:h-16 lg:w-[4.5rem] lg:h-[4.5rem] rounded-full" />
        </div>

        {/* Title skeleton */}
        <Skeleton className="w-48 h-6 rounded-lg mb-12 lg:mb-[80px] mx-auto" />

        {/* Description skeleton */}
        <Skeleton className="w-full h-5 rounded mb-2 mx-auto" />
        <Skeleton className="w-3/4 h-5 rounded mb-6 lg:mb-[25px] mx-auto" />

        {/* Download buttons skeleton */}
        <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-2">
          <Skeleton className="w-full sm:w-36 h-10 rounded-lg" />
          <Skeleton className="w-full sm:w-36 h-10 rounded-lg" />
        </div>
      </div>

      {/* Right column - Image */}
      <div className="mt-0 lg:mt-[134px] w-full max-w-[500px] lg:w-auto">
        <Skeleton className="w-full h-auto max-w-[300px] sm:max-w-[400px] lg:max-w-[500px] lg:w-[500px] lg:h-[580px] mx-auto rounded-lg" />
      </div>
    </div>
  );
}
