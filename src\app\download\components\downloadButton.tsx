"use client"
import React from "react";
import Image from "next/image";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

export const AppStoreButton: React.FC<{ href: string; platform: 'ios' | 'android' }> = ({ href, platform }) => (
    <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="z-10 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
    >
        {platform === 'android' ? (
            <Image
                src={IMAGE_LINKS.DOWNLOAD_ON_GOOGLE}
                width="140"
                height="40"
                alt="Autolnk Download on Google Play"
            />
        ) : (
            <Image
                src={IMAGE_LINKS.DOWNLOAD_ON_APPSTORE}
                width="170"
                height="40"
                alt="Autolnk Download on App Store"
            />
        )}
    </a>
);
