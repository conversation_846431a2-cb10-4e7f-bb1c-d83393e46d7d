"use client"
import React from "react";

export const AndroidPhoneMockup: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <div className="relative w-[280px] h-[572px] mx-auto">
        <div className="absolute inset-0 bg-gray-900 rounded-[40px]"></div>
        <div className="absolute top-[10px] left-[10px] right-[10px] bottom-[10px] bg-white rounded-[32px] overflow-hidden">
            <div className="absolute top-[8px] left-[20px] w-[12px] h-[12px] bg-gray-900 rounded-full z-10"></div>
            <div className="w-full h-full bg-white flex items-center justify-center">
                <div className="w-48 h-48 bg-green-100 rounded-full absolute top-12 right-[-24px] opacity-50"></div>
                <div className="w-24 h-24 bg-green-100 rounded-full absolute bottom-24 left-[-12px] opacity-50"></div>
                <div className="w-4 h-4 bg-green-300 rounded-full absolute top-32 left-8"></div>
                <div className="w-2 h-2 bg-green-300 rounded-full absolute top-48 right-16"></div>
                <div className="w-3 h-3 bg-green-300 rounded-full absolute bottom-24 left-20"></div>
            </div>
            {children}
        </div>
    </div>
);

export const IosMockup = ({ children }) => {
    return (
        <div className="relative w-[280px] h-[572px] mx-auto">
            {/* Phone body */}
            <div className="absolute inset-0 bg-gray-900 rounded-[40px]"></div>

            {/* Screen */}
            <div
                className="absolute top-[10px] left-[10px] right-[10px] bottom-[10px] bg-white rounded-[32px] overflow-hidden">
                {/* Dynamic Island */}
                <div
                    className="absolute top-[8px] left-1/2 transform -translate-x-1/2 w-[80px] h-[24px] bg-gray-900 rounded-full z-10"></div>

                {/* Decorative elements */}
                <div className="w-full h-full bg-white flex items-center justify-center">
                    <div className="w-48 h-48 bg-blue-100 rounded-full absolute top-12 right-[-24px] opacity-50"></div>
                    <div
                        className="w-24 h-24 bg-blue-100 rounded-full absolute bottom-24 left-[-12px] opacity-50"></div>
                    <div className="w-4 h-4 bg-blue-300 rounded-full absolute top-32 left-8"></div>
                    <div className="w-2 h-2 bg-blue-300 rounded-full absolute top-48 right-16"></div>
                    <div className="w-3 h-3 bg-blue-300 rounded-full absolute bottom-24 left-20"></div>
                </div>

                {/* Content */}
                {children}
            </div>
        </div>
    );
};