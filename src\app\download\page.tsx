'use client'

import React, { useEffect } from 'react';
import { isMobile, isIOS, isAndroid } from 'react-device-detect';
import { AndroidPhoneMockup, IosMockup } from './components/phoneMockup';
import { AppStoreButton } from './components/downloadButton';

export default function DownloadApp() {
    const showBothMockups = !isMobile;
    const showIOS = showBothMockups || isIOS;
    const showAndroid = showBothMockups || isAndroid;

    const playStoreAppLink = "market://details?id=com.xcelerate.xcelerate";
    const iosAppLink = "https://xcelerate.page.link/app";

    useEffect(() => {
        if (isMobile) {
            const confirmMessage = `Would you like to open the in ${isAndroid ? "Google Play" : "App Store"}?`;
            if (!window.confirm(confirmMessage)) return;

            if (isAndroid) {
                window.location.href = playStoreAppLink;
            } else {
                window.location.href = iosAppLink;
            }
        }
    }, []);

    return (
        <div className="relative h-dvh z-40 min-h-screen bg-gradient-to-b from-blue-100 to-blue-200 flex flex-col items-center justify-center p-4">
            <div className="flex flex-col md:flex-row items-center justify-center gap-8">
                {showAndroid && (
                    <AndroidPhoneMockup>
                        <AppStoreButton href={playStoreAppLink} platform="android" />
                    </AndroidPhoneMockup>
                )}
                {showIOS && (
                    <IosMockup>
                        <AppStoreButton href={iosAppLink} platform="ios" />
                    </IosMockup>
                )}
            </div>
        </div>
    );
}
