"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  But<PERSON>,
  Divider,
} from "@nextui-org/react";
import { useRouter } from "next/navigation";

interface CreateAccountOrCheckoutModalProps {
  isOpen: boolean;
  handleClose: () => void;
  openAddVehicleOnGuest: () => void;
}

export default function CreateAccountOrCheckoutModal({
  isOpen,
  handleClose,
  openAddVehicleOnGuest,
}: CreateAccountOrCheckoutModalProps) {
  const router = useRouter();

  const handleCreateAccount = () => {
    router.push("/auth/signup");
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose} 
      size="xl"
      placement="center"
    >
      <ModalContent>
        <ModalBody>
          <div className="min-h-[100px] flex justify-around items-center">
            <Button
              onPress={handleCreateAccount}
              className="text-sm bg-[#007AFF] text-white font-medium h-[35px] touch-manipulation"
            >
              Create account
            </Button>
            <Divider orientation="vertical" className="h-20 bg-[#C7C7C7]" />
            <Button
              onPress={openAddVehicleOnGuest}
              className="text-sm bg-[#007AFF] text-white font-medium h-[35px] touch-manipulation"
            >
              Guest Checkout
            </Button>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
