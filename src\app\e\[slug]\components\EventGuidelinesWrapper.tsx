"use client";

import React from "react";
import dynamic from "next/dynamic";

const EventGuidelines = dynamic(() => import("./EventGuidelines"), {
  ssr: false,
});

interface EventGuidelinesWrapperProps {
  orgDetails: any;
}

const EventGuidelinesWrapper: React.FC<EventGuidelinesWrapperProps> = ({
  orgDetails,
}) => {
  return <EventGuidelines orgSlug={orgDetails?.slug} />;
};

export default EventGuidelinesWrapper;
