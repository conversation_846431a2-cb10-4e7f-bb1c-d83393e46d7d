"use client";

import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import EventOverviewImage from "./EventOverviewImage";

const PhotoSwipeItem = dynamic(
  () => import('react-photoswipe-gallery').then(mod => {
    // Import the CSS when the component loads
    import('photoswipe/dist/photoswipe.css');
    return mod.Item;
  }),
  {
    ssr: false,
  }
);

interface EventOverviewGalleryProps {
  img: string;
  children: React.ReactNode;
}

const EventOverviewGallery: React.FC<EventOverviewGalleryProps> = ({ img, children }) => {
  const [dimensions, setDimensions] = useState({
    width: 600,
    height: 600,
  });

  useEffect(() => {
    if (img) {
      const loadImage = new window.Image();
      loadImage.onload = () => {
        setDimensions({
          width: loadImage.naturalWidth,
          height: loadImage.naturalHeight,
        });
      };
      loadImage.src = img;
    }
  }, [img]);

  return (
    <div className="w-[90vw] h-[90vw] md:w-[225px] md:h-[225px] xl:w-[340px] xl:h-[340px]">
      <PhotoSwipeItem
        original={img}
        thumbnail={img}
        width={dimensions.width}
        height={dimensions.height}
        cropped={false}
      >
        {({ ref, open }) => (
          <div ref={ref} onClick={open} className="block hover:cursor-pointer">
            {children}
          </div>
        )}
      </PhotoSwipeItem>
    </div>
  );
};

export default EventOverviewGallery; 