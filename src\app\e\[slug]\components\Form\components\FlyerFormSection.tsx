import React, { useState, useEffect, useRef } from "react";
import { Button } from "@nextui-org/react";
import FlyerCropModal from "./FlyerCropModal";
import FlyerInstructionsModal from "./FlyerInstructionsModal";
import BaseLabel from "@/components/BaseLabel";
import Image from "next/image";
import { useImageUpload } from "@/components/ImageSelector/useImageUpload";
import { processImageFile } from "@/lib/utils/heicConverter";
import {
  UseFormSetValue,
  UseFormGetValues,
  FieldErrors,
  UseFormClearErrors,
} from "react-hook-form";
import { toast } from "react-hot-toast";

interface FlyerFormSectionProps {
  overlayImage?: string;
  setValue: UseFormSetValue<any>;
  getValues: UseFormGetValues<any>;
  errors: FieldErrors<any>;
  clearErrors?: UseFormClearErrors<any>;
  isEdit?: boolean;
}

export const FlyerFormSection: React.FC<FlyerFormSectionProps> = ({
  overlayImage,
  setValue,
  getValues,
  errors,
  clearErrors,
  isEdit,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isInstructionsOpen, setIsInstructionsOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isCropped, setIsCropped] = useState(false);
  const [originalFileName, setOriginalFileName] = useState<
    string | undefined
  >();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const flyerError = errors?.["Event Flyer"];
  const hasError =
    flyerError?.type === "invalid_type" && flyerError?.message === "Required";

  useEffect(() => {
    const flyer = getValues("Event Flyer");
    if (flyer) {
      setCurrentImage(flyer);
      if (isEdit) {
        setIsCropped(true);
      }
    }
  }, [getValues]);

  const { handleImageSelect } = useImageUpload({
    fileSize: 9.5 * 1024 * 1024,
    onSuccess: (url) => {
      if (url) {
        setCurrentImage(url);
        setValue("Event Flyer", url);
        clearErrors && clearErrors("Event Flyer");
        // Don't set loading to false here - wait for both uploads
        setIsModalOpen(false);
        setIsCropped(true);
      }
    },
  });

  const { handleImageSelect: handleRawImageSelect } = useImageUpload({
    fileSize: 9.5 * 1024 * 1024,
    showSuccessToast: false, // Don't show success toast for raw image upload
    onSuccess: (url) => {
      if (url) {
        setValue("Event Flyer Raw", url);
        // Don't set loading to false here either - will be handled in handleSave
      }
    },
  });

  const handleSave = async (
    croppedImage: string,
    rawCroppedImage: string,
    originalFileName?: string
  ) => {
    if (!croppedImage || !rawCroppedImage) {
      toast.error("Please add the image.");
      return;
    }
    try {
      setIsUploading(true);

      // Prepare both images for upload
      const overlayResponse = await fetch(croppedImage);
      const overlayBlob = await overlayResponse.blob();
      const overlayFileType = overlayBlob.type || "image/jpeg";
      const validExtensions = ["jpg", "jpeg", "png", "gif", "webp"];
      const detectedExtension = overlayFileType.split("/")[1]?.toLowerCase();
      const fileExtension = validExtensions.includes(detectedExtension)
        ? detectedExtension
        : "jpg";
      const overlayFileName = originalFileName
        ? `${originalFileName}-overlay.${fileExtension}`
        : `flyer-image-overlay.${fileExtension}`;
      const overlayFile = new File([overlayBlob], overlayFileName, {
        type: overlayFileType,
      });

      const rawResponse = await fetch(rawCroppedImage);
      const rawBlob = await rawResponse.blob();
      const rawFileType = rawBlob.type || "image/jpeg";
      const rawFileName = originalFileName
        ? `${originalFileName}-raw.${fileExtension}`
        : `flyer-image-raw.${fileExtension}`;
      const rawFile = new File([rawBlob], rawFileName, {
        type: rawFileType,
      });

      // Upload both images in parallel and wait for both to complete
      await Promise.all([
        handleImageSelect(overlayFile),
        handleRawImageSelect(rawFile),
      ]);

      // Both uploads completed successfully
      setIsUploading(false);
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error(
        "Failed to upload image: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
      setIsUploading(false);
    }
  };

  const onRemoveImage = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setCurrentImage(null);
    setValue("Event Flyer", null);
    setValue("Event Flyer Raw", null);
    setIsModalOpen(false);
  };

  const handleOpenFilePickerFromInstructions = () => {
    setIsInstructionsOpen(false);
    // Delay to allow modal close animation to finish before opening file dialog
    setTimeout(() => {
      fileInputRef.current?.click();
    }, 0);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      toast.error("No file selected");
      return;
    }

    const file = files[0];
    if (!file) {
      toast.error("Invalid file selected");
      return;
    }

    // Validate file type
    const validImageTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/heic",
      "image/heif",
    ];
    if (!file.type || !validImageTypes.includes(file.type)) {
      toast.error(
        "Please upload a valid image file (JPEG, PNG, GIF, WebP, HEIC, or HEIF)"
      );
      return;
    }

    clearErrors && clearErrors("Event Flyer");

    try {
      // Process HEIC/HEIF files first (convert to JPEG if needed)
      const processedFile = await processImageFile(file);

      const fileName = file.name?.split(".")[0] || "flyer-image";
      setOriginalFileName(fileName);

      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result;
        if (typeof result === "string") {
          setCurrentImage(result);
          setIsModalOpen(true);
        } else {
          toast.error("Failed to read image file");
        }
      };
      reader.onerror = () => {
        toast.error("Failed to read image file");
      };
      reader.readAsDataURL(processedFile);
    } catch (error) {
      console.error("Error processing HEIC/HEIF file:", error);
      toast.error("Failed to process image. Please try a different format.");
    }
  };

  return (
    <div className="w-full mb-6">
      <div>
        <BaseLabel>Your Custom Flyer *</BaseLabel>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div
            className={`w-full mt-1 flex flex-col justify-center items-center border-1 border-dashed h-[120px] ${
              hasError ? "border-red-500" : "border-[#8A8A8A]"
            } rounded-lg p-4 text-center relative`}
          >
            {currentImage && isCropped ? (
              <>
                <div className="w-full h-full flex justify-center items-center relative">
                  <Image
                    src={currentImage}
                    alt="Flyer Preview"
                    fill
                    className="object-contain rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={onRemoveImage}
                    className="absolute top-[-8px] right-[-8px] w-6 h-6 flex items-center justify-center bg-white rounded-full shadow-sm"
                    aria-label="Remove image"
                  >
                    <Image
                      src="/delete-icon.svg"
                      alt="remove image"
                      width={12}
                      height={12}
                    />
                  </button>
                </div>
              </>
            ) : (
              <>
                <input
                  type="file"
                  accept=".jpeg,.jpg,.png,.gif,.webp,.heic,.heif"
                  onChange={handleFileChange}
                  className="hidden"
                  ref={fileInputRef}
                />
                <Button
                  onPress={() => setIsInstructionsOpen(true)}
                  className="relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-3 min-w-16 h-8 text-tiny gap-2 rounded-small [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none text-default-foreground data-[hover=true]:opacity-hover bg-white border-1 border-[#e3e3e3] shadow-sm"
                  size="sm"
                  variant="flat"
                >
                  Add Image
                </Button>
              </>
            )}
          </div>
        </div>
        {hasError && (
          <p className="text-red-500 text-sm mt-1">Please add your image</p>
        )}
      </div>
      <FlyerInstructionsModal
        isOpen={isInstructionsOpen}
        onClose={() => setIsInstructionsOpen(false)}
        onAddImage={handleOpenFilePickerFromInstructions}
      />
      {overlayImage && (
        <FlyerCropModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setCurrentImage(null);
            setOriginalFileName(undefined);
          }}
          onSave={handleSave}
          overlayImage={overlayImage}
          isUploading={isUploading}
          currentImage={currentImage}
          originalFileName={originalFileName}
        />
      )}
    </div>
  );
};
