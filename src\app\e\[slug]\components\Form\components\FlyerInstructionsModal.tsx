import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@nextui-org/react";
import Image from "next/image";
import { ChevronLeft } from "lucide-react";

interface FlyerInstructionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddImage: () => void;
}

const FlyerInstructionsModal: React.FC<FlyerInstructionsModalProps> = ({
  isOpen,
  onClose,
  onAddImage,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="md"
      placement="center"
      hideCloseButton
      classNames={{
        backdrop: "z-[1300]",
        wrapper: "z-[1301]",
        base: "rounded-[40px] md:rounded-[25px]",
      }}
    >
      <ModalContent className="py-2">
        <ModalHeader className="relative flex items-center justify-center">
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onPress={onClose}
            aria-label="Close"
            className="absolute left-2"
          >
            <ChevronLeft className="h-8 w-8" />
          </Button>
          <span className="text-[21px] font-medium">Flyer instructions</span>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-3 text-sm text-default-600 mb-6 mt-1">
            <div className="grid grid-cols-2 gap-8">
              <div className="flex flex-col items-center gap-4">
                <Image
                  src="/wrong-flyer.png"
                  alt="wrong flyer Autolnk"
                  width={320}
                  height={320}
                  className="w-full h-full object-cover rounded-lg"
                />
                <Image
                  src="/red-cross.svg"
                  alt="cross Autolnk"
                  width={26}
                  height={26}
                  className="w-[26px] h-[26px] object-cover"
                />
              </div>
              <div className="flex flex-col items-center gap-4 mt-[2px]">
                <Image
                  src="/wrong-flyer2.png"
                  alt="correct flyer Autolnk"
                  width={320}
                  height={320}
                  className="w-full h-full object-cover rounded-lg"
                />
                <Image
                  src="/green-tick.svg"
                  alt="tick Autolnk"
                  width={26}
                  height={26}
                  className="w-[26px] h-[26px] object-cover"
                />
              </div>
            </div>
            <p className="text-center !my-10 text-[20px] leading-[1.3]">
              A low quality image may lead to the <strong>rejection</strong> of
              your application
            </p>
            <div className="flex justify-center">
              <Button
                size="md"
                color="primary"
                onPress={onAddImage}
                className="bg-white hover:bg-white/80 shadow-sm border-1 border-gray-200 rounded-lg text-black font-medium text-[17px] py-2.5 px-4"
              >
                Add Image
              </Button>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default FlyerInstructionsModal;
