import React, { useEffect, useState, useRef } from "react";
import {
  Control,
  UseFormWatch,
  UseFormSetValue,
  UseFormClearErrors,
} from "react-hook-form";
import { Button } from "@nextui-org/react";
import { Avatar } from "@/assets/avatar";
import ProfilePictureModal from "./ProfilePictureModal";
import { useImageUpload } from "@/components/ImageSelector/useImageUpload";
import { processImageFile } from "@/lib/utils/heicConverter";
import toast from "react-hot-toast";
import { FormField } from "../types";

interface FormProfileImageUploadProps {
  control: Control<any>;
  watch: UseFormWatch<any>;
  setValue: UseFormSetValue<any>;
  field: FormField;
  errors?: any;
  clearErrors: UseFormClearErrors<any>;
  isModelOrMediaForm: boolean;
}

export const FormProfileImageUpload: React.FC<FormProfileImageUploadProps> = ({
  watch,
  setValue,
  field,
  errors,
  clearErrors,
  isModelOrMediaForm = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [currentImage, setCurrentImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (watch(field.id)) {
      if (isModelOrMediaForm) {
        setCurrentImage(watch(field.id)?.[0]);
      } else {
        setCurrentImage(watch(field.id).image);
      }
    }
  }, [watch, field.id, isModelOrMediaForm]);

  const { handleImageSelect } = useImageUpload({
    fileSize: 9.5 * 1024 * 1024,
    onSuccess: async (url) => {
      if (url) {
        const verifyUrlWithRetry = async (
          url: string,
          maxRetries = 3
        ): Promise<boolean> => {
          for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
              const response = await fetch(url, { method: "HEAD" });
              if (response.ok) {
                return true;
              }
              // If not last attempt, wait before retrying
              if (attempt < maxRetries - 1) {
                await new Promise((resolve) =>
                  setTimeout(resolve, Math.pow(2, attempt) * 1000)
                );
              }
            } catch (error) {
              console.error(
                `URL verification attempt ${attempt + 1} failed:`,
                error
              );
              if (attempt < maxRetries - 1) {
                await new Promise((resolve) =>
                  setTimeout(resolve, Math.pow(2, attempt) * 1000)
                );
              }
            }
          }
          return false;
        };

        try {
          const isUrlValid = await verifyUrlWithRetry(url);
          if (!isUrlValid) {
            throw new Error(
              "Image URL is not accessible after multiple attempts"
            );
          }

          setCurrentImage(url);
          if (isModelOrMediaForm) {
            setValue(field.id, [url]);
          } else {
            setValue(field.id, { image: url });
          }
          clearErrors(field.id);
          setIsUploading(false);
          setIsModalOpen(false);
        } catch (error) {
          console.error("Error verifying image URL:", error);
          toast.error("Failed to verify image upload. Please try again.");
          setIsUploading(false);
          setCurrentImage(null);
          if (isModelOrMediaForm) {
            setValue(field.id, []);
          } else {
            setValue(field.id, { image: null });
          }
        }
      } else {
        // Handle case when url is null (upload failed)
        console.error("Error uploading image: No URL returned");
        toast.error("Failed to upload image. Please try again.");
        setIsUploading(false);
        setCurrentImage(null);
        setValue(field.label, { image: null });
      }
    },
  });

  const handleImageSave = async (croppedImage: string) => {
    if (!croppedImage) {
      toast.error("Please add the image.");
      return;
    }
    try {
      setIsUploading(true);
      const response = await fetch(croppedImage);
      const blob = await response.blob();
      const file = new File([blob], "profile-image.jpg", {
        type: "image/jpeg",
      });
      await handleImageSelect(file);
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Failed to upload image. Please try again.");
      setIsUploading(false);
      setCurrentImage(null);
      setValue(field.label, { image: null });
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      toast.error("No file selected");
      return;
    }

    const file = files[0];
    if (!file) {
      toast.error("Invalid file selected");
      return;
    }

    try {
      // Process HEIC files first (convert to JPEG if needed)
      const processedFile = await processImageFile(file);

      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result;
        if (typeof result === "string") {
          setCurrentImage(result);
          setIsModalOpen(true);
        } else {
          toast.error("Failed to read image file");
        }
      };
      reader.onerror = () => {
        toast.error("Failed to read image file");
      };
      reader.readAsDataURL(processedFile);
    } catch (error) {
      console.error("Error processing HEIC file:", error);
      toast.error("Failed to process image. Please try a different format.");
    }
  };

  const hasError = isModelOrMediaForm
    ? errors?.[field.id]?.message || errors?.[field.id]?.type === "invalid_type"
    : errors?.[field.id]?.image?.message ||
      errors?.[field.id]?.image?.type === "invalid_type";

  return (
    <div className="flex flex-col gap-2">
      <div className="text-sm font-medium text-gray-700 mb-2">
        {field.label} {field.required && "*"}
      </div>
      <div
        className={`relative flex items-center justify-center w-[120px] h-[120px] bg-[#CBD2D9] border ${
          hasError ? "border-red-500" : "border-dashed border-[#0000008A]"
        } rounded-full overflow-hidden`}
      >
        <div className="absolute z-0 flex items-end justify-center w-full h-full">
          {currentImage ? (
            <img
              src={currentImage}
              alt="Profile"
              className="w-full h-full object-cover"
            />
          ) : (
            <Avatar className="w-[100px] h-[100px]" />
          )}
        </div>
        <div className="">
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
            ref={fileInputRef}
          />
          <Button
            onPress={() => fileInputRef.current?.click()}
            className="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-3 min-w-16 h-8 text-tiny gap-2 rounded-small [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none text-default-foreground data-[hover=true]:opacity-hover bg-white border-1 border-[#e3e3e3] shadow-sm"
            size="sm"
            variant="flat"
          >
            {currentImage ? "Change" : "Add Image"}
          </Button>
        </div>
      </div>
      {hasError && (
        <p className="text-sm text-red-500 mt-1">
          {errors[field.id]?.image?.message ||
            errors[field.id]?.message ||
            "This field is required"}
        </p>
      )}
      <ProfilePictureModal
        isOpen={isModalOpen}
        onClose={() => {
          if (!isUploading) {
            setIsModalOpen(false);
            setCurrentImage(null);
          }
        }}
        onSave={handleImageSave}
        isUploading={isUploading}
        currentImage={currentImage}
      />
    </div>
  );
};
