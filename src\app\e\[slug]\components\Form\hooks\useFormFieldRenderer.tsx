import {
  Control,
  UseFormWatch,
  <PERSON>Errors,
  UseFormSetValue,
  UseFormGetValues,
  UseFormClearErrors,
} from "react-hook-form";
import { Option } from "@/components/VirtualisedSelect/BaseVirtualisedSelect";
import FormControlledInput from "@/components/Input/FormControlledInput";
import FormControlledSelect from "@/components/Select/FormControlledSelect";
import FormControlledRadio from "@/components/Radio/FormControlledRadio";
import FormControlledCheckbox from "@/components/Checkbox/FormControlledCheckbox";
import MultipleFileUpload from "@/components/FileUpload/MultipleFileUpload";
import { VehicleFormSection } from "../components/VehicleFormSection";
import { FormProfileImageUpload } from "../components/FormProfileImageUpload";
import { FlyerFormSection } from "../components/FlyerFormSection";
import { TeamClubNameField } from "../components/TeamClubNameField";
import { FormField } from "../types";
import { But<PERSON> } from "@/components/ui/button";
import { MODAL_TYPES } from "@/app/events/constants";
import { cn } from "@/lib/utils";

interface UseFormFieldRendererReturn {
  renderFormField: ({
    field,
    ticketType,
    flyer,
  }: {
    field: FormField;
    ticketType: string;
    flyer?: string;
  }) => JSX.Element | null;
}

interface UseFormFieldRendererProps {
  isEdit: boolean;
  control: Control<any>;
  watch: UseFormWatch<any>;
  errors: FieldErrors<any>;
  allTypes: any[];
  makeOptions: Option[];
  makesLoading: boolean;
  loadMakeOptions: (
    inputValue: string,
    callback: (options: Option[]) => void
  ) => void;
  handleMakeChange: (
    option: Option | null,
    onChange: (value: string) => void
  ) => void;
  ticketId?: string;
  setValue: UseFormSetValue<any>;
  getValues: UseFormGetValues<any>;
  setIsFormLoading: (isFormLoading: boolean) => void;
  currentForm?: { formData?: FormField[] };
  clearErrors: UseFormClearErrors<any>;
  toggleModal?: (modal: string) => void;
  onTeamCreated?: (teamName: string) => void;
}

export const useFormFieldRenderer = ({
  control,
  watch,
  errors,
  allTypes,
  makeOptions,
  makesLoading,
  loadMakeOptions,
  handleMakeChange,
  ticketId,
  setValue,
  getValues,
  setIsFormLoading,
  currentForm,
  clearErrors,
  isEdit,
  toggleModal,
  onTeamCreated,
}: UseFormFieldRendererProps): UseFormFieldRendererReturn => {
  const labelClassName = "text-sm font-medium text-gray-700";

  const transformedTypes = allTypes?.map((type) => ({
    label: type?.name,
    value: type?.id,
  }));

  const renderFormField = ({
    field,
    ticketType,
    flyer,
  }: {
    field: FormField;
    ticketType: string;
    flyer?: string;
  }): JSX.Element | null => {
    const renderField = () => {
      switch (field.type) {
        case "text":
          if (field.label === "Team / Club name") {
            return (
              <TeamClubNameField
                key={field.id}
                field={field}
                control={control}
                watch={watch}
                setValue={setValue}
                errors={errors}
                toggleModal={toggleModal || (() => {})}
                labelClassName={labelClassName}
                onTeamCreated={onTeamCreated}
                clearErrors={clearErrors}
              />
            );
          }

          return (
            <div
              className={cn(
                field.label === "Instagram" &&
                  ticketType === "vehicle_registration" &&
                  "grid md:grid-cols-2 gap-4"
              )}
            >
              <FormControlledInput
                key={field.id}
                control={control}
                name={field.id}
                label={field.label}
                isRequired={field.required}
                classNames={{
                  base: "w-full",
                }}
                labelClassName={labelClassName}
              />
            </div>
          );

        case "select":
          return (
            <FormControlledSelect
              key={field.id}
              control={control}
              name={field.id}
              label={field.label}
              isRequired={field.required}
              options={field.options || []}
              classNames={{
                base: "w-full",
                trigger: "bg-default-100",
              }}
              labelClassName={labelClassName}
            />
          );

        case "radio":
          return (
            <FormControlledRadio
              key={field.id}
              control={control}
              name={field.id}
              label={field.label}
              isRequired={field.required}
              options={field.options || []}
              classNames={{
                base: "w-full",
              }}
              labelClassName={labelClassName}
            />
          );

        case "checkboxes":
          return (
            <FormControlledCheckbox
              key={field.id}
              control={control}
              name={field.id}
              label={field.label}
              isRequired={field.required}
              options={field.options || []}
              classNames={{
                base: "w-full",
              }}
              labelClassName={labelClassName}
            />
          );

        case "file_upload":
          const isModelOrMediaForm =
            ticketType === "model_registration" ||
            ticketType === "media_registration";

          const isProfileImageRequiredForm =
            ticketType === "vendor_booth" || isModelOrMediaForm;

          const isProfileImage =
            field.label === "Profile image for the company" ||
            field.label ===
              "Profile image (displayed on the event details page)";

          if (isProfileImageRequiredForm && isProfileImage) {
            return (
              <FormProfileImageUpload
                key={field.id}
                control={control}
                watch={watch}
                setValue={setValue}
                field={field}
                errors={errors}
                clearErrors={clearErrors}
                isModelOrMediaForm={isModelOrMediaForm}
              />
            );
          }

          return (
            <MultipleFileUpload
              key={field.id}
              control={control}
              name={field.id}
              label={field.label}
              isRequired={field.required}
              maxFiles={field.fileSettings?.maxFiles || 1}
              acceptedFileTypes={field.fileSettings?.fileTypes || ["image"]}
              allowSpecificTypes={
                field.fileSettings?.allowSpecificTypes || false
              }
              ticketId={ticketId}
              setIsFormLoading={setIsFormLoading}
            />
          );

        case "vehicle_info":
          return (
            <VehicleFormSection
              key={field.id}
              fieldId={"vehicle"}
              control={control}
              watch={watch}
              errors={errors}
              vehicleTypes={transformedTypes}
              makeOptions={makeOptions}
              makesLoading={makesLoading}
              loadMakeOptions={loadMakeOptions}
              handleMakeChange={handleMakeChange}
              ticketId={ticketId}
              setValue={setValue}
              setIsFormLoading={setIsFormLoading}
            />
          );

        default:
          return null;
      }
    };

    return (
      <>
        {renderField()}
        {flyer &&
          currentForm?.formData &&
          currentForm.formData.length > 0 &&
          field === currentForm.formData[currentForm.formData.length - 1] && (
            <FlyerFormSection
              getValues={getValues}
              setValue={setValue}
              overlayImage={flyer}
              errors={errors}
              clearErrors={clearErrors}
              isEdit={isEdit}
            />
          )}
      </>
    );
  };

  return { renderFormField };
};
