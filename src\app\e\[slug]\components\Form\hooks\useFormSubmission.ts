import { useCallback } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { clearActiveFormTicket, clearEventTickets, clearSavedFormData, clearSelectedVariantIds, setSavedFormData } from "@/lib/redux/slices/events/eventSlice";
import toast from "react-hot-toast";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import { useAddToCartMutation, useUpdateQuantityMutation } from "@/lib/redux/slices/cart/cartApi";
import { CART_ERRORS, CART_STORAGE_KEYS } from "@/lib/utils/constants";
import { handleInvalidCartToken } from "@/app/cart/utils/cartUtils";
import { useSessionData } from "@/lib/hooks/useSession";
import { FormField } from "../types";

interface VehicleData {
  vehicleType: string;
  year: string;
  make: string;
  customMakeName: string;
  modelName: string;
  modificationText: string;
  vehicleImages: Array<{
    image: File | null;
    imageUrl: string;
    id: string;
    type: string;
    tag: string;
  }>;
}

interface FormSubmissionData {
  vehicle?: VehicleData;
  [key: string]: any;
}

export const useFormSubmission = (
  formData: Record<number, any>,
  currentFormIndex: number,
  forms: any[],
  currentForm: any,
  selectedTicket: any,
  activeFormTicket: any,
  savedFormData: Record<string, any>,
  setCurrentFormIndex: (index: number) => void,
  setFormData: (data: Record<number, any>) => void,
  isEdit: boolean,
  reset: () => void,
  selectedVariantIds: string[],
  waivers: any,
  isWhitelabel: boolean,
  slug: string,
  lineId: string | undefined,
) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [addToCart] = useAddToCartMutation();
  const [updateQuantity] = useUpdateQuantityMutation();

  const isPaymentLink = activeFormTicket?.paymentOption === "payment_link";

  const cartToken = localStorage.getItem(CART_STORAGE_KEYS.CART_TOKEN);
  const { data: sessionData } = useSessionData();

  const handleFormSubmit = useCallback(
    async (data: FormSubmissionData, onClose: () => void) => {
      try {
        // `data` here is the output from react-hook-form, keyed by field.id / "vehicle" / "Event Flyer"
        const processedDataPayload: Record<string, any> = {};

        // Populate from currentForm.formData (fields keyed by field.id or "vehicle")
        currentForm?.formData?.forEach((field: FormField) => {
          const key = field.type === "vehicle_info" ? "vehicle" : field.id;
          const valueFromInput = data[key];

          if (field.type === "vehicle_info") {
            const vehicleValue = (valueFromInput || {}) as Partial<VehicleData>;
            processedDataPayload[key] = {
              vehicleType: vehicleValue.vehicleType || "",
              year: vehicleValue.year || "",
              make: vehicleValue.make || "",
              customMakeName: vehicleValue.customMakeName || "",
              modelName: vehicleValue.modelName || "",
              modificationText: vehicleValue.modificationText || "",
              vehicleImages: vehicleValue.vehicleImages || [{ image: null }, { image: null }],
            };
          } else {
            processedDataPayload[key] = valueFromInput; // Assign value as is (string, array, object, undefined)
          }
        });

        // Explicitly add "Event Flyer" if it exists in the input `data`
        if (data.hasOwnProperty("Event Flyer")) {
          processedDataPayload["Event Flyer"] = data["Event Flyer"];
        }

        // Explicitly add "Event Flyer Raw" if it exists in the input `data`
        if (data.hasOwnProperty("Event Flyer Raw")) {
          processedDataPayload["Event Flyer Raw"] = data["Event Flyer Raw"];
        }

        const updatedFormData = {
          ...formData,
          [currentFormIndex]: {
            formId: currentForm.id,
            formType: currentForm.ticketType,
            formName: currentForm.name,
            templateId: currentForm.templateId,
            data: processedDataPayload, // Use the correctly populated data
          },
        };

        setFormData(updatedFormData);

        const ticketKey = `ticket_${selectedTicket?.id || activeFormTicket?.id}`;

        dispatch(
          setSavedFormData({
            ...savedFormData,
            [ticketKey]: updatedFormData,
          })
        );

        if (currentFormIndex === forms.length - 1) {
          let response;
          let retryCount = 0;
          const maxRetries = 2;

          const formResponseData = Object.entries(updatedFormData).map(([formIndexStr, formEntryValue]: [string, any]) => {
            const formIndex = parseInt(formIndexStr, 10);
            const currentFormDefinition = forms[formIndex]; // Form definition for this specific form
            const dataKeyedByIds = formEntryValue.data || {}; // This is { fieldId: value, ... } or { "vehicle": vehicleData }

            const dataKeyedByLabels: Record<string, any> = {};

            if (currentFormDefinition && currentFormDefinition.formData) {
              currentFormDefinition.formData.forEach((fieldDef: FormField) => {
                const idKey = fieldDef.type === "vehicle_info" ? "vehicle" : fieldDef.id;
                const labelKey = fieldDef.type === "vehicle_info" ? "vehicle" : fieldDef.label;

                if (dataKeyedByIds && dataKeyedByIds.hasOwnProperty(idKey)) {
                  dataKeyedByLabels[labelKey] = dataKeyedByIds[idKey];
                }
              });
            }

            // Handle "Event Flyer" separately as it's not in formData array
            if (dataKeyedByIds && dataKeyedByIds.hasOwnProperty("Event Flyer")) {
              dataKeyedByLabels["Event Flyer"] = dataKeyedByIds["Event Flyer"];
            }

            // Handle "Event Flyer Raw" separately as it's not in formData array
            if (dataKeyedByIds && dataKeyedByIds.hasOwnProperty("Event Flyer Raw")) {
              dataKeyedByLabels["Event Flyer Raw"] = dataKeyedByIds["Event Flyer Raw"];
            }
            
            // Exclude 'data' from top-level snake_case conversion initially
            const { data, ...otherFormEntryProps } = formEntryValue; 
            const snakedFormEntryProps = keysToSnake(otherFormEntryProps);

            let finalDataPayload;
            if (formEntryValue.formType === "vehicle_registration" && dataKeyedByLabels.vehicle) {
              const { vehicle, ...otherDataByLabels } = dataKeyedByLabels;
              finalDataPayload = {
                ...otherDataByLabels,
                vehicle: keysToSnake(vehicle),
              };
            } else {
              finalDataPayload = dataKeyedByLabels;
            }

            return {
              ...snakedFormEntryProps,
              data: finalDataPayload,
            };
          });

          const cartBody = {
            type: "event_ticket",
            id: isEdit? lineId : selectedTicket?.id || activeFormTicket?.id,
            quantity: 1,
            ...(selectedVariantIds?.length > 0 && {
              selected_variant_ids: selectedVariantIds,
            }),
            form_response_data: formResponseData,
            ...(waivers?.length > 0 && {
              waiver_response_data: waivers
                .filter((waiver: any) => 
                  waiver.waiverTickets.includes(selectedTicket?.id || activeFormTicket?.id)
                )
                .map((waiver: any) => ({
                  waiverUrl: waiver.waiverUrl,
                  waiverSignature: waiver.waiverSignature || "",
                })),
            }),
          };

          while (retryCount <= maxRetries) {
            try {
              if (isEdit) {
                response = await updateQuantity(cartBody).unwrap();
              } else {
                response = await addToCart(cartBody).unwrap();
              }

              if (response?.error?.data?.error === CART_ERRORS.INVALID_CART_TOKEN || response?.error?.status === CART_ERRORS.PARSING_ERROR) {
                handleInvalidCartToken();
                return handleFormSubmit(data, onClose);
              }

              if (!sessionData?.user && !cartToken) {
                const checkoutData = response?.checkout as any;
                const token = checkoutData?.token;
                if (token) {
                  localStorage.setItem(CART_STORAGE_KEYS.CART_TOKEN, token);
                }
              }

              toast.success("Forms submitted successfully!");
              onClose();
              reset();
              if(isEdit) {
                window.location.reload();
              }
              
              if (isWhitelabel && isPaymentLink) {
                router.push(`/e/${slug}?oid=confirm-registration`);
              } else if (isPaymentLink) {
                router.push(`/confirm-registration`);
              } else if (isWhitelabel) {
                router.push(`/e/${slug}?oid=contact`);
              } else {
                router.push("/cart");
              }
              return;
            } catch (error) {
              retryCount++;
              if (retryCount > maxRetries) {
                console.error(`Failed to ${isEdit ? 'update' : 'add to'} cart after ${maxRetries} retries:`, error);
                throw error;
              }
              await new Promise((resolve) => setTimeout(resolve, 1000));
            } finally {
              dispatch(clearEventTickets());
              dispatch(clearActiveFormTicket());
              dispatch(clearSavedFormData());
              dispatch(clearSelectedVariantIds());
            }
          }
        } else {
          setCurrentFormIndex(currentFormIndex + 1);
          reset();
        }
      } catch (error) {
        onClose();
        reset();
        toast.error(
          error instanceof Error
            ? error.message
            : "Something went wrong. Please try again."
        );
      }
    },
    [
      currentForm,
      currentFormIndex,
      formData,
      forms.length,
      selectedTicket?.id,
      activeFormTicket?.id,
      savedFormData,
      dispatch,
      setCurrentFormIndex,
      setFormData,
      isEdit,
      addToCart,
      updateQuantity,
      router,
    ]
  );

  return {
    handleFormSubmit,
  };
}; 