import { useCallback, useState, useEffect } from "react";
import { useGetVehicleMakesListQuery, useGetVehicleTypesListQuery } from "@/lib/redux/slices/vehicles/vehiclesApi";
import { Option } from "@/components/VirtualisedSelect/BaseVirtualisedSelect";
import { UseFormWatch, UseFormSetValue } from "react-hook-form";

export const useVehicleForm = (
  watch: UseFormWatch<any>,
  setValue: UseFormSetValue<any>
) => {
  const [makeOptions, setMakeOptions] = useState<Option[]>([]);
  const { data: allTypes } = useGetVehicleTypesListQuery({});
  const { data: allMakes, isLoading: makesLoading } = useGetVehicleMakesListQuery({});

  const loadMakeOptions = useCallback(
    (inputValue: string, callback: (options: Option[]) => void) => {
      const sanitizedInput = inputValue?.toLowerCase()?.trim() ?? "";
      const filteredOptions = inputValue
        ? allMakes
            ?.filter((option: { name?: string; id?: string }) =>
              option?.name?.toLowerCase()?.includes(sanitizedInput)
            )
            ?.map((make) => ({
              label: make?.name ?? "",
              value: make?.id ?? "",
            }))
        : allMakes?.map((make) => ({
            label: make?.name ?? "",
            value: make?.id ?? "",
          }));
      callback(filteredOptions || []);
    },
    [allMakes]
  );

  const handleMakeChange = useCallback(
    (selectedOption: Option | null, onChange: (value: string) => void) => {
      onChange(selectedOption?.value || "");
    },
    []
  );

  useEffect(() => {
    if (allMakes) {
      const newMakeOptions = allMakes.map((make) => ({
        label: make.name,
        value: make.id,
      }));
      setMakeOptions(newMakeOptions);
    }
  }, [allMakes]);

  return {
    makeOptions,
    allTypes,
    makesLoading,
    loadMakeOptions,
    handleMakeChange,
  };
}; 