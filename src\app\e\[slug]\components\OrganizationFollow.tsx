"use client";

import React, { useCallback } from "react";
import { useSessionData } from "@/lib/hooks/useSession";
import {
  useFollowOrganizationMutation,
  useGetOrganizationFollowingStatusQuery,
  useUnFollowOrganizationMutation,
} from "@/lib/redux/slices/organization/api";
import { TOASTS } from "@/lib/utils/constants";
import { handleApiError } from "@/lib/utils/errorUtils";
import { Button } from "@nextui-org/react";
import Image from "next/image";
import toast from "react-hot-toast";
import Link from "next/link";

const OrganizationFollow = ({ orgSlug }: { orgSlug: string | undefined }) => {
  const { data: session } = useSessionData();
  const { data, isLoading, isUninitialized, refetch } =
    useGetOrganizationFollowingStatusQuery(
      { orgSlug },
      {
        refetchOnMountOrArgChange: true,
        skip: !orgSlug || !session?.user,
      }
    );

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetch = useCallback(() => {
    if (!isUninitialized) {
      refetch();
    }
  }, [isUninitialized, refetch]);

  const [followOrganization, { isLoading: isFollowLoading }] =
    useFollowOrganizationMutation();
  const [unFollowOrganization, { isLoading: isUnFollowLoading }] =
    useUnFollowOrganizationMutation();

  const handleOnClick = async () => {
    try {
      if (!session?.user) {
        return;
      }
      if (data?.isFollowing) {
        await unFollowOrganization({ orgSlug }).unwrap();
        toast.success(TOASTS.ORG_UNFOLLOWED);
      } else {
        await followOrganization({ orgSlug }).unwrap();
        toast.success(TOASTS.ORG_FOLLOWED);
      }
      safeRefetch();
    } catch (error: any) {
      console.error("Error following organization:", error);
      handleApiError(error, TOASTS.ERROR);
    }
  };

  const renderButton = () => {
    return data?.isFollowing ? (
      <Button
        radius="sm"
        size="sm"
        className="xl:px-4 font-semibold h-[2rem] text-gray-800 bg-gray-200"
        onPress={handleOnClick}
        isLoading={isFollowLoading || isUnFollowLoading || isLoading}
        startContent={
          <Image src="/checkMark.svg" alt="CheckMark" width={18} height={18} />
        }
      >
        Following
      </Button>
    ) : (
      <Button
        color="primary"
        radius="sm"
        size="sm"
        className="xl:px-8 h-[2rem] font-semibold text-gray-50 bg-[#007aff]"
        onPress={handleOnClick}
        isLoading={isFollowLoading || isUnFollowLoading || isLoading}
      >
        Follow
      </Button>
    );
  };

  return (
    <div>
      {session?.user ? (
        renderButton()
      ) : (
        <Link href="/auth/signin">{renderButton()}</Link>
      )}
    </div>
  );
};

export default OrganizationFollow;
