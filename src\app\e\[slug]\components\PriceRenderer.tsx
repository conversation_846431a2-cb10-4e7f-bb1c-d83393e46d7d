import { CustomTicketTemplate } from "@/app/events/types";

export const PriceRenderer = ({
  customTicketTemplate,
  isQuantityAvailable,
}: any) => {
  const calculatePrices = (template: CustomTicketTemplate) => {
    const isDiscount =
      customTicketTemplate?.priceChangeInfo?.priceChangeBadgeText ===
      "DISCOUNT";
    const scheduledPrice = isDiscount
      ? Number(customTicketTemplate?.priceChangeInfo?.oldPrice || 0)
      : Number(template?.priceSchedule?.scheduledPrice || 0);
    const basePrice = Number(template?.ticketPrice || 0);
    return { scheduledPrice, basePrice };
  };

  const { scheduledPrice } = calculatePrices(customTicketTemplate);

  const formatPrice = (price: number) => price.toFixed(2);
  const showPriceBadge = scheduledPrice !== 0;

  const formatTicketPrice = (price: string) => {
    try {
      const priceParts = price.split(".");
      return (
        <div>
          <span className="text-xl font-semibold">${priceParts?.[0]}</span>
          <span className="font-semibold">.{priceParts?.[1]}</span>
        </div>
      );
    } catch (error) {
      return <p className="text-lg font-medium">${price}</p>;
    }
  };
  return (
    <div>
      <div className={`flex flex-col items-end text-right text-[#12181C]`}>
        <div className="flex gap-x-2 items-center">
          {showPriceBadge && (
            <p
              style={{ textDecoration: "line-through" }}
              className="text-base font-medium text-[#6B7280]"
            >
              ${formatPrice(scheduledPrice)}
            </p>
          )}
          {isQuantityAvailable ? (
            <p className="hidden lg:block text-lg font-bold">
              ${formatPrice(Number(customTicketTemplate?.ticketPrice))}
            </p>
          ) : (
            <div className="flex gap-x-4 items-center">
              <p className="hidden lg:block text-[14px] text-[#EE1212]">
                Sold Out
              </p>
              <p className="hidden line-through lg:block text-lg font-bold text-[#676767]">
                ${formatPrice(Number(customTicketTemplate?.ticketPrice))}
              </p>
            </div>
          )}
          {isQuantityAvailable ? (
            <div className="lg:hidden">
              {formatTicketPrice(customTicketTemplate?.ticketPrice)}
            </div>
          ) : (
            <div className="lg:hidden flex gap-x-2 items-center">
              <p className="text-[14px] text-[#EE1212]">Sold Out</p>
              <p className=" line-through font-bold text-[#676767]">
                {formatTicketPrice(customTicketTemplate?.ticketPrice)}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
