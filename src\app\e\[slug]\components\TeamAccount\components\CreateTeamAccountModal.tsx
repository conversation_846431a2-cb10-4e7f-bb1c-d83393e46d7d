"use client";

import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { <PERSON><PERSON> } from "@nextui-org/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { MODAL_TYPES } from "@/app/events/constants";
import {
  useTeamAccountAvailability,
  useOtpTimer,
  useTeamAccountCreation,
  TeamAccountFormData,
} from "../hooks";
import TeamAccountForm from "./TeamAccountForm";
import OtpVerificationForm from "./OtpVerificationForm";
import ErrorDisplay from "./ErrorDisplay";
import type { ModalType } from "../../hooks/useModalState";

const teamAccountSchema = z.object({
  teamName: z
    .string()
    .min(1, "Team name is required")
    .min(2, "Team name must be at least 2 characters"),
  username: z
    .string()
    .min(1, "Username is required")
    .min(3, "Username must be at least 3 characters")
    .regex(/^[a-z0-9_.]+$/, {
      message:
        "Username can only contain lowercase letters, numbers, underscores, and dots",
    }),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z
    .string()
    .min(1, "Password is required")
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])(?=.*[0-9]).{8,}$/, {
      message:
        "Password must contain at least 8 characters, 1 uppercase letter, 1 lowercase letter, and 1 special character",
    }),
  profileImage: z
    .string({
      required_error: "Profile image is required",
      invalid_type_error: "Profile image is required",
    })
    .min(1, "Profile image is required"),
});

interface CreateTeamAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  toggleModal: (modal: ModalType) => void;
  onTeamCreated?: (teamName: string) => void;
}

const CreateTeamAccountModal: React.FC<CreateTeamAccountModalProps> = ({
  isOpen,
  onClose,
  toggleModal,
  onTeamCreated,
}) => {
  const [formStep, setFormStep] = useState<1 | 2>(1);
  const [persistedFormData, setPersistedFormData] =
    useState<TeamAccountFormData | null>(null);
  const [otp, setOtp] = useState<string>("");
  const [isFormLoading, setIsFormLoading] = useState(false);

  const { canResendOtp, timeLeft, startCooldown, resetTimer } = useOtpTimer();
  const {
    handleGenerateOtp,
    handleResendOtp,
    createTeamAccount,
    isFormLoading: isCreationLoading,
    isGeneratingOtp,
    error,
    setError,
  } = useTeamAccountCreation();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
  } = useForm<TeamAccountFormData>({
    resolver: zodResolver(teamAccountSchema),
    mode: "onSubmit",
    defaultValues: {
      teamName: "",
      username: "",
      email: "",
      password: "",
      profileImage: "",
    },
  });

  const email = watch("email");
  const username = watch("username");

  const {
    isEmailAvailable,
    isUsernameAvailable,
    isCheckingEmail,
    isCheckingUsername,
  } = useTeamAccountAvailability({
    email,
    username,
    emailError: errors.email,
    usernameError: errors.username,
  });

  const isFormValid =
    Object.keys(errors).length === 0 &&
    isEmailAvailable !== false &&
    isUsernameAvailable !== false &&
    email &&
    username &&
    watch("teamName") &&
    watch("password") &&
    watch("profileImage");

  const handleGenerateOtpStep = async (data: TeamAccountFormData) => {
    const result = await handleGenerateOtp(data.email);
    if (result.success) {
      setPersistedFormData(data);
      setFormStep(2);
      startCooldown(30);
    }
  };

  const handleResendOtpStep = async () => {
    if (!persistedFormData) return;

    const result = await handleResendOtp(persistedFormData.email);
    if (result.success) {
      startCooldown(30);
    }
  };

  const handleCreateTeamAccountStep = async () => {
    if (!persistedFormData || otp.length !== 6) return;

    const result = await createTeamAccount(persistedFormData, otp);
    if (result.success) {
      if (onTeamCreated) {
        onTeamCreated(persistedFormData.teamName);
      }
      if (window && (window as any).__teamCreatedCallback) {
        (window as any).__teamCreatedCallback(persistedFormData.teamName);
        delete (window as any).__teamCreatedCallback;
      }
      toggleModal(MODAL_TYPES.TICKET_FORM);
      handleCloseModal();
    }
  };

  const onSubmit = async (data: TeamAccountFormData) => {
    if (formStep === 1) {
      await handleGenerateOtpStep(data);
    } else {
      await handleCreateTeamAccountStep();
    }
  };

  const handleCloseModal = () => {
    onClose();
    reset();
    setFormStep(1);
    setPersistedFormData(null);
    setOtp("");
    setError(null);
    setIsFormLoading(false);
    resetTimer();
  };

  const handleCancel = () => {
    toggleModal(MODAL_TYPES.TICKET_FORM);
    handleCloseModal();
  };

  const handleBackFromOtp = () => {
    setFormStep(1);
    setOtp("");
    setError(null);
  };

  const isLoading =
    isSubmitting || isFormLoading || isCreationLoading || isGeneratingOtp;
  const isStep1Disabled = !isFormValid || isCheckingEmail || isCheckingUsername;
  const isStep2Disabled = otp.length !== 6;

  return (
    <Dialog
      open={isOpen}
      onClose={handleCloseModal}
      disableScrollLock={false}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "16px",
          overflow: "hidden",
        },
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogTitle
          sx={{
            background: "#F3F3F3",
            padding: "10px 16px",
            "& + .MuiDialogContent-root": {
              borderTop: "1px solid #E3E3E3",
              borderBottom: "1px solid #E3E3E3",
            },
          }}
        >
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              {formStep === 2 && (
                <button
                  type="button"
                  onClick={handleBackFromOtp}
                  className="text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
              )}
              <p className="font-sFPro text-[#303030] font-[500] text-[17px]">
                {formStep === 1 ? "Create Team Account" : "Verify Email"}
              </p>
            </div>
          </div>
        </DialogTitle>

        <DialogContent
          dividers
          sx={{
            maxHeight: "calc(100vh - 16rem)",
            overflowY: "auto",
            borderColor: "var(--nextui-border-color)",
          }}
        >
          <div className="py-2">
            {formStep === 1 ? (
              <TeamAccountForm
                control={control}
                errors={errors}
                email={email}
                username={username}
                isEmailAvailable={isEmailAvailable}
                isUsernameAvailable={isUsernameAvailable}
                isCheckingEmail={isCheckingEmail}
                isCheckingUsername={isCheckingUsername}
                setIsFormLoading={setIsFormLoading}
              />
            ) : (
              <OtpVerificationForm
                email={persistedFormData?.email || ""}
                otp={otp}
                setOtp={setOtp}
                canResendOtp={canResendOtp}
                timeLeft={timeLeft}
                isFormLoading={isCreationLoading}
                onResendOtp={handleResendOtpStep}
              />
            )}

            <ErrorDisplay error={error} />
          </div>
        </DialogContent>

        <DialogActions sx={{ padding: "12px" }}>
          <div className="flex gap-2 justify-end w-full mb-1 md:mb-0">
            <Button
              type="button"
              onPress={handleCancel}
              variant="bordered"
              className="border-1 font-semibold"
              size="sm"
              isDisabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              size="sm"
              isDisabled={
                isLoading ||
                (formStep === 1 && isStep1Disabled) ||
                (formStep === 2 && isStep2Disabled)
              }
              isLoading={isLoading}
            >
              {formStep === 1 ? "Continue" : "Create Account"}
            </Button>
          </div>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default CreateTeamAccountModal;
