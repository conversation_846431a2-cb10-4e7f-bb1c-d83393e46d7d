import React from "react";
import { Control, FieldErrors } from "react-hook-form";
import FormControlledInput from "@/components/Input/FormControlledInput";
import FormControlledImage from "@/components/ImageSelector/FormControlledImage";
import Image from "next/image";
import { TeamAccountFormData } from "../hooks/useTeamAccountCreation";

interface AvailabilityFeedbackProps {
  value: string;
  error?: any;
  isChecking: boolean;
  isAvailable: boolean | null;
  type: "email" | "username";
}

const AvailabilityFeedback: React.FC<AvailabilityFeedbackProps> = ({
  value,
  error,
  isChecking,
  isAvailable,
  type,
}) => {
  if (!value || error) return null;

  return (
    <>
      {isChecking && (
        <p className="text-xs text-gray-400 mt-1">
          Checking {type} availability...
        </p>
      )}
      {!isChecking && isAvailable === false && (
        <p className="text-xs text-red-500 mt-1">
          {type === "email"
            ? "This email is already registered. Please use a different email."
            : "This username is already taken. Please choose a different one."}
        </p>
      )}
      {!isChecking && isAvailable === true && (
        <p className="text-xs text-green-500 mt-1">
          {type === "email" ? "Email" : "Username"} is available!
        </p>
      )}
    </>
  );
};

interface TeamAccountFormProps {
  control: Control<TeamAccountFormData>;
  errors: FieldErrors<TeamAccountFormData>;
  email: string;
  username: string;
  isEmailAvailable: boolean | null;
  isUsernameAvailable: boolean | null;
  isCheckingEmail: boolean;
  isCheckingUsername: boolean;
  setIsFormLoading: (loading: boolean) => void;
}

const TeamAccountForm: React.FC<TeamAccountFormProps> = ({
  control,
  errors,
  email,
  username,
  isEmailAvailable,
  isUsernameAvailable,
  isCheckingEmail,
  isCheckingUsername,
  setIsFormLoading,
}) => {
  const labelClassName = "text-sm font-medium text-gray-700";

  return (
    <>
      <div className="mb-4">
        <div className="flex justify-center items-center mb-2">
          <Image
            src="/team-account.png"
            alt="team-account"
            width={320}
            height={320}
            className="w-full h-full object-contain md:w-[320px] md:h-[320px]"
          />
        </div>
        <h1 className="text-center text-[14px] md:text-[16px] text-[#FF0000] font-semibold">
          Team/Club Accounts
        </h1>
        <p className="text-center text-[13px] md:text-[15px] text-[#575757]">
          Create a dedicated group, access exclusive chatroom's, link show
          awards to profile, and more!
        </p>
      </div>

      <div className="space-y-6">
        {/* Team Name Input */}
        <FormControlledInput
          control={control}
          name="teamName"
          label="Team Name"
          isRequired={true}
          autoComplete="off"
          classNames={{
            base: "w-full",
          }}
          labelClassName={labelClassName}
        />

        {/* Username Input */}
        <div>
          <FormControlledInput
            control={control}
            name="username"
            label="Username"
            isRequired={true}
            autoComplete="off"
            classNames={{
              base: "w-full",
            }}
            labelClassName={labelClassName}
            onValueChange={(value) => {
              // Convert to lowercase and filter invalid characters
              const filteredValue = value
                .toLowerCase()
                .replace(/[^a-z0-9_.]/g, "");
              return filteredValue;
            }}
          />
          <AvailabilityFeedback
            value={username}
            error={errors.username}
            isChecking={isCheckingUsername}
            isAvailable={isUsernameAvailable}
            type="username"
          />
        </div>

        {/* Email Input */}
        <div>
          <FormControlledInput
            control={control}
            name="email"
            label="Email"
            type="email"
            isRequired={true}
            autoComplete="off"
            classNames={{
              base: "w-full",
            }}
            labelClassName={labelClassName}
            onValueChange={(value) => {
              // Convert to lowercase
              return value.toLowerCase();
            }}
          />
          <AvailabilityFeedback
            value={email}
            error={errors.email}
            isChecking={isCheckingEmail}
            isAvailable={isEmailAvailable}
            type="email"
          />
        </div>

        {/* Password Input */}
        <FormControlledInput
          control={control}
          name="password"
          label="Password"
          type="password"
          isRequired={true}
          autoComplete="new-password"
          classNames={{
            base: "w-full",
          }}
          labelClassName={labelClassName}
        />

        {/* Profile Image Upload */}
        <FormControlledImage
          control={control}
          name="profileImage"
          label="Profile Image"
          isRequired={true}
          className="w-full"
          setIsFormLoading={setIsFormLoading}
          accept={{
            "image/*": [".jpeg", ".jpg", ".png", ".webp", ".heic", ".heif"],
          }}
        />
      </div>
    </>
  );
};

export default TeamAccountForm;
