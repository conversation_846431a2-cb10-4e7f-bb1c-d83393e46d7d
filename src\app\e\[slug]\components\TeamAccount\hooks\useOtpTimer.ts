import { useState, useEffect } from "react";

export const useOtpTimer = () => {
  const [canResendOtp, setCanResendOtp] = useState<boolean>(true);
  const [timeLeft, setTimeLeft] = useState<number>(0);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && !canResendOtp) {
      setCanResendOtp(true);
    }
  }, [timeLeft, canResendOtp]);

  const startCooldown = (seconds: number = 30) => {
    setCanResendOtp(false);
    setTimeLeft(seconds);
  };

  const resetTimer = () => {
    setCanResendOtp(true);
    setTimeLeft(0);
  };

  return {
    canResendOtp,
    timeLeft,
    startCooldown,
    resetTimer,
  };
};
 