import React, { useMemo, useState } from "react";
import Ticket from "./Ticket";
import { TicketType } from "@/app/events/types";
import { <PERSON><PERSON>, Divider } from "@nextui-org/react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { useSessionData } from "@/lib/hooks/useSession";
import TicketsAvailablityCountdown from "./TicketsAvailablityCountdown";
import { isZero } from "@/lib/utils/numberUtil";
import { isWithinDateRange, isBeforeDate, isAfterDate } from "@/lib/utils/date";
import { EVENT_STATUS } from "@/app/events/constants";
import {
  setActiveFormTicket,
  setWaivers,
} from "@/lib/redux/slices/events/eventSlice";
import { useCartConfirmation } from "../hooks/useCartConfirmation";
import { formatMonthDay } from "@/lib/utils/date";

interface TicketListProps {
  tickets: TicketType[];
  eventId: string;
  eventTitle: string;
  eventImg: string;
  date: string;
  openModal: () => void;
  pixelId?: string;
  title: string;
  ticketAvailableFrom: string;
  ticketAvailableTill: string;
  orgId: string;
  eventStatus: string;
  timezone: string;
  waivers: any;
  pixels: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
  // Add support for multiple organizers
  mainOrganizer?: {
    pixels?: {
      meta?: string;
      ga4?: string;
      snap?: string;
      tiktok?: string;
    };
  };
  collaborators?: Array<{
    id: string;
    pixels?: {
      meta?: string;
      ga4?: string;
      snap?: string;
      tiktok?: string;
    };
  }>;
  isWhitelabel?: boolean;
  slug?: string;
  isCountDownTimerHidden?: boolean;
  isCenteredLayout?: boolean;
}

const TicketList: React.FC<TicketListProps> = ({
  tickets,
  eventId,
  eventTitle,
  eventImg,
  openModal,
  date,
  pixelId,
  title,
  ticketAvailableFrom,
  ticketAvailableTill,
  orgId,
  eventStatus,
  timezone,
  waivers,
  pixels,
  mainOrganizer,
  collaborators,
  isWhitelabel = false,
  slug = "",
  isCountDownTimerHidden = false,
  isCenteredLayout = false,
}) => {
  const { data: session } = useSessionData();
  const eventTickets = useSelector((state: any) => state?.events?.eventTickets);
  const dispatch = useDispatch();
  const router = useRouter();

  const isTicketsAvailablePeriod = useMemo(
    () => isWithinDateRange(ticketAvailableFrom, ticketAvailableTill, timezone),
    [ticketAvailableFrom, ticketAvailableTill, timezone]
  );

  const isCurrentTimeBeforeAvailability = useMemo(
    () => isBeforeDate(ticketAvailableFrom, timezone),
    [ticketAvailableFrom, timezone]
  );

  const isCurrentTimeAfterAvailability = useMemo(
    () => isAfterDate(ticketAvailableTill, timezone),
    [ticketAvailableTill, timezone]
  );

  const isPaymentLink = eventTickets.some(
    (ticket: any) => ticket.paymentOption === "payment_link"
  );

  const { handleConfirm, isItemAdding } = useCartConfirmation({
    eventTickets,
    eventId,
    title,
    date,
    pixels,
    mainOrganizer,
    collaborators,
    session,
    waivers,
    isWhitelabel,
    slug,
    isPaymentLink,
    onSuccess: () => {
      if (isWhitelabel && isPaymentLink) {
        router.push(`/e/${slug}?oid=confirm-registration`);
      } else if (isPaymentLink) {
        router.push(`/confirm-registration`);
      } else if (isWhitelabel) {
        router.push(`/e/${slug}?oid=contact`);
      } else {
        router.push("/cart");
      }
    },
  });

  const isEventContainOnlyFreeTickets = useMemo(
    () => tickets?.every((ticket) => isZero(ticket?.ticketPrice)),
    [tickets]
  );

  const isEventExpired = eventStatus === "expired";

  const isAddToCartBtnDisabled = useMemo(() => {
    return (
      eventTickets?.length === 0 ||
      isItemAdding ||
      isEventExpired ||
      !isTicketsAvailablePeriod
    );
  }, [eventTickets, isItemAdding, isEventExpired, isTicketsAvailablePeriod]);

  const ticketAvailableFromFormatted = formatMonthDay(
    ticketAvailableFrom,
    timezone
  );

  return (
    <div className="grid gap-2 lg:mt-4 bg-[#F4F4F4] rounded-md lg:rounded-xl">
      <div className="flex p-3 lg:pl-6 justify-between items-center flex-row md:flex-col xl:flex-row">
        <h1 className="text-xl text-[#1D1D1F] font-medium mb-2">Tickets</h1>

        {!isCountDownTimerHidden && (
          <TicketsAvailablityCountdown
            startDate={ticketAvailableFrom}
            endDate={ticketAvailableTill}
            timezone={timezone}
            isCenteredLayout={isCenteredLayout}
          />
        )}
      </div>
      <div className="pl-3 pr-3 lg:pl-6 lg:pr-0 pt-2 pb-4">
        {tickets.map((ticket, idx) => (
          <div key={`ticket-${ticket.id}`}>
            <Ticket
              isEventExpired={isEventExpired}
              eventId={eventId}
              eventTitle={eventTitle}
              eventImg={eventImg}
              customTicketTemplate={ticket}
              ticketQuantity={ticket.quantityLeft}
              ticketId={ticket.id}
              isOptionDisabled={!isTicketsAvailablePeriod}
              isVehicleRequired={ticket?.vehicleApprovalRequired}
              isTicketApprovalRequired={ticket?.approvalRequired}
              isPasswordProtected={ticket?.isPasswordProtected || false}
              pixelId={pixelId}
              ticketName={ticket.name}
              org={orgId}
              handleAddFormDetails={async () => {
                dispatch(setActiveFormTicket(ticket));
                dispatch(setWaivers(waivers));
                openModal();
              }}
              forms={ticket.forms}
              isCurrentTimeBeforeAvailability={isCurrentTimeBeforeAvailability}
              isCurrentTimeAfterAvailability={isCurrentTimeAfterAvailability}
              ticketAvailableFromFormatted={ticketAvailableFromFormatted}
              timezone={timezone}
              isWhitelabel={isWhitelabel}
              paymentOption={ticket?.paymentOption}
            />
            <div className="pl-3">
              {idx !== tickets.length - 1 && <Divider className="my-4" />}
            </div>
          </div>
        ))}
        {!isEventContainOnlyFreeTickets &&
          eventStatus !== EVENT_STATUS.DRAFT && (
            <div className="mb-3 mt-6 flex justify-center items-center">
              <Button
                className={`w-[85%] md:w-2/3 text-lg ${
                  isAddToCartBtnDisabled
                    ? "cursor-not-allowed"
                    : "cursor-pointer"
                } ${!isAddToCartBtnDisabled ? "bg-[#007AFF]" : ""} `}
                color={!isAddToCartBtnDisabled ? "primary" : "default"}
                onPress={handleConfirm}
                isLoading={isItemAdding}
                disabled={isAddToCartBtnDisabled}
                isDisabled={isAddToCartBtnDisabled}
              >
                {isWhitelabel
                  ? "Checkout"
                  : isEventExpired
                  ? "Event Expired"
                  : "Add to cart"}
              </Button>
            </div>
          )}
      </div>
    </div>
  );
};

export default React.memo(TicketList);
