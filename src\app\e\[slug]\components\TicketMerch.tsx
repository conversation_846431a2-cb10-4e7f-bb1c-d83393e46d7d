import Image from "next/image";
import { useState, useMemo, useCallback } from "react";
import { Select, SelectItem } from "@nextui-org/react";

type AttributeValue = {
  id: number;
  name: string;
  slug: string;
  value: string;
  fileUrl: string | null;
  contentType: string | null;
  richText: string | null;
  plainText: string | null;
  boolean: boolean | null;
  dateTime: string | null;
};

type Attribute = {
  attribute: {
    id: number;
    name: string;
    slug: string;
    inputType: string;
  };
  values: AttributeValue[];
};

type Variant = {
  id: number;
  sku: string;
  name: string;
  weight: string | null;
  attributes: Attribute[];
  media: {
    id: number;
    alt: string;
    type: string;
    sortOrder: number;
    image: string;
    isVariantImage: boolean;
    variantId: number | null;
  }[];
  price: string;
  deliveryCharges: string;
  currency: string;
  stock: {
    quantity?: number;
    quantityAllocated?: number;
  };
};

type Product = {
  id: number;
  name: string;
  variants: Variant[];
  media: {
    id: number;
    alt: string;
    type: string;
    sortOrder: number;
    image: string;
    isVariantImage: boolean;
    variantId: number | null;
  }[];
};

type ProductAssociation = {
  productAssociationId: number;
  product: Product;
  associationType: string;
  orderSequence: number;
};

interface TicketMerchProps {
  products: ProductAssociation[];
  onVariantsSelected?: (variantIds: number[]) => void;
}

export default function TicketMerch({
  products,
  onVariantsSelected,
}: TicketMerchProps) {
  // State to track selected attributes for each product
  const [selectedAttributes, setSelectedAttributes] = useState<
    Record<number, Record<string, string>>
  >({});

  if (!products?.length) return null;

  // Function to get unique attributes for a product
  const getProductAttributes = (product: Product) => {
    const attributes: Record<string, Set<string>> = {};

    product.variants.forEach((variant) => {
      variant.attributes.forEach((attr) => {
        if (!attributes[attr.attribute.name]) {
          attributes[attr.attribute.name] = new Set();
        }
        attr.values.forEach((value) => {
          attributes[attr.attribute.name].add(value.name);
        });
      });
    });

    return Object.entries(attributes).map(([name, values]) => ({
      name,
      values: Array.from(values),
    }));
  };

  // Function to find matching variant based on selected attributes
  const findMatchingVariant = (
    product: Product,
    selectedAttrs: Record<string, string>
  ) => {
    const matchingVariant = product.variants.find((variant) => {
      if (!variant.attributes || variant.attributes.length === 0) {
        return false;
      }

      const isMatch = variant.attributes.every((attr) => {
        const attrName = attr.attribute.name;
        const selectedValue = selectedAttrs[attrName];

        const hasMatchingValue = attr.values.some((value) => {
          return value.name === selectedValue;
        });

        return hasMatchingValue;
      });

      return isMatch;
    });

    if (!matchingVariant && Object.keys(selectedAttrs).length === 0) {
      return product.variants[0];
    }

    return matchingVariant;
  };

  const areAllVariantsSelected = useMemo(() => {
    if (!products?.length) return true;

    return products.every((productAssoc) => {
      const selectedAttrs = selectedAttributes[productAssoc.product.id] || {};
      const attributes = getProductAttributes(productAssoc.product);
      return attributes.every((attr) => selectedAttrs[attr.name]);
    });
  }, [products, selectedAttributes]);

  // Get all selected variant IDs
  const getSelectedVariantIds = useCallback(() => {
    if (!products?.length) return [];

    return products.reduce<number[]>((acc, productAssoc) => {
      const selectedAttrs = selectedAttributes[productAssoc.product.id] || {};
      const matchingVariant = findMatchingVariant(
        productAssoc.product,
        selectedAttrs
      );
      if (matchingVariant) {
        acc.push(matchingVariant.id);
      }
      return acc;
    }, []);
  }, [products, selectedAttributes]);

  // Handle attribute selection
  const handleAttributeChange = (
    productId: number,
    attributeName: string,
    value: string
  ) => {
    const newState = {
      ...selectedAttributes,
      [productId]: {
        ...selectedAttributes[productId],
        [attributeName]: value,
      },
    };

    setSelectedAttributes(newState);

    // Notify parent of variant changes if callback exists
    if (onVariantsSelected) {
      const allSelected = products.every((productAssoc) => {
        const attrs = getProductAttributes(productAssoc.product);
        const selected = newState[productAssoc.product.id] || {};
        const isSelected = attrs.every((attr) => selected[attr.name]);

        return isSelected;
      });

      if (allSelected) {
        const variantIds = products
          .map((productAssoc) => {
            const variant = findMatchingVariant(
              productAssoc.product,
              newState[productAssoc.product.id] || {}
            );
            return variant?.id;
          })
          .filter((id): id is number => id !== undefined);

        onVariantsSelected(variantIds);
      } else {
        onVariantsSelected([]);
      }
    }
  };

  return (
    <div className="flex flex-col gap-4 mb-8">
      <p className="text-[#86868B] text-sm font-medium">Comes with:</p>
      {products.map((productAssoc, index) => {
        const product = productAssoc.product;
        const attributes = getProductAttributes(product);
        const selectedAttrs = selectedAttributes[product.id] || {};
        const matchingVariant = findMatchingVariant(product, selectedAttrs);

        return (
          <div key={product.id} className="flex gap-6">
            <div className="flex items-center gap-4">
              <p className="text-[#303030] text-sm font-medium">{index + 1}.</p>
              <Image
                src={product?.media[0]?.image}
                alt={product.name}
                width={50}
                height={50}
                className="w-[50px] h-[50px] object-cover"
              />
            </div>
            <div className="flex flex-col gap-2 flex-grow">
              <p className="text-[14px] text-[#303030] font-thin">
                {product.name}
              </p>

              {/* Attribute selectors */}
              <div className="grid grid-cols-2 gap-4">
                {attributes.map((attr) => {
                  const currentValue = selectedAttrs[attr.name] || "";

                  return (
                    <div key={attr.name} className="flex-1 min-w-[120px]">
                      <Select
                        placeholder={attr.name}
                        classNames={{
                          trigger: "bg-white h-[30px] min-h-[30px]",
                          popoverContent: "bg-white",
                        }}
                        selectedKeys={
                          currentValue ? new Set([currentValue]) : new Set()
                        }
                        onSelectionChange={(selectedKey) => {
                          let selectedValue = "";
                          if (selectedKey instanceof Set) {
                            selectedValue = Array.from(
                              selectedKey
                            )[0] as string;
                          } else {
                            selectedValue = selectedKey as string;
                          }

                          if (selectedValue) {
                            handleAttributeChange(
                              product.id,
                              attr.name,
                              selectedValue
                            );
                          }
                        }}
                        size="sm"
                        aria-label={attr.name}
                      >
                        {attr.values.map((value) => {
                          return (
                            <SelectItem key={value} value={value}>
                              {value}
                            </SelectItem>
                          );
                        })}
                      </Select>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
