import React from "react";
import dynamic from "next/dynamic";
import { MODAL_TYPES } from "@/app/events/constants";

const CreateAccountOrCheckoutModal = dynamic(
  () => import("./CreateAccountOrCheckoutModal"),
  { ssr: false }
);

const TicketForm = dynamic(() => import("./Form/TicketForm"), { ssr: false });

const CreateTeamAccountModal = dynamic(
  () => import("./CreateTeamAccountModal"),
  {
    ssr: false,
  }
);

interface TicketModalsProps {
  modalState: Record<string, boolean>;
  toggleModal: (modal: string) => void;
  openAddVehicleOnGuest: () => void;
  isWhitelabel?: boolean;
  slug?: string;
}

const TicketModals: React.FC<TicketModalsProps> = ({
  modalState,
  toggleModal,
  openAddVehicleOnGuest,
  isWhitelabel = false,
  slug = "",
}) => {
  return (
    <>
      <CreateAccountOrCheckoutModal
        isOpen={modalState[MODAL_TYPES.GUEST]}
        handleClose={() => toggleModal(MODAL_TYPES.GUEST)}
        openAddVehicleOnGuest={openAddVehicleOnGuest}
      />
      <TicketForm
        open={modalState[MODAL_TYPES.TICKET_FORM]}
        onClose={() => toggleModal(MODAL_TYPES.TICKET_FORM)}
        toggleModal={toggleModal}
        isWhitelabel={isWhitelabel}
        slug={slug}
      />

      <CreateTeamAccountModal
        isOpen={modalState[MODAL_TYPES.CREATE_TEAM_ACCOUNT]}
        onClose={() => toggleModal(MODAL_TYPES.CREATE_TEAM_ACCOUNT)}
        toggleModal={toggleModal}
      />
    </>
  );
};

export default React.memo(TicketModals);
