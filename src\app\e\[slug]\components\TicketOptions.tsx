"use client";

import React, { useCallback } from "react";
import { useSessionData } from "@/lib/hooks/useSession";
import { MODAL_TYPES } from "@/app/events/constants";
import TicketList from "./TicketList";
import { useModalState } from "./hooks/useModalState";
import { TicketType } from "@/app/events/types";
import dynamic from "next/dynamic";

const TicketModals = dynamic(() => import("./TicketModals"), { ssr: false });

interface TicketOptionsProps {
  tickets: TicketType[];
  title: string;
  date: string;
  eventId: string;
  eventTitle: string;
  eventImg: string;
  pixelId?: string;
  ticketAvailableFrom: string;
  ticketAvailableTill: string;
  orgId: string;
  eventStatus: string;
  timezone: string;
  waivers: any;
  pixels: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
  // Add support for multiple organizers
  mainOrganizer?: {
    pixels?: {
      meta?: string;
      ga4?: string;
      snap?: string;
      tiktok?: string;
    };
  };
  collaborators?: Array<{
    id: string;
    pixels?: {
      meta?: string;
      ga4?: string;
      snap?: string;
      tiktok?: string;
    };
  }>;
}

const TicketOptions: React.FC<TicketOptionsProps> = ({
  tickets,
  date,
  title,
  eventId,
  eventTitle,
  eventImg,
  ticketAvailableFrom,
  ticketAvailableTill,
  pixelId,
  orgId,
  eventStatus,
  timezone,
  waivers,
  pixels,
  mainOrganizer,
  collaborators,
}) => {
  const { modalState, toggleModal } = useModalState();

  const { data: sessionData } = useSessionData();

  const openModal = useCallback(() => {
    if (!sessionData) {
      toggleModal(MODAL_TYPES.GUEST);
    } else {
      toggleModal(MODAL_TYPES.TICKET_FORM);
    }
  }, [sessionData, toggleModal]);

  const openAddVehicleOnGuest = useCallback(() => {
    toggleModal(MODAL_TYPES.GUEST);
    toggleModal(MODAL_TYPES.TICKET_FORM);
  }, [toggleModal]);

  if (tickets?.length === 0) {
    return <></>;
  }

  return (
    <div className="">
      <TicketList
        date={date}
        title={title}
        tickets={tickets}
        eventId={eventId}
        eventTitle={eventTitle}
        eventImg={eventImg}
        openModal={openModal}
        pixelId={pixelId}
        ticketAvailableFrom={ticketAvailableFrom}
        ticketAvailableTill={ticketAvailableTill}
        orgId={orgId}
        eventStatus={eventStatus}
        timezone={timezone}
        waivers={waivers}
        pixels={pixels}
        mainOrganizer={mainOrganizer}
        collaborators={collaborators}
      />
      <TicketModals
        modalState={modalState}
        toggleModal={toggleModal}
        openAddVehicleOnGuest={openAddVehicleOnGuest}
      />
    </div>
  );
};

export default React.memo(TicketOptions);
