import React, { useState, useEffect } from "react";
import { Button, Select, SelectItem } from "@nextui-org/react";
import { HiOutlineChevronUpDown } from "react-icons/hi2";

import { isZero } from "@/lib/utils/numberUtil";
import { PriceRenderer } from "./PriceRenderer";

interface TicketQuantityOptionProps {
  handleSelectionChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  isVehicleRequired: boolean;
  isVehicleAdded: boolean;
  customTicketTemplate: any;
  defaultQuantity: number;
  isOptionDisabled: boolean;
  ticketQuantity: number;
  eventId: string;
  pixelId?: string;
  eventTitle: string;
  ticketName: string;
  forms?: any[];
  handleAddFormDetails: () => void;
  isFormAddedInTicket: boolean;
  ticketId: string;
  isQuantityAvailable: boolean;
  isPasswordVerified: boolean;
  isVehicleRequiredCondition: boolean;
  vehicleData: any;
}

const TicketQuantityOption: React.FC<TicketQuantityOptionProps> = ({
  handleSelectionChange,
  customTicketTemplate,
  defaultQuantity,
  handleAddFormDetails,
  isOptionDisabled,
  ticketQuantity,
  forms,
  isFormAddedInTicket,
  isQuantityAvailable,
}) => {
  const [selectedQuantity, setSelectedQuantity] = useState<string>(
    defaultQuantity.toString()
  );

  useEffect(() => {
    setSelectedQuantity(defaultQuantity.toString());
  }, [defaultQuantity]);

  const idContainedForms = forms?.some((form) => form.id);

  const isFreeTicket = isZero(customTicketTemplate.ticketPrice);
  const isFreeTicketNonForm = isFreeTicket && !idContainedForms;

  const maxTickets = isFreeTicketNonForm ? 1 : 9;
  const limitTickets =
    ticketQuantity === -1
      ? maxTickets
      : Math.min(maxTickets, Number(ticketQuantity));

  const renderAddVehicleButton = () => {
    const formName = customTicketTemplate?.forms[0]?.name || "Form";

    return (
      <div className="flex flex-col md:items-end">
        <Button
          color="primary"
          className="bg-[#007AFF] px-[20px] mb-2"
          size="sm"
          onPress={handleAddFormDetails}
          isDisabled={isOptionDisabled}
        >
          Add {formName}
        </Button>
      </div>
    );
  };

  const renderPaidTicketOptions = () => (
    <>
      <div className="flex justify-end w-full gap-5 items-start">
        <PriceRenderer
          isQuantityAvailable={isQuantityAvailable}
          customTicketTemplate={customTicketTemplate}
        />
        {isQuantityAvailable ? (
          <>
            {idContainedForms && !isFormAddedInTicket ? (
              renderAddVehicleButton()
            ) : (
              <Select
                items={Array.from({ length: limitTickets + 1 }, (_, i) => ({
                  value: i.toString(),
                  label: i.toString(),
                }))}
                selectedKeys={[selectedQuantity]}
                color="default"
                size="sm"
                aria-label="Ticket Quantity"
                selectorIcon={<HiOutlineChevronUpDown />}
                onChange={handleSelectionChange}
                classNames={{
                  base: "w-[65px] rounded-l",
                  trigger: "w-[65px] bg-white",
                  value: "lg:text-[16px] text-[#000]",
                  listbox: "lg:text-[16px] ",
                  popoverContent: "lg:text-[16px] ",
                }}
                isDisabled={isOptionDisabled}
              >
                {(item) => (
                  <SelectItem key={item.value} value={item.value}>
                    {item.label}
                  </SelectItem>
                )}
              </Select>
            )}
          </>
        ) : (
          <></>
        )}
      </div>
    </>
  );

  return renderPaidTicketOptions();
};

export default TicketQuantityOption;
