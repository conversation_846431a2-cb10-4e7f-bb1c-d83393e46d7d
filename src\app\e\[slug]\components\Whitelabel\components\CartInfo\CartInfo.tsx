"use client";

import React, { useRef } from "react";
import Image from "next/image";
import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { STORAGE_KEYS } from "@/lib/constants/storage";

type Props = {
  isBarHeader: boolean;
  slug: string;
};

const CartInfo = ({ isBarHeader = false, slug }: Props) => {
  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);

  const { data } = useGetCartQuery(undefined, {
    skip: !cartToken,
  });
  const router = useRouter();
  const toastRef = useRef<string | null>(null);

  const cartItemCount = data?.checkout?.lines?.length || 0;

  const handleCartClick = () => {
    if (cartItemCount === 0) {
      if (toastRef.current) {
        return;
      }

      toastRef.current = toast("No items in your checkout", {
        icon: "🟡",
        duration: 2000,
      });

      setTimeout(() => {
        toastRef.current = null;
      }, 2000);

      return;
    }
    router.push(`/e/${slug}?oid=contact`);
  };

  if (isBarHeader) {
    return (
      <div className="relative cursor-pointer" onClick={handleCartClick}>
        <Image
          src="/cart-icon.svg"
          alt="cart"
          width={15}
          height={16}
          className="filter brightness-0 invert"
        />
        {cartItemCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
            {cartItemCount}
          </span>
        )}
      </div>
    );
  }

  return (
    <div
      className="cursor-pointer w-[55px] h-[40px] flex items-center justify-center !rounded-[30px] relative transition-all duration-200 hover:scale-105"
      style={{
        background: "rgba(255, 255, 255, 0.2)",
        backdropFilter: "blur(16px)",
        WebkitBackdropFilter: "blur(16px)",
        boxShadow: `
          inset 0 0 20px -4px rgba(255, 255, 255, 0.5),
          0px 8px 32px rgba(0, 0, 0, 0.15),
          0px 4px 16px rgba(0, 0, 0, 0.1)
        `,
        border: "1px solid rgba(255, 255, 255, 0.3)",
      }}
      onClick={handleCartClick}
      onMouseEnter={(e) => {
        e.currentTarget.style.background = "rgba(255, 255, 255, 0.3)";
        e.currentTarget.style.boxShadow = `
          inset 0 0 24px -4px rgba(255, 255, 255, 0.7),
          0px 12px 40px rgba(0, 0, 0, 0.2),
          0px 6px 20px rgba(0, 0, 0, 0.15)
        `;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.background = "rgba(255, 255, 255, 0.2)";
        e.currentTarget.style.boxShadow = `
          inset 0 0 20px -4px rgba(255, 255, 255, 0.5),
          0px 8px 32px rgba(0, 0, 0, 0.15),
          0px 4px 16px rgba(0, 0, 0, 0.1)
        `;
      }}
    >
      <Image
        src="/cart-icon.svg"
        alt="cart"
        width={15}
        height={16}
        className="opacity-80"
      />
      {cartItemCount > 0 && (
        <span
          className="absolute -top-1 -right-1 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium transition-all duration-200"
          style={{
            background: "linear-gradient(135deg, #ef4444, #dc2626)",
            backdropFilter: "blur(8px)",
            WebkitBackdropFilter: "blur(8px)",
            boxShadow: `
              inset 0 0 12px -2px rgba(255, 255, 255, 0.3),
              0px 4px 16px rgba(239, 68, 68, 0.4),
              0px 2px 8px rgba(0, 0, 0, 0.2)
            `,
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          {cartItemCount}
        </span>
      )}
    </div>
  );
};

export default CartInfo;
