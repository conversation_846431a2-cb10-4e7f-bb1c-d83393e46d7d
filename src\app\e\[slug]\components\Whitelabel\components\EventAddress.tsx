"use client";
import React from "react";
import { MdLocationPin } from "react-icons/md";
import { EventLocation } from "../../../../../event/types";

export default function EventAddress({
  location,
}: {
  location: EventLocation;
}) {
  const handleAddressClick = () => {
    try {
      // Only handle click on mobile devices
      if (window.innerWidth >= 768) return;

      if (location?.geoLocation) {
        const coords = location.geoLocation.split(",");
        if (coords.length !== 2) {
          console.warn("Invalid geoLocation format. Expected 'lat,lng'");
          return;
        }

        const [lat, lng] = coords?.map((coord) => coord?.trim());
        if (isNaN(Number(lat)) || isNaN(Number(lng))) {
          console.warn("Invalid coordinates in geoLocation");
          return;
        }

        // Detect if it's iOS or Android
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/.test(navigator.userAgent);

        let mapsUrl = "";

        if (isIOS) {
          // Apple Maps URL scheme
          mapsUrl = `maps://?q=${lat},${lng}`;
        } else if (isAndroid) {
          // Google Maps URL scheme for Android
          mapsUrl = `geo:${lat},${lng}?q=${lat},${lng}`;
        } else {
          // Fallback to Google Maps web
          mapsUrl = `https://maps.google.com/?q=${lat},${lng}`;
        }

        window.open(mapsUrl, "_blank");
      }
    } catch (error) {
      console.error("Error opening maps:", error);
    }
  };
  return (
    <div className="flex gap-2 mt-2 ml-1">
      <MdLocationPin size={20} className="mt-1" />
      <div
        className="space-y-[-4px] text-[#1D1D1F] md:cursor-default cursor-pointer md:active:bg-transparent active:bg-gray-100 md:rounded-none rounded-md md:p-0 p-1 transition-colors duration-150"
        onClick={handleAddressClick}
      >
        <p>{location?.venueName}</p>
        <p>{location?.address}</p>
        <p>
          {[
            location?.city,
            location?.state,
            location?.zipCode,
            location?.country,
          ]
            ?.filter(Boolean)
            ?.join(", ")}
        </p>
      </div>
    </div>
  );
}
