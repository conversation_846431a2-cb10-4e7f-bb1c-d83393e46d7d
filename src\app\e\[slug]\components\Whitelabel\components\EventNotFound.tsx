"use client";

import Link from "next/link";
import { Calendar, Search, Home } from "lucide-react";

interface EventNotFoundProps {
  message?: string;
  showBackButton?: boolean;
}

const EventNotFound = ({
  message = "Event not found or failed to load",
  showBackButton = true,
}: EventNotFoundProps) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-md mx-auto text-center px-6 py-12">
        <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-8">
          <Calendar className="h-12 w-12 text-red-600" />
        </div>

        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Event Not Found
        </h1>

        <p className="text-lg text-gray-600 mb-8 leading-relaxed">{message}</p>

        <div className="bg-white rounded-lg p-6 mb-8 shadow-sm border border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center justify-center gap-2">
            <Search className="h-4 w-4" />
            What you can do:
          </h3>
          <ul className="text-sm text-gray-600 space-y-2 text-left">
            <li className="flex items-start gap-2">
              <span className="text-red-500 mt-1">•</span>
              Check if the event URL is correct
            </li>
            <li className="flex items-start gap-2">
              <span className="text-red-500 mt-1">•</span>
              The event may have been removed or is no longer available
            </li>
            <li className="flex items-start gap-2">
              <span className="text-red-500 mt-1">•</span>
              Contact the event organizer for more information
            </li>
          </ul>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/"
            className="flex items-center justify-center gap-2 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 font-medium"
          >
            <Home className="h-4 w-4" />
            Visit AutoLNK
          </Link>
        </div>

        <p className="text-xs text-gray-500 mt-8">
          Need help? Contact our support team for assistance.
        </p>
      </div>
    </div>
  );
};

export default EventNotFound;
