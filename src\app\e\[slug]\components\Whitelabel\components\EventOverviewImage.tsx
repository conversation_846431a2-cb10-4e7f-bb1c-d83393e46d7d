import Image from "next/image";
import React from "react";
import TagBadge from "@/app/events/components/TagBadge";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

interface EventOverviewImageProps {
  img: string;
  tag?: string;
}

export default function EventOverviewImage({
  img = IMAGE_LINKS.NO_IMG,
  tag,
}: EventOverviewImageProps) {
  return (
    <div className="relative">
      <TagBadge tag={tag} />
      <Image
        priority={true}
        alt="Autolnk Event Image"
        width={340}
        loading="eager"
        height={340}
        src={img}
        sizes="(max-width: 768px) 90vw, (max-width: 1200px) 225px, 340px"
        quality={85}
        className="object-cover w-[90vw] h-[90vw] md:max-w-[225px] md:h-[225px] xl:max-w-[340px] xl:h-[340px] rounded-xl flex aspect-square"
      />
    </div>
  );
}
