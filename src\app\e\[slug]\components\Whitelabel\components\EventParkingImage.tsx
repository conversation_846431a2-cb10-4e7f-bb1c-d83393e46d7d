"use client";
import ModalComponent from "@/components/Modal/Modal";
import { Button } from "@nextui-org/react";
import { BsCardImage } from "react-icons/bs";
import React from "react";
import Image from "next/image";

export const EventParkingImage = ({ img }: { img: string }) => {
  return (
    <ModalComponent
      trigger={
        <Button
          variant="flat"
          startContent={<BsCardImage size={18} fontWeight={100} />}
          className="flex w-fit mt-3"
        >
          Event Parking
        </Button>
      }
      title="Event Parking"
      size="xl"
      scrollBehavior="outside"
      isKeyboardDismissDisabled={true}
    >
      <Image
        src={img}
        alt="Event Parking"
        width={1000}
        height={1000}
        className="w-full h-full object-cover"
      />
    </ModalComponent>
  );
};
