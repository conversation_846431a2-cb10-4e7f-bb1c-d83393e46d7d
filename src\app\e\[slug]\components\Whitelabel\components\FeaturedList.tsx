import React from "react";
import Image from "next/image";
import { Card, CardBody } from "@nextui-org/react";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

interface FeaturedItem {
  image: string;
  name: string;
}

interface FeaturedListProps {
  list: FeaturedItem[];
}

const FeaturedList: React.FC<FeaturedListProps> = ({ list }) => {
  if (!list || list.length === 0) return null;

  return (
    <Card className="!bg-[#F8F8F8] !rounded-[20px] !border-[#E3E3E3] !border-1 !shadow-none md:my-8">
      <CardBody className="px-5 py-1 md:py-3">
        <h2 className="text-[19px] md:text-[20px] font-medium md:font-semibold text-center mb-2.5 md:mb-3">
          Featured
        </h2>
        <div className="flex flex-wrap justify-center gap-y-2.5 gap-4">
          {list?.map((item, index) => (
            <div key={index} className="flex flex-col items-center truncate justify-center w-16 sm:w-[70px] md:w-[80px] 2xl:w-[90px]">
              <div className="rounded-xl overflow-hidden mb-1 w-full aspect-square relative">
                <Image
                  src={item?.image || IMAGE_LINKS.NO_IMG}
                  alt={`AutoLNK - Featured: ${item?.name}`}
                  fill
                  sizes="(max-width: 640px) 45vw, (max-width: 1024px) 22vw, 12vw"
                  className="object-cover"
                  priority
                />
              </div>
              <p className="text-[11px] md:text-[12px] font-medium text-center truncate w-[95%]">
                {item?.name}
              </p>
            </div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

export default FeaturedList;
