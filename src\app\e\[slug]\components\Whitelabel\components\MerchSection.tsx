"use client";

import { lazy, Suspense } from "react";
import { Product } from "@/app/event/types";

const ProductList = lazy(() =>
  import("./merch/ProductList").then((module) => ({
    default: module.ProductList,
  }))
);

interface MerchSectionProps {
  products: Product[];
  isWhitelabel?: boolean;
  slug?: string;
}

const MerchSection: React.FC<MerchSectionProps> = ({
  products,
  isWhitelabel = false,
  slug = "",
}) => {
  if (!products?.length) {
    return <></>;
  }

  return (
    <Suspense fallback={<div></div>}>
      <ProductList
        products={products}
        isWhitelabel={isWhitelabel}
        slug={slug}
      />
    </Suspense>
  );
};

export default MerchSection;
