import React from "react";
import { Divider } from "@nextui-org/react";
import Image from "next/image";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

interface ProfileItem {
  name: string;
  orderNumber: number;
  image: string;
}

interface ProfileGridSectionProps {
  title: string;
  items?: ProfileItem[];
}

const ProfileGridSection = ({ title, items }: ProfileGridSectionProps) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="mt-6 lg:mt-12">
      <h1 className="text-xl lg:mt-12 font-medium text-[#1D1D1F]">{title}</h1>
      <Divider className="mt-2 bg-[#B7B8B4] w-[95%] h-[0.6px]" />

      <div className="mt-5 grid grid-cols-5 md:grid-cols-6 gap-4 md:gap-5 w-[95%]">
        {items?.map((item, index) => (
          <div
            key={`${item.name}-${index}`}
            className="flex flex-col items-center"
          >
            <div className="relative w-16 h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 xl:w-28 xl:h-28 2xl:w-32 2xl:h-32 rounded-full overflow-hidden border border-gray-100">
              <Image
                src={item?.image || IMAGE_LINKS.NO_IMG}
                alt={`AutoLNK ${title} ${item?.name || ""}`}
                fill
                className="object-cover"
                sizes="(max-width: 640px) 64px, (max-width: 768px) 80px, (max-width: 1024px) 96px, (max-width: 1280px) 112px, 128px"
              />
            </div>
            <p className="text-xs md:text-sm lg:text-sm mt-2 text-center font-medium truncate max-w-full">
              {item?.name || ""}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProfileGridSection;
