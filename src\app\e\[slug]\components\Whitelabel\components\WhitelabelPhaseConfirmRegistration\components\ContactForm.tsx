"use client";

import React, { useState, useMemo } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  validateEmailWithTLD,
  INVALID_EMAIL_ADDRESS,
} from "@/app/checkout/constants/formMessages";
import {
  useClaimCartMutation,
  useCreateOrderMutation,
  useCreatePaymentMutation,
  useGetCartQuery,
  useUpdateQuantityMutation,
} from "@/lib/redux/slices/cart/cartApi";
import { useSessionData } from "@/lib/hooks/useSession";
import { isZero } from "@/lib/utils/numberUtil";
import { handleApiError } from "@/lib/utils/errorUtils";
import { DIRECT_ORDER_CREATION_SOURCE } from "@/lib/utils/constants";
import { extractTrackingData } from "@/lib/utils/extractTrackingData";
import { STORAGE_KEYS, PAYMENT_OPTIONS } from "@/lib/constants/storage";
import { CHECKOUT_TRANSACTION_PREFIXES } from "@/app/checkout/constants/checkoutConstants";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import WaiverModal from "@/app/cart/components/WaiverModal";

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

// Waiver types
type WaiverData = {
  waiverUrl: string;
  waiverSignature: string;
  waiverSignedDateTime: string;
  timezone: string;
};

type WaiverInfo = {
  lineId: string;
  waiverUrl: string;
  waiverId: string;
  type: "ticketWaiver" | "waiverResponse";
};

type WaiverSignatures = Record<
  string,
  { signature: string; signedDateTime: string; timezone: string }
>;

const contactSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Name must be at least 2 characters." })
    .max(100, { message: "Name must be at most 100 characters." })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Name cannot contain only whitespace",
    })
    .refine((value) => /^[a-zA-Z\s'-]+$/.test(value), {
      message:
        "Name can only contain letters, spaces, apostrophes, and hyphens",
    }),
  email: z
    .string()
    .email({ message: INVALID_EMAIL_ADDRESS })
    .min(1, { message: "Email is required" })
    .refine(
      (email) => {
        const validation = validateEmailWithTLD(email);
        return validation.isValid;
      },
      (email) => {
        const validation = validateEmailWithTLD(email);
        return {
          message: validation.error || INVALID_EMAIL_ADDRESS,
          suggestion: validation.suggestion,
        };
      }
    )
    .transform((value) => value.trim().toLowerCase()),
  phone: z
    .string()
    .min(1, { message: "Phone number is required" })
    .transform((value) => value.replace(/[^\d]/g, ""))
    .refine((value) => value.length === 10, {
      message: "Please enter a valid 10-digit phone number",
    })
    .refine((value) => /^[2-9]\d{2}[2-9]\d{2}\d{4}$/.test(value), {
      message: "Please enter a valid US phone number",
    }),
});

export type ContactFormValues = z.infer<typeof contactSchema>;

export interface ContactFormProps {
  onSubmit?: (values: ContactFormValues) => Promise<void> | void;
  defaultValues?: Partial<ContactFormValues>;
}

const ContactForm: React.FC<ContactFormProps> = ({
  onSubmit,
  defaultValues,
}) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  // Waiver handling state
  const [showWaiver, setShowWaiver] = useState(false);
  const [currentWaiverIndex, setCurrentWaiverIndex] = useState(0);
  const [waiverSignatures, setWaiverSignatures] = useState<WaiverSignatures>(
    {}
  );

  // Cart and session data
  const { data: sessionData } = useSessionData();
  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);

  const { data: cartData } = useGetCartQuery(undefined, {
    skip: !cartToken && !sessionData?.user,
  });

  // Mutations
  const [claimCart] = useClaimCartMutation();
  const [createPayment] = useCreatePaymentMutation();
  const [createOrder] = useCreateOrderMutation();
  const [updateQuantity] = useUpdateQuantityMutation();

  // Check if this is a payment link method
  const isPaymentLinkMethod =
    cartData?.checkout?.lines?.some(
      (line) =>
        line?.eventDetails?.paymentOption === PAYMENT_OPTIONS?.PAYMENT_LINK
    ) || false;

  // Collect all waivers from cart data
  const allWaivers = useMemo(() => {
    if (!cartData?.checkout?.lines) return [];

    const waiverMap = new Map<string, WaiverInfo>();

    cartData.checkout.lines.forEach((line: any) => {
      if (line.ticketWaiverUrl) {
        const key = line.ticketWaiverUrl.waiverUrl;
        if (!waiverMap.has(key)) {
          waiverMap.set(key, {
            lineId: line.id,
            waiverUrl: line.ticketWaiverUrl.waiverUrl,
            waiverId: line.ticketWaiverUrl.id,
            type: "ticketWaiver",
          });
        }
      }

      if (line.waiverResponseData?.length > 0) {
        line.waiverResponseData.forEach((waiver: any) => {
          const key = waiver.waiverUrl;
          if (!waiverMap.has(key)) {
            waiverMap.set(key, {
              lineId: line.id,
              waiverUrl: waiver.waiverUrl,
              waiverId: `${line.id}-${waiver.waiverUrl}`,
              type: "waiverResponse",
            });
          }
        });
      }
    });

    return Array.from(waiverMap.values());
  }, [cartData?.checkout?.lines]);

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      ...defaultValues,
    },
    mode: "onTouched",
    reValidateMode: "onChange",
  });

  // Utility functions from checkout flow
  const storeTrackingData = (transactionIdPrefix: string) => {
    try {
      const trackingData = extractTrackingData(cartData?.checkout);
      if (trackingData) {
        sessionStorage.setItem(
          "pixelPurchaseData",
          JSON.stringify({
            event: trackingData.event,
            tickets: trackingData.tickets,
          })
        );
      }
    } catch (error) {
      console.error("Failed to store tracking data:", error);
    }
  };

  const cleanupSessionStorage = () => {
    try {
      sessionStorage.removeItem(STORAGE_KEYS.CHECKOUT_FORM_DATA);
    } catch (error) {
      console.error("Failed to cleanup sessionStorage:", error);
    }
  };

  // Waiver handling functions
  const handleWaiverAccept = async (signature: string) => {
    if (!allWaivers[currentWaiverIndex]) return;

    const currentWaiver = allWaivers[currentWaiverIndex];
    const lineWithWaiver = cartData?.checkout?.lines?.find(
      (line: any) => line.id === currentWaiver.lineId
    );

    const timezone =
      lineWithWaiver?.eventDetails?.eventTimezone || dayjs.tz.guess();
    const signedDateTime = dayjs().tz(timezone).utc().format();

    const newSignatures = {
      ...waiverSignatures,
      [currentWaiver.waiverId]: {
        signature,
        signedDateTime,
        timezone,
      },
    };
    setWaiverSignatures(newSignatures);

    if (currentWaiverIndex < allWaivers.length - 1) {
      setCurrentWaiverIndex((prev) => prev + 1);
    } else {
      setShowWaiver(false);
      return newSignatures;
    }
  };

  const handleWaiverClose = () => {
    setShowWaiver(false);
  };

  const startWaiverProcess = () => {
    setCurrentWaiverIndex(0);
    setWaiverSignatures({});
    setShowWaiver(true);
  };

  // Handle waiver acceptance and proceed with registration
  const handleWaiverAcceptWithRegistration = async (signature: string) => {
    const newSignatures = await handleWaiverAccept(signature);
    if (newSignatures) {
      // Update cart lines with waiver signatures
      await updateCartLinesWithWaivers(newSignatures);
      // Proceed with registration
      await processRegistration(form.getValues());
    }
  };

  // Update cart lines with waiver signatures
  const updateCartLinesWithWaivers = async (
    waiverSignatures: WaiverSignatures
  ) => {
    try {
      const linesWithWaivers =
        cartData?.checkout?.lines?.filter((line: any) => {
          if (
            line.ticketWaiverUrl &&
            waiverSignatures[line.ticketWaiverUrl.id]
          ) {
            return true;
          }
          if (line.waiverResponseData?.length > 0) {
            return line.waiverResponseData.some(
              (waiver: any) =>
                waiverSignatures[`${line.id}-${waiver.waiverUrl}`]
            );
          }
          return false;
        }) || [];

      if (linesWithWaivers.length > 0) {
        const updatePromises = linesWithWaivers.map((line: any) => {
          const waiverData: WaiverData[] = [];

          if (
            line.ticketWaiverUrl &&
            waiverSignatures[line.ticketWaiverUrl.id]
          ) {
            const waiverInfo = waiverSignatures[line.ticketWaiverUrl.id];
            waiverData.push({
              waiverUrl: line.ticketWaiverUrl.waiverUrl,
              waiverSignature: waiverInfo.signature,
              waiverSignedDateTime: waiverInfo.signedDateTime,
              timezone: waiverInfo.timezone,
            });
          }

          if (line.waiverResponseData?.length > 0) {
            line.waiverResponseData.forEach((waiver: any) => {
              const waiverInfo =
                waiverSignatures[`${line.id}-${waiver.waiverUrl}`];
              if (waiverInfo) {
                waiverData.push({
                  waiverUrl: waiver.waiverUrl,
                  waiverSignature: waiverInfo.signature,
                  waiverSignedDateTime: waiverInfo.signedDateTime,
                  timezone: waiverInfo.timezone,
                });
              }
            });
          }

          const payload = keysToSnake({
            id: line.id,
            type: "event_ticket",
            quantity: line.quantity,
            waiverResponseData: waiverData,
          });

          return updateQuantity(payload).unwrap();
        });

        await Promise.all(updatePromises);
      }
    } catch (error) {
      console.error("Failed to update cart lines with waivers:", error);
      toast.error("Failed to process waivers. Please try again.");
      throw error;
    }
  };


  const formatPhoneNumber = (value: string) => {
    const digits = value.replace(/[^\d]/g, "");

    if (digits.length <= 3) {
      return digits;
    } else if (digits.length <= 6) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    } else {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(
        6,
        10
      )}`;
    }
  };

  const handleSubmit = async (values: ContactFormValues) => {
    if (isProcessing) return;

    // Check if there are waivers that need to be signed
    if (allWaivers.length > 0) {
      startWaiverProcess();
      return;
    }

    // If no waivers, proceed with the existing logic
    await processRegistration(values);
  };

  const processRegistration = async (values: ContactFormValues) => {
    if (isProcessing) return;

    setIsProcessing(true);

    // Show loading toast
    const loadingToast = toast.loading("Submitting registration...");

    try {
      // Prepare user data for cart claim
      const userData = {
        email: values.email,
        firstName: values.name.split(" ")[0] || "",
        lastName: values.name.split(" ").slice(1).join(" ") || "",
        newsletter: true,
        smsUpdates: true,
        customerMetadata: {
          phone: values.phone,
        },
      };

      if (
        (isZero((cartData?.checkout as any)?.total as number) ||
          isPaymentLinkMethod) &&
        ((cartData?.checkout as any)?.token as string)
      ) {
        storeTrackingData(CHECKOUT_TRANSACTION_PREFIXES.FREE);

        try {
          // Step 1: Claim the cart with user data
          const claimCartResult = await claimCart(userData).unwrap();
          console.log("Cart claimed successfully:", claimCartResult);
        } catch (claimError) {
          toast.dismiss(loadingToast);
          handleApiError(claimError, "Failed to claim cart. Please try again.");
          console.error("Claim cart error:", claimError);
          return;
        }

        // Step 2: Create payment if needed (payment link with non-zero total)
        if (
          isPaymentLinkMethod &&
          !isZero((cartData?.checkout as any)?.total as number)
        ) {
          try {
            const createPaymentResult = await createPayment({
              token: (cartData?.checkout as any)?.token as string,
              metadata: {
                checkoutToken: (cartData?.checkout as any)?.token as string,
              },
            }).unwrap();
            console.log("Payment created successfully:", createPaymentResult);
          } catch (paymentError) {
            toast.dismiss(loadingToast);
            handleApiError(paymentError, "Failed to create payment. Please try again.");
            console.error("Create payment error:", paymentError);
            return;
          }
        }

        // Step 3: Create the order
        try {
          const createOrderResult = await createOrder({
            checkoutToken: (cartData?.checkout as any)?.token as string,
            source: DIRECT_ORDER_CREATION_SOURCE,
          }).unwrap();
          console.log("Order created successfully:", createOrderResult);
        } catch (orderError) {
          toast.dismiss(loadingToast);
          handleApiError(orderError, "Failed to create order. Please try again.");
          console.error("Create order error:", orderError);
          return;
        }

        // Step 4: Only after all API calls succeed, cleanup and redirect
        localStorage.removeItem(STORAGE_KEYS.CART_TOKEN);
        cleanupSessionStorage();

        // Dismiss loading toast and show success message
        toast.dismiss(loadingToast);
        toast.success("Registration submitted successfully!");

        // Get current event slug from URL for proper redirect
        const pathSegments = window.location.pathname.split("/");
        const eventSlug = pathSegments[2]; // Assuming /e/[slug]/...
        router.replace(`/e/${eventSlug}?oid=registration-success`);
        return;
      }

      if (onSubmit) {
        try {
          await onSubmit(values);
        } catch (customSubmitError) {
          toast.dismiss(loadingToast);
          handleApiError(customSubmitError, "Failed to submit registration. Please try again.");
          console.error("Custom submit error:", customSubmitError);
        }
      }
    } catch (error) {
      console.error("Error submitting registration:", error);

      // Dismiss loading toast and show error message
      toast.dismiss(loadingToast);
      handleApiError(error, "Registration failed. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-3 md:space-y-4">
      <h2 className="text-[16px] md:text-[18px] font-[600] text-[#424242]">
        Contact
      </h2>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-3 md:space-y-4"
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="Name"
                    className="h-10 md:h-11 bg-[#FCFCFC] rounded-lg border-[0.87px] border-gray-200 px-3 md:px-4 py-2 text-base md:text-lg leading-relaxed placeholder:text-neutral-400"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="email"
                    inputMode="email"
                    placeholder="Email"
                    className="h-10 md:h-11 bg-[#FCFCFC] rounded-lg border-[0.87px] border-gray-200 px-3 md:px-4 py-2 text-base md:text-lg leading-relaxed placeholder:text-neutral-400"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    type="tel"
                    inputMode="tel"
                    placeholder="(*************"
                    className="h-10 md:h-11 bg-[#FCFCFC] rounded-lg border-[0.87px] border-gray-200 px-3 md:px-4 py-2 text-base md:text-lg leading-relaxed placeholder:text-neutral-400"
                    value={field.value ? formatPhoneNumber(field.value) : ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      // Only allow digits and remove any formatting for storage
                      const digitsOnly = value.replace(/[^\d]/g, "");
                      // Limit to 10 digits
                      const limitedDigits = digitsOnly.slice(0, 10);
                      field.onChange(limitedDigits);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="pt-2">
            <Button
              type="submit"
              className="w-full bg-[#007AFF] hover:bg-[#007AFF]/80 text-white font-[500] text-[16px] md:text-[18px] rounded-[12px] py-[12px] md:py-[15px] h-[48px] md:h-[52px]"
              disabled={isProcessing || form.formState.isSubmitting}
              loading={isProcessing || form.formState.isSubmitting}
            >
              Submit for approval
            </Button>
          </div>
        </form>
      </Form>
      <WaiverModal
        isOpen={showWaiver}
        onClose={handleWaiverClose}
        onSave={handleWaiverAcceptWithRegistration}
        pdfUrl={allWaivers[currentWaiverIndex]?.waiverUrl || ""}
      />
    </div>
  );
};

export default ContactForm;
