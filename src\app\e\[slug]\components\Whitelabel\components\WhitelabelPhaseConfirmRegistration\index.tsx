import { WhitelabelHeader } from "../shared/WhitelabelHeader";
import CartItems from "./components/CartItem";
import ContactForm from "./components/ContactForm";

const WhitelabelPhaseConfirmRegistration = ({
  layoutConfig,
  slug,
}: {
  layoutConfig: any;
  slug: string;
}) => {
  return (
    <div className="min-h-[calc(100vh-200px)] pt-18 mb-8 md:mb-16">
      {layoutConfig && (
        <WhitelabelHeader
          layoutConfig={layoutConfig}
          slug={slug}
          showCartInfo={false}
        />
      )}
      <div className="mt-8 md:mt-[150px] max-w-[1060px] mx-auto bg-[#F9F9F9] rounded-[20px] md:rounded-[38px] grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 p-4 md:p-6">
        <div className="p-4 md:p-6 lg:p-10 order-2 lg:order-1">
          <h1 className="text-[20px] md:text-[24px] font-[600]">
            Confirm Registration
          </h1>
          <p className="text-[#373737] text-[14px] md:text-[15px] mt-[6px] mb-[20px] md:mb-[30px]">
            You won't need to pay at this moment. If the organizer approves your
            registration, you'll get an email with a payment link.
          </p>
          <ContactForm />
        </div>
        <div className="p-4 md:p-6 order-1 lg:order-2">
          <CartItems />
        </div>
      </div>
    </div>
  );
};

export default WhitelabelPhaseConfirmRegistration;
