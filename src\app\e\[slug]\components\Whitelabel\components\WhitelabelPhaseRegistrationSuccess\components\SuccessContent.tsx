import { IMAGE_LINKS } from "@/lib/utils/image-links";
import Image from "next/image";
import Link from "next/link";

const SuccessContent = () => {
  return (
    <div className="flex flex-col lg:flex-row justify-center items-center lg:items-start gap-6 lg:gap-[30px] min-h-screen px-4 py-8 lg:py-0">
      <div className="mt-8 lg:mt-[200px] w-full max-w-[334px] text-center">
        <div className="flex justify-center items-center mb-6 lg:mb-[25px]">
          <Image
            src="/checkmark-circle.svg"
            className="w-12 h-12 sm:w-16 sm:h-16 lg:w-[4.5rem] lg:h-[4.5rem]"
            alt="AutoLNK order checkmark-circle"
            width={65}
            height={65}
          />
        </div>
        <h1 className="text-xl sm:text-2xl lg:text-[24px] font-semibold text-black mb-12 lg:mb-[80px]">
          Registration Submitted
        </h1>

        <p className="text-gray-700 text-lg sm:text-xl lg:text-[20px] font-normal mb-6 lg:mb-[25px] leading-relaxed">
          Download AutoLNK to track the current
          <br className="hidden sm:block" /> status of your registration
        </p>
        <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-2">
          <Link
            href="https://apps.apple.com/in/app/autolnk/id6478376890"
            target="_blank"
            className="w-full sm:w-36"
          >
            <Image
              src={IMAGE_LINKS.DOWNLOAD_ON_APPSTORE}
              alt="AutoLNK Download on App Store"
              width={135}
              height={40}
              className="h-10 w-auto object-contain mx-auto"
              style={{
                imageRendering: "auto",
                shapeRendering: "geometricPrecision",
              }}
              priority
            />
          </Link>
          <Link
            href="https://play.google.com/store/apps/details?id=com.xcelerate.xcelerate"
            target="_blank"
            className="w-full sm:w-36"
          >
            <Image
              src={IMAGE_LINKS.DOWNLOAD_ON_GOOGLE}
              alt="Autolnk Download on Google Play"
              width={135}
              height={40}
              className="h-10 w-auto object-contain mx-auto"
              style={{
                imageRendering: "auto",
                shapeRendering: "geometricPrecision",
              }}
              priority
            />
          </Link>
        </div>
      </div>
      <div className="mt-0 lg:mt-[134px] w-full max-w-[500px] lg:w-auto">
        <Image
          src="/registration-success.png"
          alt="success"
          width={500}
          height={500}
          className="w-full h-auto max-w-[300px] sm:max-w-[400px] lg:max-w-[500px] lg:w-[500px] lg:h-[580px] mx-auto object-contain"
        />
      </div>
    </div>
  );
};

export default SuccessContent;