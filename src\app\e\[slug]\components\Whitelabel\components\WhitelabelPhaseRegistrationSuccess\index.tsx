"use client";

import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import SuccessContent from "./components/SuccessContent";
import LoadingState from "./components/LoadingState";
import { WhitelabelHeader } from "../shared/WhitelabelHeader";

interface WhitelabelPhaseRegistrationSuccessProps {
  isBarHeader?: boolean;
  className?: string;
  layoutConfig: any;
  slug: string;
}

const WhitelabelPhaseRegistrationSuccess = ({
  isBarHeader,
  className,
  layoutConfig,
  slug,
}: WhitelabelPhaseRegistrationSuccessProps) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time to match the behavior of the original success page
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen">
      {layoutConfig && (
        <WhitelabelHeader
          layoutConfig={layoutConfig}
          slug={slug}
          showCartInfo={false}
        />
      )}
      <div className={cn(isBarHeader && "pt-16", className)}>
        {isLoading ? <LoadingState /> : <SuccessContent />}
      </div>
    </div>
  );
};

export default WhitelabelPhaseRegistrationSuccess;
