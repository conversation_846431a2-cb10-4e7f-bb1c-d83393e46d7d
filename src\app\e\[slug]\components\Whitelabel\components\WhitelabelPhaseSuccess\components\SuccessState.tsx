import OrderConfirmation from "@/app/order/components/OrderConfirmation";
import PurchaseTracker from "@/app/order/success/components/PurchaseTracker";
import { SuccessData } from "@/app/order/success/types";
import { cn } from "@/lib/utils";

interface SuccessStateProps {
  successData: SuccessData | null;
  eventName?: string;
  isBarHeader?: boolean;
}

export function SuccessState({
  successData,
  eventName,
  isBarHeader,
}: SuccessStateProps) {
  return (
    <div className={cn("min-h-screen bg-white", isBarHeader && "pt-16")}>
      {successData?.order?.id && (
        <PurchaseTracker
          orderId={successData?.order?.id}
          pixels={successData?.order?.organization?.pixels || {}}
        />
      )}

      <div className="flex justify-center">
        <div className="w-full max-w-4xl px-4 md:px-10 py-8">
          <div className="mb-6 text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Order Complete
            </h1>
            <p className="text-gray-600">
              Your order for <strong>{eventName}</strong> has been processed
              successfully
            </p>
          </div>

          <div className="flex items-center justify-center bg-white">
            <div className="w-full max-w-[440px]">
              <OrderConfirmation data={successData} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
