import { useEffect } from "react";
import {
  useGetCartQuery,
  useInitializePaymentGatewayQuery,
} from "@/lib/redux/slices/cart/cartApi";
import { useSessionData } from "@/lib/hooks/useSession";
import { useStripeInit } from "@/app/checkout/hooks/useStripeInit";
import { useDeferredPayment } from "@/app/checkout/hooks/useDeferredPayment";
import { STORAGE_KEYS, PAYMENT_OPTIONS } from "@/lib/constants/storage";

interface UseWhitelabelPaymentProps {
  localClientSecret: string | null;
  localConnectId: string | null;
  isCollaboratorCheckout: boolean;
  updatePaymentData: (
    newClientSecret: string,
    newConnectId: string,
    isCollaboratorCheckoutUpdate: boolean
  ) => void;
  setLocalClientSecret: (secret: string | null) => void;
  setLocalConnectId: (id: string | null) => void;
  setIsCollaboratorCheckout: (isCollaborator: boolean) => void;
}

export function useWhitelabelPayment({
  localClientSecret,
  localConnectId,
  isCollaboratorCheckout,
  updatePaymentData,
  setLocalClientSecret,
  setLocalConnectId,
  setIsCollaboratorCheckout,
}: UseWhitelabelPaymentProps) {
  const { data: session } = useSessionData();
  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);

  const {
    data: cartData,
    isLoading: isCartLoading,
    isUninitialized,
    refetch,
  } = useGetCartQuery(undefined, {
    skip: !cartToken && !session?.user,
  });

  const isPaymentLinkMethod =
    cartData?.checkout?.lines?.some(
      (line) =>
        line?.eventDetails?.paymentOption === PAYMENT_OPTIONS?.PAYMENT_LINK
    ) || false;

  const { data: paymentGatewayData } = useInitializePaymentGatewayQuery();

  const { stripePromise, initializeStripe } = useStripeInit({
    publishableKey: paymentGatewayData?.data?.publishableKey,
    connectId: localConnectId || null,
    isCollaboratorCheckout,
  });

  const {
    createPaymentIntent,
    isCreatingPayment,
    error: paymentError,
    clientSecret: deferredClientSecret,
    connectId: deferredConnectId,
    hasPaymentIntent,
  } = useDeferredPayment({
    cartData,
    sessionUser: session?.user,
    onPaymentIntentCreated: (
      newClientSecret,
      newConnectId,
      isCollaboratorCheckoutUpdate
    ) => {
      updatePaymentData(newClientSecret, newConnectId, isCollaboratorCheckoutUpdate);
      initializeStripe(newConnectId, isCollaboratorCheckoutUpdate);
      // Only refetch if query is properly initialized
      if (!isUninitialized) {
        refetch();
      }
    },
  });

  const currentClientSecret = localClientSecret || deferredClientSecret;

  // Initialize Stripe when connectId is available
  useEffect(() => {
    const connectIdToUse = localConnectId || deferredConnectId;

    if (connectIdToUse && !stripePromise) {
      initializeStripe(connectIdToUse, isCollaboratorCheckout);
    }
  }, [
    localConnectId,
    deferredConnectId,
    stripePromise,
    initializeStripe,
    isCollaboratorCheckout,
  ]);

  const refreshCartData = async () => {
    if (!isUninitialized) {
      await refetch();
    }
  };

  // Check if checkout is free
  const isFree =
    cartData?.checkout?.lines?.length === 0 ||
    (cartData?.checkout as any)?.total === "0.00";

  return {
    cartData,
    isCartLoading,
    stripePromise,
    currentClientSecret,
    createPaymentIntent,
    isCreatingPayment,
    paymentError,
    hasPaymentIntent,
    refreshCartData,
    isFree,
    isPaymentLinkMethod,
  };
} 