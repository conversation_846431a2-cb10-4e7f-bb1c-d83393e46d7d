import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { STORAGE_KEYS } from "@/lib/constants/storage";

interface UseWhitelabelStorageProps {
  slug: string;
}

export function useWhitelabelStorage({ slug }: UseWhitelabelStorageProps) {
  const router = useRouter();
  const [basicUserInfo, setBasicUserInfo] = useState<any>(null);
  const [localClientSecret, setLocalClientSecret] = useState<string | null>(null);
  const [localConnectId, setLocalConnectId] = useState<string | null>(null);
  const [isCollaboratorCheckout, setIsCollaboratorCheckout] = useState<boolean>(false);

  // Load data from localStorage on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Load basic user info
      const storedBasicInfo = localStorage.getItem("whitelabel_basic_info");
      if (storedBasicInfo) {
        setBasicUserInfo(JSON.parse(storedBasicInfo));
      } else {
        // If no basic info found, redirect back to contact phase
        router.push(`/e/${slug}?oid=contact`);
        return;
      }

      // Load existing payment info
      const storedClientSecret = localStorage.getItem(STORAGE_KEYS.CLIENT_SECRET);
      const storedConnectId = localStorage.getItem(STORAGE_KEYS.CONNECT_ID);
      const storedIsCollaboratorCheckout = localStorage.getItem(
        STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT
      );

      if (storedClientSecret) {
        setLocalClientSecret(storedClientSecret);
      }
      if (storedConnectId) {
        setLocalConnectId(storedConnectId);
      }
      if (storedIsCollaboratorCheckout) {
        setIsCollaboratorCheckout(storedIsCollaboratorCheckout === "true");
      }
    }
  }, [router, slug]);

  // Function to update payment data in both state and localStorage
  const updatePaymentData = (
    newClientSecret: string,
    newConnectId: string,
    isCollaboratorCheckoutUpdate: boolean
  ) => {
    if (typeof window !== "undefined") {
      localStorage.setItem(STORAGE_KEYS.CLIENT_SECRET, newClientSecret);
      localStorage.setItem(STORAGE_KEYS.CONNECT_ID, newConnectId);
      localStorage.setItem(
        STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT,
        String(isCollaboratorCheckoutUpdate)
      );
    }

    setLocalClientSecret(newClientSecret);
    setLocalConnectId(newConnectId);
    setIsCollaboratorCheckout(isCollaboratorCheckoutUpdate);
  };

  return {
    basicUserInfo,
    localClientSecret,
    localConnectId,
    isCollaboratorCheckout,
    updatePaymentData,
    setLocalClientSecret,
    setLocalConnectId,
    setIsCollaboratorCheckout,
  };
} 