import { z } from "zod";
import { validateEmailWithTLD, INVALID_EMAIL_ADDRESS } from "@/app/checkout/constants/formMessages";

// Validation schema
export const basicUserInfoSchema = z.object({
  email: z
    .string()
    .email({message: INVALID_EMAIL_ADDRESS})
    .min(1, { message: "Email is required" })
    .refine((email) => {
      const validation = validateEmailWithTLD(email);
      return validation.isValid;
    }, (email) => {
      const validation = validateEmailWithTLD(email);
      return { 
        message: validation.error || INVALID_EMAIL_ADDRESS,
        suggestion: validation.suggestion 
      };
    })
    .transform((value) => value.trim().toLowerCase()),
  firstName: z
    .string()
    .min(1, { message: "First name is required" })
    .min(2, { message: "First name must be at least 2 characters" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "First name cannot contain only whitespace",
    })
    .refine((value) => /^[a-zA-Z\s]+$/.test(value), {
      message: "First name can only contain letters and spaces",
    }),
  lastName: z
    .string()
    .min(1, { message: "Last name is required" })
    .min(2, { message: "Last name must be at least 2 characters" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Last name cannot contain only whitespace",
    })
    .refine((value) => /^[a-zA-Z\s]+$/.test(value), {
      message: "Last name can only contain letters and spaces",
    }),
  phone: z
    .string()
    .min(1, { message: "Phone number is required" }),
  smsUpdates: z.boolean().default(true),
  newsletter: z.boolean().default(true),
});

export type BasicUserInfo = z.infer<typeof basicUserInfoSchema>;

export const DEFAULT_FORM_VALUES: BasicUserInfo = {
  email: "",
  firstName: "",
  lastName: "",
  phone: "",
  smsUpdates: true,
  newsletter: true,
}; 