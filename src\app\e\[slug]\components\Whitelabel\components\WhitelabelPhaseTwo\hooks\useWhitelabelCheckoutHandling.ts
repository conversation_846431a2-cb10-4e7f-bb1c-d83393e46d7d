import { useState } from "react";
import { useRouter } from "next/navigation";
import { useUpdateQuantityMutation } from "@/lib/redux/slices/cart/cartApi";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import { isZero } from "@/lib/utils/numberUtil";
import toast from "react-hot-toast";
import { handleApiError } from "@/lib/utils/errorUtils";
import { STORAGE_KEYS } from "@/lib/constants/storage";

type WaiverData = {
  waiverUrl: string;
  waiverSignature: string;
  waiverSignedDateTime: string;
  timezone: string;
};

export const useWhitelabelCheckoutHandling = (cartData: any, refetchCart: () => void, slug: string) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [updateQuantity] = useUpdateQuantityMutation();

  const handleCheckout = async (waiverSignatures: any = {}) => {
    if (loading || isSubmitting) return;

    const isZeroPrice = isZero(cartData?.checkout?.total || 0);

    setLoading(true);
    setIsSubmitting(true);

    try {
      if (cartData?.checkout?.token) {
        const linesWithWaivers = cartData?.checkout?.lines?.filter((line: any) => {
          if (line.ticketWaiverUrl && waiverSignatures[line.ticketWaiverUrl.id]) {
            return true;
          }
          if (line.waiverResponseData?.length > 0) {
            return line.waiverResponseData.some(
              (waiver: any) => waiverSignatures[`${line.id}-${waiver.waiverUrl}`]
            );
          }
          return false;
        }) || [];

        if (linesWithWaivers.length > 0) {
          const updatePromises = linesWithWaivers.map((line: any) => {
            const waiverData: WaiverData[] = [];

            if (line.ticketWaiverUrl && waiverSignatures[line.ticketWaiverUrl.id]) {
              const waiverInfo = waiverSignatures[line.ticketWaiverUrl.id];
              waiverData.push({
                waiverUrl: line.ticketWaiverUrl.waiverUrl,
                waiverSignature: waiverInfo.signature,
                waiverSignedDateTime: waiverInfo.signedDateTime,
                timezone: waiverInfo.timezone,
              });
            }

            if (line.waiverResponseData?.length > 0) {
              line.waiverResponseData.forEach((waiver: any) => {
                const waiverInfo = waiverSignatures[`${line.id}-${waiver.waiverUrl}`];
                if (waiverInfo) {
                  waiverData.push({
                    waiverUrl: waiver.waiverUrl,
                    waiverSignature: waiverInfo.signature,
                    waiverSignedDateTime: waiverInfo.signedDateTime,
                    timezone: waiverInfo.timezone,
                  });
                }
              });
            }

            const payload = keysToSnake({
              id: line?.id,
              type: "event_ticket",
              quantity: line.quantity,
              waiverResponseData: waiverData,
            });

            return updateQuantity(payload).unwrap();
          });

          await Promise.all(updatePromises);
        }

        const checkoutToken = cartData.checkout.token;
        localStorage.setItem(STORAGE_KEYS.CART_TOKEN, checkoutToken);
        
        router.push(`/e/${slug}?oid=checkout`);
      }
    } catch (error) {
      handleApiError(error, "Checkout failed. Please try again.");
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  return {
    loading,
    isSubmitting,
    handleCheckout,
  };
}; 