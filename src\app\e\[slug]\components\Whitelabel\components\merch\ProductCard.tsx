import { Product } from "../../../../../../event/types";
import { getFirstNonZeroVariantPrice } from "@/lib/utils/product";
import Image from "next/image";
import React from "react";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
interface ProductCardProps {
  product: Product;
  onClick: (product: Product) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onClick,
}) => {
  const mainImage =
    product?.media?.find((image: any) => image?.isVariantImage === false) ||
    product?.media?.[0];

  const price = Number(getFirstNonZeroVariantPrice(product) || 0)?.toFixed(2);

  return (
    <div
      onClick={() => onClick(product)}
      className="rounded-lg p-1 cursor-pointer"
    >
      <div className="aspect-square mb-1 bg-white border-2 border-gray-300 rounded-2xl overflow-hidden">
        <Image
          src={mainImage?.image ?? IMAGE_LINKS.NO_IMG}
          alt={product?.name + " Autolnk Product Image"}
          width={500}
          height={500}
          className="w-full h-full object-contain"
        />
      </div>
      <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">
        {product?.name}
      </h3>
      <p className="text-[11px] font-semibold text-gray-900">${price}</p>
    </div>
  );
};
