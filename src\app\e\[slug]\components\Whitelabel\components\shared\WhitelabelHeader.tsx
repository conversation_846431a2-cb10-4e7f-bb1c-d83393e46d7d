import React from "react";
import { cn } from "@/lib/utils";
import NeedHelpModal from "@/app/e/[slug]/components/Whitelabel/components/NeedHelpModal";
import CartInfo from "@/app/e/[slug]/components/Whitelabel/components/CartInfo/CartInfo";

interface WhitelabelHeaderProps {
  layoutConfig: {
    headerStyle?: string;
    stickyHeader?: boolean;
    showNeedHelp?: boolean;
    logoImage?: string;
  };
  slug: string;
  showCartInfo?: boolean;
}

export const WhitelabelHeader: React.FC<WhitelabelHeaderProps> = ({
  layoutConfig,
  slug,
  showCartInfo = true,
}) => {
  if (layoutConfig.headerStyle === "bar") {
    return (
      <div
        className={cn(
          "w-full z-[999] top-0 left-0 right-0 h-[50px] bg-[#141414] border-b border-[#202020] flex items-center justify-between px-[5vw] mx-auto shadow-md",
          layoutConfig.stickyHeader ? "fixed" : "absolute"
        )}
      >
        <img
          src={layoutConfig?.logoImage || ""}
          alt="logo"
          className="max-h-[80%] max-w-[130px] object-contain"
        />
        <div className="flex items-center gap-8">
          {layoutConfig.showNeedHelp && <NeedHelpModal isBarHeader={true} />}
          {showCartInfo && <CartInfo isBarHeader={true} slug={slug} />}
        </div>
      </div>
    );
  } else if (layoutConfig.headerStyle === "float") {
    return (
      <div
        className={cn(
          "w-full z-[999] top-5 right-0 left-0 w-full flex justify-end items-center gap-3 !px-[30px] mx-auto",
          layoutConfig.stickyHeader ? "fixed" : "absolute"
        )}
      >
        <div className="flex items-center gap-2">
          {layoutConfig.showNeedHelp && <NeedHelpModal isBarHeader={false} />}
          {showCartInfo && <CartInfo isBarHeader={false} slug={slug} />}
        </div>
      </div>
    );
  }
  return <></>;
};
