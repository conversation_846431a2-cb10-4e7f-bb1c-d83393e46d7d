import { useState, useCallback, useRef } from "react";
import { MODAL_TYPES } from "@/app/events/constants";

export type ModalType = (typeof MODAL_TYPES)[keyof typeof MODAL_TYPES];

export const useModalState = () => {
  const [modalState, setModalState] = useState<Record<ModalType, boolean>>({
    [MODAL_TYPES.TICKET_FORM]: false,
    [MODAL_TYPES.GUEST]: false,
    [MODAL_TYPES.TICKET_UPDATE]: false,
    [MODAL_TYPES.CREATE_TEAM_ACCOUNT]: false,
  });

  const lastToggleTime = useRef<Record<ModalType, number>>(
    {} as Record<ModalType, number>
  );

  const toggleModal = useCallback((modalType: ModalType) => {
    const now = Date.now();
    const lastTime = lastToggleTime.current[modalType] || 0;

    if (now - lastTime > 300) {
      setModalState((prev) => ({ ...prev, [modalType]: !prev[modalType] }));
      lastToggleTime.current[modalType] = now;
    }
  }, []);

  return { modalState, toggleModal };
};
