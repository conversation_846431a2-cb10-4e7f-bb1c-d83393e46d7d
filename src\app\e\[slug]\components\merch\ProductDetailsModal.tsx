import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dalBody,
  ModalContent,
  ModalHeader,
  Select,
  SelectItem,
} from "@nextui-org/react";
import { Product, Variant } from "@/app/events/types";
import { useGetProductDetailsQuery } from "@/lib/redux/slices/products/productsApi";
import parse from "html-react-parser";
import Image from "next/image";
import ProductSkeleton from "./ProductSkeleton";
import { useDispatch } from "react-redux";
import { addProduct } from "@/lib/redux/slices/cart/cartSlice";
import {
  useCheckInventoryQuery,
  useAddToCartMutation,
} from "@/lib/redux/slices/cart/cartApi";
import toast from "react-hot-toast";
import { useSessionData } from "@/lib/hooks/useSession";
import { CART_ERRORS, CART_STORAGE_KEYS } from "@/lib/utils/constants";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { handleInvalidCartToken } from "@/app/cart/utils/cartUtils";
import { useRouter } from "next/navigation";
import { handleApiError } from "@/lib/utils/errorUtils";

interface ProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
  isWhitelabel?: boolean;
  slug?: string;
}

interface VariantAttribute {
  attribute: {
    id: number;
    name: string;
  };
  values: Array<{
    id: number;
    name: string;
  }>;
}

export const ProductModal: React.FC<ProductModalProps> = ({
  isOpen,
  onClose,
  product,
  isWhitelabel,
  slug,
}) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
  const [quantity, setQuantity] = useState<number>(1);
  const [selectedAttributes, setSelectedAttributes] = useState<
    Record<string, string>
  >({});
  const [stockError, setStockError] = useState<string>("");

  const router = useRouter();

  const { data: sessionData } = useSessionData();

  // Store matching variant in state
  const [currentVariant, setCurrentVariant] = useState<Variant | null>(null);

  // Update currentVariant whenever selected attributes change
  useEffect(() => {
    const newVariant = getMatchingVariant();
    setCurrentVariant(newVariant);
  }, [selectedAttributes, product]);

  const dispatch = useDispatch();

  const [addToCart] = useAddToCartMutation();

  // Get unique attribute types from variants
  const getAttributeTypes = () => {
    if (!product?.variants) return [];
    const attributeTypes = new Set<string>();
    product.variants.forEach((variant: Variant) => {
      variant.attributes?.forEach((attr) => {
        attributeTypes.add(attr.attribute.name);
      });
    });
    return Array.from(attributeTypes);
  };

  // Get available values for a specific attribute type
  const getAttributeValues = (attributeType: string) => {
    if (!product?.variants) return [];
    const values = new Set<string>();
    product.variants.forEach((variant: Variant) => {
      variant.attributes?.forEach((attr) => {
        if (attr.attribute.name === attributeType) {
          attr.values.forEach((value) => {
            values.add(value.name);
          });
        }
      });
    });
    return Array.from(values);
  };

  // Find matching variant based on selected attributes
  const getMatchingVariant = (): Variant | null => {
    if (!product?.variants) return null;

    // Get all attribute types that have been selected
    const selectedAttributeTypes = Object.keys(selectedAttributes);

    // If no attributes are selected, return the default variant or first variant
    if (selectedAttributeTypes.length === 0) {
      return (
        product.variants.find((variant) => variant.attributes?.length === 0) ||
        product.variants[0] ||
        null
      );
    }

    // Find variant that matches all selected attributes
    const matchingVariant =
      product.variants.find((variant) => {
        // Skip variants with no attributes if we have selected attributes
        if (!variant.attributes || variant.attributes.length === 0) {
          return false;
        }

        // Check if this variant has all the required attributes and they match
        return selectedAttributeTypes.every((attributeType) => {
          const selectedValue = selectedAttributes[attributeType];

          // Find if this variant has this attribute type with the selected value
          return variant.attributes.some(
            (attr) =>
              attr.attribute.name === attributeType &&
              attr.values?.some((value) => value.name === selectedValue)
          );
        });
      }) || null;

    return matchingVariant;
  };

  // Check stock availability and update error message
  const checkStock = (
    variant: Variant | null,
    requestedQuantity: number
  ): boolean => {
    if (!variant) return false;

    const availableStock = variant.stock?.quantity ?? 0;
    if (availableStock < requestedQuantity) {
      setStockError(`Only ${availableStock} items available`);
      return false;
    }
    setStockError("");
    return true;
  };

  useEffect(() => {
    if (!product?.variants?.length) return;
    // Reset selected attributes and quantity when product changes
    const defaultAttributes: Record<string, string> = {};
    const attributeTypes = getAttributeTypes();
    attributeTypes.forEach((type) => {
      const values = getAttributeValues(type);
      if (values.length > 0) {
        defaultAttributes[type] = values[0];
      }
    });
    setSelectedAttributes(defaultAttributes);
    setQuantity(1);
    setStockError("");
  }, [product]);

  // Check stock whenever quantity or selected attributes change
  useEffect(() => {
    const matchingVariant = getMatchingVariant();
    if (matchingVariant) {
      checkStock(matchingVariant, quantity);
    }
  }, [quantity, selectedAttributes]);

  if (!product) return null;

  // Use currentVariant instead of calling getMatchingVariant multiple times
  const attributeTypes = getAttributeTypes();

  const handleQuantityChange = (newQuantity: number) => {
    if (currentVariant) {
      const availableStock = currentVariant.stock?.quantity ?? 0;
      if (newQuantity <= availableStock) {
        setQuantity(newQuantity);
      }
    }
  };

  const handleAddToCart = async () => {
    if (!currentVariant) return;

    // Check current inventory before proceeding
    try {
      const stockQuantity = currentVariant.stock?.quantity ?? 0;
      if (stockQuantity < quantity) {
        toast.error(
          `Only ${stockQuantity} items available. Please reduce quantity.`
        );
        return;
      }

      // Proceed with adding to cart if stock check passes
      let response;
      try {
        response = await addToCart({
          type: "product",
          id: currentVariant.id,
          quantity,
        });
        if (
          response?.error?.data?.error === CART_ERRORS.INVALID_CART_TOKEN ||
          response?.error?.status === CART_ERRORS.PARSING_ERROR
        ) {
          handleInvalidCartToken();
          return handleAddToCart();
        }
        if (isWhitelabel) {
          router.push(`/e/${slug}?oid=contact`);
        }
      } catch (error: any) {
        throw error;
      }

      // Todo: remove in future this is temp solution (open)

      const getLocalVariants = localStorage.getItem(
        CART_STORAGE_KEYS.CART_VARIANTS
      );

      const localVariants = JSON.parse(getLocalVariants || "{}");

      localVariants[currentVariant.id] = {
        id: currentVariant.id,
        image: currentVariant.media?.[0]?.image ?? product?.media?.[0]?.image,
      };

      localStorage.setItem(
        CART_STORAGE_KEYS.CART_VARIANTS,
        JSON.stringify(localVariants)
      );

      // Todo: remove in future this is temp solution (close)

      if (!sessionData?.user) {
        const checkoutToken = response?.data?.checkout?.token;
        if (checkoutToken) {
          localStorage.setItem(CART_STORAGE_KEYS.CART_TOKEN, checkoutToken);
        }
      }

      if (response?.error) {
        handleApiError(response.error, "Failed to add item to cart. Please try again later.");
        return;
      }
      toast.success("Item added to cart successfully!");
      onClose();
    } catch (error) {
      handleApiError(error, "Something went wrong. Please try again later.");
    }
  };

  const renderProductMedia = () => {
    if (!product?.media?.length) return null;

    return product.media.map((media, index) => (
      <button
        key={index}
        className={`min-w-[50px] min-h-[50px] sm:min-w-[70px] sm:min-h-[70px] flex-shrink-0 border-2 rounded overflow-hidden
        ${selectedImageIndex === index ? "border-red-500" : "border-gray-200"}`}
        onClick={() => setSelectedImageIndex(index)}
      >
        <Image
          src={media?.image ?? IMAGE_LINKS.NO_IMG}
          width={70}
          height={70}
          alt={`${product?.name} view ${index + 1} in Autolnk`}
          className="w-full h-full object-cover"
        />
      </button>
    ));
  };

  const isAddToCartBtnDisabled =
    !currentVariant ||
    stockError !== "" ||
    (currentVariant?.stock?.quantity ?? 0) === 0;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="4xl"
      scrollBehavior="outside"
    >
      <ModalContent>
        <ModalBody className="gap-4 my-4 sm:gap-6 sm:my-10">
          <div className="flex flex-col lg:flex-row lg:gap-10">
            {/* Left column - Image gallery */}
            <div className="w-full lg:w-1/2">
              <div className="flex gap-2 sm:gap-4 flex-row">
                {/* Thumbnails */}

                <div className="w-[60px] sm:w-[100px] flex flex-col gap-2 max-h-[300px] sm:max-h-[380px] overflow-y-auto custom-scrollbar scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
                  {renderProductMedia()}
                </div>

                {/* Main image */}
                <div className="flex-1">
                  <Image
                    src={
                      product?.media?.[selectedImageIndex]?.image ??
                      IMAGE_LINKS.NO_IMG
                    }
                    width={500}
                    height={500}
                    alt={product?.name + " Autolnk Product Image"}
                    className="w-full aspect-square object-cover rounded"
                  />
                </div>
              </div>
            </div>

            {/* Right column - Product details */}
            <div className="w-full lg:w-1/2 space-y-4 sm:space-y-6 mt-6 lg:mt-0">
              {/* Product info */}
              <div className="mb-4 sm:mb-6">
                <h2 className="text-lg sm:text-xl font-medium text-gray-900 uppercase mb-1 max-w-full break-words">
                  {product?.name}
                </h2>
                <p className="text-gray-500">
                  {currentVariant ? (
                    `$${Number(currentVariant?.price)?.toFixed(2)}`
                  ) : (
                    <span className="text-red-500">Not Available</span>
                  )}
                </p>
                <div className="mt-1">
                  <p className="text-gray-400 text-xs">
                    <span className="text-red-500 mr-1">Shipping</span>
                    calculated at checkout
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between gap-4 sm:gap-2 items-start">
                {/* Variant selections */}
                <div className="space-y-4 w-full sm:w-[70%]">
                  {attributeTypes.map((attributeType) => (
                    <div key={attributeType} className="w-full">
                      <label className="text-xs text-gray-400 uppercase">
                        Select {attributeType}
                      </label>
                      <Select
                        radius="none"
                        placeholder={`Select ${attributeType}`}
                        size="lg"
                        selectedKeys={[selectedAttributes[attributeType]]}
                        onChange={(e) => {
                          setSelectedAttributes({
                            ...selectedAttributes,
                            [attributeType]: e.target.value,
                          });
                          setQuantity(1); // Reset quantity when variant changes
                        }}
                      >
                        {getAttributeValues(attributeType).map((value) => (
                          <SelectItem key={value} value={value}>
                            {value}
                          </SelectItem>
                        ))}
                      </Select>
                    </div>
                  ))}
                </div>

                {/* Quantity selector */}
                <div className="w-full sm:w-auto">
                  <span className="text-xs text-gray-400 uppercase block mb-2">
                    Quantity
                  </span>
                  <div className="flex border w-full sm:w-auto">
                    <button
                      onClick={() =>
                        handleQuantityChange(Math.max(1, quantity - 1))
                      }
                      className="w-12 sm:w-6 h-12 flex items-center justify-center border-r bg-[#F4F4F5] hover:bg-[#E4E4E7]"
                      disabled={quantity <= 1}
                    >
                      -
                    </button>
                    <span className="flex-1 sm:w-8 h-12 flex items-center justify-center bg-[#F4F4F5]">
                      {quantity}
                    </span>
                    <button
                      onClick={() => handleQuantityChange(quantity + 1)}
                      className="w-12 sm:w-6 h-12 flex items-center justify-center border-l bg-[#F4F4F5] hover:bg-[#E4E4E7]"
                      disabled={
                        !!(
                          currentVariant &&
                          quantity >= (currentVariant.stock?.quantity ?? 0)
                        )
                      }
                    >
                      +
                    </button>
                  </div>
                </div>
              </div>

              {stockError && (
                <p className="text-red-500 text-sm">{stockError}</p>
              )}

              {/* Add to cart button */}
              <Button
                onPress={handleAddToCart}
                className="w-full h-12 bg-red-500 text-white uppercase font-medium rounded hover:bg-red-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                disabled={isAddToCartBtnDisabled}
                isDisabled={isAddToCartBtnDisabled}
              >
                {(currentVariant?.stock?.quantity ?? 0) === 0
                  ? "Out of Stock"
                  : "Add to Cart"}
              </Button>

              <div className="text-xs text-gray-400 mt-4">
                {parse(product?.descriptionPlaintext || "")}
              </div>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
