import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";
import { STORAGE_KEYS } from "@/lib/constants/storage";
import { useSessionData } from "@/lib/hooks/useSession";

interface UseTicketInCartReturn {
  isTicketInCart: boolean;
  hasForm: boolean;
  otherTicketsExist: boolean;
  isLoading: boolean;
}

export const useTicketInCart = (
  ticketId: string,
): UseTicketInCartReturn => {
  const { data: session } = useSessionData();
  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);

  const { data: cartData, isLoading } = useGetCartQuery(undefined, {
    refetchOnFocus: true,
    refetchOnReconnect: true,
    refetchOnMountOrArgChange: true,
    skip: !cartToken && !session?.user,
  });

  const isTicketInCart = cartData?.checkout?.lines?.some(
    (line) => line.eventTicket === ticketId
  ) || false;

  const hasForm = cartData?.checkout?.lines?.some(
    (line) => line.eventTicket === ticketId && line.formResponseData?.length > 0
  ) || false;

  const otherTicketsExist = false;

  return {
    isTicketInCart,
    hasForm,
    otherTicketsExist,
    isLoading,
  };
}; 