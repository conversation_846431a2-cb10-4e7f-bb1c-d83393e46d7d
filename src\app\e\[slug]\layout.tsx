import TopNavBar from "@/features/TopNavBar/TopNavBar";
import { getSubdomainInfo } from "@/lib/utils/subdomainUtils";
import { headers } from "next/headers";

export default async function EventLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const headersList = await headers();
  const hostHeader = headersList.get("host");

  const { isSubdomain } = await getSubdomainInfo(hostHeader || "");

  if (isSubdomain) {
    return (
      <div className="flex flex-col justify-between">
        <main className="w-full relative h-screen">{children}</main>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-between">
      <main className="w-full relative h-screen">
        <TopNavBar>{children}</TopNavBar>
      </main>
    </div>
  );
}
