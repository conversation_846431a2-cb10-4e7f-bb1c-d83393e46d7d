import { Skeleton } from "@nextui-org/react";

export default function Loading() {
  return (
    <div className="mt-20 md:mt-12">
      {/* Banner Image */}
      <div className="hidden md:block">
        <Skeleton className="w-[100vw] aspect-[5/2] md:max-h-[60vh]" />
      </div>

      <div className="grid justify-center pb-14 mt-2">
        <div className="flex flex-col lg:flex-row justify-between p-4 gap-10 md:w-[94vw]">
          {/* Left Column */}
          <div className="lg:w-[62%] mb-5">
            {/* Event Overview */}
            <div className="mb-8">
              <div className="flex flex-col gap-4">
                <Skeleton className="h-8 w-3/4 rounded-lg" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-24 rounded-lg" />
                  <Skeleton className="h-4 w-4 rounded-full" />
                </div>
              </div>

              {/* Main Event Image */}
              <Skeleton className="w-full aspect-square md:aspect-video rounded-xl mt-6" />

              {/* Event Info */}
              <div className="mt-6 space-y-4">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-5 rounded-lg" />
                  <Skeleton className="h-5 w-32 rounded-lg" />
                </div>
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-5 rounded-lg" />
                  <Skeleton className="h-5 w-48 rounded-lg" />
                </div>
              </div>
            </div>

            {/* Event Details Section */}
            <div className="hidden lg:block space-y-6">
              <Skeleton className="h-7 w-40 rounded-lg" />
              <div className="space-y-3">
                <Skeleton className="h-4 w-full rounded-lg" />
                <Skeleton className="h-4 w-full rounded-lg" />
                <Skeleton className="h-4 w-3/4 rounded-lg" />
              </div>
            </div>
          </div>

          {/* Right Column - Ticket Section */}
          <div className="lg:w-[38%]">
            <div className="bg-default-50 rounded-xl p-6">
              <div className="space-y-6">
                {/* Ticket Types */}
                {[1, 2, 3, 4].map((i) => (
                  <TicketSkeleton key={i} />
                ))}

                {/* Total Section */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-6 w-20 rounded-lg" />
                    <Skeleton className="h-6 w-24 rounded-lg" />
                  </div>
                </div>

                {/* Apply Button */}
                <Skeleton className="h-12 w-full rounded-lg" />
              </div>
            </div>
          </div>

          {/* Mobile Event Details */}
          <div className="lg:hidden space-y-6">
            <Skeleton className="h-7 w-40 rounded-lg" />
            <div className="space-y-3">
              <Skeleton className="h-4 w-full rounded-lg" />
              <Skeleton className="h-4 w-full rounded-lg" />
              <Skeleton className="h-4 w-3/4 rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const TicketSkeleton = () => (
  <div className="border-b border-gray-200 last:border-none pb-4">
    <div className="flex flex-col gap-3">
      <div className="flex justify-between">
        <div className="space-y-2">
          <Skeleton className="h-5 w-40 rounded-lg" />
          <Skeleton className="h-4 w-32 rounded-lg" />
        </div>
        <Skeleton className="h-6 w-20 rounded-lg" />
      </div>
      <div className="flex justify-between items-center">
        <Skeleton className="h-4 w-16 rounded-lg" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-20 rounded-lg" />
        </div>
      </div>
    </div>
  </div>
);
