"use client";
import dynamic from "next/dynamic";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
const PhotoSwipeItem = dynamic(
  () =>
    import("react-photoswipe-gallery").then((mod) => {
      // Import the CSS when the component loads
      import("photoswipe/dist/photoswipe.css");
      return mod.Item;
    }),
  {
    ssr: false,
  }
);

export function EventCoverImage({
  image,
  showHeader,
  showBannerDesktop = true,
  showBannerMobile = false,
}: {
  image: any;
  showHeader: boolean;
  showBannerDesktop: boolean;
  showBannerMobile: boolean;
}) {
  const [dimensions, setDimensions] = useState({
    width: 1400,
    height: 560,
  });

  const loadImage = (
    src: string
  ): Promise<{ width: number; height: number }> => {
    return new Promise((resolve) => {
      const img = new window.Image();
      img.onload = () => {
        resolve({ width: img.naturalWidth, height: img.naturalHeight });
      };
      img.src = src;
    });
  };

  useEffect(() => {
    if (image?.photo) {
      loadImage(image.photo).then(setDimensions);
    }
  }, [image?.photo]);

  const renderEventCoverImage = () => {
    if (image) {
      return (
        <PhotoSwipeItem
          original={image?.photo}
          thumbnail={image?.photo}
          width={dimensions.width}
          height={dimensions.height}
          cropped={false}
        >
          {({ ref, open }) => (
            <>
              {/* Desktop Banner */}
              {showBannerDesktop && (
                <div
                  ref={ref}
                  onClick={open}
                  className={cn(
                    "hidden md:block",
                    showHeader ? "pt-[50px]" : ""
                  )}
                >
                  <Image
                    src={image?.photo}
                    alt="Event Cover Image"
                    width={dimensions.width}
                    height={dimensions.height}
                    className="w-[100vw] aspect-[5/2] md:max-h-[70vh] object-cover hover:cursor-pointer"
                  />
                </div>
              )}

              {/* Mobile Banner */}
              {showBannerMobile && (
                <div
                  ref={ref}
                  onClick={open}
                  className={cn(
                    "block md:hidden",
                    showHeader ? "pt-[50px]" : ""
                  )}
                >
                  <Image
                    src={image?.photo}
                    alt="Event Cover Image"
                    width={dimensions.width}
                    height={dimensions.height}
                    className="w-[100vw] aspect-[3/2] object-cover hover:cursor-pointer"
                  />
                </div>
              )}
            </>
          )}
        </PhotoSwipeItem>
      );
    }

    return <div className={cn(showHeader ? "h-[50px]" : "h-0")}></div>;
  };
  return renderEventCoverImage();
}
