"use client";
import parse from "html-react-parser";

interface EventDetailsClientProps {
  details: string;
}

const EventDetailsClient = ({ details }: EventDetailsClientProps) => {
  function replaceBoldWithStrong(text) {
    return text?.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
  }
  const formattedString = replaceBoldWithStrong(details);
  return <div className="prose">{parse(formattedString)}</div>;
};

export default EventDetailsClient;
