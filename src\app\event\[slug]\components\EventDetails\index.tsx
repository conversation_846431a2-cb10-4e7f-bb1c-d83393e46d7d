import { Divider } from "@nextui-org/react";
import dynamic from "next/dynamic";

const EventDetailsClient = dynamic(() => import("./EventDetails"), {
  loading: () => (
    <div className="animate-pulse h-24 bg-gray-100 rounded-lg mt-4" />
  ),
  ssr: false,
});

interface EventDetailsProps {
  details: string;
}

const EventDetails = ({ details }: EventDetailsProps) => {
  if (!details || details.trim() === "") {
    return null;
  }

  return (
    <div className="mt-6 lg:mt-12">
      <h1 className="text-xl lg:mt-12 font-medium text-[#1D1D1F]">
        About the event
      </h1>
      <Divider className="mt-2 bg-[#B7B8B4] w-[95%]" />
      <div className="mt-3">
        <p className="text-[12px] md:text-[14px] lg:text-[16px]">
          <EventDetailsClient details={details} />
        </p>
      </div>
    </div>
  );
};

export default EventDetails;
