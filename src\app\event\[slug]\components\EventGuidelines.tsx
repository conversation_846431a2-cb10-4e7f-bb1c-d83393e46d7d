"use client";

import ModalComponent from "@/components/Modal/Modal";
import { useGetOrganizationPoliciesAndGuidelinesQuery } from "@/lib/redux/slices/organization/api";
import { <PERSON><PERSON>, Card, Skeleton } from "@nextui-org/react";
import React, { useState, Suspense, lazy } from "react";
import { RiNewspaperLine } from "react-icons/ri";
import { eventGuidelinesIconMap } from "../../../events/constants";
import Image from "next/image";
import { MdOutlineChevronRight } from "react-icons/md";
import { HiArrowLeft } from "react-icons/hi2";
import styles from "@/lib/style/listStyling.module.css";

// Only import the parser when modal is opened and content needs to be shown
const ParseHTML = lazy(async () => {
  const [parserModule, purifyModule] = await Promise.all([
    import("html-react-parser"),
    import("isomorphic-dompurify"),
  ]);

  return {
    default: ({ content }: { content: string }) => (
      <>{parserModule.default(purifyModule.default.sanitize(content))}</>
    ),
  };
});

const relevantFields = [
  "safetyGuidelines",
  "prohibitedItems",
  "permittedItems",
  "petPolicy",
  "cameraPolicy",
];

const EventGuidelines = ({ orgSlug }: { orgSlug?: string }) => {
  const { data, isFetching, isLoading } =
    useGetOrganizationPoliciesAndGuidelinesQuery(
      {
        orgSlug,
      },
      {
        refetchOnMountOrArgChange: true,
        skip: !orgSlug,
      }
    );

  const [selectedPolicy, setSelectedPolicy] = useState<string | null>(null);
  const defaultTrigger = (
    <Button
      variant="flat"
      startContent={<RiNewspaperLine size={18} fontWeight={100} />}
      className="flex w-fit mt-3"
    >
      Event Guidelines
    </Button>
  );

  if (!data || !data?.showGuidelinesInEventDetails || isFetching || isLoading)
    return null;

  const renderContent = () => {
    if (!selectedPolicy) {
      return (
        <Card className="shadow-none border border-gray-200">
          <div className="flex flex-col">
            {Object.keys(data).map((key) => {
              if (!relevantFields.includes(key)) return null;
              const policy = eventGuidelinesIconMap[key]?.name;
              const icon = eventGuidelinesIconMap[key]?.icon;
              return (
                <div
                  key={key}
                  onClick={() => setSelectedPolicy(key)}
                  className="flex p-3 justify-between w-full hover:bg-gray-100 cursor-pointer border-b border-gray-200"
                >
                  <div className="flex gap-3">
                    <Image src={icon} alt={key} width={15} height={15} />
                    <p className="text-sm">{policy}</p>
                  </div>
                  <MdOutlineChevronRight size={20} />
                </div>
              );
            })}
          </div>
        </Card>
      );
    }

    return (
      <div className={`text-sm ${styles?.guidelineContent}`}>
        <Suspense
          fallback={
            <div className="animate-pulse bg-gray-200 h-40 rounded-md" />
          }
        >
          <ParseHTML content={data[selectedPolicy]} />
        </Suspense>
      </div>
    );
  };

  return (
    <ModalComponent
      trigger={defaultTrigger}
      onCloseModal={() => setSelectedPolicy(null)}
      title={
        selectedPolicy ? (
          <div className="flex gap-1 items-center">
            <HiArrowLeft
              size={16}
              className="cursor-pointer"
              onClick={() => setSelectedPolicy(null)}
            />
            <p className="inline-block ml-2">
              {eventGuidelinesIconMap[selectedPolicy]?.name}
            </p>
          </div>
        ) : (
          "Event Guidelines"
        )
      }
      size="xl"
      scrollBehavior="outside"
      isDismissable={false}
      isKeyboardDismissDisabled={true}
    >
      {renderContent()}
    </ModalComponent>
  );
};

export default EventGuidelines;
