import React from "react";
import { dateToLongString, getTimeOnlyFromIsoDate } from "@/lib/utils/date";
import { OrganizationConfig } from "./OrganizationConfig";
import { AiFillClockCircle } from "react-icons/ai";
import { MdLocationPin } from "react-icons/md";
import EventOverviewGallery from "./EventOverviewGallery";
import EventOverviewImage from "./EventOverviewImage";
import EventGuidelinesWrapper from "./EventGuidelinesWrapper";
import { EventParkingImage } from "./EventParkingImage";
import { EventLocation, Organization } from "../../types";
import EventAddress from "./EventAddress";

interface EventOverViewProps {
  img: string;
  title: string;
  category: string;
  location: EventLocation;
  orgDetails?: any;
  eventId: string;
  pixels: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
  startDate: string;
  endDate: string;
  timezone: string;
  tag?: string;
  parkingImg?: string;
  collaborators?: Organization[];
}

const EventOverview: React.FC<EventOverViewProps> = ({
  img,
  title,
  category,
  location,
  orgDetails,
  eventId,
  pixels,
  startDate,
  endDate,
  timezone,
  tag,
  parkingImg,
  collaborators,
}) => {
  return (
    <div className="flex flex-col md:flex-row md:mt-4 gap-10">
      {/* The image will be server-rendered inside the client gallery wrapper */}
      <EventOverviewGallery img={img}>
        <EventOverviewImage img={img} tag={tag} />
      </EventOverviewGallery>

      <div className="w-full md:w-9/12 flex flex-col">
        <div className="flex flex-col gap-1">
          <h1 className="leading-10 text-[30px] md:text-[32px] font-semibold text-[#000000] break-words">
            {title}
          </h1>
          <div className="flex flex-wrap items-center my-2 gap-2">
            <OrganizationConfig
              orgDetails={orgDetails}
              eventId={eventId}
              eventTitle={title}
              pixels={pixels}
            />
            {collaborators?.map((collaborator: Organization, index: number) => (
              <React.Fragment key={collaborator?.id}>
                <span className="mx-[6px] text-[#87878C] text-sm xl:text-base">
                  //
                </span>
                <OrganizationConfig
                  orgDetails={collaborator}
                  eventId={eventId}
                  eventTitle={title}
                  pixels={collaborator?.pixels || {}}
                />
              </React.Fragment>
            ))}
          </div>

          <div className="flex flex-col gap-2">
            <div className="flex gap-2 mt-4 ml-1">
              <AiFillClockCircle size={20} className="mt-1" />
              <div className="space-y-[-2px]">
                <p className="font-normal text-[#1D1D1F]">
                  {dateToLongString(startDate, timezone)}
                </p>
                <p className="font-normal text-[#1D1D1F]">
                  {`${getTimeOnlyFromIsoDate(
                    startDate,
                    timezone
                  )} - ${getTimeOnlyFromIsoDate(endDate, timezone)}`}
                </p>
              </div>
            </div>
            <EventAddress location={location} />
            <div className="flex gap-2">
              <EventGuidelinesWrapper orgDetails={orgDetails} />
              {parkingImg && <EventParkingImage img={parkingImg} />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventOverview;
