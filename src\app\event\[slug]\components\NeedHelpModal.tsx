"use client";

import * as React from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogTrigger,
  DialogPortal,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useSendOrgMessageMutation } from "@/lib/redux/slices/organization/api";
import { useOrganizerSlug } from "@/lib/Providers/SubdomainProvider";
import toast from "react-hot-toast";

const NeedHelpModal: React.FC<{ isBarHeader: boolean }> = ({ isBarHeader }) => {
  const organizerSlug = useOrganizerSlug();
  const router = useRouter();
  const [showMessageForm, setShowMessageForm] = React.useState(false);
  const [formData, setFormData] = React.useState({
    name: "",
    email: "",
    message: "",
  });

  const [sendOrgMessage, { isLoading }] = useSendOrgMessageMutation();

  const resetComponentState = () => {
    setShowMessageForm(false);
    setFormData({
      name: "",
      email: "",
      message: "",
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSendMessage = async () => {
    try {
      if (!organizerSlug) {
        toast.error("Organizer slug not found");
        return;
      }
      if (
        formData.name === "" ||
        formData.email === "" ||
        formData.message === ""
      ) {
        toast.error("Please fill in all fields");
        return;
      }
      await sendOrgMessage({
        orgSlug: organizerSlug,
        body: {
          name: formData.name,
          email: formData.email,
          message: formData.message,
        },
      }).unwrap();
      setFormData({ name: "", email: "", message: "" });
      setShowMessageForm(false);
      toast.success("Message sent successfully");
    } catch (error: any) {
      const errorMessage = error?.data?.detail || error?.data?.message;
      toast.error(errorMessage);
    }
  };

  return (
    <Dialog onOpenChange={(open) => !open && resetComponentState()}>
      <DialogTrigger asChild>
        {isBarHeader ? (
          <p className="text-[14px] font-medium text-white cursor-pointer select-none">
            Need Help?
          </p>
        ) : (
          <div
            className="px-[10px] h-[40px] cursor-pointer flex items-center justify-center !rounded-[30px] relative transition-all duration-200 hover:scale-105 text-[12px] text-black/90 font-medium"
            style={{
              background: "rgba(255, 255, 255, 0.2)",
              backdropFilter: "blur(16px)",
              WebkitBackdropFilter: "blur(16px)",
              boxShadow: `
                inset 0 0 20px -4px rgba(255, 255, 255, 0.5),
                0px 8px 32px rgba(0, 0, 0, 0.15),
                0px 4px 16px rgba(0, 0, 0, 0.1)
              `,
              border: "1px solid rgba(255, 255, 255, 0.3)",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "rgba(255, 255, 255, 0.3)";
              e.currentTarget.style.boxShadow = `
                inset 0 0 24px -4px rgba(255, 255, 255, 0.7),
                0px 12px 40px rgba(0, 0, 0, 0.2),
                0px 6px 20px rgba(0, 0, 0, 0.15)
              `;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "rgba(255, 255, 255, 0.2)";
              e.currentTarget.style.boxShadow = `
                inset 0 0 20px -4px rgba(255, 255, 255, 0.5),
                0px 8px 32px rgba(0, 0, 0, 0.15),
                0px 4px 16px rgba(0, 0, 0, 0.1)
              `;
            }}
          >
            Need Help?
          </div>
        )}
      </DialogTrigger>

      <DialogPortal>
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-transparent" />

        <DialogPrimitive.Content
          className={`liquid-glass-modal fixed left-1/2 top-1/2 z-50 flex ${
            showMessageForm
              ? "w-[90vw] max-w-[550px]"
              : "w-[90vw] max-w-[471px]"
          } -translate-x-1/2 -translate-y-1/2 flex-col items-center justify-center gap-4 rounded-[40.70px] p-4 sm:p-8 pb-[62px] isolation-isolate`}
          style={{
            background: "rgba(255, 255, 255, 0.35)",
            backdropFilter: "blur(16px)",
            WebkitBackdropFilter: "blur(16px)",
            boxShadow: `
                 inset 0 0 24px -6px rgba(255, 255, 255, 0.7),
                 0px 32px 96px rgba(0, 0, 0, 0.2),
                 0px 16px 48px rgba(0, 0, 0, 0.15),
                 0px 8px 24px rgba(0, 0, 0, 0.1)
               `,
            border: "0.92px solid #A0A0A0",
          }}
        >
          <DialogClose asChild>
            <button
              className="absolute right-6 top-6 flex h-10 w-10 border-none items-center justify-center rounded-full focus:outline-none focus-visible:outline-none focus:ring-0 ring-offset-0 transition-all duration-200 hover:scale-105"
              style={{
                background: "rgba(255, 255, 255, 0.25)",
                backdropFilter: "blur(12px)",
                WebkitBackdropFilter: "blur(12px)",
                boxShadow: `
                  inset 0 0 16px -4px rgba(255, 255, 255, 0.6),
                  0px 4px 16px rgba(0, 0, 0, 0.1)
                `,
                border: "1px solid rgba(255, 255, 255, 0.2)",
              }}
            >
              <X className="h-5 w-5 text-black/70" />
            </button>
          </DialogClose>

          {!showMessageForm ? (
            <>
              <Image
                src="/question-mark.svg"
                alt="Need Help"
                width={64}
                height={64}
              />

              <h2 className="text-zinc-800 text-[20px] font-[500]">
                Need assistance?
              </h2>

              <div className="mt-[90px] flex flex-col gap-[27px] w-full max-w-[309px]">
                <Button
                  className="w-full h-12 rounded-[21px] bg-blue-500 text-[17px] font-[400] text-white hover:bg-blue-500/90"
                  onClick={() => router.push("/resend")}
                  disabled={isLoading}
                >
                  I never received my tickets
                </Button>
                <Button
                  className="w-full h-12 rounded-[21px] bg-blue-500 text-[17px] font-[400] text-white hover:bg-blue-500/90"
                  onClick={() => setShowMessageForm(true)}
                  disabled={isLoading}
                >
                  Message organizer
                </Button>
              </div>
            </>
          ) : (
            <>
              <Image
                src="/question-mark.svg"
                alt="Message Organizer"
                width={64}
                height={64}
              />

              <h2 className="text-zinc-800 text-[20px] font-[500]">
                Need assistance?
              </h2>

              <div className="mt-8 flex flex-col gap-4 w-full max-w-[433px] px-2 sm:px-0">
                <Input
                  placeholder="Your name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="h-[40px] px-[20px] text-[#303030] placeholder:text-[#00000066] placeholder:font-[400] text-[13px] rounded-[13px] focus:border-none focus:ring-0 focus:outline-none transition-all duration-200"
                  style={{
                    background: "rgba(255, 255, 255, 0.4)",
                    backdropFilter: "blur(12px)",
                    WebkitBackdropFilter: "blur(12px)",
                    boxShadow: `
                      inset 0 0 16px -4px rgba(255, 255, 255, 0.7),
                      0px 3px 20px rgba(0, 0, 0, 0.08)
                    `,
                    border: "1px solid rgba(255, 255, 255, 0.4)",
                  }}
                />
                <Input
                  placeholder="Your email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className="h-[40px] px-[20px] text-[#303030] placeholder:text-[#00000066] placeholder:font-[400] text-[13px] rounded-[13px] focus:border-none focus:ring-0 focus:outline-none transition-all duration-200"
                  style={{
                    background: "rgba(255, 255, 255, 0.4)",
                    backdropFilter: "blur(12px)",
                    WebkitBackdropFilter: "blur(12px)",
                    boxShadow: `
                      inset 0 0 16px -4px rgba(255, 255, 255, 0.7),
                      0px 3px 20px rgba(0, 0, 0, 0.08)
                    `,
                    border: "1px solid rgba(255, 255, 255, 0.4)",
                  }}
                />
                <Textarea
                  placeholder="Your message"
                  value={formData.message}
                  onChange={(e) => handleInputChange("message", e.target.value)}
                  className="min-h-[144px] px-[20px] rounded-[13px] placeholder:text-[#00000066] placeholder:font-[400] resize-none focus:border-none focus:ring-0 focus:outline-none transition-all duration-200"
                  style={{
                    background: "rgba(255, 255, 255, 0.4)",
                    backdropFilter: "blur(12px)",
                    WebkitBackdropFilter: "blur(12px)",
                    boxShadow: `
                      inset 0 0 16px -4px rgba(255, 255, 255, 0.7),
                      0px 3px 20px rgba(0, 0, 0, 0.08)
                    `,
                    border: "1px solid rgba(255, 255, 255, 0.4)",
                  }}
                />

                <div className="flex gap-3 mt-4 w-full">
                  <Button
                    onClick={handleSendMessage}
                    className="flex-1 h-12 rounded-[21px] bg-blue-500 text-[17px] font-[400] text-white hover:bg-blue-500/90 flex items-center gap-2"
                    loading={isLoading}
                    disabled={isLoading}
                  >
                    Send
                  </Button>
                </div>
              </div>
            </>
          )}
        </DialogPrimitive.Content>
      </DialogPortal>
    </Dialog>
  );
};

export default NeedHelpModal;
