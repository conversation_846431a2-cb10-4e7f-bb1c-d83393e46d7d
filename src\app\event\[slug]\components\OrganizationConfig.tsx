"use client";

import { useSessionData } from "@/lib/hooks/useSession";
import { trackOrganizerProfileClick } from "@/lib/utils/pixelTracking";
import { Image } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import NextImage from "next/image";
import React from "react";
import { getName } from "@/lib/utils/string";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { Organization } from "../../types";
import { CALICREAMING_ORGANIZER_ID } from "@/lib/utils/constants";

type TOrganizationConfigProps = {
  orgDetails: Organization;
  eventId: string;
  eventTitle: string;
  pixels: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
};

export function OrganizationConfig({
  orgDetails,
  eventId,
  eventTitle,
  pixels,
}: TOrganizationConfigProps) {
  const navigate = useRouter();
  const { data: session } = useSessionData();

  const isCrownShow = orgDetails?.owner?.id === CALICREAMING_ORGANIZER_ID;

  const handleClick = () => {
    if (pixels?.meta || pixels?.ga4 || pixels?.snap || pixels?.tiktok) {
      trackOrganizerProfileClick(
        {
          id: eventId,
          title: eventTitle,
          pixels,
          date: new Date().toISOString(),
        },
        {
          id: session?.user?.id,
          email: session?.user?.email,
          name: getName(session?.user),
          isAnonymous: !session?.user,
        }
      );
    }

    navigate.push(`/profile/${orgDetails?.owner?.id}`);
  };

  return (
    <div className="flex flex-row items-start sm:items-center gap-2 lg:gap-4 justify-between">
      <div className="flex flex-row gap-2 max-w-full">
        <Image
          src={orgDetails?.owner?.avatar ?? IMAGE_LINKS.NO_IMG}
          alt="Company Image in Autolnk"
          as={NextImage}
          quality={50}
          width={28}
          height={28}
          className="rounded-full border border-gray-200 shadow-sm"
        />
        <div className="flex justify-center gap-1 truncate items-center">
          <h1
            className=" text-[#FA233A] text-sm xl:text-base  truncate  hover:cursor-pointer"
            onClick={handleClick}
          >
            {orgDetails?.owner?.name}
          </h1>
          {orgDetails?.isVerified && (
            <Image
              src={isCrownShow ? "/crown.svg" : "/verified.svg"}
              alt="Verified"
              width={20}
              height={20}
              className="w-4 h-4"
              as={NextImage}
            />
          )}
        </div>
      </div>
    </div>
  );
}
