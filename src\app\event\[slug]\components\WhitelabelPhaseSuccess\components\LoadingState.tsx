import { Spinner } from "@nextui-org/react";
import { WhitelabelFooter } from "../../shared/WhitelabelFooter";

interface LoadingStateProps {
  isApprovalRequiredCheckout: boolean;
}

export function LoadingState({
  isApprovalRequiredCheckout,
}: LoadingStateProps) {
  return (
    <div className="min-h-screen bg-white">
      <div className="flex justify-center">
        <div className="w-full max-w-4xl px-4 md:px-10 py-8">
          <div className="flex flex-col items-center justify-center h-[60vh]">
            <div className="flex flex-col gap-5 items-center justify-center">
              <Spinner size="lg" />
              {!isApprovalRequiredCheckout ? (
                <p className="text-lg text-gray-600">
                  Generating your tickets...
                </p>
              ) : (
                <p className="text-lg text-gray-600">
                  Processing your registration...
                </p>
              )}
              <p className="text-sm text-gray-500">
                Please don't close this page
              </p>
            </div>
          </div>
          <WhitelabelFooter />
        </div>
      </div>
    </div>
  );
}
