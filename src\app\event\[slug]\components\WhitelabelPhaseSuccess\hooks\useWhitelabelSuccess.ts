import { useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useCheckoutCompleteMutation } from "@/lib/redux/slices/cart/cartApi";
import { SuccessData } from "@/app/order/success/types";
import { STORAGE_KEYS } from "@/lib/constants/storage";
import { CHECKOUT_STORAGE_KEYS } from "@/app/checkout/constants/checkoutConstants";

interface UseWhitelabelSuccessProps {
  slug: string;
}

export function useWhitelabelSuccess({ slug }: UseWhitelabelSuccessProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [successData, setSuccessData] = useState<SuccessData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [checkoutComplete] = useCheckoutCompleteMutation();
  
  const isApprovalRequiredCheckout =
    typeof window !== "undefined" &&
    localStorage.getItem(CHECKOUT_STORAGE_KEYS.IS_APPROVAL_REQUIRED_CHECKOUT) === "true";
  
  const isRequestPendingRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    const checkoutTokenFromUrl = searchParams.get("checkoutToken");
    if (checkoutTokenFromUrl) {
      localStorage.setItem(STORAGE_KEYS.CART_TOKEN, checkoutTokenFromUrl);
    }
  }, [searchParams]);

  // Storage cleanup function
  const cleanupStorage = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem(STORAGE_KEYS.CART_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.CLIENT_SECRET);
      localStorage.removeItem(STORAGE_KEYS.CONNECT_ID);
      localStorage.removeItem(STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT);
      localStorage.removeItem("whitelabel_basic_info");
      sessionStorage.removeItem(STORAGE_KEYS.CHECKOUT_FORM_DATA);
    }
  };

  // Play success sound
  const playSuccessSound = () => {
    if (typeof window !== "undefined") {
      const audio = new Audio("/success.mp3");
      audio.play().catch(console.error);
    }
  };

  // Attempt checkout completion
  const attemptCheckout = async (checkoutToken: string): Promise<boolean> => {
    if (isRequestPendingRef.current) {
      return false;
    }

    isRequestPendingRef.current = true;
    try {
      const response = await checkoutComplete({
        checkoutToken,
        paymentType: "web",
        verify: true,
      } as any).unwrap();

      if (response?.order) {
        setSuccessData(response);
        setIsLoading(false);
        playSuccessSound();
        localStorage.removeItem("whitelabel_basic_info");
        return true;
      }
      return false;
    } catch (error) {
      console.error("Checkout attempt error:", error);
      return false;
    } finally {
      isRequestPendingRef.current = false;
      cleanupStorage();
    }
  };

  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;
    abortControllerRef.current = new AbortController();

    const completeCheckout = async () => {
      try {
        const checkoutToken =
          typeof window !== "undefined"
            ? localStorage.getItem(STORAGE_KEYS.CART_TOKEN)
            : null;

        if (!checkoutToken) {
          router.replace(`/event/${slug}?oid=event`);
          return;
        }

        // Add delay for processing animation
        await new Promise((resolve) => setTimeout(resolve, 3000));

        const initialSuccess = await attemptCheckout(checkoutToken);

        if (!initialSuccess && !abortControllerRef?.current?.signal?.aborted) {
          const poll = async () => {
            if (abortControllerRef?.current?.signal?.aborted) return;

            const success = await attemptCheckout(checkoutToken);
            if (success) return;

            timeoutId = setTimeout(poll, 2000);
          };

          poll();
        }
      } catch (error) {
        console.error("Checkout completion setup error:", error);
        setIsLoading(false);
      }
    };

    completeCheckout();

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [checkoutComplete, router, slug]);

  const handleBackToEvent = () => {
    router.push(`/event/${slug}?oid=event`);
  };

  const handleBrowseEvents = () => {
    router.push("/events");
  };

  return {
    successData,
    isLoading,
    isApprovalRequiredCheckout,
    handleBackToEvent,
    handleBrowseEvents,
  };
} 