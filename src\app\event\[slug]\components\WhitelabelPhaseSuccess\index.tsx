"use client";

import { useWhitelabelSuccess } from "./hooks/useWhitelabelSuccess";
import { LoadingState } from "./components/LoadingState";
import { SuccessState } from "./components/SuccessState";
import { WhitelabelHeader } from "../shared/WhitelabelHeader";

interface WhitelabelPhaseSuccessProps {
  eventData: any;
  slug: string;
  layoutConfig?: any;
}

export function WhitelabelPhaseSuccess({
  eventData,
  slug,
  layoutConfig,
}: WhitelabelPhaseSuccessProps) {
  const {
    successData,
    isLoading,
    isApprovalRequiredCheckout,
    handleBackToEvent,
    handleBrowseEvents,
  } = useWhitelabelSuccess({ slug });

  if (isLoading) {
    return (
      <div>
        {layoutConfig && (
          <WhitelabelHeader
            layoutConfig={layoutConfig}
            slug={slug}
            showCartInfo={false}
          />
        )}
        <LoadingState isApprovalRequiredCheckout={isApprovalRequiredCheckout} />
      </div>
    );
  }

  return (
    <div>
      {layoutConfig && (
        <WhitelabelHeader
          layoutConfig={layoutConfig}
          slug={slug}
          showCartInfo={false}
        />
      )}
      <SuccessState
        successData={successData}
        eventName={eventData?.name}
        isBarHeader={layoutConfig?.headerStyle === "bar"}
      />
    </div>
  );
}
