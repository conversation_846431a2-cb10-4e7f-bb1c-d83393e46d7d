interface CheckoutHeaderProps {
  eventName?: string;
  onBackToContact: () => void;
}

export function CheckoutHeader({
  eventName,
  onBackToContact,
}: CheckoutHeaderProps) {
  return (
    <div className="mb-6">
      <button
        onClick={onBackToContact}
        className="text-[#1773B0] hover:text-[#1773B0] underline text-sm mb-4"
      >
        ← Back to contact information
      </button>
      <h1 className="text-2xl font-bold text-gray-900 mb-2">
        Complete Your Order
      </h1>
      <p className="text-gray-600">
        Finish your order for <strong>{eventName}</strong>
      </p>
    </div>
  );
}
