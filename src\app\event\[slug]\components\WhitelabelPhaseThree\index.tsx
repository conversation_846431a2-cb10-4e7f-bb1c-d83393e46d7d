"use client";

import { useRouter } from "next/navigation";
import { Spinner } from "@nextui-org/react";
import CheckoutForm from "@/app/checkout/components/CheckoutForm";
import OrderSummary from "@/app/checkout/components/OrderSummary";
import StripeElementsProvider from "@/app/checkout/components/StripeElementsProvider";
import { PAYMENT_MODE } from "@/app/checkout/constants/paymentConstants";
import { useWhitelabelStorage } from "./hooks/useWhitelabelStorage";
import { useWhitelabelPayment } from "./hooks/useWhitelabelPayment";
import { CheckoutHeader } from "./components/CheckoutHeader";
import { WhitelabelHeader } from "../shared/WhitelabelHeader";
import { STRIPE_APPEARANCE } from "./constants";
import { cn } from "@/lib/utils";

interface WhitelabelPhaseThreeProps {
  eventData: any;
  slug: string;
  layoutConfig?: any;
}

export function WhitelabelPhaseThree({
  eventData,
  slug,
  layoutConfig,
}: WhitelabelPhaseThreeProps) {
  const router = useRouter();

  const {
    basicUserInfo,
    localClientSecret,
    localConnectId,
    isCollaboratorCheckout,
    updatePaymentData,
    setLocalClientSecret,
    setLocalConnectId,
    setIsCollaboratorCheckout,
  } = useWhitelabelStorage({ slug });

  const {
    cartData,
    isCartLoading,
    stripePromise,
    currentClientSecret,
    createPaymentIntent,
    isCreatingPayment,
    paymentError,
    hasPaymentIntent,
    refreshCartData,
    isFree,
  } = useWhitelabelPayment({
    localClientSecret,
    localConnectId,
    isCollaboratorCheckout,
    updatePaymentData,
    setLocalClientSecret,
    setLocalConnectId,
    setIsCollaboratorCheckout,
  });

  const handleBackToContact = () => {
    router.push(`/event/${slug}?oid=contact`);
  };

  if (isCartLoading || !basicUserInfo) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spinner />
      </div>
    );
  }

  if (!cartData?.checkout?.lines?.length) {
    router.push(`/event/${slug}?oid=event`);
    return null;
  }

  return (
    <div className="min-h-screen bg-[#F1F1F1]">
      {layoutConfig && (
        <WhitelabelHeader
          layoutConfig={layoutConfig}
          slug={slug}
          showCartInfo={false}
        />
      )}
      <div className="flex flex-col-reverse md:flex-row items-start">
        <div
          className={cn(
            "w-full md:w-[60%] flex justify-end min-h-screen bg-white px-4 md:px-10 py-8",
            layoutConfig?.headerStyle === "bar" && "pt-8 md:pt-16"
          )}
        >
          <div className="w-full md:w-[85%] md:max-w-[900px] mt-4 md:mt-8">
            <CheckoutHeader
              eventName={eventData?.name}
              onBackToContact={handleBackToContact}
            />

            {!isFree ? (
              <StripeElementsProvider
                clientSecret={currentClientSecret}
                stripePromise={stripePromise}
                appearance={STRIPE_APPEARANCE}
                mode={
                  currentClientSecret
                    ? PAYMENT_MODE.PAYMENT
                    : PAYMENT_MODE.DEFERRED
                }
              >
                <CheckoutForm
                  refreshCartData={refreshCartData}
                  onCreatePaymentIntent={createPaymentIntent}
                  isCreatingPayment={isCreatingPayment}
                  hasPaymentIntent={hasPaymentIntent}
                  paymentError={paymentError}
                  basicUserInfo={basicUserInfo}
                  isWhitelabel={true}
                  slug={slug}
                />
              </StripeElementsProvider>
            ) : (
              <CheckoutForm
                isFreeCheckout={true}
                refreshCartData={refreshCartData}
                basicUserInfo={basicUserInfo}
                isWhitelabel={true}
                slug={slug}
              />
            )}
          </div>
        </div>
        <div
          className={cn(
            "w-full md:w-[40%] flex justify-start px-4 md:px-8 py-8",
            layoutConfig?.headerStyle === "bar" && "pt-16"
          )}
        >
          <div className="w-full md:w-[80%] max-w-[900px]">
            <OrderSummary
              cartData={cartData}
              isCartLoading={isCartLoading}
              isProcessingPayment={isCreatingPayment}
              refetchCart={refreshCartData}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
