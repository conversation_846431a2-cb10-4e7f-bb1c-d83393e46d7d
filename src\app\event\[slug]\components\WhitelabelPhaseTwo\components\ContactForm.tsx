import { Control, FieldErrors } from "react-hook-form";
import { Button } from "@/app/checkout/components/ui/Button";
import { FormInput } from "@/app/checkout/components/Form";
import { PhoneInput } from "@/app/checkout/components/Form/PhoneInput";
import { FormCheckbox } from "@/app/checkout/components/Form/FormCheckbox";
import { BasicUserInfo } from "../constants";

interface ContactFormProps {
  eventName?: string;
  control: Control<BasicUserInfo>;
  errors: FieldErrors<BasicUserInfo>;
  isLoading: boolean;
  onSubmit: () => void;
  onBackToEvent: () => void;
}

export function ContactForm({
  eventName,
  control,
  errors,
  isLoading,
  onSubmit,
  onBackToEvent,
}: ContactFormProps) {
  return (
    <div className="mt-4 md:mt-8">
      <div className="mb-6">
        <button
          onClick={onBackToEvent}
          className="text-[#1773B0] hover:text-[#1773B0] underline text-sm mb-4"
        >
          ← Back to event
        </button>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Contact Information
        </h1>
        <p className="text-gray-600">
          Please provide your basic information to continue with your order for{" "}
          <strong>{eventName}</strong>
        </p>
      </div>

      <form onSubmit={onSubmit} className="space-y-6">
        <div>
          <h2 className="text-[24px] font-semibold text-gray-900 mb-4">
            Your Details
          </h2>

          <div className="space-y-4">
            <FormInput
              name="email"
              control={control}
              type="text"
              label="Email address"
              errorMessage={errors.email?.message}
              isRequired
            />
            <FormCheckbox
              name="newsletter"
              control={control}
              label="Email me with news and offers"
            />

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormInput
                name="firstName"
                control={control}
                type="text"
                label="First name"
                errorMessage={errors.firstName?.message}
                isRequired
              />
              <FormInput
                name="lastName"
                control={control}
                type="text"
                label="Last name"
                errorMessage={errors.lastName?.message}
                isRequired
              />
            </div>

            <PhoneInput
              name="phone"
              control={control}
              label="Phone number"
              errorMessage={errors.phone?.message}
              isRequired={true}
            />
          </div>
        </div>

        <Button
          type="submit"
          color="primary"
          isDisabled={isLoading}
          isLoading={isLoading}
          className="w-full bg-[#1773B0] mt-0 mb-8"
          size="lg"
        >
          {isLoading ? "Processing..." : "Continue to Checkout"}
        </Button>
      </form>
    </div>
  );
}
