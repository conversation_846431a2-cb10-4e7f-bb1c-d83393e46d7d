import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";

interface UseCartCheckProps {
  slug: string;
}

export function useCartCheck({ slug }: UseCartCheckProps) {
  const router = useRouter();
  const [cartCheckTimer, setCartCheckTimer] = useState<NodeJS.Timeout | null>(null);
  const isRefetchingRef = useRef(false);
  const hasCheckedOnceRef = useRef(false);

  const {
    data: cartData,
    isLoading: isCartLoading,
    isUninitialized,
    refetch,
  } = useGetCartQuery();

  const refreshCartData = async () => {
    if (!isUninitialized && !isRefetchingRef.current) {
      isRefetchingRef.current = true;
      try {
        await refetch();
      } finally {
        isRefetchingRef.current = false;
      }
    }
  };

  // Main cart check effect - only runs once when component mounts
  useEffect(() => {
    if (hasCheckedOnceRef.current || isCartLoading || cartCheckTimer) {
      return;
    }

    hasCheckedOnceRef.current = true;

    if (!cartData?.checkout?.lines?.length) {
      const referrer = typeof document !== "undefined" ? document.referrer : "";
      const isFromEventPage =
        referrer.includes(`/event/${slug}`) && !referrer.includes("oid=");

      const delayTime = isFromEventPage ? 2500 : 1500;

      const timer = setTimeout(() => {
        // Only refetch if query is properly initialized and not already refetching
        if (!isUninitialized && !isRefetchingRef.current) {
          isRefetchingRef.current = true;
          refetch()
            .then((latestCartResult) => {
              const latestCartData = latestCartResult.data;

              if (!latestCartData?.checkout?.lines?.length) {
                router.push(`/event/${slug}?oid=event`);
              }
            })
            .catch((error) => {
              console.error("Error refetching cart data in timer:", error);
              router.push(`/event/${slug}?oid=event`);
            })
            .finally(() => {
              isRefetchingRef.current = false;
            });
        } else {
          // If query is uninitialized, just redirect
          router.push(`/event/${slug}?oid=event`);
        }
      }, delayTime);

      setCartCheckTimer(timer);
    }
  }, [isCartLoading, router, slug, refetch, isUninitialized]); // Removed cartData from dependencies

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (cartCheckTimer) {
        clearTimeout(cartCheckTimer);
      }
    };
  }, [cartCheckTimer]);

  // Clear timer when cart has items
  useEffect(() => {
    if (!isCartLoading && cartData?.checkout?.lines?.length && cartCheckTimer) {
      clearTimeout(cartCheckTimer);
      setCartCheckTimer(null);
    }
  }, [cartData?.checkout?.lines?.length, isCartLoading, cartCheckTimer]);

  // Force refetch if cart token exists but no cart data - only once
  useEffect(() => {
    if (typeof window !== "undefined" && !hasCheckedOnceRef.current) {
      const cartToken = localStorage.getItem("cartToken");
      const hasCartData =
        cartData?.checkout?.lines && cartData.checkout.lines.length > 0;

      if (cartToken && !isCartLoading && !hasCartData && !isUninitialized && !isRefetchingRef.current) {
        isRefetchingRef.current = true;
        setTimeout(() => {
          refetch().catch((error) => {
            console.error("Forced cart refetch failed:", error);
          }).finally(() => {
            isRefetchingRef.current = false;
          });
        }, 300);
      }
    }
  }, [isCartLoading, refetch, isUninitialized]); // Removed cartData from dependencies

  return {
    cartData,
    isCartLoading,
    cartCheckTimer,
    refreshCartData,
  };
} 