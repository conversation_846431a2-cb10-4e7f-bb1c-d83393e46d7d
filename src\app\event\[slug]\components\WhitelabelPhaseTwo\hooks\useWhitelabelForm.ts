import { useState, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { basicUserInfoSchema, BasicUserInfo, DEFAULT_FORM_VALUES } from "../constants";
import { toast } from "react-hot-toast";
import { validateEmailWithTLD } from "@/app/checkout/constants/formMessages";

interface UseWhitelabelFormProps {
  slug: string;
  onProceedToCheckout?: (waiverSignatures?: any) => void;
}

export function useWhitelabelForm({ slug, onProceedToCheckout }: UseWhitelabelFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Load saved data from localStorage and merge with defaults
  const defaultValues = useMemo(() => {
    if (typeof window !== "undefined") {
      const savedData = localStorage.getItem("whitelabel_basic_info");
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          // Merge saved data with defaults to ensure all fields are present
          return { ...DEFAULT_FORM_VALUES, ...parsedData };
        } catch (error) {
          console.error("Error parsing saved form data:", error);
        }
      }
    }
    return DEFAULT_FORM_VALUES;
  }, []);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<BasicUserInfo>({
    resolver: zodResolver(basicUserInfoSchema),
    mode: "onSubmit",
    defaultValues,
  });

  const onSubmit = async (data: BasicUserInfo) => {
    setIsLoading(true);
    try {
      const emailValidation = validateEmailWithTLD(data.email);
      if (!emailValidation.isValid) {
        toast.error(emailValidation.error || "Please enter a valid email address");
        setIsLoading(false);
        return;
      }

      const numericDigits = data.phone.replace(/\D/g, '');
      if (numericDigits.length < 10) {
        toast.error("Phone number must be at least 10 digits");
        setIsLoading(false);
        return;
      }

      // Store the basic user info in localStorage for phase 3
      localStorage.setItem("whitelabel_basic_info", JSON.stringify(data));

      if (onProceedToCheckout) {
        onProceedToCheckout();
      } else {
        // Navigate to checkout phase
        router.push(`/event/${slug}?oid=checkout`);
      }
    } catch (error) {
      console.error("Error saving basic info:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToEvent = () => {
    router.push(`/event/${slug}?oid=event`);
  };

  return {
    control,
    handleSubmit,
    onSubmit,
    errors,
    isLoading,
    handleBackToEvent,
  };
} 