import { useState, useMemo } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

type WaiverData = {
  waiverUrl: string;
  waiverSignature: string;
  waiverSignedDateTime: string;
  timezone: string;
};

type WaiverInfo = {
  lineId: string;
  waiverUrl: string;
  waiverId: string;
  type: "ticketWaiver" | "waiverResponse";
};

type WaiverSignatures = Record<
  string,
  { signature: string; signedDateTime: string; timezone: string }
>;

export const useWhitelabelWaiverHandling = (cartData: any) => {
  const [showWaiver, setShowWaiver] = useState(false);
  const [currentWaiverIndex, setCurrentWaiverIndex] = useState(0);
  const [waiverSignatures, setWaiverSignatures] = useState<WaiverSignatures>({});

  const allWaivers = useMemo(() => {
    if (!cartData?.checkout?.lines) return [];

    const waiverMap = new Map<string, WaiverInfo>();

    cartData.checkout.lines.forEach((line: any) => {
      if (line.ticketWaiverUrl) {
        const key = line.ticketWaiverUrl.waiverUrl;
        if (!waiverMap.has(key)) {
          waiverMap.set(key, {
            lineId: line.id,
            waiverUrl: line.ticketWaiverUrl.waiverUrl,
            waiverId: line.ticketWaiverUrl.id,
            type: "ticketWaiver",
          });
        }
      }

      if (line.waiverResponseData?.length > 0) {
        line.waiverResponseData.forEach((waiver: any) => {
          const key = waiver.waiverUrl;
          if (!waiverMap.has(key)) {
            waiverMap.set(key, {
              lineId: line.id,
              waiverUrl: waiver.waiverUrl,
              waiverId: `${line.id}-${waiver.waiverUrl}`,
              type: "waiverResponse",
            });
          }
        });
      }
    });

    return Array.from(waiverMap.values());
  }, [cartData?.checkout?.lines]);

  const handleWaiverAccept = async (signature: string) => {
    if (!allWaivers[currentWaiverIndex]) return;

    const currentWaiver = allWaivers[currentWaiverIndex];
    const lineWithWaiver = cartData?.checkout?.lines?.find(
      (line: any) => line.id === currentWaiver.lineId
    );

    const timezone = lineWithWaiver?.eventDetails?.eventTimezone || dayjs.tz.guess();
    const signedDateTime = dayjs().tz(timezone).utc().format();

    const newSignatures = {
      ...waiverSignatures,
      [currentWaiver.waiverId]: {
        signature,
        signedDateTime,
        timezone,
      },
    };
    setWaiverSignatures(newSignatures);

    if (currentWaiverIndex < allWaivers.length - 1) {
      setCurrentWaiverIndex((prev) => prev + 1);
    } else {
      setShowWaiver(false);
      return newSignatures;
    }
  };

  const handleWaiverClose = () => {
    setShowWaiver(false);
  };

  const startWaiverProcess = () => {
    setCurrentWaiverIndex(0);
    setWaiverSignatures({});
    setShowWaiver(true);
  };

  return {
    showWaiver,
    currentWaiverIndex,
    waiverSignatures,
    allWaivers,
    handleWaiverAccept,
    handleWaiverClose,
    startWaiverProcess,
  };
}; 