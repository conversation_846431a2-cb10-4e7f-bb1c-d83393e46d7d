"use client";

import { Spinner } from "@nextui-org/react";
import OrderSummary from "@/app/checkout/components/OrderSummary";
import WaiverModal from "@/app/cart/components/WaiverModal";
import { useCartCheck } from "./hooks/useCartCheck";
import { useWhitelabelForm } from "./hooks/useWhitelabelForm";
import { useWhitelabelWaiverHandling } from "./hooks/useWhitelabelWaiverHandling";
import { useWhitelabelCheckoutHandling } from "./hooks/useWhitelabelCheckoutHandling";
import { ContactForm } from "./components/ContactForm";
import { WhitelabelHeader } from "../shared/WhitelabelHeader";
import { cn } from "@/lib/utils";

interface WhitelabelPhaseTwoProps {
  eventData: any;
  slug: string;
  layoutConfig?: any;
}

export function WhitelabelPhaseTwo({
  eventData,
  slug,
  layoutConfig,
}: WhitelabelPhaseTwoProps) {
  const { cartData, isCartLoading, cartCheckTimer, refreshCartData } =
    useCartCheck({ slug });

  const {
    showWaiver,
    currentWaiverIndex,
    allWaivers,
    handleWaiverAccept,
    handleWaiverClose,
    startWaiverProcess,
  } = useWhitelabelWaiverHandling(cartData);

  const { loading, isSubmitting, handleCheckout } =
    useWhitelabelCheckoutHandling(cartData, refreshCartData, slug);

  const handleProceedToCheckout = () => {
    if (allWaivers.length > 0) {
      startWaiverProcess();
    } else {
      handleCheckout();
    }
  };

  const handleWaiverAcceptWithCheckout = async (signature: string) => {
    const newSignatures = await handleWaiverAccept(signature);
    if (newSignatures) {
      handleCheckout(newSignatures);
    }
  };

  const {
    control,
    handleSubmit,
    onSubmit,
    errors,
    isLoading,
    handleBackToEvent,
  } = useWhitelabelForm({
    slug,
    onProceedToCheckout: handleProceedToCheckout,
  });

  if (isCartLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spinner />
      </div>
    );
  }

  if (!cartData?.checkout?.lines?.length && cartCheckTimer) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F1F1F1]">
      {layoutConfig && (
        <WhitelabelHeader
          layoutConfig={layoutConfig}
          slug={slug}
          showCartInfo={false}
        />
      )}
      <div className="flex flex-col-reverse md:flex-row items-start">
        <div
          className={cn(
            "w-full md:w-[60%] flex justify-end min-h-screen bg-white px-4 md:px-10 py-8",
            layoutConfig?.headerStyle === "bar" && "pt-8 md:pt-16"
          )}
        >
          <div className="w-full md:w-[85%] md:max-w-[900px]">
            <ContactForm
              eventName={eventData?.name}
              control={control}
              errors={errors}
              isLoading={isLoading || loading || isSubmitting}
              onSubmit={handleSubmit(onSubmit)}
              onBackToEvent={handleBackToEvent}
            />
          </div>
        </div>
        <div
          className={cn(
            "w-full md:w-[40%] flex justify-start px-4 md:px-8 py-8",
            layoutConfig?.headerStyle === "bar" && "pt-16"
          )}
        >
          <div className="w-full md:w-[80%] max-w-[900px]">
            <OrderSummary
              cartData={cartData}
              isCartLoading={isCartLoading}
              isProcessingPayment={isLoading || loading || isSubmitting}
              refetchCart={refreshCartData}
              showDiscountSection={true}
            />
          </div>
        </div>
      </div>

      <WaiverModal
        isOpen={showWaiver}
        onClose={handleWaiverClose}
        onSave={handleWaiverAcceptWithCheckout}
        pdfUrl={allWaivers[currentWaiverIndex]?.waiverUrl}
      />
    </div>
  );
}
