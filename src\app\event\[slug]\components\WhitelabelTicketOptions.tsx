"use client";

import React, { useCallback } from "react";
import { useSessionData } from "@/lib/hooks/useSession";
import { MODAL_TYPES } from "@/app/events/constants";
import TicketList from "@/app/events/[slug]/components/TicketList";
import { useModalState } from "@/app/events/[slug]/components/hooks/useModalState";
import { TicketType } from "@/app/events/types";
import dynamic from "next/dynamic";

const TicketModals = dynamic(
  () => import("@/app/events/[slug]/components/TicketModals"),
  { ssr: false }
);

interface WhitelabelTicketOptionsProps {
  tickets: TicketType[];
  title: string;
  date: string;
  eventId: string;
  eventTitle: string;
  eventImg: string;
  pixelId?: string;
  ticketAvailableFrom: string;
  ticketAvailableTill: string;
  orgId: string;
  eventStatus: string;
  timezone: string;
  waivers: any;
  pixels: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
  slug: string;
  isCountDownTimerHidden: boolean;
  isCenteredLayout: boolean;
}

const WhitelabelTicketOptions: React.FC<WhitelabelTicketOptionsProps> = ({
  tickets,
  date,
  title,
  eventId,
  eventTitle,
  eventImg,
  ticketAvailableFrom,
  ticketAvailableTill,
  pixelId,
  orgId,
  eventStatus,
  timezone,
  waivers,
  pixels,
  slug,
  isCountDownTimerHidden = false,
  isCenteredLayout = false,
}) => {
  const { modalState, toggleModal } = useModalState();
  const { data: sessionData } = useSessionData();

  const openModal = useCallback(() => {
    toggleModal(MODAL_TYPES.TICKET_FORM);
  }, [sessionData, toggleModal]);

  const openAddVehicleOnGuest = useCallback(() => {
    toggleModal(MODAL_TYPES.TICKET_FORM);
  }, [toggleModal]);

  if (tickets?.length === 0) {
    return <></>;
  }

  return (
    <div className="">
      <TicketList
        date={date}
        title={title}
        tickets={tickets}
        eventId={eventId}
        eventTitle={eventTitle}
        eventImg={eventImg}
        openModal={openModal}
        pixelId={pixelId}
        ticketAvailableFrom={ticketAvailableFrom}
        ticketAvailableTill={ticketAvailableTill}
        orgId={orgId}
        eventStatus={eventStatus}
        timezone={timezone}
        waivers={waivers}
        pixels={pixels}
        isWhitelabel={true}
        slug={slug}
        isCountDownTimerHidden={isCountDownTimerHidden}
        isCenteredLayout={isCenteredLayout}
      />
      <TicketModals
        modalState={modalState}
        toggleModal={toggleModal}
        openAddVehicleOnGuest={openAddVehicleOnGuest}
        isWhitelabel={true}
        slug={slug}
      />
    </div>
  );
};

export default WhitelabelTicketOptions;
