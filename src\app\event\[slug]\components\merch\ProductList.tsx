import React, { useState } from "react";
import { Divider, useDisclosure } from "@nextui-org/react";
import { Product } from "../../../types";
import { ProductCard } from "./ProductCard";
import { ProductModal } from "./ProductDetailsModal";
import { useSessionData } from "@/lib/hooks/useSession";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";

interface ProductAssociation {
  productAssociationId: number;
  product: Product;
  associationType: string;
  orderSequence: number;
}

interface ProductListProps {
  products: ProductAssociation[];
  isWhitelabel?: boolean;
  slug?: string;
}

export const ProductList: React.FC<ProductListProps> = ({
  products: productsList,
  isWhitelabel = false,
  slug = "",
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const { data: session } = useSessionData();

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
    onOpen();
  };

  // Sort products by orderSequence
  const sortedProducts = [...productsList].sort(
    (a, b) => a.orderSequence - b.orderSequence
  );
  const camelCaseProducts = keysToCamel(sortedProducts);

  return (
    <div className="mt-6 lg:mt-12">
      <h1 className="text-xl lg:mt-12 font-bold  text-[#1D1D1F]">Merch</h1>
      <Divider className="mt-2 bg-[#B7B8B4] w-[95%]" />
      <div className="mt-3">
        <div className="">
          <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {camelCaseProducts?.map((productAssociation) => (
              <ProductCard
                key={productAssociation.productAssociationId}
                product={productAssociation.product}
                onClick={handleProductClick}
              />
            ))}
          </div>

          <ProductModal
            isOpen={isOpen}
            onClose={onClose}
            product={selectedProduct}
            isWhitelabel={isWhitelabel}
            slug={slug}
          />
        </div>
      </div>
    </div>
  );
};
