import { Button, Skeleton } from "@nextui-org/react";
import React from "react";

const ProductSkeleton: React.FC = () => {
  return (
    <div className="w-full max-w-4xl">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-4">
          <Skeleton className="h-72 w-full" />
          <div className="flex justify-between">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
        <div className="space-y-4">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Button className="w-full">
            <Skeleton className="h-6 w-24" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProductSkeleton;
