import Link from "next/link";

const FOOTER_INFO = [
  {
    id: "1",
    name: "Privacy Policy",
    path: "/privacy-policy",
  },
  {
    id: "2",
    name: "Terms of Service",
    path: "/terms-of-service",
  },
];

export function WhitelabelFooter() {
  return (
    <div
      className="mt-16 flex justify-center gap-6 border-t border-gray-200 pt-4"
      aria-label="Legal Information"
    >
      {FOOTER_INFO.map((footer) => (
        <Link
          key={footer.id}
          href={footer.path}
          className="text-[#1773B0] hover:text-[#1773B0] underline text-sm transition-colors"
        >
          {footer.name}
        </Link>
      ))}
    </div>
  );
}
