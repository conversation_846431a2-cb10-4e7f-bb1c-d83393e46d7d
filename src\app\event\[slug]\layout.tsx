"use client";

import { useSubdomain } from "@/lib/Providers/SubdomainProvider";
import { useEffect, useState } from "react";

export default function WhitelabelEventLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { organizerSlug } = useSubdomain();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      <main className="w-full relative">{children}</main>
    </div>
  );
}
