import { Skeleton } from "@nextui-org/react";

export default function Loading() {
  return (
    <div className="mt-2 min-h-[calc(100vh-100px)]">
      <div className="grid justify-center pb-14 mt-2">
        <div className="flex flex-col lg:flex-row p-4 gap-10 w-[94vw]">
          <div className="lg:w-[62%] mb-5">
            {/* Cover Image Skeleton */}
            <Skeleton className="w-full h-[400px] md:h-[400px] rounded-lg mb-6" />

            {/* Event Overview Skeleton */}
            <div className="space-y-4">
              <Skeleton className="w-3/4 h-8" />
              <Skeleton className="w-1/2 h-6" />
              <Skeleton className="w-full h-4" />
              <Skeleton className="w-full h-4" />
              <Skeleton className="w-2/3 h-4" />
            </div>
          </div>

          {/* Ticket Options Skeleton */}
          <div className="lg:w-[38%]">
            <div className="bg-white border rounded-lg p-6">
              <Skeleton className="w-1/2 h-6 mb-4" />
              <div className="space-y-3">
                <Skeleton className="w-full h-16" />
                <Skeleton className="w-full h-16" />
                <Skeleton className="w-full h-12" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
