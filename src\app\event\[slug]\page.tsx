import { Metadata } from "next";
import { validate as isUUID } from "uuid";
import React from "react";
import { headers } from "next/headers";

import { getUserTimeZone } from "@/lib/utils/getUserTimeZone";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import ErrorBoundary from "@/components/ErrorBoundary";
import { createEventJsonLd } from "@/lib/seo-constants";
import { StoreSessions } from "@/features/Analytics/StoreSessions";
import EventPageTracker from "@/components/Tracker/EventPageTracker";

import EventDetails from "./components/EventDetails";
import { EventImageViewer } from "./components/EventImageViewer";
import EventOverview from "./components/EventOverview";
import { EventCoverImage } from "./components/EventCoverImage";
import MerchSection from "./components/MerchSection";
import ProfileGridSection from "./components/ProfileGridSection";
import FeaturedList from "./components/FeaturedList";
import { ClientRedirect } from "@/app/event/[slug]/components/ClientRedirect";
import { WhitelabelPhaseTwo } from "@/app/event/[slug]/components/WhitelabelPhaseTwo";
import { WhitelabelPhaseThree } from "@/app/event/[slug]/components/WhitelabelPhaseThree";
import { WhitelabelPhaseSuccess } from "@/app/event/[slug]/components/WhitelabelPhaseSuccess";
import { WhitelabelHeader } from "@/app/event/[slug]/components/shared/WhitelabelHeader";
import WhitelabelTicketOptions from "@/app/event/[slug]/components/WhitelabelTicketOptions";
import EventNotFound from "@/app/event/[slug]/components/EventNotFound";
import { getSubdomainInfo } from "@/lib/utils/subdomainUtils";
import clsx from "clsx";
import { cn } from "@/lib/utils";

type Props = {
  params: {
    slug: string;
  };
  searchParams: {
    oid?: string;
  };
};

export const revalidate = 0;

const getOrganizerSlug = () => {
  try {
    const headersList = headers();
    const hostHeader = headersList.get("host");

    if (hostHeader) {
      const { organizerSlug } = getSubdomainInfo(hostHeader);
      return organizerSlug;
    }
  } catch {
    console.error("Error getting organizer slug");
  }

  return null;
};

// Shared fetch function with optimized caching for whitelabel
const fetchEventData = async (slugOrUuid: string) => {
  if (!slugOrUuid || slugOrUuid === "_") {
    return null;
  }

  const userTimeZone = await getUserTimeZone();
  const isInputUUID = isUUID(slugOrUuid);
  const organizerSlug = getOrganizerSlug();

  try {
    // Make the appropriate API call based on input type
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}api/channels/${organizerSlug}/events/${
        isInputUUID ? "detail" : "slug"
      }/${slugOrUuid}/`,
      {
        headers: { "X-Timezone": userTimeZone },
        credentials: "include",
        next: { revalidate },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch data");
    }

    const rawData = await response.json();
    const data = keysToCamel(rawData);

    // If the URL is UUID, store redirect info in the data
    if (isInputUUID && data.slug) {
      data._shouldRedirect = true;
    }

    // Backward compatibility: set pixelId from pixels.meta if available
    if (data?.pixels?.meta && !data?.pixelId) {
      data.pixelId = data.pixels.meta;
    }

    return data;
  } catch (error) {
    console.error("Error fetching event data:", error);
    return null;
  }
};

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = params;
  const data = await fetchEventData(slug);

  if (!data) {
    return {
      title: "Event Not Found",
      description: "The requested event could not be found.",
    };
  }

  // Get the best available image - first try type 1, then fallback to first image
  const getBestImage = () => {
    if (!data?.images?.length) return null;

    const typeOneImage = data.images.find(
      (img: any) => Number(img?.type) === 1
    );
    if (typeOneImage?.photo) return typeOneImage.photo;

    // Fallback to first image if type 1 not found
    return data.images[0]?.photo || null;
  };

  const bestImage = getBestImage();

  // Create metadata object with cache control
  const metadata: Metadata = {
    title: `${data?.name || "Event"}`,
    description: `${data?.description || "No description available"}`,
    openGraph: {
      title: `${data?.name || "Event"}`,
      description: `${data?.description || "No description available"}`,
      images: bestImage
        ? [
            {
              url: bestImage,
              alt: data?.name || "Event Image",
            },
          ]
        : [],
    },
    other: {
      "Cache-Control": "public, max-age=3600, stale-while-revalidate=86400",
    },
  };

  // Add preload link if we have an image
  if (bestImage) {
    metadata.other = metadata.other ?? {};
    metadata.other["link"] = [
      `rel=preload; as=image; href=${bestImage}; fetchpriority=high`,
    ];
  }

  return metadata;
};

const getEvent = async (slugOrUuid: string) => {
  if (!slugOrUuid || slugOrUuid === "_") {
    console.error("getEvent called without identifier");
    return null;
  }

  const data = await fetchEventData(slugOrUuid);
  return data;
};

const WhitelabelEventDetails = async ({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams: { oid?: string };
}) => {
  const { slug } = params;
  const { oid = "event" } = searchParams;

  if (!slug || slug === "_") {
    return (
      <EventNotFound
        message="Event identifier is wrong or missing. Cannot load event details."
        showBackButton={false}
      />
    );
  }

  const data = await getEvent(slug);

  // Handle redirect case - redirect to whitelabel route
  if (data && data._shouldRedirect && data.slug) {
    return <ClientRedirect slug={data.slug} />;
  }

  if (!data) {
    return <EventNotFound />;
  }

  // Layout configuration - shared across all phases
  const layoutConfig = (() => {
    const defaultConfig = {
      pageLayout: "default",
      headerStyle: "float",
      stickyHeader: true,
      showNeedHelp: true,
      showEventDetails: true,
      showCountdownTimer: true,
      showDescription: true,
      showModelsList: true,
      showVendorsList: true,
      showMediaList: true,
      logoImage: null,
      showBannerDesktop: true,
      showBannerMobile: false,
      showMerch: true,
      showFeatured: true,
    };

    // If meta is empty or doesn't exist, use defaults
    if (!data?.meta || Object.keys(data.meta).length === 0) {
      return defaultConfig;
    }

    // If meta has a layout property, merge with defaults
    if (data.meta.layout) {
      return { ...defaultConfig, ...data.meta.layout };
    }

    // If meta is a flat object, merge with defaults
    return { ...defaultConfig, ...data.meta };
  })();

  // Contact phase: Basic user information collection
  if (oid === "contact") {
    return (
      <WhitelabelPhaseTwo
        eventData={data}
        slug={slug}
        layoutConfig={layoutConfig}
      />
    );
  }

  // Checkout phase: Complete checkout
  if (oid === "checkout") {
    return (
      <WhitelabelPhaseThree
        eventData={data}
        slug={slug}
        layoutConfig={layoutConfig}
      />
    );
  }

  // Success phase: Order completion and confirmation
  if (oid === "success") {
    return (
      <WhitelabelPhaseSuccess
        eventData={data}
        slug={slug}
        layoutConfig={layoutConfig}
      />
    );
  }

  // Event phase: Default event details page (oid=event or no oid)
  const eventId = data?.id;
  const mainEventImg = data?.images?.find(
    (img: any) => Number(img?.type) === 1
  );
  const eventBannerImg = data?.images?.find(
    (img: any) => Number(img?.type) === 2
  );
  const eventParkingImg = data?.images?.find(
    (img: any) => Number(img?.type) === 3
  );
  const isFreeEvent = data?.tickets?.length === 0 && data?.freeToAttend;
  const waivers = data?.waivers;

  const isOnlyTicketsShow =
    layoutConfig?.pageLayout === "default" &&
    !layoutConfig?.showEventDetails &&
    !layoutConfig?.showDescription &&
    !layoutConfig?.showModelsList &&
    !layoutConfig?.showVendorsList &&
    !layoutConfig?.showMediaList &&
    !layoutConfig?.showMerch &&
    !layoutConfig?.showFeatured;

  const getEventDetailsConditionalStyles = () => {
    let classname = "";
    if (eventBannerImg) {
      if (layoutConfig?.headerStyle === "bar") {
        if (layoutConfig?.showBannerDesktop && layoutConfig?.showBannerMobile) {
          classname = "pt-6 md:pt-2";
        } else if (
          layoutConfig?.showBannerDesktop &&
          !layoutConfig?.showBannerMobile
        ) {
          classname = "pt-14 md:pt-2";
        } else if (
          !layoutConfig?.showBannerDesktop &&
          layoutConfig?.showBannerMobile
        ) {
          classname = "pt-6 md:pt-12";
        } else if (
          !layoutConfig?.showBannerDesktop &&
          !layoutConfig?.showBannerMobile
        ) {
          classname = "pt-14 md:pt-12";
        }
      } else {
        classname = "pt-6";
      }
    } else {
      classname = "pt-2";
    }

    return classname;
  };

  const renderEventDetails = () => {
    if (layoutConfig?.pageLayout === "centered") {
      return (
        <EventImageViewer>
          <EventCoverImage
            image={eventBannerImg}
            showHeader={layoutConfig?.headerStyle === "bar"}
            showBannerDesktop={layoutConfig?.showBannerDesktop}
            showBannerMobile={layoutConfig?.showBannerMobile}
          />

          <div
            className={clsx(
              "grid justify-center pb-14",
              getEventDetailsConditionalStyles()
            )}
          >
            <div className={`flex flex-col p-4 gap-10 md:w-[55vw]`}>
              <div className="mb-5">
                {layoutConfig?.showEventDetails && (
                  <EventOverview
                    eventId={data?.id}
                    img={mainEventImg?.photo}
                    title={data?.name}
                    location={{
                      city: data?.city,
                      state: data?.state,
                      country: data?.country,
                      venueName: data?.venueName,
                      address: data?.address,
                      zipCode: data?.zipCode,
                      geoLocation: data?.geoLocation,
                    }}
                    category={data?.category}
                    orgDetails={data?.organization}
                    startDate={data?.startDate}
                    endDate={data?.endDate}
                    timezone={data?.timezone}
                    tag={data?.metadata?.tag}
                    parkingImg={eventParkingImg?.photo}
                    collaborators={data?.collaborators}
                    pixels={data?.pixels}
                  />
                )}

                {!isFreeEvent && (
                  <div className="mt-10">
                    <WhitelabelTicketOptions
                      tickets={data?.tickets}
                      title={data?.name}
                      date={data?.startDate}
                      eventId={data?.id}
                      eventTitle={data?.name}
                      eventImg={data?.images?.[0]?.photo}
                      pixelId={data?.pixelId}
                      ticketAvailableFrom={data?.ticketsAvailableFrom}
                      ticketAvailableTill={data?.ticketsAvailableTill}
                      orgId={data?.organization}
                      eventStatus={data?.status}
                      timezone={data?.timezone}
                      waivers={waivers}
                      pixels={data?.pixels}
                      slug={slug}
                      isCountDownTimerHidden={!layoutConfig.showCountdownTimer}
                      isCenteredLayout={true}
                    />
                  </div>
                )}
                {data && data?.showVipList && layoutConfig?.showFeatured && (
                  <FeaturedList list={data?.vipList} />
                )}

                {layoutConfig?.showDescription && (
                  <EventDetails details={data?.description} />
                )}
                {layoutConfig?.showModelsList && (
                  <ProfileGridSection title="Models" items={data?.models} />
                )}
                {layoutConfig?.showVendorsList && (
                  <ProfileGridSection title="Vendors" items={data?.vendors} />
                )}
                {layoutConfig?.showMediaList && (
                  <ProfileGridSection title="Media" items={data?.media} />
                )}
                {layoutConfig?.showMerch && (
                  <MerchSection
                    products={data?.products}
                    isWhitelabel={true}
                    slug={slug}
                  />
                )}
              </div>
            </div>
          </div>
        </EventImageViewer>
      );
    }

    return (
      <EventImageViewer>
        <EventCoverImage
          image={eventBannerImg}
          showHeader={layoutConfig?.headerStyle === "bar"}
          showBannerDesktop={layoutConfig?.showBannerDesktop}
          showBannerMobile={layoutConfig?.showBannerMobile}
        />

        <div
          className={clsx(
            "grid justify-center pb-14",
            getEventDetailsConditionalStyles()
          )}
        >
          <div
            className={`flex flex-col lg:flex-row p-4 gap-10 md:w-[94vw] ${
              isFreeEvent || isOnlyTicketsShow
                ? "justify-center"
                : "justify-between"
            }`}
          >
            {!isOnlyTicketsShow && (
              <div className="lg:w-[62%] mb-5">
                {layoutConfig?.showEventDetails && (
                  <EventOverview
                    eventId={data?.id}
                    img={mainEventImg?.photo}
                    title={data?.name}
                    location={{
                      city: data?.city,
                      state: data?.state,
                      country: data?.country,
                      venueName: data?.venueName,
                      address: data?.address,
                      zipCode: data?.zipCode,
                      geoLocation: data?.geoLocation,
                    }}
                    category={data?.category}
                    orgDetails={data?.organization}
                    startDate={data?.startDate}
                    endDate={data?.endDate}
                    timezone={data?.timezone}
                    tag={data?.metadata?.tag}
                    parkingImg={eventParkingImg?.photo}
                    collaborators={data?.collaborators}
                    pixels={data?.pixels}
                  />
                )}
                <div className="hidden lg:block">
                  {data && data?.showVipList && layoutConfig?.showFeatured && (
                    <FeaturedList list={data?.vipList} />
                  )}
                  {layoutConfig?.showDescription && (
                    <EventDetails details={data?.description} />
                  )}
                  {layoutConfig?.showModelsList && (
                    <ProfileGridSection title="Models" items={data?.models} />
                  )}
                  {layoutConfig?.showVendorsList && (
                    <ProfileGridSection title="Vendors" items={data?.vendors} />
                  )}
                  {layoutConfig?.showMediaList && (
                    <ProfileGridSection title="Media" items={data?.media} />
                  )}
                  {layoutConfig?.showMerch && (
                    <MerchSection
                      products={data?.products}
                      isWhitelabel={true}
                      slug={slug}
                    />
                  )}
                </div>
              </div>
            )}

            {/* Ticket Options */}
            {!isFreeEvent && (
              <div
                className={clsx(
                  "lg:w-[38%]",
                  isOnlyTicketsShow && "lg:w-[55vw]"
                )}
              >
                <WhitelabelTicketOptions
                  tickets={data?.tickets}
                  title={data?.name}
                  date={data?.startDate}
                  eventId={data?.id}
                  eventTitle={data?.name}
                  eventImg={data?.images?.[0]?.photo}
                  pixelId={data?.pixelId}
                  ticketAvailableFrom={data?.ticketsAvailableFrom}
                  ticketAvailableTill={data?.ticketsAvailableTill}
                  orgId={data?.organization}
                  eventStatus={data?.status}
                  timezone={data?.timezone}
                  waivers={waivers}
                  pixels={data?.pixels}
                  slug={slug}
                  isCountDownTimerHidden={!layoutConfig.showCountdownTimer}
                  isCenteredLayout={isOnlyTicketsShow}
                />
              </div>
            )}

            <div className="lg:hidden">
              {data && data?.showVipList && layoutConfig?.showFeatured && (
                <FeaturedList list={data?.vipList} />
              )}
              {layoutConfig?.showDescription && (
                <EventDetails details={data?.description} />
              )}
              {layoutConfig?.showModelsList && (
                <ProfileGridSection title="Models" items={data?.models} />
              )}
              {layoutConfig?.showVendorsList && (
                <ProfileGridSection title="Vendors" items={data?.vendors} />
              )}
              {layoutConfig?.showMediaList && (
                <ProfileGridSection title="Media" items={data?.media} />
              )}
              {layoutConfig?.showMerch && (
                <MerchSection
                  products={data?.products}
                  isWhitelabel={true}
                  slug={slug}
                />
              )}
            </div>
          </div>
        </div>
      </EventImageViewer>
    );
  };

  return (
    <div className="min-h-[calc(100vh-100px)] relative">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(createEventJsonLd(data)),
        }}
      />

      {data?.organization && (
        <ErrorBoundary>
          <StoreSessions orgSlug={data?.organization?.slug} eventId={eventId} />
        </ErrorBoundary>
      )}
      <EventPageTracker
        event={{
          id: data.id,
          title: data?.name,
          pixelId: data?.pixelId,
          pixels: data?.pixels,
          date: data.startDate,
        }}
      />
      <WhitelabelHeader
        layoutConfig={layoutConfig}
        slug={slug}
        showCartInfo={true}
      />

      {renderEventDetails()}
    </div>
  );
};

export default WhitelabelEventDetails;
