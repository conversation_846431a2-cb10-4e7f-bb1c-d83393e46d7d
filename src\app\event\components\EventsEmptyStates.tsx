import React from "react";

export const NoOrganizerState: React.FC = () => (
  <div className="text-center py-12">
    <p className="text-gray-500">No organizer specified</p>
  </div>
);

export const ErrorState: React.FC = () => (
  <div className="text-center py-12">
    <p className="text-red-500">
      Failed to load events. Please try again later.
    </p>
  </div>
);

export const NoEventsState: React.FC = () => (
  <div className="text-center py-12">
    <h2 className="text-2xl font-semibold text-gray-900 mb-4">
      No Events Available
    </h2>
    <p className="text-gray-500">
      There are currently no events available from this organizer.
    </p>
  </div>
);

interface EventsHeaderProps {
  organizerSlug: string;
}

export const EventsHeader: React.FC<EventsHeaderProps> = ({
  organizerSlug,
}) => (
  <div className="mb-8">
    <h1 className="text-3xl font-bold text-gray-900 mb-2">
      {organizerSlug.charAt(0).toUpperCase() + organizerSlug.slice(1)} Events
    </h1>
    <p className="text-gray-600">
      Discover upcoming events from {organizerSlug}
    </p>
  </div>
);
