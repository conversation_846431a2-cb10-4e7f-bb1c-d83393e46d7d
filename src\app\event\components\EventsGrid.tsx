import React from "react";
import EventCard from "@/components/EventCard/EventCard";
import EventCardSkeleton from "@/components/EventCard/EventCardSkeleton";
import type { Event } from "@/lib/redux/slices/events/eventTypes";

interface EventsGridProps {
  events: Event[];
  isLoading: boolean;
  isFetching: boolean;
  hasMore: boolean;
  loadingRef: React.RefObject<HTMLDivElement>;
}

export const EventsGrid: React.FC<EventsGridProps> = ({
  events,
  isLoading,
  isFetching,
  hasMore,
  loadingRef,
}) => {
  // Initial loading state
  if (isLoading && events.length === 0) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 p-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <EventCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 items-start">
        {events.map((event) => {
          const mainEventImg = event?.images?.find(
            (img: any) => Number(img?.type) === 1
          );

          return (
            <div
              key={event.id}
              className="flex w-full justify-center items-center"
            >
              <EventCard
                title={event.name}
                date={event.startDate}
                city={event.city}
                state={event.state}
                imgSrc={mainEventImg?.photo}
                href={`/e/${event.slug}`}
                eventId={event.id}
                timezone={(event as any)?.timezone}
                tag={(event as any)?.metadata?.tag}
                pixels={event?.pixels || {}}
              />
            </div>
          );
        })}
      </div>

      {/* Loading indicator for infinite scroll */}
      {isFetching && events.length > 0 && (
        <div className="flex justify-center mt-8">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <EventCardSkeleton key={`loading-${index}`} />
            ))}
          </div>
        </div>
      )}

      {/* Intersection observer target for infinite scroll */}
      {hasMore && <div ref={loadingRef} className="h-4 mt-8" />}

      {/* End of results indicator */}
      {!hasMore && events.length > 0 && (
        <div className="text-center mt-8">
          <p className="text-gray-500">You've reached the end of all events</p>
        </div>
      )}
    </>
  );
};
