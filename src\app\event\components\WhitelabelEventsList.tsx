"use client";

import React from "react";
import { useOrganizerSlug } from "@/lib/Providers/SubdomainProvider";
import { useEventsData } from "./hooks/useEventsData";
import { useInfiniteScroll } from "./hooks/useInfiniteScroll";
import { EventsGrid } from "./EventsGrid";
import {
  NoOrganizerState,
  ErrorState,
  NoEventsState,
  EventsHeader,
} from "./EventsEmptyStates";

export const WhitelabelEventsList: React.FC = () => {
  const organizerSlug = useOrganizerSlug();

  const {
    allEvents,
    isLoading,
    error,
    isFetching,
    hasMore,
    eventsData,
    loadMoreEvents,
    shouldLoadMore,
  } = useEventsData(organizerSlug);

  const { loadingRef } = useInfiniteScroll({
    hasMore,
    isFetching,
    allEventsLength: allEvents.length,
    onLoadMore: loadMoreEvents,
    shouldLoadMore,
    nextCursor: eventsData?.nextCursor,
  });

  // Handle different states
  if (!organizerSlug) {
    return <NoOrganizerState />;
  }

  if (error) {
    return <ErrorState />;
  }

  if (!isLoading && !allEvents.length) {
    return <NoEventsState />;
  }

  return (
    <div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <EventsHeader organizerSlug={organizerSlug} />
      <EventsGrid
        events={allEvents}
        isLoading={isLoading}
        isFetching={isFetching}
        hasMore={hasMore}
        loadingRef={loadingRef}
      />
    </div>
  );
};
