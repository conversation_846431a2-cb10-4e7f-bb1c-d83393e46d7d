"use client";

import React, { Suspense } from "react";
import EventCardSkeleton from "@/components/EventCard/EventCardSkeleton";
import { WhitelabelEventsList } from "./WhitelabelEventsList";

const LoadingFallback = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
    {Array.from({ length: 8 }).map((_, index) => (
      <EventCardSkeleton key={index} />
    ))}
  </div>
);

export default function WhitelabelEventsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Suspense fallback={<LoadingFallback />}>
        <WhitelabelEventsList />
      </Suspense>
    </div>
  );
}
