import { useState, useEffect, useRef } from "react";
import { useGetEventsByOrgIdOrSlugQuery } from "@/lib/redux/slices/events/eventsApi";
import type { Event } from "@/lib/redux/slices/events/eventTypes";

export const useEventsData = (organizerSlug: string | null) => {
  const [allEvents, setAllEvents] = useState<Event[]>([]);
  const [currentCursor, setCurrentCursor] = useState<string | undefined>(undefined);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const shouldLoadMore = useRef<boolean>(false);

  const {
    data: eventsData,
    isLoading,
    error,
    isFetching,
  } = useGetEventsByOrgIdOrSlugQuery(
    {
      orgIdOrSlug: organizerSlug || "",
      perPage: 20,
      orderBy: "start_date",
      status:"active",
      cursor: currentCursor,
    },
    {
      skip: !organizerSlug,
      refetchOnMountOrArgChange: true,
    }
  );

  // Process events data when it arrives from RTK Query
  useEffect(() => {
    if (!eventsData || !eventsData.results) return;

    if (currentCursor === undefined) {
      // Initial load - set events
      setAllEvents(eventsData.results);
    } else {
      // Pagination - add new events
      const existingIds = new Set(allEvents.map((event) => event.id));
      const newEvents = eventsData.results.filter(
        (event) => !existingIds.has(event.id)
      );

      if (newEvents.length > 0) {
        setAllEvents((prev) => [...prev, ...newEvents]);
      }
    }

    // Update pagination state
    setHasMore(eventsData.nextPageResults || false);
    shouldLoadMore.current = false;
  }, [eventsData, currentCursor, allEvents]);

  const loadMoreEvents = () => {
    if (eventsData?.nextCursor && hasMore && !isFetching && !shouldLoadMore.current) {
      shouldLoadMore.current = true;
      setCurrentCursor(eventsData.nextCursor);
    }
  };

  return {
    allEvents,
    isLoading,
    error,
    isFetching,
    hasMore,
    eventsData,
    loadMoreEvents,
    shouldLoadMore,
  };
}; 