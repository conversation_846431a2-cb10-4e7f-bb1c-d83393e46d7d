import { useEffect, useRef, MutableRefObject } from "react";

interface UseInfiniteScrollProps {
  hasMore: boolean;
  isFetching: boolean;
  allEventsLength: number;
  onLoadMore: () => void;
  shouldLoadMore: MutableRefObject<boolean>;
  nextCursor?: string;
}

export const useInfiniteScroll = ({
  hasMore,
  isFetching,
  allEventsLength,
  onLoadMore,
  shouldLoadMore,
  nextCursor,
}: UseInfiniteScrollProps) => {
  const loadingRef = useRef<HTMLDivElement>(null);

  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    // Don't set up observer if we don't have events yet or if we don't have more events to load
    if (allEventsLength === 0 || !hasMore) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;

        if (
          entry.isIntersecting &&
          hasMore &&
          !isFetching &&
          nextCursor &&
          !shouldLoadMore.current
        ) {
          onLoadMore();
        }
      },
      { threshold: 0.1 }
    );

    const currentLoadingRef = loadingRef.current;
    if (currentLoadingRef) {
      observer.observe(currentLoadingRef);
    }

    return () => {
      if (currentLoadingRef) {
        observer.unobserve(currentLoadingRef);
      }
    };
  }, [allEventsLength, hasMore, isFetching, nextCursor, onLoadMore, shouldLoadMore]);

  // Backup scroll-based infinite scroll trigger
  useEffect(() => {
    if (!hasMore || allEventsLength === 0) return;

    let lastScrollY = window.scrollY;

    const handleScroll = () => {
      if (isFetching || !nextCursor || shouldLoadMore.current) return;

      const currentScrollY = window.scrollY;
      const isScrollingDown = currentScrollY > lastScrollY;
      const scrollPercentage =
        (currentScrollY + window.innerHeight) / document.body.offsetHeight;
      const bottom = scrollPercentage > 0.8;

      // Only load more if scrolling down AND near bottom
      if (bottom && isScrollingDown) {
        onLoadMore();
      }

      lastScrollY = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [nextCursor, hasMore, isFetching, allEventsLength, onLoadMore, shouldLoadMore]);

  return { loadingRef };
}; 