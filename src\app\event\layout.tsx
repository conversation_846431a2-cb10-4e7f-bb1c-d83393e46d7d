"use client";

import { useSubdomain } from "@/lib/Providers/SubdomainProvider";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function EventLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isWhitelabel, organizerSlug } = useSubdomain();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // Redirect to main events if not on whitelabel subdomain
    if (isClient && !isWhitelabel) {
      router.push("/events");
    }
  }, [isClient, isWhitelabel, router]);

  if (!isClient) {
    return null;
  }

  // Only show content if we're on a whitelabel subdomain
  if (!isWhitelabel) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      <main className="w-full relative">{children}</main>
    </div>
  );
}
