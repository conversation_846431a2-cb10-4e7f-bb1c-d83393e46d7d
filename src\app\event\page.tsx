"use client";

import { useSubdomain } from "@/lib/Providers/SubdomainProvider";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import WhitelabelEventsPage from "./components/WhitelabelEventsPage";

export default function EventHomePage() {
  const { isWhitelabel } = useSubdomain();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // Redirect to main events if not on whitelabel subdomain
    if (isClient && !isWhitelabel) {
      router.push("/events");
    }
  }, [isClient, isWhitelabel, router]);

  if (!isClient) {
    return null;
  }

  // Only show content if we're on a whitelabel subdomain
  if (!isWhitelabel) {
    return null;
  }

  return <WhitelabelEventsPage />;
}
