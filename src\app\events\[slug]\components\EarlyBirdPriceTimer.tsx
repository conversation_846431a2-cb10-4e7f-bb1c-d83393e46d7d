import React, { useState, useEffect, useCallback } from 'react'
import { CustomTicketTemplate } from '../../types'
import { calculateTimeRemaining } from '@/lib/utils/date'

interface TimeRemaining {
  hours: number
  minutes: number
  isWithin24Hours: boolean
  totalMinutes: number
  scheduledTime: string
  currentTime: string
}

const EarlyBirdPriceTimer = ({ priceSchedule, timezone }: Pick<CustomTicketTemplate, 'priceSchedule'> & { timezone: string }) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining | null>(null)

  // Format the display string
  const formatTimeRemaining = useCallback((time: TimeRemaining): string => {
    if (time.hours > 0) {
      return `${time.hours}h`
    }
    return `${time.minutes}m`
  }, [])

  useEffect(() => {
    if (!priceSchedule?.scheduledAt) {
      setTimeRemaining(null)
      return
    }

    try {
      const remaining = calculateTimeRemaining(priceSchedule.scheduledAt, timezone)
      setTimeRemaining(remaining)
    } catch (err) {
      setTimeRemaining(null)
    }
  }, [priceSchedule?.scheduledAt])

  // Don't render if no time remaining or not within 24 hours
  if (!timeRemaining?.isWithin24Hours) {
    return null
  }

  return (
    <div className="text-right mt-2.5">
        <p className="text-[11px] text-[#F01111] font-medium ">Price increases in {formatTimeRemaining(timeRemaining)}</p>
    </div>
  )
}

export default EarlyBirdPriceTimer

