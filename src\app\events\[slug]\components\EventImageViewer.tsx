"use client";
import dynamic from "next/dynamic";

const PhotoSwipeGallery = dynamic(
  () => import('react-photoswipe-gallery').then(mod => {
    // Import the CSS when the component loads
    import('photoswipe/dist/photoswipe.css');
    return mod.Gallery;
  }),
  {
    ssr: false,
  }
);

export function EventImageViewer({ children }) {
  return <PhotoSwipeGallery>{children}</PhotoSwipeGallery>;
}
