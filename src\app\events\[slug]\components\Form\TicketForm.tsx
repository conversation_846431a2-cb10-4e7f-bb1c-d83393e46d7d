import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "@nextui-org/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { useGetEventByIdQuery } from "@/lib/redux/slices/events/eventsApi";
import { useVehicleForm } from "./hooks/useVehicleForm";
import { useFormSubmission } from "./hooks/useFormSubmission";
import { useFormNavigation } from "./hooks/useFormNavigation";
import { useFormFieldRenderer } from "./hooks/useFormFieldRenderer";
import { createCurrentFormSchema } from "./schema";
import { TicketFormProps } from "./types";
import toast from "react-hot-toast";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import { PAYMENT_OPTIONS } from "@/lib/constants/storage";

export default function TicketForm({
  open,
  onClose,
  isEdit = false,
  ticketFormData = {},
  eventId,
  ticketId,
  toggleModal,
  onTeamCreated,
  isWhitelabel = false,
  slug = "",
}: TicketFormProps) {
  const activeFormTicket = useSelector(
    (state: any) => state?.events?.activeFormTicket
  );
  const savedFormData = useSelector(
    (state: any) => state?.events?.savedFormData
  );
  const selectedVariantIds = useSelector(
    (state: any) => state?.events?.selectedVariantIds
  );
  const waivers = useSelector((state: any) => state?.events?.waivers);

  const dispatch = useDispatch();

  const [currentFormIndex, setCurrentFormIndex] = useState(0);
  const [formData, setFormData] = useState<Record<number, any>>({});
  const [selectedTicket, setSelectedTicket] = useState<any>(null);
  const [isFormLoading, setIsFormLoading] = useState(false);

  const { data: eventData = {} } = useGetEventByIdQuery(
    { id: eventId || "" },
    {
      skip: !eventId || !isEdit,
    }
  );

  const isSavePaymentDetails =
    activeFormTicket?.paymentOption === PAYMENT_OPTIONS.SAVE_PAYMENT_METHOD &&
    activeFormTicket?.approvalRequired;

  const forms = selectedTicket?.forms || activeFormTicket?.forms || [];
  const currentForm = forms[currentFormIndex];

  // Safe schema creation with fallback
  const currentFormSchema = currentForm
    ? createCurrentFormSchema(currentForm, activeFormTicket, currentFormIndex)
    : null;

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    reset,
    setValue,
    getValues,
    clearErrors,
  } = useForm({
    resolver: currentFormSchema ? zodResolver(currentFormSchema) : undefined,
    mode: "onSubmit",
    defaultValues:
      currentForm?.formData?.reduce((acc: any, field: any) => {
        if (!field?.id) return acc;

        const key = field.type === "vehicle_info" ? "vehicle" : field.id;
        if (field.type === "vehicle_info") {
          acc[key] = {
            vehicleType: "",
            year: "",
            make: "",
            customMakeName: "",
            modelName: "",
            modificationText: "",
            vehicleImages: [
              {
                image: null,
              },
            ],
          };
        } else if (field.type === "checkboxes") {
          acc[key] = [];
        } else if (
          field.type === "file_upload" &&
          (field.label === "Profile image for the company" ||
            field.label ===
              "Profile image (displayed on the event details page)")
        ) {
          acc[key] = {
            image: null,
          };
        } else if (field.type === "file_upload") {
          acc[key] = [];
        } else if (field.type === "radio") {
          acc[key] =
            field.required && field.options?.length > 0
              ? field.options[0].value
              : "";
        } else {
          acc[key] = "";
        }
        return acc;
      }, {}) || {},
  });

  // Populate the form with the previously saved response while editing
  useEffect(() => {
    if (!isEdit) return;

    // Find the current ticket from the fetched event data
    const ticket = (eventData as any)?.tickets?.find(
      (t: any) => t?.id === ticketId
    );

    if (!ticket) return;

    setSelectedTicket(ticket);

    const cartFormData =
      ticketFormData?.form_response_data?.[currentFormIndex]?.data;
    if (!cartFormData) return;

    // Map the response object keys (which come back as field labels) to the
    // keys that our form expects (field.id or "vehicle")
    const mappedData =
      currentForm?.formData?.reduce((acc: Record<string, any>, field: any) => {
        if (!field?.id) return acc;

        const key = field.type === "vehicle_info" ? "vehicle" : field.id;

        // API returns the user's answer keyed by the field label
        const labelKey = field.label;

        if (field.type === "vehicle_info" && cartFormData?.vehicle) {
          // Convert snake_case keys that may come from the backend to camelCase
          acc[key] = keysToCamel(cartFormData.vehicle);
        } else if (cartFormData.hasOwnProperty(labelKey)) {
          // Special handling for Team / Club name field
          if (field.label === "Team / Club name") {
            const teamValue = cartFormData[labelKey];
            if (teamValue === null) {
              // If team name is null, set "No Team" checkbox to true
              acc[key] = null;
              acc[`${field.id}_no_team`] = true;
            } else {
              acc[key] = teamValue;
              acc[`${field.id}_no_team`] = false;
            }
          } else {
            acc[key] = cartFormData[labelKey];
          }
        } else if (cartFormData.hasOwnProperty(key)) {
          acc[key] = cartFormData[key];
        }

        return acc;
      }, {} as Record<string, any>) || {};

    // Preserve flyer value if present on the first form
    if (cartFormData["Event Flyer"]) {
      mappedData["Event Flyer"] = cartFormData["Event Flyer"];
    }

    reset(mappedData);
  }, [
    isEdit,
    eventData,
    ticketFormData?.form_response_data,
    currentFormIndex,
    currentForm,
    ticketId,
    reset,
  ]);

  const {
    makeOptions,
    allTypes,
    makesLoading,
    loadMakeOptions,
    handleMakeChange,
  } = useVehicleForm(watch, setValue);

  const { handleFormSubmit } = useFormSubmission(
    formData,
    currentFormIndex,
    forms,
    currentForm,
    selectedTicket,
    activeFormTicket,
    savedFormData,
    setCurrentFormIndex,
    setFormData,
    isEdit,
    reset,
    selectedVariantIds,
    waivers,
    isWhitelabel,
    slug
  );

  const { handleNextForm, handlePreviousForm } = useFormNavigation(
    formData,
    currentFormIndex,
    currentForm,
    selectedTicket,
    activeFormTicket,
    savedFormData,
    forms,
    reset
  );

  const formFieldRenderer = useFormFieldRenderer({
    control,
    watch,
    errors,
    allTypes,
    makeOptions,
    makesLoading,
    loadMakeOptions,
    handleMakeChange,
    ticketId,
    setValue,
    setIsFormLoading,
    currentForm,
    getValues,
    clearErrors,
    isEdit,
    toggleModal,
    onTeamCreated,
  });

  const onSubmit = async (data: any) => {
    if (!data || Object.keys(errors || {}).length > 0) {
      return;
    }

    try {
      // Transform the data to match the expected structure
      const transformedData =
        currentForm?.formData?.reduce((acc: any, field: any) => {
          if (!field?.id) return acc;

          const key = field.type === "vehicle_info" ? "vehicle" : field.id;
          if (data.hasOwnProperty(key)) {
            // Handle team/club field: set to null if "No Team" is checked
            if (
              field.label === "Team / Club name" &&
              data[`${field.id}_no_team`]
            ) {
              acc[key] = null;
            } else {
              acc[key] = data[key];
            }
          }
          return acc;
        }, {}) || {};

      // Filter out _no_team fields from the final data
      Object.keys(transformedData).forEach((key) => {
        if (key.endsWith("_no_team")) {
          delete transformedData[key];
        }
      });

      // Add flyer to the transformed data if it exists
      const flyer = getValues?.("Event Flyer");
      if (flyer && currentFormIndex === 0) {
        transformedData["Event Flyer"] = flyer;
      }

      await handleFormSubmit(transformedData, onClose);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit form. Please try again.");
    }
  };

  const onNext = async () => {
    if (Object.keys(errors).length > 0) {
      return;
    }
    const nextIndex = await handleNextForm(watch());
    setCurrentFormIndex(nextIndex);
  };

  const onPrevious = () => {
    const previousIndex = handlePreviousForm();
    setCurrentFormIndex(previousIndex);
  };

  const editStateFlyer =
    ticketFormData?.form_response_data?.[currentFormIndex]?.data?.[
      "Event Flyer"
    ];

  const shouldShowFlyer = () =>
    (activeFormTicket?.flyerUrl && currentFormIndex === 0) ||
    (isEdit && editStateFlyer);

  const renderFormFields = () => (
    <div className="space-y-6">
      {currentForm?.formData?.map((field: any) =>
        formFieldRenderer.renderFormField({
          field,
          ticketType: currentForm?.ticketType,
          ...(shouldShowFlyer() && {
            flyer: activeFormTicket?.flyerUrl || editStateFlyer,
          }),
        })
      ) || null}
    </div>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      disableScrollLock={false}
      maxWidth={
        currentForm?.ticketType === "vehicle_registration" ? "md" : "sm"
      }
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "16px",
          overflow: "hidden",
        },
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogTitle
          sx={{
            background: "#F3F3F3",
            padding: "10px 16px",
            "& + .MuiDialogContent-root": {
              borderTop: "1px solid #E3E3E3",
              borderBottom: "1px solid #E3E3E3",
            },
          }}
        >
          <div className="flex justify-between items-center">
            <p className="font-sFPro text-[#303030] font-[500] text-[17px]">
              {currentForm?.name || "Form"}
            </p>

            <div className="flex gap-2">
              {forms.length > 1 && (
                <>
                  {/* <Button
                    isIconOnly
                    variant="light"
                    onPress={onPrevious}
                    isDisabled={currentFormIndex === 0}
                    size="sm"
                  >
                    <IoIosArrowBack />
                  </Button>
                  <Button
                    isIconOnly
                    variant="light"
                    onPress={onNext}
                    isDisabled={currentFormIndex === forms.length - 1}
                    size="sm"
                  >
                    <IoIosArrowForward />
                  </Button> */}
                </>
              )}
            </div>
          </div>
        </DialogTitle>
        <DialogContent
          dividers
          sx={{
            maxHeight: "calc(100vh - 16rem)",
            overflowY: "auto",
            borderColor: "var(--nextui-border-color)",
          }}
        >
          <div className="py-2">{renderFormFields()}</div>
          {isSavePaymentDetails && (
            <div className="my-[20px] text-center">
              <p className="text-[13px] text-black">
                <span className="font-semibold">
                  This ticket requires approval.
                </span>{" "}
                Your card won’t be charged until the organizer accepts your
                application.
              </p>

              <p className="text-[13px] text-black">
                Check registration status via the Autolnk app using the same
                email as your order.
              </p>
            </div>
          )}
        </DialogContent>
        <DialogActions sx={{ padding: "12px" }}>
          <div className="flex gap-2 justify-end w-full mb-3 md:mb-0">
            <Button
              onPress={onClose}
              variant="bordered"
              className="border-1 font-semibold"
              size="sm"
              isLoading={isFormLoading}
              isDisabled={isFormLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              size="sm"
              isDisabled={isFormLoading}
              isLoading={isSubmitting || isFormLoading}
            >
              {isEdit ? "Save" : "Add"}
            </Button>
          </div>
        </DialogActions>
      </form>
    </Dialog>
  );
}
