import React, { useState, useC<PERSON>back, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@nextui-org/react";
import <PERSON><PERSON><PERSON>, { Point, Area } from "react-easy-crop";
import { fetchOverlayImage } from "../actions";

interface FlyerCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (croppedImage: string, originalFileName?: string) => void;
  isUploading?: boolean;
  overlayImage: string;
  currentImage: string | null;
  originalFileName?: string;
}

const FlyerCropModal: React.FC<FlyerCropModalProps> = ({
  isOpen,
  onClose,
  onSave,
  isUploading = false,
  overlayImage,
  currentImage,
  originalFileName,
}) => {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [overlayImageData, setOverlayImageData] = useState<string | null>(null);
  const [overlayDimensions, setOverlayDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);

  useEffect(() => {
    if (!isOpen || !currentImage) {
      setCrop({ x: 0, y: 0 });
      setZoom(1);
      setCroppedAreaPixels(null);
    }
  }, [isOpen, currentImage]);

  useEffect(() => {
    const loadOverlayImage = async () => {
      try {
        const base64Image = await fetchOverlayImage(overlayImage);
        setOverlayImageData(base64Image);

        // Get overlay image dimensions
        const img = new Image();
        img.onload = () => {
          setOverlayDimensions({
            width: img.width,
            height: img.height,
          });
        };
        img.src = base64Image;
      } catch (error) {
        console.error("Error loading overlay image:", error);
      }
    };

    if (overlayImage) {
      loadOverlayImage();
    }
  }, [overlayImage]);

  const onCropComplete = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const createCroppedImage = async () => {
    if (!currentImage || !croppedAreaPixels || !overlayImageData) return;

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Create image elements for both the user's image and the overlay
    const imageObj = new Image();
    const overlayObj = new Image();

    try {
      // Load both images
      await Promise.all([
        new Promise<void>((resolve) => {
          imageObj.onload = () => resolve();
          imageObj.src = currentImage;
        }),
        new Promise<void>((resolve) => {
          overlayObj.onload = () => resolve();
          overlayObj.src = overlayImageData;
        }),
      ]);

      // Set canvas size to match cropped dimensions
      canvas.width = croppedAreaPixels.width;
      canvas.height = croppedAreaPixels.height;

      // Draw the cropped user image
      ctx.drawImage(
        imageObj,
        croppedAreaPixels.x,
        croppedAreaPixels.y,
        croppedAreaPixels.width,
        croppedAreaPixels.height,
        0,
        0,
        croppedAreaPixels.width,
        croppedAreaPixels.height
      );

      // Draw the overlay image on top
      ctx.drawImage(
        overlayObj,
        0,
        0,
        croppedAreaPixels.width,
        croppedAreaPixels.height
      );

      // Convert to base64
      const croppedImage = canvas.toDataURL("image/png");
      onSave(croppedImage, originalFileName);
    } catch (error) {
      console.error("Error creating cropped image:", error);
    }
  };

  // Calculate the aspect ratio based on overlay dimensions
  const aspectRatio = overlayDimensions
    ? overlayDimensions.width / overlayDimensions.height
    : 3 / 4;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="2xl"
      classNames={{
        base: "max-h-[90vh]",
        backdrop: "z-[1300]",
        wrapper: "z-[1301]",
        body: "max-h-[90vh] overflow-y-auto",
      }}
    >
      <ModalContent>
        <ModalHeader>Crop Flyer Image</ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            <div className="relative mx-auto w-[90%] md:w-[600px] h-[400px] md:h-[400px] flex flex-col gap-4">
              <div className="relative flex-1 w-full h-full">
                {currentImage && (
                  <Cropper
                    image={currentImage}
                    crop={crop}
                    zoom={zoom}
                    aspect={aspectRatio}
                    onCropChange={setCrop}
                    onZoomChange={setZoom}
                    onCropComplete={onCropComplete}
                    showGrid={false}
                    style={{
                      containerStyle: {
                        width: "100%",
                        height: "100%",
                        backgroundColor: "#000",
                      },
                      cropAreaStyle: {
                        border: "2px solid rgba(255, 255, 255, 0.7)",
                        backgroundImage: overlayImageData
                          ? `url(${overlayImageData})`
                          : "none",
                        backgroundSize: "contain",
                        backgroundPosition: "center",
                        backgroundRepeat: "no-repeat",
                      },
                      mediaStyle: {
                        width: "100%",
                        height: "100%",
                        objectFit: "contain",
                      },
                    }}
                  />
                )}
              </div>
              <div className="px-4">
                <div className="flex items-center gap-2">
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onPress={() => setZoom(Math.max(1, zoom - 0.1))}
                    className="min-w-unit-8 text-lg"
                  >
                    -
                  </Button>
                  <Slider
                    aria-label="Zoom"
                    size="sm"
                    step={0.1}
                    minValue={1}
                    maxValue={3}
                    value={zoom}
                    onChange={(value) => setZoom(value as number)}
                    className="max-w-full"
                    classNames={{
                      base: "gap-2",
                      track: "bg-default-100",
                      filler: "bg-primary",
                      thumb: "bg-primary shadow-none border-none",
                      label: "text-default-500",
                      value: "text-default-500",
                    }}
                  />
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onPress={() => setZoom(Math.min(3, zoom + 0.1))}
                    className="min-w-unit-8 text-lg"
                  >
                    +
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex gap-2 justify-end w-full">
            <Button
              className="border-1 font-semibold"
              size="sm"
              variant="light"
              onPress={onClose}
              isDisabled={isUploading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              size="sm"
              onPress={createCroppedImage}
              isDisabled={!currentImage || isUploading || !overlayImageData}
              isLoading={isUploading}
            >
              Save
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default FlyerCropModal;
