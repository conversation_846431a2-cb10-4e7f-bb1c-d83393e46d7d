import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@nextui-org/react";
import { useState, useCallback, useEffect } from "react";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import { Point, Area } from "react-easy-crop";

interface ProfilePictureModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (croppedImage: string) => void;
  isUploading?: boolean;
  currentImage: string | null;
}

const ProfilePictureModal = ({
  isOpen,
  onClose,
  onSave,
  isUploading = false,
  currentImage,
}: ProfilePictureModalProps) => {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

  useEffect(() => {
    if (!isOpen || !currentImage) {
      setCrop({ x: 0, y: 0 });
      set<PERSON><PERSON>(1);
      setCroppedAreaPixels(null);
    }
  }, [isOpen, currentImage]);

  const onCropComplete = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const createCroppedImage = async () => {
    if (!currentImage || !croppedAreaPixels) return;

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const imageObj = new Image();

    imageObj.src = currentImage;
    await new Promise((resolve) => {
      imageObj.onload = resolve;
    });

    // Set canvas size to match cropped dimensions
    canvas.width = croppedAreaPixels.width;
    canvas.height = croppedAreaPixels.height;

    // Draw the cropped image
    ctx?.drawImage(
      imageObj,
      croppedAreaPixels.x,
      croppedAreaPixels.y,
      croppedAreaPixels.width,
      croppedAreaPixels.height,
      0,
      0,
      croppedAreaPixels.width,
      croppedAreaPixels.height
    );

    // Convert to base64
    const croppedImage = canvas.toDataURL("image/jpeg");
    onSave(croppedImage);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="2xl"
      classNames={{
        base: "max-h-[90vh]",
        backdrop: "z-[1300]",
        wrapper: "z-[1301]",
      }}
    >
      <ModalContent>
        <ModalHeader>Choose profile picture</ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            <div className="relative mx-auto w-[80%] h-[300px] flex flex-col gap-4">
              <div className="relative flex-1">
                {currentImage && (
                  <Cropper
                    image={currentImage}
                    crop={crop}
                    zoom={zoom}
                    aspect={1}
                    onCropChange={setCrop}
                    onZoomChange={setZoom}
                    onCropComplete={onCropComplete}
                    cropShape="round"
                    showGrid={false}
                  />
                )}
              </div>
              <div className="px-4">
                <div className="flex items-center gap-2">
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onPress={() => setZoom(Math.max(1, zoom - 0.1))}
                    className="min-w-unit-8 text-lg"
                  >
                    -
                  </Button>
                  <Slider
                    aria-label="Zoom"
                    size="sm"
                    step={0.1}
                    minValue={1}
                    maxValue={3}
                    value={zoom}
                    onChange={(value) => setZoom(value as number)}
                    className="max-w-full"
                    classNames={{
                      base: "gap-2",
                      track: "bg-default-100",
                      filler: "bg-primary",
                      thumb: "bg-primary shadow-none border-none",
                      label: "text-default-500",
                      value: "text-default-500",
                    }}
                  />
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onPress={() => setZoom(Math.min(3, zoom + 0.1))}
                    className="min-w-unit-8 text-lg"
                  >
                    +
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex gap-2 justify-end w-full">
            <Button
              className="border-1 font-semibold"
              size="sm"
              variant="light"
              onPress={onClose}
              isDisabled={isUploading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              size="sm"
              onPress={createCroppedImage}
              isDisabled={!currentImage || isUploading}
              isLoading={isUploading}
            >
              Save
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ProfilePictureModal;
