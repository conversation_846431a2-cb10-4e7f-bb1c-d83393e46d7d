import {
  Control,
  FieldErrors,
  UseFormWatch,
  UseFormSetValue,
  UseFormClearErrors,
} from "react-hook-form";
import FormControlledTeamClubInput from "@/components/VirtualisedSelect/FormControlledTeamClubInput";
import { TeamClubOption } from "@/components/VirtualisedSelect/TeamClubInput";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { MODAL_TYPES } from "@/app/events/constants";
import { useTeamListQuery } from "@/lib/redux/slices/organization/api";
import { X } from "lucide-react";
import { FormField } from "../types";
import { useCallback } from "react";

interface TeamClubNameFieldProps {
  field: FormField;
  control: Control<any>;
  watch: UseFormWatch<any>;
  setValue: UseFormSetValue<any>;
  errors: FieldErrors<any>;
  toggleModal: (modal: string) => void;
  labelClassName?: string;
  onTeamCreated?: (teamName: string) => void;
  clearErrors: UseFormClearErrors<any>;
}

export const TeamClubNameField = ({
  field,
  control,
  watch,
  setValue,
  errors,
  toggleModal,
  labelClassName = "text-sm font-medium text-gray-700",
  onTeamCreated,
  clearErrors,
}: TeamClubNameFieldProps) => {
  const { data: teamsData, isLoading: teamsLoading } = useTeamListQuery({});

  const handleCreateTeamAccount = () => {
    if (window) {
      (window as any).__teamCreatedCallback = handleTeamCreated;
    }
    toggleModal(MODAL_TYPES.CREATE_TEAM_ACCOUNT);
    toggleModal(MODAL_TYPES.TICKET_FORM);
  };

  const handleClearSelection = () => {
    setValue(field.id, "");
  };

  const handleNoTeamChange = (checked: boolean) => {
    setValue(`${field.id}_no_team`, checked);
    if (checked) {
      // Clear team selection when "No Team" is checked
      setValue(field.id, null);
      clearErrors(field.id);
    }
  };

  const handleTeamCreated = useCallback(
    (teamName: string) => {
      setValue(field.id, teamName);
      clearErrors(field.id);
      if (onTeamCreated) {
        onTeamCreated(teamName);
      }
    },
    [field.id, setValue, clearErrors, onTeamCreated]
  );

  const transformTeamsToOptions = useCallback(
    (teams: any[], inputLength: number = 0): TeamClubOption[] => {
      if (!teams || !Array.isArray(teams)) return [];
      try {
        let filteredTeams = teams;

        // Filter out non-verified teams if input is less than 4 characters
        if (inputLength < 4) {
          filteredTeams = teams.filter((team) => team.isVerified);
        }

        return filteredTeams
          .map((team) => {
            const teamName =
              team?.name && team?.name?.trim() !== ""
                ? team?.name
                : team?.owner?.name || "Unnamed Team";

            const isVerified = team.isVerified;

            return {
              label: teamName,
              value: teamName,
              isVerified: isVerified,
            };
          })
          .filter((option) => option?.label && option?.label?.trim() !== "")
          .sort((a, b) => {
            // Prioritize verified teams first
            if (a?.isVerified && !b?.isVerified) return -1;
            if (!a?.isVerified && b?.isVerified) return 1;
            // Then sort alphabetically
            return a?.label?.localeCompare(b?.label);
          });
      } catch (error) {
        console.error("Error transforming teams to options:", error);
        return [];
      }
    },
    []
  );

  const defaultTeamOptions = teamsData?.results
    ? transformTeamsToOptions(teamsData.results, 0)
    : [];

  const selectedTeamValue = watch(field.id);
  const noTeamValue = watch(`${field.id}_no_team`);

  const selectedTeam = teamsData?.results?.find((team) => {
    const teamName =
      team.name && team.name.trim() !== ""
        ? team.name
        : team.owner?.name || "Unnamed Team";
    return teamName === selectedTeamValue;
  });

  const isCustomTeamName = selectedTeamValue && !selectedTeam;

  const loadTeamOptions = useCallback(
    (inputValue: string, callback: (options: TeamClubOption[]) => void) => {
      if (teamsData?.results) {
        const options = transformTeamsToOptions(
          teamsData.results,
          inputValue.length
        );

        if (!inputValue || inputValue.trim() === "") {
          callback(options);
          return;
        }

        const filteredOptions = options.filter((option) => {
          const lowerLabel = option.label.toLowerCase();
          const lowerInput = inputValue.toLowerCase();

          // For 2 characters or less, only show options that start with those characters
          if (inputValue.length <= 2) {
            return lowerLabel.startsWith(lowerInput);
          }

          // For more than 2 characters, show options that include the characters anywhere
          return lowerLabel.includes(lowerInput);
        });

        callback(filteredOptions);
      } else {
        callback([]);
      }
    },
    [teamsData, transformTeamsToOptions]
  );

  const renderSelectedTeamUI = () => {
    if (selectedTeam) {
      const teamName =
        selectedTeam.name && selectedTeam.name.trim() !== ""
          ? selectedTeam.name
          : selectedTeam.owner?.name || "Unknown Team";

      return (
        <div className="w-fit flex items-center gap-3 px-3 py-2 bg-[#D3D3D34D] rounded-lg">
          <img
            src={selectedTeam.owner?.avatar || "/default-avatar.png"}
            alt={`${teamName} avatar`}
            className="w-[30px] h-[30px] rounded-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/default-avatar.png";
            }}
          />
          <div className="flex-1">
            <p className="text-[14px] font-medium text-[#3A3A3A]">{teamName}</p>
          </div>
          <div
            onClick={handleClearSelection}
            className="cursor-pointer hover:bg-gray-200 rounded-full p-1 transition-colors"
          >
            <X className="w-4 h-4 text-[#8D8D8D] hover:text-gray-700" />
          </div>
        </div>
      );
    }

    if (isCustomTeamName) {
      // For custom team names, no separate UI is needed; the value will appear in the select input itself.
      return null;
    }

    return (
      <Button
        size="sm"
        className="rounded-lg"
        onClick={handleCreateTeamAccount}
      >
        Create team account
      </Button>
    );
  };

  return (
    <div className="grid md:grid-cols-2 gap-4">
      <div className="w-full space-y-3">
        <FormControlledTeamClubInput
          key={field.id}
          control={control}
          name={field.id}
          label={field.label}
          isRequired={field.required && !noTeamValue}
          loadOptions={loadTeamOptions}
          defaultOptions={defaultTeamOptions}
          isLoading={teamsLoading}
          placeholder=""
          className="w-full"
          errors={errors}
          isDisabled={noTeamValue}
        />

        {/* No Team Checkbox */}
        <div className="flex items-center justify-end space-x-2">
          <Checkbox
            id={`${field.id}_no_team`}
            checked={noTeamValue || false}
            onCheckedChange={handleNoTeamChange}
          />
          <label
            htmlFor={`${field.id}_no_team`}
            className="text-sm font-medium text-gray-700 cursor-pointer"
          >
            No team
          </label>
        </div>

        {!noTeamValue && renderSelectedTeamUI()}
      </div>
    </div>
  );
};
