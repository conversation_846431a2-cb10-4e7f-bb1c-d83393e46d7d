import React, { useEffect, useState } from "react";
import { Control, UseFormWatch, UseFormSetValue } from "react-hook-form";
import FormControlledSelect from "@/components/Select/FormControlledSelect";
import FormControlledInput from "@/components/Input/FormControlledInput";
import FormControlledBaseVirtualisedSelect from "@/components/VirtualisedSelect/FormControlledVirtualisedSelect";
import FormControlledRichTextInput from "@/components/Input/FormControlledRichTextInput";
import FormControlledImageWithAnalysis from "@/components/ImageSelector/FormControlledImageWithAnalysis";
import { ALLOWED_IMAGE_FILE_SIZE } from "@/features/VehicleAddOrSelect/constants";
import { PiWarningCircle } from "react-icons/pi";
import { NestedErrors } from "../types";
import { Option } from "@/components/VirtualisedSelect/BaseVirtualisedSelect";

type VehicleFormSectionProps = {
  fieldId: string;
  control: Control<any>;
  watch: UseFormWatch<any>;
  errors: any;
  vehicleTypes: Option[];
  makeOptions: Option[];
  makesLoading: boolean;
  loadMakeOptions: (
    inputValue: string,
    callback: (options: Option[]) => void
  ) => void;
  handleMakeChange: (
    option: Option | null,
    onChange: (value: string) => void
  ) => void;
  ticketId?: string;
  setValue: UseFormSetValue<any>;
  setIsFormLoading: (isFormLoading: boolean) => void;
};

export const VehicleFormSection: React.FC<VehicleFormSectionProps> = ({
  fieldId,
  control,
  watch,
  errors,
  vehicleTypes,
  makeOptions,
  makesLoading,
  loadMakeOptions,
  handleMakeChange,
  ticketId,
  setValue,
  setIsFormLoading,
}) => {
  const vehicleTypeValue = watch(`vehicle.vehicleType`);
  const isVehicleTypeSelected = Boolean(vehicleTypeValue);
  
  const [firstImageUploaded, setFirstImageUploaded] = useState<string | null>(null);
  const [analysisCompleted, setAnalysisCompleted] = useState(false);

  const handleImageSelection = (imageName: string) => {
    if (!firstImageUploaded && !analysisCompleted) {
      setFirstImageUploaded(imageName);
    }
  };

  // Handle vehicle analysis completion
  const handleVehicleAnalysisComplete = (data: { make?: string; model?: string; year?: string; }) => {
    setAnalysisCompleted(true);
  };

  // Create vehicle analysis options
  const vehicleAnalysisOptions = {
    vehicleTypes,
    makeOptions,
  };

  return (
    <div className="space-y-4">
       <div
        className={
          errors?.[fieldId] &&
          (errors[fieldId] as unknown as NestedErrors)?.vehicle?.vehicleImages
            ?.root?.message
            ? "border-1 rounded-md border-dashed border-red-400 p-2"
            : ""
        }
      >
        <div className="grid md:grid-cols-2 gap-4">
          <FormControlledImageWithAnalysis
            name={`vehicle.vehicleImages.0.image`}
            control={control}
            label="Image 1"
            className="mb-4"
            fileSize={ALLOWED_IMAGE_FILE_SIZE}
            isRequired={true}
            ticketId={ticketId}
            enableCrop={true}
            aspectRatio={1}
            cropShape="rect"
            setIsFormLoading={setIsFormLoading}
            enableVehicleAnalysis={firstImageUploaded === "image1" || (!firstImageUploaded && !analysisCompleted)}
            setValue={setValue}
            onImageSelect={() => handleImageSelection("image1")}
            onVehicleAnalysisComplete={handleVehicleAnalysisComplete}
            vehicleAnalysisOptions={vehicleAnalysisOptions}
            accept={{
              "image/*": [".jpeg", ".jpg", ".png", ".webp", ".heic", ".heif"],
            }}
          />
          <FormControlledImageWithAnalysis
            name={`vehicle.vehicleImages.1.image`}
            control={control}
            label="Image 2"
            className="mb-4"
            fileSize={ALLOWED_IMAGE_FILE_SIZE}
            isRequired={true}
            ticketId={ticketId}
            enableCrop={true}
            aspectRatio={1}
            cropShape="rect"
            setIsFormLoading={setIsFormLoading}
            enableVehicleAnalysis={firstImageUploaded === "image2" || (!firstImageUploaded && !analysisCompleted)}
            setValue={setValue}
            onImageSelect={() => handleImageSelection("image2")}
            onVehicleAnalysisComplete={handleVehicleAnalysisComplete}
            vehicleAnalysisOptions={vehicleAnalysisOptions}
            accept={{
              "image/*": [".jpeg", ".jpg", ".png", ".webp", ".heic", ".heif"],
            }}
          />
        </div>
        {errors?.[fieldId] &&
          (errors[fieldId] as unknown as NestedErrors)?.vehicle?.vehicleImages
            ?.root?.message && (
            <div className="flex gap-x-2 items-center">
              <div className="text-red-500">
                <PiWarningCircle />
              </div>
              <p className="font-medium text-sm text-red-500">
                {
                  (errors[fieldId] as unknown as NestedErrors)?.vehicle
                    ?.vehicleImages?.root?.message
                }
              </p>
            </div>
          )}
      </div>
      <div className="grid md:grid-cols-2 gap-4">
        <div className="flex flex-col gap-3">
          <FormControlledSelect
            control={control}
            name={`vehicle.vehicleType`}
            label="Vehicle type"
            isRequired={true}
            options={vehicleTypes}
          />
          <FormControlledInput
            control={control}
            name={`vehicle.year`}
            label="Year"
            isRequired={true}
          />
          <FormControlledBaseVirtualisedSelect
            name={`vehicle.make`}
            control={control}
            label="Make"
            loadOptions={loadMakeOptions}
            defaultOptions={makeOptions}
            className="max-w-full bg-transparent rounded-md text-black"
            isRequired={true}
            variant="bordered"
            isLoading={makesLoading}
            errors={errors}
            isDisabled={!isVehicleTypeSelected}
            helperText={
              !isVehicleTypeSelected ? "Please select a vehicle type first" : ""
            }
            onChange={(option: Option | null) => {
              const fieldName = `vehicle.make`;
              setValue(fieldName, option?.value || "", {
                shouldValidate: true,
              });
              setValue(`vehicle.customMakeName`, option?.label || "", {
                shouldValidate: true,
              });
            }}
          />
          <FormControlledInput
            control={control}
            name={`vehicle.customMakeName`}
            label="Custom Make Name"
            isRequired={false}
            className="hidden"
          />
          <FormControlledInput
            control={control}
            name={`vehicle.modelName`}
            label="Model"
            isRequired={true}
            isDisabled={!isVehicleTypeSelected}
            helperText={
              !isVehicleTypeSelected ? "Please select a vehicle type first" : ""
            }
          />
        </div>
      </div>
      <div className="mt-4 mb-6">
        <FormControlledRichTextInput
          control={control}
          name={`vehicle.modificationText`}
          label="Modifications"
        />
      </div>
    </div>
  );
};
