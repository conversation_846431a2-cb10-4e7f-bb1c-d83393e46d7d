import { useCallback } from "react";
import { UseFormReset } from "react-hook-form";
import { useDispatch } from "react-redux";
import { setSavedFormData } from "@/lib/redux/slices/events/eventSlice";
import toast from "react-hot-toast";

export const useFormNavigation = (
  formData: Record<number, any>,
  currentFormIndex: number,
  currentForm: any,
  selectedTicket: any,
  activeFormTicket: any,
  savedFormData: Record<string, any>,
  forms: any[],
  reset: UseFormReset<any>
) => {
  const dispatch = useDispatch();

  const handleNextForm = useCallback(
    async (currentFormData: any) => {
      try {
        if (!currentFormData) return currentFormIndex;

        // Transform the form data to match the expected structure
        const transformedData = currentForm.formData.reduce((acc: Record<string, any>, field: any) => {
          const fieldKey = field.type === "vehicle_info" ? (field.label || "vehicle") : field.label;
          const value = currentFormData[fieldKey];

          if (value === undefined) return acc;

          if (field.type === "vehicle_info" && value?.vehicle) {
            // Handle vehicle data
            acc[fieldKey] = {
              vehicle: {
                ...value.vehicle,
                vehicleImages: value.vehicle.vehicleImages.map((img: any) => ({
                  ...img,
                  type: "exterior",
                  tag: "side",
                })),
              },
            };
          } else if (field.type === "checkboxes" && Array.isArray(value)) {
            // Handle checkboxes
            acc[fieldKey] = value;
          } else if (["radio", "select", "text"].includes(field.type)) {
            // Handle other field types
            acc[fieldKey] = value;
          }

          return acc;
        }, {});

        // Update form data with transformed data
        const updatedFormData = {
          ...formData,
          [currentFormIndex]: {
            formId: currentForm.id,
            formType: currentForm.ticketType === "vendor_booth" ? "Vendor" : "Vehicle",
            formName: currentForm.name,
            templateId: currentForm.templateId,
            data: transformedData,
          },
        };

        // Save to Redux store
        const ticketKey = `ticket_${selectedTicket?.id || activeFormTicket?.id}`;
        dispatch(
          setSavedFormData({
            ...savedFormData,
            [ticketKey]: updatedFormData,
          })
        );

        // Prepare next form data
        const nextIndex = currentFormIndex + 1;
        const nextForm = forms[nextIndex];
        if (!nextForm) return currentFormIndex;

        // Reset form with next form's saved data or default values
        const nextFormSavedData = formData[nextIndex]?.data || {};
        const nextFormDefaultData = nextForm.formData.reduce((acc: Record<string, any>, field: any) => {
          const fieldKey = field.type === "vehicle_info" ? (field.label || "vehicle") : field.label;
          
          if (field.type === "vehicle_info") {
            acc[fieldKey] = nextFormSavedData[fieldKey] || {
              vehicle: {
                vehicleType: "",
                year: "",
                make: "",
                customMakeName: "",
                modelName: "",
                modificationText: "",
                vehicleImages: [
                  { image: null, imageUrl: "", id: "", type: "exterior", tag: "side" },
                  { image: null, imageUrl: "", id: "", type: "exterior", tag: "side" },
                ],
              },
            };
          } else if (field.type === "checkboxes") {
            acc[fieldKey] = nextFormSavedData[fieldKey] || [];
          } else {
            acc[fieldKey] = nextFormSavedData[fieldKey] || "";
          }
          
          return acc;
        }, {});

        reset(nextFormDefaultData);
        return nextIndex;
      } catch (error) {
        console.error("Error in handleNextForm:", error);
        toast.error("Error saving form data");
        return currentFormIndex;
      }
    },
    [
      currentForm,
      currentFormIndex,
      formData,
      forms,
      selectedTicket?.id,
      activeFormTicket?.id,
      savedFormData,
      dispatch,
      reset,
    ]
  );

  const handlePreviousForm = useCallback(() => {
    const previousIndex = Math.max(0, currentFormIndex - 1);
    const previousFormData = formData[previousIndex]?.data || {};
    reset(previousFormData);
    return previousIndex;
  }, [currentFormIndex, formData, reset]);

  return {
    handleNextForm,
    handlePreviousForm,
  };
}; 