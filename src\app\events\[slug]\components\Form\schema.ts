import { z } from "zod";

export const createCurrentFormSchema = (form: any, activeFormTicket?: any, currentFormIndex: number = 0) => {
  const schemaFields: Record<string, any> = {};
  let teamClubFieldId: string | null = null;

  // Add Event Flyer validation if flyerUrl is available
  if (activeFormTicket?.flyerUrl && currentFormIndex === 0) {
    schemaFields["Event Flyer"] = z.string().min(1, "Event Flyer is required");
  }

  form?.formData?.forEach((field: any) => {
    const fieldSchema = (() => {
      switch (field.type) {
        case "text":
          // Special handling for Team / Club name field
          if (field.label === "Team / Club name") {
            teamClubFieldId = field.id;
            return z.string().nullable();
          }
          return z.string().min(1, "This field is required");
        case "radio":
          return z.string().min(1, "This field is required").refine(val => val !== undefined && val !== null, {
            message: "This field is required"
          });
        case "select":
          return z.string().min(1, "This field is required");
        case "checkboxes":
          return z.array(z.string());
        case "vehicle_info":
          return z.object({
            vehicleType: z.string().min(1, "Vehicle type is required"),
            year: z.string()
              .min(1, "Year is required")
              .refine((val) => {
                const year = parseInt(val);
                const currentYear = new Date().getFullYear();
                return !isNaN(year) && year >= 1900 && year <= currentYear + 1;
              }, "Please enter a valid year between 1900 and next year"),
            make: z.string().min(1, "Make is required"),
            customMakeName: z.string().optional(),
            modelName: z.string().min(1, "Model is required"),
            modificationText: z.string().optional(),
            vehicleImages: z
              .array(
                z.object({
                  image: z.string({
                    required_error: "Image is required",
                    invalid_type_error: "Image is required",
                  }).min(1, "Image cannot be empty"),
                })
              )
              .length(2, "Two images are required")
          });
        case "file_upload":
          if(field.label === "Profile image for the company"){
            return z.object({
              image: z.string({
                required_error: "Image is required",
                invalid_type_error: "Image is required",
              }).min(1, "Image cannot be empty"),
              description: z.string().optional().nullable(),
            });
          }
          return z.array(z.string({
            required_error: "Image is required",
            invalid_type_error: "Image is required",
          }).min(1, "Image cannot be empty"));
        default:
          return z.string();
      }
    })();

    // Handle required/optional fields
    const key = field.type === "vehicle_info" ? "vehicle" : field.id;
    schemaFields[key] = field.required ? fieldSchema : fieldSchema.optional();
    
    // Add no team checkbox field for Team / Club name
    if (field.label === "Team / Club name") {
      schemaFields[`${field.id}_no_team`] = z.boolean().optional();
    }
  });

  const baseSchema = z.object(schemaFields);

  // Add custom validation for team/club field
  if (teamClubFieldId) {
    return baseSchema.superRefine((data, ctx) => {
      const fieldId = teamClubFieldId!; // We know it's not null here
      const teamValue = data[fieldId];
      const noTeamValue = data[`${fieldId}_no_team`];
      
      // If neither team is selected nor "no team" is checked, show error
      if (!noTeamValue && (!teamValue || teamValue.trim() === "")) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select a team or check 'No Team'",
          path: [fieldId],
        });
      }
    });
  }

  return baseSchema;
};
