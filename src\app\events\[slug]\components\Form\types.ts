import { z } from "zod";
import { createCurrentFormSchema } from "./schema";

export type VehicleImageType = {
  image: File | null;
  imageUrl?: string;
  id?: string;
  type?: string;
  tag?: string;
};

export type FileSettingsType = {
  maxFiles?: number;
  fileTypes?: string[];
  allowSpecificTypes?: boolean;
};

export type FormField = {
  id: string;
  type: string;
  label: string;
  required: boolean;
  options?: Array<{ label: string; value: string }>;
  isEditable?: boolean;
  isDeletable?: boolean;
  fileSettings?: FileSettingsType;
};

export type FormData = {
  formId: string;
  formType: string;
  formName: string;
  templateId: string;
  data: Record<string, any>;
};

export type NestedErrors = {
  vehicle?: {
    vehicleImages?: {
      root?: {
        message?: string;
      };
    };
  };
};

export type TicketFormProps = {
  open: boolean;
  onClose: () => void;
  isEdit?: boolean;
  ticketFormData?: any;
  eventId?: string;
  ticketId?: string;
  toggleModal?: (modal: string) => void;
  onTeamCreated?: (teamName: string) => void;
  isWhitelabel?: boolean;
  slug?: string;
};

export type CurrentFormSchema = z.infer<ReturnType<typeof createCurrentFormSchema>>;

export type FormFieldMap = {
  labels: Record<string, string>;
  types: Record<string, string>;
}; 