"use client";

import { getErrorMessage } from "@/lib/utils/errorUtils";
import { Button } from "@nextui-org/react";
import { toast } from "react-hot-toast";
import { useCallback, useState } from "react";
import {
  useBookFreeToAttendEventMutation,
  useCancelFreeToAttendEventMutation,
  useGetBookFreeToAttendEventQuery,
} from "@/lib/redux/slices/cart/cartApi";
import { useMetaPixel } from "@/lib/hooks/useMetaPixel";
import { useThirdPartyPixelTracking } from "@/lib/hooks/useThirdPartyPixelTracking";
import { TiTick } from "react-icons/ti";
import { useSessionData } from "@/lib/hooks/useSession";
import { AUTH_STATUS } from "@/lib/utils/constants";
import { PriceRenderer } from "./PriceRenderer";
import Link from "next/link";

interface FreeEventTicketOptionsProps {
  eventId: string;
  pixelId?: string;
  eventTitle: string;
  ticketName: string;
  customTicketTemplate?: any;
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

export function FreeEventTicketOptions({
  eventId,
  pixelId,
  eventTitle,
  ticketName,
  customTicketTemplate,
  pixels,
}: FreeEventTicketOptionsProps) {
  const { data: session, status } = useSessionData();
  const [isBooked, setIsBooked] = useState(false);
  const [bookFreeToAttendEvent] = useBookFreeToAttendEventMutation();
  const [cancelFreeToAttendEvent] = useCancelFreeToAttendEventMutation();
  
  // Use the new tracking hooks
  const { trackFreeEventTicketSelect: trackMetaFreeTicketSelect, trackFreeEventTicketDeselect: trackMetaFreeTicketDeselect } = useMetaPixel();
  const { trackFreeEventTicketSelect: trackThirdPartyFreeTicketSelect, trackFreeEventTicketDeselect: trackThirdPartyFreeTicketDeselect } = useThirdPartyPixelTracking();
  
  const {
    data: BookingStatus,
    isLoading,
    refetch,
  } = useGetBookFreeToAttendEventQuery(
    {
      eventId,
    },
    { skip: !session?.user }
  );
  const isAuthenticated: boolean = status === AUTH_STATUS.AUTHENTICATED;
  
  const handleFreeEventTickets = useCallback(async () => {
    try {
      const response = await bookFreeToAttendEvent({
        eventId,
      }).unwrap();
      if (response) setIsBooked(true);
      
      // Prepare event and ticket data for tracking
      const eventData = {
        id: eventId,
        title: eventTitle,
        date: new Date().toISOString(),
        pixels: pixels || (pixelId ? { meta: pixelId } : undefined),
      };
      
      const ticketData = {
        id: response?.id,
        eventId: eventId,
        type: "free" as const,
        name: ticketName,
        price: 0,
        quantity: 1,
      };

      // Track with Meta pixel (backward compatibility with pixelId)
      if (pixelId || pixels?.meta) {
        trackMetaFreeTicketSelect(eventData, ticketData);
      }

      // Track with third-party pixels
      const hasThirdPartyPixels = pixels?.ga4 || pixels?.snap || pixels?.tiktok;
      if (hasThirdPartyPixels) {
        trackThirdPartyFreeTicketSelect(eventData, ticketData);
      }
    } catch (err) {
      console.error("Error booking free event:", err);
      const errMessage = getErrorMessage(err);
      toast.error(errMessage);
    }
  }, [eventId, pixelId, pixels, eventTitle, ticketName, trackMetaFreeTicketSelect, trackThirdPartyFreeTicketSelect]);

  const handleCancel = useCallback(async () => {
    try {
      const response = await cancelFreeToAttendEvent({
        eventId,
      }).unwrap();
      if (response) {
        setIsBooked(false);
        refetch();
      }
      
      // Prepare event and ticket data for tracking
      const eventData = {
        id: eventId,
        title: eventTitle,
        date: new Date().toISOString(),
        pixels: pixels || (pixelId ? { meta: pixelId } : undefined),
      };
      
      const ticketData = {
        id: response?.id,
        eventId: eventId,
        type: "free" as const,
        name: ticketName,
        price: 0,
        quantity: 1,
      };

      // Track with Meta pixel (backward compatibility with pixelId)
      if (pixelId || pixels?.meta) {
        trackMetaFreeTicketDeselect(eventData, ticketData);
      }

      // Track with third-party pixels
      const hasThirdPartyPixels = pixels?.ga4 || pixels?.snap || pixels?.tiktok;
      if (hasThirdPartyPixels) {
        trackThirdPartyFreeTicketDeselect(eventData, ticketData);
      }
    } catch (error) {
      console.error("Error canceling free event:", error);
      const errMessage = getErrorMessage(error);
      toast.error(errMessage);
    }
  }, [eventId, pixelId, pixels, eventTitle, ticketName, trackMetaFreeTicketDeselect, trackThirdPartyFreeTicketDeselect]);

  if (isBooked || BookingStatus?.isAttending) {
    return (
      <div className="flex w-full justify-end">
        <Button
          color="default"
          className="text-[16px] px-[35px] "
          size="sm"
          endContent={<TiTick />}
          onClick={handleCancel}
        >
          Booked
        </Button>
      </div>
    );
  }

  const attendButton = (
    <div className="flex w-full justify-end gap-x-2">
      {customTicketTemplate?.showPriceChangeBadge && (
        <PriceRenderer customTicketTemplate={customTicketTemplate} />
      )}
      <Button
        color="primary"
        className="bg-[#007AFF] text-[14px] px-[32px]"
        size="sm"
        onClick={isAuthenticated ? handleFreeEventTickets : undefined}
        isLoading={isLoading}
      >
        I'm Attending
      </Button>
    </div>
  );

  return (
    <div className="flex w-full justify-end">
      {isAuthenticated ? (
        attendButton
      ) : (
        <Link href="/auth/signin">{attendButton}</Link>
      )}
    </div>
  );
}
