import React from "react";
import VehicleInfo from "./VehicleInfo";
import Image from "next/image";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteEventTicket,
  deleteSavedFormData,
} from "@/lib/redux/slices/events/eventSlice";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

interface SavedFormDataProps {
  ticketId: string;
}

interface VehicleData {
  vehicle: {
    vehicleType: string;
    year: string;
    make: string;
    customMakeName: string;
    modelName: string;
    modificationText?: string;
    vehicleImages?: Array<{
      image: string;
    }>;
  };
}

const SavedFormData: React.FC<SavedFormDataProps> = ({ ticketId }) => {
  const savedFormData = useSelector((state: any) => state.events.savedFormData);
  const dispatch = useDispatch();

  const getSavedData = () => {
    if (typeof window === "undefined") return null;
    const data = savedFormData[`ticket_${ticketId}`];
    return data ? data : null;
  };

  const savedData = getSavedData();

  const handleRemove = () => {
    dispatch(deleteSavedFormData(ticketId));
    dispatch(deleteEventTicket(ticketId));
  };

  if (!savedData) return null;

  const renderFormData = (formData: any, formIndex: string) => {
    const formType = formData?.formType;
    const formName = formData?.formName;

    if (formType === "vehicle_registration") {
      return (
        <div key={formIndex} className="mb-4">
          <VehicleInfo vehicleData={formData?.data} onRemove={handleRemove} />
        </div>
      );
    }

    // Handle Vendor form type
    if (formType === "vendor_booth") {
      const vendorName = formData.data["Vendor name"];
      const merchandise = formData.data["Merchandise you plan to sell"];
      const website = formData.data["Website"];

      return (
        <div
          key={formIndex}
          className="mb-4 bg-gray-50 p-2 rounded-lg last:mb-0 flex justify-between "
        >
          <div className="flex items-center gap-2">
            <Image src={IMAGE_LINKS.VENDOR} alt="Autolnk Vendor" width={85} height={85} />
            <div className="flex flex-col gap-2">
              <p className="font-medium text-sm mb-2">{vendorName}</p>
              <div className="flex flex-col gap-1">
                <p className="text-xs text-gray-600">{merchandise}</p>
                <p className="text-xs text-gray-600">{website}</p>
              </div>
            </div>
          </div>
          <button className="cursor-pointer h-10" onClick={handleRemove}>
            <Image src="/delete-icon.svg" alt="remove" width={10} height={10} />
          </button>
        </div>
      );
    }

    // Handle any other form type
    return (
      <div
        key={formIndex}
        className="mb-4 bg-gray-50 p-2 rounded-lg last:mb-0 flex justify-between items-center"
      >
        <div className="flex items-center gap-2">
          <Image src="/file.svg" alt="Form" width={20} height={20} />
          <p className="font-medium text-md">{formName}</p>
        </div>
        <button className="cursor-pointer" onClick={handleRemove}>
          <Image src="/delete-icon.svg" alt="remove" width={10} height={10} />
        </button>
      </div>
    );
  };

  return (
    <div className="mt-4">
      {Object.entries(savedData).map(([formIndex, formData]: [string, any]) =>
        renderFormData(formData, formIndex)
      )}
    </div>
  );
};

export default SavedFormData;
