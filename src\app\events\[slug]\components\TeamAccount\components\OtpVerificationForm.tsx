import React from "react";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";

interface OtpVerificationFormProps {
  email: string;
  otp: string;
  setOtp: (value: string) => void;
  canResendOtp: boolean;
  timeLeft: number;
  isFormLoading: boolean;
  onResendOtp: () => Promise<void>;
}

const OtpVerificationForm: React.FC<OtpVerificationFormProps> = ({
  email,
  otp,
  setOtp,
  canResendOtp,
  timeLeft,
  isFormLoading,
  onResendOtp,
}) => {
  return (
    <div className="flex flex-col items-center space-y-6">
      <div className="text-center">
        <h2 className="text-xl font-semibold text-[#303030] mb-2">
          Enter the confirmation code
        </h2>
        <p className="text-sm text-[#575757] mb-4">
          To confirm your account, enter the 6-digit code we sent via email to{" "}
          {email}
        </p>
      </div>

      {/* OTP Input */}
      <div className="flex flex-col items-center space-y-4">
        <InputOTP
          maxLength={6}
          pattern={REGEXP_ONLY_DIGITS}
          value={otp}
          onChange={(value) => setOtp(value)}
        >
          <InputOTPGroup>
            <InputOTPSlot index={0} />
            <InputOTPSlot index={1} />
            <InputOTPSlot index={2} />
            <InputOTPSlot index={3} />
            <InputOTPSlot index={4} />
            <InputOTPSlot index={5} />
          </InputOTPGroup>
        </InputOTP>

        {/* Resend OTP */}
        <div className="text-center">
          {canResendOtp ? (
            <button
              type="button"
              onClick={onResendOtp}
              className="text-sm text-blue-500 hover:text-blue-600 transition-colors"
              disabled={isFormLoading}
            >
              Resend code
            </button>
          ) : (
            <p className="text-xs text-gray-500">
              Resend code in {timeLeft} seconds
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default OtpVerificationForm;
