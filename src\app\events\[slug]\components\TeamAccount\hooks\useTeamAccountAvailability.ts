import { useState, useEffect } from "react";
import { useEmailCheckMutation } from "@/lib/redux/slices/auth/authApi";
import { useCheckUsernameMutation } from "@/lib/redux/slices/user/userApi";
import { isEmailValid, isUsernameFormatValid } from "@/lib/utils/string";

interface UseTeamAccountAvailabilityProps {
  email: string;
  username: string;
  emailError?: any;
  usernameError?: any;
}

export const useTeamAccountAvailability = ({
  email,
  username,
  emailError,
  usernameError,
}: UseTeamAccountAvailabilityProps) => {
  const [checkEmail, { isLoading: isCheckingEmail }] = useEmailCheckMutation();
  const [checkUsername, { isLoading: isCheckingUsername }] = useCheckUsernameMutation();

  const [isEmailAvailable, setIsEmailAvailable] = useState<boolean | null>(null);
  const [isUsernameAvailable, setIsUsernameAvailable] = useState<boolean | null>(null);

  useEffect(() => {
    const timer = setTimeout(async () => {
      const isValidEmailCheck = isEmailValid(email);
      if (email && !emailError && isValidEmailCheck) {
        try {
          const result = await checkEmail({ email }).unwrap();
          setIsEmailAvailable(!result?.existing);
        } catch (err) {
          console.error("Failed to check email:", err);
          setIsEmailAvailable(null);
        }
      } else {
        setIsEmailAvailable(null);
      }
    }, 600);

    return () => clearTimeout(timer);
  }, [email, checkEmail, emailError]);

  useEffect(() => {
    const timer = setTimeout(async () => {
      const isValidUsername = isUsernameFormatValid(username);
      if (username && !usernameError && isValidUsername && username.length >= 3) {
        try {
          const result = await checkUsername({ username }).unwrap();
          setIsUsernameAvailable(false);
        } catch (err) {
          console.error("Failed to check username:", err);
          setIsUsernameAvailable(true);
        }
      } else {
        setIsUsernameAvailable(null);
      }
    }, 600);

    return () => clearTimeout(timer);
  }, [username, checkUsername, usernameError]);

  return {
    isEmailAvailable,
    isUsernameAvailable,
    isCheckingEmail,
    isCheckingUsername,
  };
};
