import { useState } from "react";
import { 
  useGenerateOtpMutation, 
  useSignUpWithOtpMutation 
} from "@/lib/redux/slices/auth/authApi";

export interface TeamAccountFormData {
  teamName: string;
  username: string;
  email: string;
  password: string;
  profileImage: string;
}

export const useTeamAccountCreation = () => {
  const [generateOtp, { isLoading: isGeneratingOtp }] = useGenerateOtpMutation();
  const [signUpWithOtp] = useSignUpWithOtpMutation();
  
  const [isFormLoading, setIsFormLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateOtp = async (email: string) => {
    try {
      setError(null);
      setIsFormLoading(true);
      
      await generateOtp({ email }).unwrap();
      return { success: true };
    } catch (err: any) {
      console.error("Failed to generate OTP:", err);
      const errorMessage = err?.data?.message || "Failed to send verification code. Please try again.";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsFormLoading(false);
    }
  };

  const handleResendOtp = async (email: string) => {
    try {
      setError(null);
      await generateOtp({ email }).unwrap();
      return { success: true };
    } catch (err: any) {
      console.error("Failed to resend OTP:", err);
      const errorMessage = err?.data?.message || "Failed to resend verification code. Please try again.";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const createTeamAccount = async (formData: TeamAccountFormData, otp: string) => {
    try {
      setError(null);
      setIsFormLoading(true);
      
      const signUpFormData = new FormData();
      signUpFormData.append("email", formData.email);
      signUpFormData.append("password", formData.password);
      signUpFormData.append("name", formData.teamName);
      signUpFormData.append("first_name", formData.teamName.split(" ")[0] || formData.teamName);
      signUpFormData.append("last_name", formData.teamName.split(" ").slice(1).join(" ") || "");
      signUpFormData.append("username", formData.username);
      signUpFormData.append("code", otp);
      
      const signUpResponse = await signUpWithOtp(signUpFormData).unwrap();
      
      const accessToken = signUpResponse?.accessToken;
      
      if (!accessToken) {
        throw new Error("Failed to get access token from signup response");
      }

      const updateProfileResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}api/users/me/profile/`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          onboarding_step: {
            enter_name: true,
            enter_username: true,
            upload_profile_picture: true,
          },
          is_onboarded: true,
        }),
      });
      
      if (!updateProfileResponse.ok) {
        throw new Error(`Profile update failed: ${updateProfileResponse.statusText}`);
      }

      const updateUserResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}api/users/me/`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.teamName,
          username: formData.username,
          avatar: formData.profileImage,
          user_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        }),
      });
      
      if (!updateUserResponse.ok) {
        throw new Error(`User update failed: ${updateUserResponse.statusText}`);
      }

      const createTeamResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}api/teams/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.teamName,
        }),
      });
      
      if (!createTeamResponse.ok) {
        throw new Error(`Team creation failed: ${createTeamResponse.statusText}`);
      }

      const teamResponse = await createTeamResponse.json();

      return { success: true };
      
    } catch (error: any) {
      console.error("Error creating team account:", error);
      const errorMessage = error?.data?.message || error?.message || "Failed to create team account. Please try again.";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsFormLoading(false);
    }
  };

  return {
    handleGenerateOtp,
    handleResendOtp,
    createTeamAccount,
    isFormLoading,
    isGeneratingOtp,
    error,
    setError,
  };
};
 