import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { Input } from "@/components/ui/input";
import TicketQuantityOption from "./TicketQuantityOption";
import parse from "html-react-parser";
import { useVerifyTicketPasswordMutation } from "@/lib/redux/slices/tickets/ticketsApi";
import { debounce } from "lodash";
import { TICKET_PRICE_BADGE } from "../../constants";
import TicketMerch from "./TicketMerch";
import {
  addEventTicket,
  setSelectedVariantIds,
  deleteEventTicket,
} from "@/lib/redux/slices/events/eventSlice";
import SavedFormData from "./SavedFormData";
import { MODAL_TYPES } from "../../constants";
import { useModalState } from "./hooks/useModalState";
import { useTicketInCart } from "../hooks/useTicketInCart";
import dynamic from "next/dynamic";
import toast from "react-hot-toast";
import {
  useGetCartQuery,
  useRemoveVoucherMutation,
} from "@/lib/redux/slices/cart/cartApi";
import { useClearCartMutation } from "@/lib/redux/slices/cart/cartApi";
import clsx from "clsx";
import EarlyBirdPriceTimer from "./EarlyBirdPriceTimer";
import { calculateTimeRemaining } from "@/lib/utils/date";

const TicketModals = dynamic(() => import("./TicketModals"), { ssr: false });

export interface PriceSchedule {
  scheduledPrice: number;
  scheduledAt: string;
}

interface TicketProps {
  eventId: string;
  eventTitle: string;
  eventImg?: string;
  ticketQuantity: number;
  customTicketTemplate: {
    ticketPrice: string;
    name: string;
    description: string;
    showPriceChangeBadge?: boolean;
    priceSchedule?: PriceSchedule;
    priceChangeBadgeText?: string;
    priceChangeInfo?: {
      priceChangeBadgeText: string;
      oldPrice: string;
    };
    products?: any[];
  };
  ticketId: string;
  isBooked: boolean;
  setIsBooked: (isBooked: boolean) => void;
  bookingId: string | null;
  isVehicleRequired: boolean;
  isTicketApprovalRequired: boolean;
  isPasswordProtected: boolean;
  pixelId?: string;
  ticketName: string;
  org: any;
  handleAddFormDetails: () => Promise<void>;
  forms?: any[];
  isEventExpired: boolean;
  isOptionDisabled: boolean;
  isCurrentTimeBeforeAvailability: boolean;
  isCurrentTimeAfterAvailability: boolean;
  ticketAvailableFromFormatted: {
    month: string;
    day: string;
    suffix: string;
  };
  timezone: string;
  isWhitelabel: boolean;
}

const Ticket: React.FC<TicketProps> = ({
  eventId,
  eventTitle,
  eventImg,
  ticketQuantity,
  customTicketTemplate,
  ticketId,
  ticketName,
  isVehicleRequired,
  isTicketApprovalRequired,
  pixelId,
  isPasswordProtected,
  org,
  handleAddFormDetails,
  forms,
  isEventExpired,
  isOptionDisabled,
  isCurrentTimeBeforeAvailability,
  isCurrentTimeAfterAvailability,
  ticketAvailableFromFormatted,
  timezone,
  isWhitelabel,
}) => {
  const dispatch = useDispatch();
  const [isPasswordVerified, setIsPasswordVerified] = useState(
    !isPasswordProtected
  );
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { modalState, toggleModal } = useModalState();
  const [pendingAction, setPendingAction] = useState<
    "quantity" | "form" | null
  >(null);
  const [pendingQuantity, setPendingQuantity] = useState<number | null>(null);
  const { isTicketInCart, hasForm, otherTicketsExist } = useTicketInCart(
    ticketId,
    isTicketApprovalRequired
  );

  const [clearCart, { isLoading: isClearCartLoading }] = useClearCartMutation();
  const [removeVoucher, { isLoading: isRemoveVoucherLoading }] =
    useRemoveVoucherMutation();
  const { refetch: refetchCart } = useGetCartQuery();

  const [verifyTicketPassword, { isLoading, isError }] =
    useVerifyTicketPasswordMutation();
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const eventTickets = useSelector((state: any) => state.events.eventTickets);
  const savedFormData = useSelector((state: any) => state.events.savedFormData);
  const selectedVariantIds = useSelector(
    (state: any) => state.events.selectedVariantIds
  );

  const scheduledPrice =
    Number(customTicketTemplate?.priceSchedule?.scheduledPrice || 0) || 0;
  const basePrice = Number(customTicketTemplate?.ticketPrice || 0) || 0;

  const isVehicleRequiredCondition = useMemo(
    () => isVehicleRequired,
    [customTicketTemplate.ticketPrice, isVehicleRequired]
  );

  const isFormAddedInTicket = useCallback(() => {
    if (typeof window === "undefined") return false;
    const data = savedFormData[`ticket_${ticketId}`];
    if (!data) return false;
    // Check if we have any form data with actual values
    return Object.values(data).some(
      (form: any) => form.formId && Object.keys(form.data).length > 0
    );
  }, [savedFormData, ticketId]);

  const getSavedTicketFormData = useCallback(() => {
    if (typeof window === "undefined") return null;
    const data = savedFormData[`ticket_${ticketId}`];
    return data ? data : null;
  }, [savedFormData, ticketId]);

  const currentAddedTicketQuantity = useMemo(() => {
    const addedTicket = eventTickets?.find(
      (ticket: any) => ticket.ticket.ticketId === ticketId
    );

    return addedTicket ? addedTicket.ticket.quantity : 0;
  }, [eventTickets, ticketId]);

  // price increase timer
  const remainingTime = useMemo(() => {
    if (!customTicketTemplate?.priceSchedule?.scheduledAt) return null;
    return calculateTimeRemaining(
      customTicketTemplate?.priceSchedule?.scheduledAt,
      timezone
    );
  }, [customTicketTemplate?.priceSchedule?.scheduledAt, timezone]);

  const isPriceIncreaseWithin24Hours = useMemo(() => {
    if (!remainingTime) return false;
    return remainingTime?.isWithin24Hours;
  }, [remainingTime]);

  useEffect(() => {
    if (savedFormData[`ticket_${ticketId}`]) {
      handleAddTicket({
        quantity: 1,
        price: Number(customTicketTemplate.ticketPrice),
        formData: savedFormData[`ticket_${ticketId}`],
        hasFormData: true,
      });
    }
  }, [savedFormData]);

  const [latestVariantIds, setLatestVariantIds] = useState<string[]>([]);

  const handleVariantsSelected = (variantIds: number[]) => {
    if (variantIds?.length > 0) {
      const stringVariantIds = variantIds.map((id) => id.toString());
      setLatestVariantIds(stringVariantIds);
      dispatch(setSelectedVariantIds(stringVariantIds));
    } else {
      setLatestVariantIds([]);
      dispatch(setSelectedVariantIds([]));
    }
  };

  const handleAddTicket = useCallback(
    async ({
      quantity,
      price,
      formData,
      hasFormData,
    }: {
      quantity: number;
      price: number;
      formData: any;
      hasFormData: boolean;
    }) => {
      const payload = {
        title: eventTitle,
        img: eventImg,
        eventId,
        eventTitle,
        eventTotal: quantity * price,
        ticket: {
          ticketId,
          quantity,
          price,
          ticketType: customTicketTemplate?.name,
          variantIds: latestVariantIds,
        },
        total: quantity * price,
        formData: hasFormData ? formData : null,
      };

      dispatch(addEventTicket(payload));
    },
    [
      dispatch,
      eventId,
      eventTitle,
      eventImg,
      ticketId,
      customTicketTemplate,
      latestVariantIds,
    ]
  );

  const handleQuantityChange = useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      const quantity = Number(e.target.value);

      if (quantity === 0) {
        dispatch(deleteEventTicket(ticketId));
        return;
      }

      if (isTicketInCart && !isWhitelabel) {
        toast.custom((t) => (
          <div className="border border-yellow-500 bg-white p-2 rounded-md">
            You already have this ticket in the cart, you can update the
            quantity in the cart
          </div>
        ));
        return;
      }

      if (otherTicketsExist && !isWhitelabel) {
        setPendingAction("quantity");
        setPendingQuantity(quantity);
        toggleModal(MODAL_TYPES.TICKET_UPDATE);
        return;
      }

      const price = Number(customTicketTemplate.ticketPrice);
      const formData = getSavedTicketFormData();
      const hasFormData = isFormAddedInTicket();
      handleAddTicket({ quantity, price, formData, hasFormData });
    },
    [
      dispatch,
      ticketId,
      customTicketTemplate,
      getSavedTicketFormData,
      isFormAddedInTicket,
      handleAddTicket,
      isTicketInCart,
      otherTicketsExist,
      toggleModal,
    ]
  );

  const handleFormDetailsWrapper = useCallback(() => {
    if (isTicketInCart && !isWhitelabel) {
      toast.custom((t) => (
        <div className="border border-yellow-500 bg-white p-2 rounded-md">
          You already have this ticket in the cart
        </div>
      ));
      return;
    }

    if (otherTicketsExist && !isWhitelabel) {
      setPendingAction("form");
      toggleModal(MODAL_TYPES.TICKET_UPDATE);
      return;
    }

    handleAddFormDetails();
  }, [
    isTicketInCart,
    otherTicketsExist,
    hasForm,
    toggleModal,
    handleAddFormDetails,
  ]);

  const isQuantitySelectDisabled = useMemo(() => {
    if (isOptionDisabled) {
      return true;
    }

    if (!customTicketTemplate?.products?.length) {
      return false;
    }

    const isDisabled =
      selectedVariantIds?.length !== customTicketTemplate?.products?.length;

    return isDisabled;
  }, [customTicketTemplate?.products, selectedVariantIds, isOptionDisabled]);

  const handleInputChange = debounce(async (value: string) => {
    try {
      if (value.trim()) {
        const isVerified = await verifyTicketPassword({
          orgSlug: org?.slug,
          ticketId,
          body: { password: value },
        }).unwrap();
        setIsPasswordVerified(true);

        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(
          () => setIsPasswordVerified(false),
          5 * 60 * 1000
        ); // 5 minutes
      }
    } catch (err) {
      console.error(err);
    }
  }, 300);

  useEffect(() => {
    return () => {
      handleInputChange.cancel();
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [handleInputChange]);

  const [isContentClipped, setIsContentClipped] = useState(false);
  const descriptionRef = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    const element = descriptionRef?.current;
    if (element) {
      setIsContentClipped(element?.scrollHeight > element?.clientHeight);
    }
  }, [customTicketTemplate?.description]);

  const showPriceBadge = useMemo(
    () => customTicketTemplate?.showPriceChangeBadge || false,
    [customTicketTemplate]
  );

  const isQuantityAvailable = ticketQuantity > 0;

  const BadgeRenderer = useCallback(
    () => {
      if (customTicketTemplate?.priceChangeBadgeText) {
        return (
          <div className="w-fit px-3 py-1 tracking-wider text-nowrap rounded-full bg-[#CDFEE1] border border-[#CDFEE1] text-green-600 text-xs font-semibold">
            <p>{customTicketTemplate?.priceChangeBadgeText}</p>
          </div>
        );
      }
      return null;
    },
    [customTicketTemplate]
  );
  const handleConfirmUpdate = useCallback(async () => {
    await clearCart();
    await removeVoucher();
    await refetchCart();
    toggleModal(MODAL_TYPES.TICKET_UPDATE);

    if (pendingAction === "form") {
      handleAddFormDetails();
    } else if (pendingAction === "quantity" && pendingQuantity !== null) {
      const price = Number(customTicketTemplate.ticketPrice);
      const formData = getSavedTicketFormData();
      const hasFormData = isFormAddedInTicket();
      handleAddTicket({
        quantity: pendingQuantity,
        price,
        formData,
        hasFormData,
      });
    }
    setPendingAction(null);
    setPendingQuantity(null);
  }, [
    toggleModal,
    handleAddFormDetails,
    pendingAction,
    pendingQuantity,
    customTicketTemplate,
    getSavedTicketFormData,
    isFormAddedInTicket,
    handleAddTicket,
  ]);

  const handleCancelUpdate = useCallback(() => {
    toggleModal(MODAL_TYPES.TICKET_UPDATE);
    setPendingAction(null);
    setPendingQuantity(null);
  }, [toggleModal]);

  return (
    <div className="px-4">
      <div className="">
        <div className="mb-2">
          <div className="mb-2">
            <h1 className="text-lg font-medium text-[#000000]">
              {customTicketTemplate?.name}
              {showPriceBadge && (
                <span className="inline-block ml-2 align-middle">
                  <BadgeRenderer />
                </span>
              )}
            </h1>
          </div>
          <div className="relative mb-6 lg:mb-3">
            <div className="flex items-start justify-between">
              <p
                ref={descriptionRef}
                className={clsx(
                  "text-xs text-[#86868B] font-normal tracking-wider break-words flex-1",
                  !isDescriptionExpanded && "line-clamp-1 md:line-clamp-2"
                )}
              >
                {parse(customTicketTemplate?.description)}
              </p>
              {isContentClipped &&
                customTicketTemplate?.description &&
                !isDescriptionExpanded && (
                  <button
                    onClick={() => setIsDescriptionExpanded(true)}
                    className="text-xs font-medium ml-2 md:hidden whitespace-nowrap"
                  >
                    See description
                  </button>
                )}
            </div>
            {isContentClipped &&
              customTicketTemplate?.description &&
              isDescriptionExpanded && (
                <div className="flex flex-col items-end md:hidden mt-2">
                  <button
                    onClick={() => setIsDescriptionExpanded(false)}
                    className="text-xs font-medium"
                  >
                    Show less
                  </button>
                </div>
              )}
            {isContentClipped && customTicketTemplate?.description && (
              <div className="hidden md:flex md:flex-col md:items-end">
                <button
                  onClick={() =>
                    setIsDescriptionExpanded(!isDescriptionExpanded)
                  }
                  className="text-xs font-medium mt-2"
                >
                  {isDescriptionExpanded ? "Show less" : "See full description"}
                </button>
              </div>
            )}
          </div>
        </div>
        <div>
          <TicketMerch
            products={customTicketTemplate?.products || []}
            onVariantsSelected={handleVariantsSelected}
          />
        </div>
        {isCurrentTimeBeforeAvailability && (
          <p className="text-normal font-[400] text-[#DB382C] text-end">
            Tickets available: {ticketAvailableFromFormatted?.month},{" "}
            {ticketAvailableFromFormatted?.day}
            <sup className="text-xs">
              {ticketAvailableFromFormatted?.suffix}
            </sup>
          </p>
        )}
        {isCurrentTimeAfterAvailability && (
          <p className="text-normal font-[400] text-[#DB382C] text-end">
            Tickets are not available anymore
          </p>
        )}
        {isPasswordVerified &&
          !isCurrentTimeBeforeAvailability &&
          !isCurrentTimeAfterAvailability && (
            <>
              <div className="flex items-center justify-between">
                <TicketQuantityOption
                  isQuantityAvailable={isQuantityAvailable && !isEventExpired}
                  customTicketTemplate={customTicketTemplate}
                  ticketQuantity={ticketQuantity}
                  defaultQuantity={currentAddedTicketQuantity}
                  handleSelectionChange={handleQuantityChange}
                  isVehicleRequired={isVehicleRequired}
                  isVehicleAdded={false}
                  pixelId={pixelId}
                  isPasswordVerified={isPasswordVerified}
                  isVehicleRequiredCondition={isVehicleRequiredCondition}
                  vehicleData={null}
                  handleAddFormDetails={handleFormDetailsWrapper}
                  eventId={eventId}
                  eventTitle={eventTitle}
                  ticketName={ticketName}
                  isOptionDisabled={isQuantitySelectDisabled}
                  forms={forms}
                  isFormAddedInTicket={isFormAddedInTicket()}
                  ticketId={ticketId}
                />
              </div>
              {customTicketTemplate?.priceSchedule && (
                <EarlyBirdPriceTimer
                  priceSchedule={customTicketTemplate?.priceSchedule}
                  timezone={timezone}
                />
              )}
            </>
          )}
      </div>
      {!isPasswordVerified &&
        !isCurrentTimeBeforeAvailability &&
        !isCurrentTimeAfterAvailability && (
          <>
            <div className="flex flex-col md:justify-center md:items-end mt-4">
              <div className=" md:w-1/2">
                <Input
                  type="password"
                  placeholder="Password"
                  className="bg-white focus-visible:ring-0 border-2 border-lg"
                  name={`ticket-password-${ticketId}`}
                  autoComplete="off"
                  data-lpignore="true"
                  onChange={(e) => handleInputChange(e.target.value)}
                />
                {isLoading && (
                  <p className="text-sm font-medium text-orange-500">
                    Verifying password...
                  </p>
                )}
                {isError && (
                  <p className="text-sm font-medium text-red-500">
                    Incorrect password. Please contact the admin.
                  </p>
                )}
              </div>
            </div>
          </>
        )}

      <SavedFormData ticketId={ticketId} />
      <TicketModals
        modalState={modalState}
        toggleModal={toggleModal}
        openAddVehicleOnGuest={() => {}}
        onConfirmUpdate={handleConfirmUpdate}
        onCancelUpdate={handleCancelUpdate}
        isLoading={isClearCartLoading || isRemoveVoucherLoading}
      />
    </div>
  );
};

export default React.memo(Ticket);
