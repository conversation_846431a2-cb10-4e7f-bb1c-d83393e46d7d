import React, { useState, useEffect } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import clsx from "clsx";

// Initialize dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);

interface CountdownProps {
  startDate: string;
  endDate: string;
  timezone: string;
  isCenteredLayout: boolean;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const TicketsAvailablityCountdown = ({
  startDate,
  endDate,
  timezone,
  isCenteredLayout,
}: CountdownProps) => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft | null>(null);
  const [isPreStart, setIsPreStart] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const validateDates = () => {
      try {
        const start = dayjs.utc(startDate).tz(timezone);
        const end = dayjs.utc(endDate).tz(timezone);

        if (!start.isValid()) throw new Error("Invalid start date");
        if (!end.isValid()) throw new Error("Invalid end date");
        if (end.isBefore(start))
          throw new Error("End date must be after start date");

        setError(null);
        return { start, end };
      } catch (err) {
        setError(err instanceof Error ? err.message : "Invalid date format");
        return null;
      }
    };

    const calculateTimeLeft = () => {
      const dates = validateDates();
      if (!dates) return null;

      const { start, end } = dates;
      const now = dayjs().tz(timezone);

      if (now.isBefore(start)) {
        setIsPreStart(true);
        const difference = start.diff(now);
        return calculateTimeParts(difference);
      }

      // If we're after start but before end, count down to end date
      setIsPreStart(false);
      if (now.isBefore(end)) {
        const difference = end.diff(now);
        return calculateTimeParts(difference);
      }

      return null;
    };

    const calculateTimeParts = (difference: number): TimeLeft => {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      return { days, hours, minutes, seconds };
    };

    // Initial calculation
    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [startDate, endDate, timezone]);

  if (!startDate && !endDate) {
    return null;
  }

  if (error) {
    return (
      <div className="bg-red-100 rounded-lg p-4 text-center text-xs">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (!timeLeft) {
    return null;
  }

  const calculateWidthClass = (value) =>
    `w-[${Math.max(value?.toString().length, 3)}ch]`;

  return (
    <div
      className={clsx(
        "bg-[#D9D9D9] rounded-lg px-5 py-1 ",
        isCenteredLayout
          ? "w-[55%] lg:w-[250px]"
          : "min-w-[55%] lg:min-w-[65%] 2xl:min-w-[50%]"
      )}
    >
      <div className="text-l xl:text-2xl text-red-500 flex justify-center space-x-2">
        <div className={`inline-block ${calculateWidthClass(timeLeft?.days)}`}>
          <span>{timeLeft?.days?.toString()?.padStart(2, "0")}d</span>
        </div>
        <div
          className={`inline-block ${
            Number(calculateWidthClass(timeLeft?.hours)) - 0.3
          }`}
        >
          <span>{timeLeft?.hours?.toString()?.padStart(2, "0")}h</span>
        </div>
        <div
          className={`inline-block  ${calculateWidthClass(timeLeft?.minutes)}`}
        >
          <span>{timeLeft?.minutes?.toString()?.padStart(2, "0")}m</span>
        </div>
        <div
          className={`inline-block ${calculateWidthClass(timeLeft?.seconds)}`}
        >
          <span>{timeLeft?.seconds?.toString()?.padStart(2, "0")}s</span>
        </div>
      </div>
      <div className="text-gray-400 text-center text-[10px] lg:text-xs">
        {isPreStart ? "Coming soon!" : "Remaining time to buy tickets"}
      </div>
    </div>
  );
};

export default TicketsAvailablityCountdown;
