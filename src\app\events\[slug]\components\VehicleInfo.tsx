import React from "react";
import Image from "next/image";
import VehicleImageDisplay from "@/components/VehicleImageDisplay";

interface VehicleInfoProps {
  vehicleData: {
    vehicle: {
      vehicleType: string;
      year: string;
      make: string;
      customMakeName: string;
      modelName: string;
      modificationText?: string;
      vehicleImages?: Array<{
        image: string;
      }>;
    };
    Instagram?: string;
    "Team / Club name"?: string;
  };
  onRemove: () => void;
  readOnly?: boolean;
}

const VehicleInfo: React.FC<VehicleInfoProps> = ({
  vehicleData,
  onRemove,
  readOnly = false,
}) => {
  const imageUrl = vehicleData?.vehicle?.vehicleImages?.[0]?.image;

  return (
    <div className="bg-gray-50 rounded-lg p-2 mb-6 mt-4 relative md:mt-2 flex gap-3">
      <div className="h-[50px] w-[50px] sm:h-[85px] sm:w-[85px]">
        <VehicleImageDisplay
          imageUrl={imageUrl}
          width={85}
          height={85}
          className="object-cover rounded-md"
        />
      </div>
      <div className="flex flex-col justify-between">
        <div className="mt-1">
          <p className="text-[15px] font-semibold">
            {vehicleData?.vehicle?.modelName}{" "}
            {vehicleData?.vehicle?.customMakeName}
          </p>
          <p className="text-[13px]">{vehicleData?.vehicle?.year}</p>
        </div>
        <div>
          {vehicleData?.["Team / Club name"] && (
            <p className="text-xs font-medium">
              Team / Club: {vehicleData?.["Team / Club name"]}
            </p>
          )}
        </div>
      </div>
      {!readOnly && (
        <button
          className="absolute top-4 right-4 cursor-pointer"
          onClick={onRemove}
        >
          <Image src="/delete-icon.svg" alt="remove" width={10} height={10} />
        </button>
      )}
    </div>
  );
};

export default React.memo(VehicleInfo);
