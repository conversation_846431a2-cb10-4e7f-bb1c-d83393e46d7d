import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { clearEventTickets, clearActiveFormTicket, clearSavedFormData, clearSelectedVariantIds } from "@/lib/redux/slices/events/eventSlice";
import { useAddToCartMutation, cartApi } from "@/lib/redux/slices/cart/cartApi";
import { CART_ERRORS, CART_STORAGE_KEYS } from "@/lib/utils/constants";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import { handleInvalidCartToken } from "@/app/cart/utils/cartUtils";
import { trackAddToCart } from "@/lib/utils/pixelTracking";
import { getName } from "@/lib/utils/string";

interface UseCartConfirmationProps {
  eventTickets: any[];
  eventId: string;
  title: string;
  date: string;
  pixels: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
  session: any;
  onSuccess?: () => void;
  waivers: any;
  isWhitelabel: boolean;
  slug: string;
}

export const useCartConfirmation = ({
  eventTickets,
  eventId,
  title,
  date,
  pixels,
  session,
  onSuccess,
  waivers,
  isWhitelabel,
  slug,
}: UseCartConfirmationProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [addToCart, { isLoading: isAddToCartLoading }] = useAddToCartMutation();

  const handleConfirm = async () => {
    if ((pixels?.meta || pixels?.ga4 || pixels?.snap || pixels?.tiktok) && eventTickets.length > 0) {
      const ticketsData = eventTickets.map((ticketItem: any) => ({
        id: ticketItem.ticket.ticketId,
        eventId: eventId,
        type: "paid" as const,
        name: ticketItem?.ticket?.ticketType,
        price: ticketItem?.ticket?.price,
        quantity: ticketItem?.ticket?.quantity,
      }));

      trackAddToCart(
        {
          id: eventId,
          title,
          date,
          pixels: {
            meta: pixels?.meta,
            ga4: pixels?.ga4,
            snap: pixels?.snap,
            tiktok: pixels?.tiktok,
          },
        },
        ticketsData as any,
        {
          id: session?.user?.id,
          email: session?.user?.email,
          name: getName(session?.user),
          isAnonymous: !session?.user,
        }
      );
    }

    try {
      const cartBody = eventTickets.map((item: any) => {
        const formData = item.formData ? JSON.parse(JSON.stringify(item.formData)) : null;
        const modifiedFormData = formData ? Object.values(formData).map((form: any) => {
          if (form.formType === "vehicle_registration" && form.data?.vehicle) {
            const vehicleData = keysToSnake({ ...form?.data?.vehicle });
            return {
              ...keysToSnake(form),
              data: {
                ...form.data,
                vehicle: vehicleData,
              },
            };
          }
          return { ...keysToSnake(form), data: form.data };
        }) : null;

        // Find all waivers associated with this ticket
        const associatedWaivers = waivers?.filter((waiver: any) => 
          waiver.waiverTickets.includes(item.ticket.ticketId)
        ) || [];

        return {
          type: "event_ticket",
          id: item.ticket.ticketId,
          quantity: item.ticket.quantity,
          ...(item?.ticket?.variantIds?.length > 0 && {
            selected_variant_ids: item?.ticket?.variantIds,
          }),
          ...(modifiedFormData && {
            form_response_data: modifiedFormData,
          }),
          ...(associatedWaivers.length > 0 && {
            waiver_response_data: associatedWaivers.map((waiver: any) => ({
              waiverUrl: waiver.waiverUrl,
              waiverSignature: waiver.waiverSignature || "",
            })),
          }),
        };
      });

      let response;
      let retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          response = await addToCart(cartBody);

          if (response?.error?.data?.error === CART_ERRORS.INVALID_CART_TOKEN || response?.error?.status === CART_ERRORS.PARSING_ERROR) {
            handleInvalidCartToken();
            return handleConfirm();
          }

          if (!session?.user) {
            const checkoutData = response?.data?.checkout as any;
            const token = checkoutData?.token;
            if (token) {
              try {
                localStorage.setItem(CART_STORAGE_KEYS.CART_TOKEN, token);
                if (isWhitelabel) {
                  await new Promise(resolve => setTimeout(resolve, 100));
                }
              } catch (error) {
                console.error("Error setting cart token:", error);
              }
            }
          }

          dispatch(clearEventTickets());
          dispatch(clearActiveFormTicket());
          dispatch(clearSavedFormData());
          dispatch(clearSelectedVariantIds());
          
          if (isWhitelabel) {
            dispatch(cartApi.util.invalidateTags(['ProductCart']));
            
            await new Promise(resolve => setTimeout(resolve, 200));
          }
          
          if (onSuccess) {
            onSuccess();
          } else {
            if (isWhitelabel) {
              router.push(`/event/${slug}?oid=contact`);
            } else {
              router.push("/cart");
            }
          }
          return;
        } catch (error) {
          retryCount++;
          if (retryCount > maxRetries) {
            console.error(`Failed to add to cart after ${maxRetries} retries:`, error);
            throw error;
          }
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
    } catch (error) {
      console.error("Error in handleConfirm:", error);
      toast.error("Failed to process your request. Please try again.");
    }
  };

  return { handleConfirm, isItemAdding: isAddToCartLoading };
}; 