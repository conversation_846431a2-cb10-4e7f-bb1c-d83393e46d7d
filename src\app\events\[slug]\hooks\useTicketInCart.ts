import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";

interface UseTicketInCartReturn {
  isTicketInCart: boolean;
  hasForm: boolean;
  otherTicketsExist: boolean;
  isLoading: boolean;
}

export const useTicketInCart = (
  ticketId: string,
  isTicketApprovalRequired: boolean
): UseTicketInCartReturn => {
  const { data: cartData, isLoading } = useGetCartQuery(undefined, {
    refetchOnFocus: true,
    refetchOnReconnect: true,
    refetchOnMountOrArgChange: true,
  });

  const isTicketInCart = cartData?.checkout?.lines?.some(
    (line) => line.eventTicket === ticketId
  ) || false;

  const hasForm = cartData?.checkout?.lines?.some(
    (line) => line.eventTicket === ticketId && line.formResponseData?.length > 0
  ) || false;

  const otherTicketsExist = Boolean(cartData?.checkout?.lines?.find((line) => {
    if (line.eventTicket === ticketId) return false;
    
    if (line.eventDetails?.ticketApprovalRequired) {
      if (isTicketApprovalRequired) return false;
      return true;
    }

    if (!line.eventDetails?.ticketApprovalRequired && isTicketApprovalRequired) return true;
    
    return false;
  }));

  return {
    isTicketInCart,
    hasForm,
    otherTicketsExist,
    isLoading,
  };
}; 