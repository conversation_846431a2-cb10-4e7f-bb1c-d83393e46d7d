import { <PERSON>ada<PERSON> } from "next";
import { validate as isUUID } from "uuid";
import React from "react";

import { getUserTimeZone } from "@/lib/utils/getUserTimeZone";
import { ClientRedirect } from "./components/ClientRedirect";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import ErrorBoundary from "@/components/ErrorBoundary";
import { createEventJsonLd } from "@/lib/seo-constants";
import { StoreSessions } from "@/features/Analytics/StoreSessions";
import EventPageTracker from "@/components/Tracker/EventPageTracker";

import EventDetails from "./components/EventDetails";
import { EventImageViewer } from "./components/EventImageViewer";
import EventOverview from "./components/EventOverview";
import { EventCoverImage } from "./components/EventCoverImage";
import TicketOptions from "./components/TicketOptions";
import MerchSection from "./components/MerchSection";
import ProfileGridSection from "./components/ProfileGridSection";
import FeaturedList from "./components/FeaturedList";

type Props = {
  params: {
    slug: string;
  };
};

export const revalidate = 5;

// Shared fetch function with optimized caching
const fetchEventData = async (slugOrUuid: string) => {
  if (!slugOrUuid || slugOrUuid === "_") {
    return null;
  }

  const userTimeZone = await getUserTimeZone();
  const isInputUUID = isUUID(slugOrUuid);

  try {
    // Make the appropriate API call based on input type
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}api/events/${
        isInputUUID ? "detail" : "slug"
      }/${slugOrUuid}/`,
      {
        headers: { "X-Timezone": userTimeZone },
        credentials: "include",
        next: { revalidate },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch data");
    }

    const rawData = await response.json();
    const data = keysToCamel(rawData);

    // If the URL is UUID, store redirect info in the data
    if (isInputUUID && data.slug) {
      data._shouldRedirect = true;
    }

    // Backward compatibility: set pixelId from pixels.meta if available
    if (data?.pixels?.meta && !data?.pixelId) {
      data.pixelId = data.pixels.meta;
    }

    return data;
  } catch (error) {
    console.error("Error fetching event data:", error);
    return null;
  }
};

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = params;
  const data = await fetchEventData(slug);

  if (!data) {
    return {
      title: "Event Not Found",
      description: "The requested event could not be found.",
      robots: "noindex, nofollow", // Don't index 404 pages
    };
  }

  // Get the best available image - first try type 1, then fallback to first image
  const getBestImage = () => {
    if (!data?.images?.length) return null;

    const typeOneImage = data.images.find(
      (img: any) => Number(img?.type) === 1
    );
    if (typeOneImage?.photo) return typeOneImage.photo;

    // Fallback to first image if type 1 not found
    return data.images[0]?.photo || null;
  };

  const bestImage = getBestImage();

  // Create metadata object with proper canonical tag
  const metadata: Metadata = {
    title: `${data?.name || "Event"}`,
    description: `${data?.description || "No description available"}`,
    alternates: {
      canonical: `/events/${slug}`, // Self-referencing canonical tag
    },
    openGraph: {
      title: `${data?.name || "Event"}`,
      description: `${data?.description || "No description available"}`,
      images: bestImage
        ? [
            {
              url: bestImage,
              alt: data?.name || "Event Image",
            },
          ]
        : [],
    },
    other: {
      "Cache-Control": "public, max-age=3600, stale-while-revalidate=86400",
    },
  };

  // Add preload link if we have an image
  if (bestImage) {
    metadata.other = metadata.other ?? {};
    metadata.other["link"] = [
      `rel=preload; as=image; href=${bestImage}; fetchpriority=high`,
    ];
  }

  return metadata;
};

const getEvent = async (slugOrUuid: string) => {
  if (!slugOrUuid || slugOrUuid === "_") {
    console.error("getEvent called without identifier");
    return null;
  }

  const data = await fetchEventData(slugOrUuid);
  return data;
};

const EventsDetails = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params;

  if (!slug || slug === "_") {
    return (
      <div className="mt-20">
        Event identifier is wrong or missing. Cannot load event details.
      </div>
    );
  }

  const data = await getEvent(slug);

  // Handle redirect case
  if (data && data._shouldRedirect && data.slug) {
    return <ClientRedirect slug={data.slug} />;
  }

  if (!data) {
    return <div className="mt-20">Event not found or failed to load.</div>;
  }

  const eventId = data?.id;
  const mainEventImg = data?.images?.find(
    (img: any) => Number(img?.type) === 1
  );
  const eventBannerImg = data?.images?.find(
    (img: any) => Number(img?.type) === 2
  );
  const eventParkingImg = data?.images?.find(
    (img: any) => Number(img?.type) === 3
  );
  const isFreeEvent = data?.tickets?.length === 0 && data?.freeToAttend;
  const waivers = data?.waivers;

  return (
    <div className="mt-20 md:mt-12 min-h-[calc(100vh-100px)]">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(createEventJsonLd(data)),
        }}
      />

      {data?.organization && (
        <ErrorBoundary>
          <StoreSessions orgSlug={data?.organization?.slug} eventId={eventId} />
        </ErrorBoundary>
      )}
      <EventPageTracker
        event={{
          id: data.id,
          title: data?.name,
          pixelId: data?.pixelId,
          pixels: data?.pixels,
          date: data.startDate,
        }}
      />

      <EventImageViewer>
        <EventCoverImage image={eventBannerImg} />

        <div className="grid justify-center pb-14 mt-2">
          <div
            className={`flex flex-col lg:flex-row p-4 gap-10 md:w-[94vw] ${
              isFreeEvent ? "justify-center" : "justify-between"
            }`}
          >
            <div className="lg:w-[62%] mb-5">
              <EventOverview
                eventId={data?.id}
                img={mainEventImg?.photo}
                title={data?.name}
                location={{
                  city: data?.city,
                  state: data?.state,
                  country: data?.country,
                  venueName: data?.venueName,
                  address: data?.address,
                  zipCode: data?.zipCode,
                  geoLocation: data?.geoLocation,
                }}
                category={data?.category}
                orgDetails={data?.organization}
                startDate={data?.startDate}
                endDate={data?.endDate}
                timezone={data?.timezone}
                tag={data?.metadata?.tag}
                parkingImg={eventParkingImg?.photo}
                collaborators={data?.collaborators}
                pixels={data?.pixels}
              />
              <div className="hidden lg:block">
                {data && data?.showVipList && (
                  <FeaturedList list={data?.vipList} />
                )}
                <EventDetails details={data?.description} />
                <ProfileGridSection title="Models" items={data?.models} />
                <ProfileGridSection title="Vendors" items={data?.vendors} />
                <ProfileGridSection title="Media" items={data?.media} />
                <MerchSection products={data?.products} />
              </div>
            </div>

            {/* Ticket Options */}
            {!isFreeEvent && (
              <div className="lg:w-[38%]">
                <TicketOptions
                  tickets={data?.tickets}
                  title={data?.name}
                  date={data?.startDate}
                  eventId={data?.id}
                  eventTitle={data?.name}
                  eventImg={data?.images?.[0]?.photo}
                  pixelId={data?.pixelId}
                  ticketAvailableFrom={data?.ticketsAvailableFrom}
                  ticketAvailableTill={data?.ticketsAvailableTill}
                  orgId={data?.organization}
                  eventStatus={data?.status}
                  timezone={data?.timezone}
                  waivers={waivers}
                  pixels={data?.pixels}
                />
              </div>
            )}

            <div className="lg:hidden">
              {data && data?.showVipList && (
                <FeaturedList list={data?.vipList} />
              )}
              <EventDetails details={data?.description} />
              <ProfileGridSection title="Models" items={data?.models} />
              <ProfileGridSection title="Vendors" items={data?.vendors} />
              <ProfileGridSection title="Media" items={data?.media} />
              <MerchSection products={data?.products} />
            </div>
          </div>
        </div>
      </EventImageViewer>
    </div>
  );
};

export default EventsDetails;
