import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";

export async function getEvents(isFeaturedEvents: boolean = false, isQa: boolean = false) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const futureDate = new Date();
  futureDate.setMonth(futureDate.getMonth() + (isFeaturedEvents ? 6 : 2));

  const startDate = today.toISOString().split(".")[0] + "Z";
  const endDate = futureDate.toISOString().split(".")[0] + "Z";

  let url = `${process.env.NEXT_PUBLIC_API_URL}api/events/list/?&order_by=start_date${
    isQa ? "&is_qa=true" : ""
  }`;
  
  if (isFeaturedEvents) {
    url += "&has_paid_tickets=true&status=active";
  } else {
    url += `&date=${startDate};after,${endDate};before`;
  }

  try {
    const response = await fetch(url, {
      next: {
        revalidate: 60, // Revalidate every 1 minute
      },
      headers: {
        Accept: "application/json",
      },
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return keysToCamel(data);
  } catch (error) {
    console.error("Failed to fetch events:", error);
    return { results: [] };
  }
}
