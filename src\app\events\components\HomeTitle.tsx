interface HomeTitleProps {
  primaryTitle: string;
  secondaryTitle?: string;
}

const HomeTitle: React.FC<HomeTitleProps> = ({
  primaryTitle,
  secondaryTitle = "",
}) => {
  return (
    <div className="flex gap-2 md:gap-6 my-4 ml-4 items-center">
      <h1 className="text-xl md:text-3xl font-bold">{primaryTitle}</h1>
      {secondaryTitle && (
        <h2 className="text-xl md:text-3xl font-bold text-neutral-400">
          {secondaryTitle}
        </h2>
      )}
    </div>
  );
};

export default HomeTitle;
