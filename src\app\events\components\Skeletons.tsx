export const EventsSkeleton = () => (
  <div className="space-y-4 p-3">
    <div className="flex justify-between">
      <div className="w-1/3 h-8 animate-pulse bg-gray-200 rounded" />
      <div className="w-24 h-8 animate-pulse bg-gray-200 rounded" />
    </div>
    <div className="flex gap-4 overflow-hidden">
      {[1, 2, 3, 4].map((i) => (
        <div
          key={i}
          className="min-w-[250px] h-[300px] rounded-lg animate-pulse bg-gray-200"
        />
      ))}
    </div>
  </div>
);

export const CategoriesSkeleton = () => (
  <div className="space-y-4 p-3">
    <div className="w-1/3 h-8 animate-pulse bg-gray-200 rounded" />
    <div className="flex gap-4 overflow-hidden">
      {[1, 2, 3, 4].map((i) => (
        <div
          key={i}
          className="min-w-[200px] h-[250px] rounded-lg animate-pulse bg-gray-200"
        />
      ))}
    </div>
  </div>
); 