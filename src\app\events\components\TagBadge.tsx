import { cn } from "@/lib/utils";
import Image from "next/image";
import React from "react";

interface TagBadgeProps {
  tag?: string;
  className?: string;
  iconClassName?: string;
}

const TagBadge = ({ tag, className, iconClassName }: TagBadgeProps) => {
  if (!tag) return null;

  return (
    <div
      className={cn(
        "absolute flex items-center gap-[3px] top-[-10px] right-[2px] !z-50 bg-[#FFC800] text-white text-sm font-medium px-[5.5px] py-[1.5px] rounded-[5.5px] uppercase",
        className
      )}
    >
      <Image
        src="/tag-icon.svg"
        alt="AutoLNK Event Tag"
        width={13}
        height={17}
        className={cn("w-[13px] h-[17px] font-medium", iconClassName)}
        priority={true}
        loading="eager"
        quality={100}
      />
      {tag}
    </div>
  );
};

export default TagBadge;
