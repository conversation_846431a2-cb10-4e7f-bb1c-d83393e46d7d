export const MODAL_TYPES = {
  TICKET_FORM: "ticketForm",
  GUEST: "guest",
  TICKET_UPDATE: "ticketUpdate",
  CREATE_TEAM_ACCOUNT: "createTeamAccount",
} as const;

export const HEADINGS = {
  TICKET_OPTIONS: "Ticket Options",
};

export const policiesAndGuidelinesMap = {
  refundPolicy: "Return and refund policy",
  privacyPolicy: "Privacy policy",
  termsOfService: "Terms of service",
  safetyGuidelines: "Safety",
  prohibitedItems: "Prohibited",
  permittedItems: "Permitted",
  petPolicy: "Pet Policy",
  cameraPolicy: "Camera Policy",
};

export const eventGuidelinesIconMap = {
  safetyGuidelines: { name: "Safety", icon: "/safety.svg" },
  prohibitedItems: { name: "Prohibited", icon: "/prohibited.svg" },
  permittedItems: { name: "Permitted", icon: "/permitted.svg" },
  petPolicy: { name: "Pet Policy", icon: "/pet.svg" },
  cameraPolicy: { name: "Camera Policy", icon: "/camera.svg" },
};

export const TICKET_PRICE_BADGE = {
  EARLY_BIRD: "Early bird discount",
  DISCOUNT: "Discount",
  LIMITED_TIME_DISCOUNT: "Limited Time",
};


export const EVENT_STATUS = {
  DRAFT: "draft",
  ACTIVE: "active",
};