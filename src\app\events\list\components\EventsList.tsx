"use client";

import { useState, useEffect, useRef } from "react";
import EventCard from "@/components/EventCard/EventCard";
import { capitalize } from "@/lib/utils/string";
import { useSessionData } from "@/lib/hooks/useSession";

const EventsList = ({
  initialData,
  category,
  perPage,
  startDate,
  endDate,
}: {
  initialData: any;
  category: string;
  perPage: number | string;
  startDate: string;
  endDate: string;
}) => {
  const [events, setEvents] = useState(initialData.results);
  const [cursor, setCursor] = useState(initialData.next_cursor);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(!!initialData.next_page_results);
  const observer = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const { data: session } = useSessionData();

  const fetchMoreEvents = async () => {
    const userTimeZone =
      session?.user?.userTimezone ||
      Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone;

    if (!hasMore || loading) return;
    setLoading(true);
    const url = `${
      process.env.NEXT_PUBLIC_API_URL
    }api/events/list?cursor=${cursor}&per_page=${perPage}&order_by=start_date${
      category !== "Events" ? `&category=${category}` : ""
    }`;
    const response = await fetch(url, {
      cache: "no-store",
      headers: {
        "X-Timezone": userTimeZone,
      },
      credentials: "include",
    });
    const data = await response.json();
    setEvents((prev) => [...prev, ...data.results]);
    setCursor(data.next_cursor || null);
    setHasMore(!!data.next_page_results);
    setLoading(false);
  };

  useEffect(() => {
    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        fetchMoreEvents();
      }
    });

    if (loadMoreRef.current) {
      observer.current.observe(loadMoreRef.current);
    }
  }, [cursor, hasMore, loading]);

  return (
    <div className="w-full mt-24 md:mt-16 lg:mt-20 px-10 pb-20 xl:max-w-[1920px] m-auto">
      <h1 className="mx-0 md:mx-1 lg:mx-6 xl:mx-8 2xl:mx-12 my-4 2xl:my-6 2xl:text-[60px] xl:text-[55px] lg:text-[40px] md:text-[35px] text-[27px] font-bold">
        {capitalize(category)}
      </h1>
      <div className="flex justify-center mb-12">
        <div className="w-full grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4 justify-items-center">
          {events?.map((event: any, index: number) => {
            const mainEventImg = event?.images?.find(
              (img: any) => Number(img?.type) === 1
            );
            // Only prioritize first 12 images (covers initial viewport on most screens)
            const shouldLazyLoad = index >= 12;

            return (
              <EventCard
                title={event.name}
                date={event?.start_date}
                city={event?.city}
                state={event?.state}
                imgSrc={mainEventImg?.photo}
                key={event.id}
                href={`/e/${event.slug}`}
                eventId={event.id}
                pixels={event?.organization?.pixel || {}}
                timezone={event?.timezone}
                tag={event?.metadata?.tag}
                lazy={shouldLazyLoad}
              />
            );
          })}
        </div>
      </div>
      {loading && <p className="text-center">Loading...</p>}
      <div ref={loadMoreRef}></div>
    </div>
  );
};

export default EventsList;
