import EventsList from "./components/EventsList";
import { getUserTimeZone } from "@/lib/utils/getUserTimeZone";
import { eventsListBreadcrumbJsonLd } from "@/lib/seo-constants";

const getEvents = async (param: string) => {
  const userTimeZone = await getUserTimeZone();
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}api/events/list${param}`,
    {
      headers: {
        "X-Timezone": userTimeZone,
      },
      next: {
        revalidate: 60,
      },
      credentials: "include",
    }
  );
  const data = await response.json();
  return data;
};

const EventsPage = async ({
  searchParams,
}: {
  searchParams: { [key: string]: string };
}) => {
  const cursor = searchParams["cursor"] || "8:0:0";
  const per_page = searchParams["per_page"] || 8;
  const category = searchParams["category"] || "Events";
  const isQa = searchParams["is_qa"] === "true";

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const sixMonthsLater = new Date();
  sixMonthsLater.setMonth(sixMonthsLater.getMonth() + 6);

  const startDate = today.toISOString().split(".")[0] + "Z";
  const endDate = sixMonthsLater.toISOString().split(".")[0] + "Z";

  const url = `?cursor=${cursor}&per_page=${per_page}&order_by=start_date&date=${startDate};after,${endDate};before${
    isQa ? `&is_qa=${isQa}` : ""
  }${category !== "Events" ? `&category=${category}` : ""}`;
  const data = await getEvents(url);

  return (
    <div className="min-h-screen">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(eventsListBreadcrumbJsonLd),
        }}
      />
      <EventsList
        initialData={data}
        category={category}
        perPage={per_page}
        startDate={startDate}
        endDate={endDate}
      />
    </div>
  );
};

export default EventsPage;
