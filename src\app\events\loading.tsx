import { Skeleton } from "@nextui-org/react";

export default function Loading() {
  return (
    <div className="w-full min-h-screen p-4 space-y-4">
      <Skeleton className="w-full h-16 rounded-lg" />

      <Skeleton className="w-full h-[30vh] md:h-[55vh] rounded-lg" />

      <div className="flex justify-between items-center px-3">
        <Skeleton className="w-1/3 h-8" />
        <Skeleton className="w-24 h-8" />
      </div>

      <div className="w-full px-3">
        <div className="flex gap-4 overflow-hidden">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="min-w-[250px] h-[300px] rounded-lg" />
          ))}
        </div>
      </div>

      <div className="px-6">
        <Skeleton className="w-full h-[200px] rounded-lg" />
      </div>

      <div className="px-3">
        <Skeleton className="w-1/3 h-8 mb-4" />
        <div className="flex gap-4 overflow-hidden">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="min-w-[200px] h-[250px] rounded-lg" />
          ))}
        </div>
      </div>
    </div>
  );
}
