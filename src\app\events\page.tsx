import { Suspense } from "react";
import Link from "next/link";
import { Divider, Skeleton } from "@nextui-org/react";
import type { Metadata } from "next";
import HomeTitle from "./components/HomeTitle";
import HeroSection from "./components/HeroSection";
import { EventsSkeleton, CategoriesSkeleton } from "./components/Skeletons";
import { getEvents } from "./api/getEvents";
import EventsCarousels from "@/components/RenderCarousels/EventsCarousels";
import BannerSection from "./components/BannerSection";
import CategoriesCarousels from "@/components/RenderCarousels/CategoriesCarousels";
import { createFeaturedEventsJsonLd } from "@/lib/seo-constants";

export const metadata: Metadata = {
  alternates: {
    canonical: "/events", // Explicit canonical for events page
  },
};

const EventsSection = async ({
  searchParams,
}: {
  searchParams: { [key: string]: string };
}) => {
  const isQa = searchParams["is_qa"] === "true";
  const data = await getEvents(false, isQa);
  if (!data?.results?.length) return null;

  return (
    <>
      <div className="w-full flex justify-between items-center px-3 py-1 md:px-3 md:py-1 lg:px-5 lg:py-3 xl:px-6 xl:py-3 2xl:px-8 2xl:py-4 mx-auto">
        <HomeTitle primaryTitle="All events." secondaryTitle="Take your pick" />
        <Link
          className="text-blue-600 hover:text-blue-700 transition-colors"
          href="/events/list"
          prefetch={false}
        >
          See all events
        </Link>
      </div>
      <EventsCarousels events={data?.results} />
    </>
  );
};

const FeaturedEventsSection = async () => {
  const data = await getEvents(true);

  if (!data?.results?.length) return null;

  return (
    <>
      <div className="w-full flex justify-between items-center px-3 py-1 md:px-3 md:py-1 lg:px-5 lg:py-3 xl:px-6 xl:py-3 2xl:px-8 2xl:py-4 mx-auto">
        <HomeTitle primaryTitle="Featured events" />
      </div>
      <EventsCarousels events={data?.results} />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(createFeaturedEventsJsonLd(data.results)),
        }}
      />
    </>
  );
};

const CategoriesSection = () => (
  <>
    <div className="w-full px-3 py-0 md:px-3 md:py-0 md:pb-2 lg:px-5 lg:py-2 xl:px-8 xl:py-6 2xl:px-8 2xl:py-8 mx-auto">
      <HomeTitle
        primaryTitle="Popular categories."
        secondaryTitle="Find your fit"
      />
    </div>
    <CategoriesCarousels />
  </>
);

export default function Home({
  searchParams,
}: {
  searchParams: { [key: string]: string };
}) {
  return (
    <div className="mb-24">
      <Suspense fallback={<Skeleton className="w-full h-[30vh] md:h-[55vh]" />}>
        <HeroSection />
      </Suspense>

      <Suspense fallback={<EventsSkeleton />}>
        <FeaturedEventsSection />
      </Suspense>
      <Suspense fallback={<EventsSkeleton />}>
        <EventsSection searchParams={searchParams} />
      </Suspense>

      <Suspense
        fallback={<Skeleton className="w-full h-[200px] mx-6 rounded-lg" />}
      >
        <BannerSection />
      </Suspense>

      <Suspense fallback={<CategoriesSkeleton />}>
        <CategoriesSection />
      </Suspense>
    </div>
  );
}
