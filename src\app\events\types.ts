export interface TicketType {
  id: string;
  ticketTemplate: any;
  ticketQuantity: number;
  vehicleApprovalRequired: boolean;
  ticketPrice: string;
  name: string;
  description: string;
  isPasswordProtected: boolean;
  privateMetadata?: {
    requireLocation?: boolean;
    requirePhoneNumber?: boolean;
  };
  quantityLeft: number;
}

export interface Attribute {
  attribute: {
    id: number;
    name: string;
    slug: string;
    inputType: string;
  };
  values: {
    id: number;
    name: string;
    slug: string;
    value: string;
    fileUrl: string | null;
    contentType: string | null;
    richText: string | null;
    plainText: string | null;
    boolean: boolean | null;
    dateTime: string | null;
  }[];
}

export interface Variant {
  id: number;
  sku: string;
  name: string;
  weight: string;
  attributes: Attribute[];
  media: {
    id: number;
    alt: string;
    type: string;
    sortOrder: number;
    image: string | null;
    isVariantImage: boolean;
    variantId: number;
  }[];
  createdAt: string;
  updatedAt: string;
  price: number;
  currency: string;
  stock: {
    quantity: number;
    quantityAllocated: number;
  };
}

interface ProductTypeDetails {
  id: number;
  name: string;
  slug: string;
  hasVariants: boolean;
  isShippingRequired: boolean;
  weight: string;
}

interface CategoryDetails {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  parent: string | null;
}

export interface ProductVariant {
  id: number;
  price: string | number;
}

export interface Product {
  id: number;
  name: string;
  media?: Array<{
    image: string;
    isVariantImage: boolean;
  }>;
  defaultVariant?: ProductVariant;
  defaultVariantPrice?: {
    price: string;
    currency: string;
  };
  variants?: Variant[];
  descriptionPlaintext?: string;
  slug: string;
}

export interface CustomTicketTemplate {
  forms: any;
  ticketPrice: string;
  priceSchedule?: { scheduledPrice: number | string, scheduledAt: string };
  showPriceChangeBadge?: boolean;
  priceChangeInfo?: {
    priceChangeBadgeText: string;
  };
}

export interface EventImage {
  type: number;
  photo: string;
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  event: string;
}

export interface Organization {
  id: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  isVerified: boolean;
  owner: {
    id: string;
    name: string;
    avatar: string;
    username: string;
    bio: string;
    coverPhoto: string;
    firstName: string;
    lastName: string;
  };
  pixelId?: string;
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

export interface EventLocation {
  venueName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  geoLocation?: string;
}

export interface Event {
  id: string;
  organization: string;
  slug: string;
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}
