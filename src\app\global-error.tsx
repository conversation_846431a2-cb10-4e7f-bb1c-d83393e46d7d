// app/global-error.tsx
"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import { useEffect, useState } from "react";
import { CacheManager } from "@/lib/utils/cacheManager";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

interface ExtendedError extends Error {
  digest?: string;
  code?: string;
  statusCode?: number;
}

export default function GlobalError({
  error,
  reset,
}: {
  error: ExtendedError;
  reset: () => void;
}) {
  const [isClearing, setIsClearing] = useState(false);
  const [browserInfo, setBrowserInfo] = useState<{ isSafari: boolean }>({
    isSafari: false,
  });

  // Initialize CacheManager and detect browser on mount
  useEffect(() => {
    CacheManager.initialize({
      protectedKeys: ["user", "app-deploy-id"],
      clearCookies: true,
      clearServiceWorkers: true,
    });

    // Detect Safari
    const userAgent = navigator.userAgent;
    setBrowserInfo({
      isSafari: /^((?!chrome|android).)*safari/i.test(userAgent),
    });

    Sentry.captureException(error);

    logErrorDetails(error);
    performCacheDiagnosis();
  }, [error]);

  const logErrorDetails = (error: ExtendedError) => {
    console.group("Global Error Details");
    console.error("Error:", error);
    console.error("Error name:", error.name);
    console.error("Error message:", error.message);
    console.error("Error stack:", error.stack);
    console.error("Error digest:", error.digest);
    console.error("Error code:", error.code);
    console.error("Error status:", error.statusCode);
    console.groupEnd();
  };

  const performCacheDiagnosis = async () => {
    try {
      const diagnosis = await CacheManager.diagnoseCache();
      console.group("Cache Diagnosis at Error");
      console.log("Cache status:", diagnosis);
      console.groupEnd();
    } catch (error) {
      console.error("Cache diagnosis failed:", error);
    }
  };

  const handleClearAndRefresh = async () => {
    try {
      setIsClearing(true);
      console.log("Starting complete site data cleanup...");

      // Clear all caches using CacheManager
      await CacheManager.clearAllCaches();
      console.log("CacheManager cleanup completed");

      // Reset the error boundary
      reset();

      // Log final diagnosis
      const finalDiagnosis = await CacheManager.diagnoseCache();
      console.log("Final cache state before reload:", finalDiagnosis);

      // Use safe reload with loop protection
      const reloadSuccess = CacheManager.safeReload("global-error-handler");
      if (!reloadSuccess) {
        console.warn("Safe reload was prevented, trying alternative approach");
        // Alternative: redirect to home page instead of reload
        window.location.href = "/";
      }
    } catch (clearError) {
      console.error("Error during cleanup process:", clearError);
      // Fallback: try safe reload, then redirect if blocked
      const reloadSuccess = CacheManager.safeReload("global-error-fallback");
      if (!reloadSuccess) {
        window.location.href = "/";
      }
    }
  };

  return (
    <html className="h-full">
      <body className="h-full font-sfPro bg-gray-50">
        <div className="min-h-screen flex flex-col items-center justify-center p-4">
          <div className="w-full max-w-lg">
            {/* Logo Container */}
            <div className="flex justify-center mb-8">
              <img
                src={IMAGE_LINKS.LOGO_TRANSPARENT}
                alt="AutoLNK"
                className="w-full max-w-[200px] h-auto"
              />
            </div>

            {/* Error Card */}
            <div className="bg-white rounded-xl shadow-lg p-8 w-full">
              {/* Error Title */}
              <h1 className="text-2xl font-semibold text-gray-900 mb-4">
                Something went wrong
              </h1>

              {/* Error Message */}
              <p className="text-gray-600 mb-6 leading-relaxed">
                {browserInfo.isSafari
                  ? "We've encountered an issue. Please click the button below to refresh and try again."
                  : "We've encountered an unexpected error. Click the button below to refresh the application and clear temporary data."}
              </p>

              {/* Development Mode Error Details */}
              {error.message && process.env.NODE_ENV === "development" && (
                <div className="mb-6 space-y-2">
                  <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm text-gray-600 font-mono">
                    {error.message}
                  </pre>
                  {error.stack && (
                    <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm text-gray-600 font-mono whitespace-pre-wrap">
                      {error.stack}
                    </pre>
                  )}
                </div>
              )}

              {/* Refresh Button */}
              <button
                onClick={handleClearAndRefresh}
                disabled={isClearing}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg 
                          transition duration-150 ease-in-out focus:outline-none focus:ring-2 
                          focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed
                          flex items-center justify-center space-x-2"
              >
                {isClearing ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span>Clearing data...</span>
                  </>
                ) : (
                  <span>Refresh Application</span>
                )}
              </button>
            </div>

            {/* Development Mode Error ID */}
            {process.env.NODE_ENV === "development" && error.digest && (
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-500">
                  Error ID: <code className="font-mono">{error.digest}</code>
                </p>
              </div>
            )}

            {/* Support Link */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                Need help?{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  Contact support
                </a>
              </p>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
