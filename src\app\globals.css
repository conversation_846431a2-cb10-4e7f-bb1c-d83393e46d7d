@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Hero Section Animations */
@keyframes hero-word-entrance {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hero-subtitle-entrance {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes hero-button-entrance {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-word {
  opacity: 0;
  animation: hero-word-entrance 0.3s ease-out forwards;
}

.hero-subtitle {
  opacity: 0;
  animation: hero-subtitle-entrance 0.6s ease-out 1s forwards;
}

.hero-button-entrance {
  opacity: 0;
  animation: hero-button-entrance 0.3s ease-out 1.2s forwards;
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Handle landscape orientation */
  @media (orientation: landscape) {
    .landscape\:pb-\[50vh\] {
      padding-bottom: 50vh;
    }
    .landscape\:xl\:pb-0 {
      @media (min-width: 1280px) {
        padding-bottom: 0;
      }
    }
  }
}

.ios-input {
  font-size: 16px !important;
  -webkit-text-size-adjust: 100%;
  transform: scale(1);
}

/* Prevent zoom on focus */
input[type="text"],
input[type="email"],
input[type="url"],
textarea {
  font-size: 16px !important;
  transform: scale(1);
}

/* Disable zoom for Safari */
@supports (-webkit-touch-callout: none) {
  input, textarea, select {
    font-size: 16px !important;
  }
}

/* Prevent zoom on iOS */
input, select, textarea {
  font-size: 16px !important;
}

.fill-white {
  fill: #ffffff;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 2px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #00000075;
  border-radius: 5px;
}

.embla {
  max-width: 48rem;
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 100%;
}
.embla__viewport {
  overflow: hidden;
}
.embla__container {
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
.embla__slide {
  transform: translate3d(0, 0, 0);
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}
.embla__slide__number {
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  border-radius: 1.8rem;
  font-size: 4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  user-select: none;
}

.react-select-container .react-select__menu {
  z-index: 100;
}

.react-select-container .react-select__option {
  cursor: pointer;
}

.react-select-container .react-select__option--is-focused {
  background-color: #e7e7e7 !important;
  border-radius: 8px;
}

.react-select-container .react-select__option--is-selected {
  background-color: #e7e7e7 !important;
  border-radius: 8px;
}

.reactEasyCrop_Container {
  position: relative;
  width: 100%;
  height: 100%;
  touch-action: none;
}

.reactEasyCrop_Image,
.reactEasyCrop_Video {
  max-width: 100%;
  max-height: 100%;
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  object-fit: contain;
}

.richtext-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.richtext-dark a {
  color: #3b82f6;
  text-decoration: underline;
}

.richtext-dark code {
  font-family: monospace;
  background-color: #f1f5f9;
  padding: 0.1rem 0.3rem;
  border-radius: 0.25rem;
}

.richtext-dark pre {
  background-color: #f1f5f9;
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow-x: auto;
  margin: 0.5rem 0;
}

.richtext-dark blockquote {
  border-left: 3px solid #cbd5e1;
  padding-left: 0.75rem;
  font-style: italic;
  margin: 0.5rem 0;
}

.richtext-light a {
  color: #93c5fd;
  text-decoration: underline;
}

.richtext-light code {
  font-family: monospace;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 0.1rem 0.3rem;
  border-radius: 0.25rem;
  color: #fff;
}

.richtext-light pre {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow-x: auto;
  margin: 0.5rem 0;
  color: #fff;
}

.richtext-light blockquote {
  border-left: 3px solid rgba(255, 255, 255, 0.5);
  padding-left: 0.75rem;
  font-style: italic;
  margin: 0.5rem 0;
}

.richtext-content a {
  text-decoration: underline;
}

.richtext-content p {
  margin-bottom: 0.5rem;
}

.richtext-content p:last-child {
  margin-bottom: 0;
}

.richtext-content ul, 
.richtext-content ol {
  margin-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.richtext-content ul {
  list-style-type: disc;
}

.richtext-content ol {
  list-style-type: decimal;
}

.richtext-content strong {
  font-weight: bold;
}

.richtext-content em {
  font-style: italic;
}

.richtext-content h1, 
.richtext-content h2, 
.richtext-content h3,
.richtext-content h4, 
.richtext-content h5, 
.richtext-content h6 {
  font-weight: bold;
  margin: 0.5rem 0;
}

.richtext-content img {
  max-width: 100%;
  height: auto;
  margin: 0.5rem 0;
} 

.pac-container {
  border-radius: 8px;
  margin-top: 4px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  font-family: inherit;
  font-size: 0.875rem;
}

.pac-item {
  padding: 8px 12px;
  cursor: pointer;
  font-family: inherit;
  border-top: 1px solid #e5e7eb;
}

.pac-item:first-child {
  border-top: none;
}

.pac-item:hover {
  background-color: #f3f4f6;
}

.pac-item-selected {
  background-color: #f3f4f6;
}

.pac-icon {
  margin-right: 8px;
}

.pac-item-query {
  font-size: 0.875rem;
  color: #111827;
  font-weight: 500;
}

.pac-matched {
  font-weight: 600;
  color: #1773b0;
}

.pac-item span:not(.pac-item-query) {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Customize Google attribution */
.pac-container:after {
  background-image: none !important;
  height: 0;
  padding: 0;
  color: #999;
  font-size: 0;
  position: absolute;
  margin: 0;
  content: "";
}

/* Custom Google attribution styling */
.google-maps-attribution {
  font-size: 6px;
  color: #e5e7eb;
  text-align: right;
  padding-top: 1px;
  margin-right: 3px;
  opacity: 0.2;
  position: absolute;
  right: 0;
  bottom: -10px;
}

/* Quill editor border radius styling */
.quill-rounded-wrapper .ql-toolbar {
  border-top-left-radius: 0.8rem !important;
  border-top-right-radius: 0.8rem !important;
  border-bottom: none !important;
}

.quill-rounded-wrapper .ql-container {
  border-bottom-left-radius: 0.8rem !important;
  border-bottom-right-radius: 0.8rem !important;
  border-top: none !important;
  min-height: 10rem;
}

.quill-rounded-wrapper .ql-editor {
  min-height: 10rem;
  font-size: 1rem;
}

.ql-container.ql-snow {
  border-top: 1px solid #e5e7eb !important;
}