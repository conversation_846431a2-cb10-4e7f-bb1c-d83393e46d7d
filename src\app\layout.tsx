import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { NextUIProviderWrapper } from "@/lib/Providers/NextUIProvider";
import { StoreProvider } from "@/lib/Providers/StoreProvider";
import { SessionDataProvider } from "@/lib/Providers/CustomSessionProvider";
import localFont from "next/font/local";
import { Toaster } from "react-hot-toast";
import { CacheProvider } from "@/components/CacheProvider";
import { SessionVerificationWrapper } from "@/components/SessionVerificationWrapper";
import PostHogPageView from "@/app/PostHogPageView";
import { PHProvider } from "@/app/providers";
import { Dancing_Script, Inter } from "next/font/google";
import { AuthCookieHandler } from "@/components/AuthCookieHandler";
import { Providers } from "@/components/Providers";

import "photoswipe/dist/photoswipe.css";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { websiteJsonLd } from "@/lib/seo-constants";
import { UserDetailsProvider } from "@/lib/Providers/AuthProvider";
import { FacebookPixelProvider } from "@/lib/Providers/FacebookPixelProvider";
import { ThirdPartyPixelsProvider } from "@/lib/Providers/ThirdPartyPixelsProvider";
import { SubdomainProvider } from "@/lib/Providers/SubdomainProvider";

const sfProp = localFont({
  src: [
    {
      path: "../../public/font/SFProDisplay-Thin.woff",
      weight: "100",
      style: "thin",
    },
    {
      path: "../../public/font/SFProDisplay-Regular.woff",
      weight: "400",
      style: "regular",
    },
    {
      path: "../../public/font/SFProDisplay-Medium.woff2",
      weight: "500",
      style: "medium",
    },
    {
      path: "../../public/font/SFProDisplay-Semibold.woff",
      weight: "600",
      style: "semibold",
    },
    {
      path: "../../public/font/SFProDisplay-Bold.woff",
      weight: "700",
      style: "bold",
    },
    {
      path: "../../public/font/SFProDisplay-Black.woff",
      weight: "800",
      style: "black",
    },
  ],
  variable: "--font-sf-pro",
});

const dancingScript = Dancing_Script({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-dancing-script",
});

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: {
    default: "AutoLNK",
    template: "%s - AutoLNK",
  },
  description:
    "AutoLNK centralizes all areas of the automotive community onto one platform, connecting car enthusiasts, mechanics, and automotive companies. AutoLNK allows you to find and purchase tickets for automotive events, connect with friends through drive shares, and manage your vehicles in a virtual garage.",
  metadataBase: new URL("https://www.autolnkusa.com"), // Fixed: Use www domain consistently
  // Removed the global canonical that was forcing all pages to point to homepage
  openGraph: {
    title: "AutoLNK",
    description:
      "AutoLNK centralizes all areas of the automotive community onto one platform, connecting car enthusiasts, mechanics, and automotive companies. AutoLNK allows you to find and purchase tickets for automotive events, connect with friends through drive shares, and manage your vehicles in a virtual garage.",
    images: [
      {
        url: IMAGE_LINKS.BANNER,
        alt: "AutoLNK Banner",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "AutoLNK",
    description:
      "AutoLNK centralizes all areas of the automotive community onto one platform, connecting car enthusiasts, mechanics, and automotive companies. AutoLNK allows you to find and purchase tickets for automotive events, connect with friends through drive shares, and manage your vehicles in a virtual garage.",
    images: [
      {
        url: IMAGE_LINKS.BANNER,
        alt: "AutoLNK Banner",
      },
    ],
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const wrapWithProviders = (content: React.ReactNode) => {
    return (
      <CacheProvider>
        <SessionDataProvider>
          <ThirdPartyPixelsProvider>
            <FacebookPixelProvider>
              <UserDetailsProvider>{content}</UserDetailsProvider>
            </FacebookPixelProvider>
          </ThirdPartyPixelsProvider>
        </SessionDataProvider>
      </CacheProvider>
    );
  };

  return (
    <html
      lang="en"
      className={`${sfProp.variable} ${dancingScript.variable} ${inter.variable}`}
      suppressHydrationWarning
    >
      <PHProvider>
        <head>
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
          />
          <link rel="preconnect" href="https://connect.facebook.net" />
          <link rel="dns-prefetch" href="https://connect.facebook.net" />

          <link rel="preconnect" href="https://us-assets.i.posthog.com" />
          <link rel="dns-prefetch" href="https://us-assets.i.posthog.com" />

          <link rel="preconnect" href="https://app.posthog.com" />
          <link rel="dns-prefetch" href="https://app.posthog.com" />

          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(websiteJsonLd[0]),
            }}
          />
        </head>
        <body>
          <Providers>
            <NextUIProviderWrapper>
              <StoreProvider>
                <SubdomainProvider>
                  <AuthCookieHandler />
                  {wrapWithProviders(
                    <>
                      <PostHogPageView />
                      <SessionVerificationWrapper>
                        <div className="w-full min-h-screen flex flex-col">
                          <div className="flex-grow font-sfPro">{children}</div>
                        </div>
                      </SessionVerificationWrapper>
                      <Toaster />
                    </>
                  )}
                </SubdomainProvider>
              </StoreProvider>
            </NextUIProviderWrapper>
          </Providers>
        </body>
      </PHProvider>
    </html>
  );
}
