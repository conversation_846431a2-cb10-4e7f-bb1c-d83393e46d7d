import React from "react";
import { Avatar } from "@nextui-org/react";
import Image from "next/image";
import dayjs from "dayjs";
import { Message } from "../types/messageTypes";
import { MessageContent } from "./MessageContent";

type MessageBubbleProps = {
  message: Message;
  currentUserId: string;
  avatar: string;
  username: string;
};

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  currentUserId,
  avatar,
  username,
}) => {
  const isSentByCurrentUser = message.sender === currentUserId;

  return (
    <div
      className={`flex mb-4 items-center ${
        isSentByCurrentUser ? "justify-end" : "justify-start"
      }`}
    >
      {!isSentByCurrentUser && (
        <Avatar
          src={avatar}
          size="sm"
          name={username}
          className="mr-2"
          aria-label="User Avatar"
          alt="User Avatar"
          ImgComponent={Image}
          imgProps={{ width: 32, height: 32 }}
        />
      )}
      <div
        className={`max-w-[70%] py-2 px-4 rounded-xl ${
          isSentByCurrentUser ? "bg-blue-500 text-white" : "bg-gray-200"
        }`}
      >
        <MessageContent
          message={message}
          isSentByCurrentUser={isSentByCurrentUser}
        />
        <div className="flex mt-1 justify-end">
          <p
            className={`text-[10px] font-thin ${
              isSentByCurrentUser ? "text-white" : "text-gray-500"
            }`}
          >
            {dayjs.unix(message.sent).format("h:mm A")}
          </p>
        </div>
      </div>
    </div>
  );
};
