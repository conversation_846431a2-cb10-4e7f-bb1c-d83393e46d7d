import React from "react";
import Image from "next/image";
import { Message } from "../types/messageTypes";
import {
  containsHTML,
  sanitizeHTML,
  extractImageUrl,
} from "../utils/messageUtils";
import {
  ImageFileMessage,
  VideoFileMessage,
  GenericFileMessage,
} from "./file-messages";
import { getFileType, FileType } from "../utils/fileUtils";

type MessageContentProps = {
  message: Message | { text: string };
  isSentByCurrentUser: boolean;
};

export const MessageContent: React.FC<MessageContentProps> = ({
  message,
  isSentByCurrentUser,
}) => {
  const text = "text" in message ? message.text : message;
  const imageUrl = extractImageUrl(text);
  
  // Check if message has file attachment
  const hasFile = "file" in message && message.file;
  
  const renderFileContent = () => {
    if (!hasFile) return null;

    const fileType = getFileType(message.file!.name);
    const hasTextContent = text && text?.trim()?.length > 0;

    return (
      <div className={hasTextContent ? "mt-2" : ""}>
        {fileType === FileType.IMAGE && (
          <ImageFileMessage
            file={message.file!}
            isCurrentUser={isSentByCurrentUser}
          />
        )}
        {fileType === FileType.VIDEO && (
          <VideoFileMessage
            file={message.file!}
            isCurrentUser={isSentByCurrentUser}
          />
        )}
        {fileType === FileType.OTHER && (
          <GenericFileMessage
            file={message.file!}
            isCurrentUser={isSentByCurrentUser}
          />
        )}
      </div>
    );
  };

  const renderTextContent = () => {
    if (!text || text?.trim()?.length === 0) return null;

    if (imageUrl) {
      return (
        <div className="flex flex-col gap-2">
          {text !== imageUrl && (
            <p className="text-sm">{text.replace(imageUrl, "")}</p>
          )}
          <div className="w-[200px] h-[200px] relative rounded-md overflow-hidden">
            <Image
              src={imageUrl}
              alt="Shared image"
              fill
              style={{ objectFit: "cover" }}
              className="rounded-md"
            />
          </div>
        </div>
      );
    }

    if (containsHTML(text)) {
      const sanitizedHtml = sanitizeHTML(text);
      return (
        <div
          className={`text-sm richtext-content ${
            isSentByCurrentUser ? "richtext-light" : "richtext-dark"
          }`}
          dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
        />
      );
    }

    return <p className="text-sm">{text}</p>;
  };

  return (
    <>
      {renderTextContent()}
      {renderFileContent()}
    </>
  );
};
