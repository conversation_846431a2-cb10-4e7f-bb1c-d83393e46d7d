import React, { useState, useCallback, useMemo } from "react";
import { Button, Input, Avatar, Spinner } from "@nextui-org/react";
import { useUserSearchQuery } from "@/lib/redux/slices/user/userApi";
import { useDebouncedValue } from "@/lib/hooks/useDebounce";
import { IoSend } from "react-icons/io5";
import { getName } from "@/lib/utils/string";

type User = {
  id: string;
  username: string;
  name: string;
  avatar: string;
};

type Props = {
  panelClose: () => void;
  onInitiateConversation: (user: User, initialMessage: string) => void;
};

const MessageInitiatePanel: React.FC<Props> = ({
  panelClose,
  onInitiateConversation,
}) => {
  const [search, setSearch] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [messageContent, setMessageContent] = useState("");
  const debouncedSearch = useDebouncedValue(search, 300);

  const {
    data: searchResults,
    isLoading,
    isFetching,
  } = useUserSearchQuery(
    { search: debouncedSearch },
    { skip: debouncedSearch.length < 2 }
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value);
    },
    []
  );

  const handleUserClick = useCallback((user: User) => {
    setSelectedUser(user);
    setSearch("");
  }, []);

  const handleSendMessage = useCallback(() => {
    if (selectedUser && messageContent.trim()) {
      onInitiateConversation(selectedUser, messageContent.trim());
    }
  }, [selectedUser, messageContent, onInitiateConversation]);

  const renderSearchResults = useMemo(() => {
    if (isLoading || isFetching) {
      return (
        <p className="text-center py-2">
          <Spinner size="md" />
        </p>
      );
    }
    if (searchResults?.results && searchResults.results.length > 0) {
      return searchResults.results.map((user) => (
        <div
          key={user.id}
          className="flex items-center gap-2 py-2 cursor-pointer hover:bg-gray-100"
          onClick={() => handleUserClick(user)}
        >
          <Avatar src={user.avatar} alt={getName(user)} size="lg" />
          <div>
            <p className="text-[15px] text-black font-semibold">
              {user.username}
            </p>
            <p className="text-[14px] text-[#737373] font-medium">
              {getName(user)}
            </p>
          </div>
        </div>
      ));
    }
    return <p className="text-center py-2">No users found</p>;
  }, [searchResults, isLoading, isFetching, handleUserClick]);

  const renderSearchInput = useMemo(
    () => (
      <Input
        placeholder="Search"
        value={search}
        onChange={handleSearchChange}
        classNames={{
          base: "max-w-full",
          mainWrapper: "h-full",
          input:
            "text-[16px] placeholder:text-[#8E8E8E] placeholder:text-[15px]",
          inputWrapper:
            "h-full font-normal text-black bg-transparent hover:!bg-transparent focus:!bg-transparent active:!bg-transparent shadow-none !border-0 !outline-0 !ring-0",
        }}
        size="sm"
        style={{ boxShadow: "none", outline: "none" }}
      />
    ),
    [search, handleSearchChange]
  );

  const renderSelectedUser = useMemo(
    () => (
      <div className="flex-grow flex flex-col">
        <div className="mt-8 flex flex-col justify-center items-center gap-2">
          <Avatar
            src={selectedUser?.avatar}
            alt={getName(selectedUser)}
            className="w-[74px] h-[74px] text-large"
          />
          <p className="text-[18px] font-semibold">
            {getName(selectedUser) ?? selectedUser?.username}
          </p>
        </div>
        <div className="mt-auto p-4">
          <Input
            className="md:w-[85%] mx-auto"
            value={messageContent}
            onChange={(e) => setMessageContent(e.target.value)}
            placeholder="Type a message..."
            onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
            endContent={
              <div className="cursor-pointer" onClick={handleSendMessage}>
                <IoSend />
              </div>
            }
          />
        </div>
      </div>
    ),
    [selectedUser, messageContent, handleSendMessage]
  );

  return (
    <div className="bg-white rounded-xl flex flex-col h-full">
      <div className="py-4 relative flex justify-center items-center border-b">
        <h2 className="text-base font-semibold">New Message</h2>
        <Button
          onClick={panelClose}
          className="absolute left-5 bg-transparent text-foreground hover:bg-transparent active:bg-transparent focus:bg-transparent text-[18px]"
          size="sm"
        >
          Cancel
        </Button>
      </div>
      <div className="flex flex-col flex-grow">
        <div className="flex gap-2 items-center py-2 px-4">
          <p className="text-[15px] text-[#8E8E8E]">To:</p>
          {selectedUser ? (
            <div className="py-1 px-2 bg-[#EEEEEF] text-black text-[16px] font-medium rounded-md">
              {getName(selectedUser) ?? selectedUser.username}
            </div>
          ) : (
            renderSearchInput
          )}
        </div>
        {selectedUser
          ? renderSelectedUser
          : debouncedSearch.length >= 2 && (
              <div className="flex-grow max-h-80 overflow-y-auto px-4 custom-scrollbar">
                {renderSearchResults}
              </div>
            )}
      </div>
    </div>
  );
};

export default React.memo(MessageInitiatePanel);
