import React, { useCallback } from "react";
import { Input } from "@nextui-org/react";
import { IoSend } from "react-icons/io5";

type MessageInputProps = {
  messageContent: string;
  setMessageContent: (content: string) => void;
  onSendMessage: (content: string) => void;
};

export const MessageInput: React.FC<MessageInputProps> = ({
  messageContent,
  setMessageContent,
  onSendMessage,
}) => {
  const handleSendMessage = useCallback(() => {
    if (messageContent.trim()) {
      onSendMessage(messageContent.trim());
      setMessageContent("");
    }
  }, [messageContent, onSendMessage, setMessageContent]);

  return (
    <div className="p-4">
      <Input
        className="md:w-[85%] mx-auto"
        value={messageContent}
        onChange={(e) => setMessageContent(e.target.value)}
        placeholder="Type a message..."
        onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
        endContent={
          <div className="cursor-pointer" onClick={handleSendMessage}>
            <IoSend />
          </div>
        }
      />
    </div>
  );
};
