import React from "react";
import { Spinner } from "@nextui-org/react";
import { FaArrowLeft } from "react-icons/fa";
import MessageInitiatePanel from "./MessageInitiatePanel";
import MessagePanel from "./MessagePanel";
import { getName } from "@/lib/utils/string";

type MessageMainAreaProps = {
  isNewDialog: boolean;
  currentDialog: string | null;
  isMobileChat: boolean;
  isMessagesLoading: boolean;
  isNewUserLoading: boolean;
  handleBackToList: () => void;
  toggleInitiatePanel: () => void;
  handleInitiateConversation: (user: any, initialMessage: string) => void;
  combinedMessages: any[];
  sessionData: any;
  handleSendMessage: (content: string) => void;
  typingUsers: string[];
  pendingMessages: Record<number, { content: string; timestamp: number }>;
  chatBoxContainerRef: React.RefObject<HTMLDivElement>;
  messageContent: string;
  setMessageContent: (content: string) => void;
  hasMoreMessages: boolean;
  setMessagesPageCount: React.Dispatch<React.SetStateAction<number>>;
  selectedUser: any;
};

const MessageMainArea: React.FC<MessageMainAreaProps> = ({
  isNewDialog,
  currentDialog,
  isMobileChat,
  isMessagesLoading,
  isNewUserLoading,
  handleBackToList,
  toggleInitiatePanel,
  handleInitiateConversation,
  combinedMessages,
  sessionData,
  handleSendMessage,
  typingUsers,
  pendingMessages,
  chatBoxContainerRef,
  messageContent,
  setMessageContent,
  hasMoreMessages,
  setMessagesPageCount,
  selectedUser,
}) => {
  return (
    <div
      className={`bg-white relative overflow-hidden shadow h-full md:h-[92%] rounded-xl w-full md:w-2/3 transition-all duration-300 ${
        isMobileChat || isNewDialog ? "block" : "hidden md:block"
      }`}
    >
      {isNewDialog ? (
        <MessageInitiatePanel
          panelClose={toggleInitiatePanel}
          onInitiateConversation={handleInitiateConversation}
        />
      ) : currentDialog ? (
        <>
          <div className="md:hidden absolute top-2 left-2 z-10">
            <div onClick={handleBackToList} className="cursor-pointer">
              <FaArrowLeft className="text-gray-600" />
            </div>
          </div>
          {isMessagesLoading || isNewUserLoading ? (
            <div className="w-full h-full flex justify-center items-center">
              <Spinner />
            </div>
          ) : (
            <MessagePanel
              messages={combinedMessages}
              currentUserId={sessionData.user.id}
              onSendMessage={handleSendMessage}
              typingUsers={typingUsers}
              pendingMessages={pendingMessages}
              chatBoxContainerRef={chatBoxContainerRef}
              messageContent={messageContent}
              setMessageContent={setMessageContent}
              hasMoreMessages={hasMoreMessages}
              loadMoreMessages={() => setMessagesPageCount((prev) => prev + 1)}
              selectedUserAvatar={selectedUser?.avatar}
              selectedUserName={getName(selectedUser)}
            />
          )}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center h-full">
          <p className="mt-4 text-lg text-gray-600">
            Select a conversation to start messaging
          </p>
        </div>
      )}
    </div>
  );
};

export default React.memo(MessageMainArea);
