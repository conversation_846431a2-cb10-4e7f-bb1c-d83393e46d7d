import React, { useRef } from "react";
import { MessagePanelProps } from "../types/messageTypes";
import { useMessageGroups } from "../hooks/useMessageGroups";
import { useMessageScroll } from "../hooks/useMessageScroll";
import { MessageBubble } from "./MessageBubble";
import { PendingMessage } from "./PendingMessage";
import { MessageInput } from "./MessageInput";
import { UserProfileHeader } from "./UserProfileHeader";

const MessagePanel: React.FC<MessagePanelProps> = ({
  messages,
  currentUserId,
  onSendMessage,
  typingUsers,
  pendingMessages,
  chatBoxContainerRef,
  messageContent,
  setMessageContent,
  selectedUserAvatar,
  selectedUserName,
  hasMoreMessages,
  loadMoreMessages,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { groupedMessages, sortedDates, formatDate } =
    useMessageGroups(messages);
  const { handleScroll } = useMessageScroll(
    messagesEndRef,
    chatBoxContainerRef,
    hasMoreMessages,
    loadMoreMessages,
    messages
  );

  return (
    <div className="flex flex-col h-full">
      <div
        className="flex-grow overflow-y-auto p-4 custom-scrollbar"
        ref={chatBoxContainerRef}
        onScroll={handleScroll}
      >
        {hasMoreMessages && (
          <div
            className="text-center text-sm text-blue-500 mb-4 cursor-pointer"
            onClick={loadMoreMessages}
          >
            Load more messages
          </div>
        )}

        <UserProfileHeader
          avatar={selectedUserAvatar}
          username={selectedUserName}
        />

        {sortedDates.map((dateKey) => (
          <React.Fragment key={dateKey}>
            <div className="text-center text-sm text-gray-500 my-2">
              {formatDate(dateKey)}
            </div>
            {groupedMessages[dateKey]
              .sort((a, b) => a.sent - b.sent)
              .map((message) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  currentUserId={currentUserId}
                  avatar={selectedUserAvatar}
                  username={selectedUserName}
                />
              ))}
          </React.Fragment>
        ))}

        {Object.entries(pendingMessages).map(([id, message]) => (
          <PendingMessage key={id} id={id} content={message.content} />
        ))}

        {typingUsers.length > 0 && (
          <div className="text-gray-500 italic">
            {selectedUserName} is typing...
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <MessageInput
        messageContent={messageContent}
        setMessageContent={setMessageContent}
        onSendMessage={onSendMessage}
      />
    </div>
  );
};

export default React.memo(MessagePanel);
