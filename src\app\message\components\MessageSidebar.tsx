import React from "react";
import { <PERSON>, <PERSON>, Spinner, Toolt<PERSON>, Badge } from "@nextui-org/react";
import { GrConnect } from "react-icons/gr";
import UserList from "./UserList";

type MessageSidebarProps = {
  connectionStatus: string;
  unreadCount: number;
  isDialogsLoading: boolean;
  dialogs: any[];
  currentDialog: string | null;
  isMobileChat: boolean;
  isNewDialog: boolean;
  toggleInitiatePanel: () => void;
  handleUserSelect: (userId: string) => void;
};

const MessageSidebar: React.FC<MessageSidebarProps> = ({
  connectionStatus,
  unreadCount,
  isDialogsLoading,
  dialogs,
  currentDialog,
  isMobileChat,
  isNewDialog,
  toggleInitiatePanel,
  handleUserSelect,
}) => {
  const statusColor = {
    connected: "success",
    disconnected: "danger",
    reconnecting: "warning",
  };

  const statusMessage = {
    connected: "WebSocket connected",
    disconnected: "WebSocket disconnected",
    reconnecting: "Attempting to reconnect...",
  };

  return (
    <div
      className={`bg-white shadow h-full md:h-[92%] rounded-xl w-full md:w-1/3 overflow-hidden transition-all duration-300 ${
        isMobileChat || isNewDialog ? "hidden md:block" : "block"
      }`}
    >
      <div className="px-4 py-2 flex justify-between items-center border-b-1">
        <div className="flex gap-2 items-center">
          <Tooltip
            content={statusMessage[connectionStatus]}
            placement="top-end"
          >
            <Badge
              content=""
              color={statusColor[connectionStatus]}
              shape="circle"
              placement="bottom-right"
              className="absolute bottom-4 right-4"
            >
              <GrConnect />
            </Badge>
          </Tooltip>
          {unreadCount > 0 && (
            <Chip
              variant="dot"
              className="bg-[#DCF2FF] text-[#0097FE] text-[12px] border-none"
              classNames={{ dot: "w-1 h-1 bg-[#0097FE] rounded-[50%]" }}
              radius="sm"
            >
              {unreadCount} unread
            </Chip>
          )}
        </div>
        <div className="cursor-pointer" onClick={toggleInitiatePanel}>
          <Image src="/message.svg" alt="messaging" />
        </div>
      </div>
      {isDialogsLoading ? (
        <div className="w-full h-full flex justify-center items-center">
          <Spinner />
        </div>
      ) : (
        <UserList
          users={dialogs}
          onUserSelect={handleUserSelect}
          selectedUserId={currentDialog}
        />
      )}
    </div>
  );
};

export default React.memo(MessageSidebar);
