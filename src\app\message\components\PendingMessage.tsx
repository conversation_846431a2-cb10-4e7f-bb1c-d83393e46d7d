import React from "react";
import { MessageContent } from "./MessageContent";

type PendingMessageProps = {
  id: number | string;
  content: string;
};

export const PendingMessage: React.FC<PendingMessageProps> = ({
  id,
  content,
}) => {
  return (
    <div key={id} className="flex justify-end mb-4">
      <div className="max-w-[70%] py-2 px-4 rounded-xl bg-blue-300 text-white">
        <MessageContent
          message={{ text: content }}
          isSentByCurrentUser={true}
        />
      </div>
    </div>
  );
};
