import React, { useMemo } from "react";
import { Avatar } from "@nextui-org/react";
import { getTimeDifference } from "@/lib/utils/date";
import Image from "next/image";
import { getName } from "@/lib/utils/string";

type User = {
  id: string;
  otherUserId: string;
  name: string;
  avatar: string;
  lastMessage: {
    text: string;
    sent: number;
    read: boolean;
    sender: string;
  };
};

type Props = {
  users: User[];
  onUserSelect: (userId: string) => void;
  selectedUserId: string | null;
};

const stripHtmlTags = (html: string): string => {
  if (!html) return "";
  const withSpaces = html
    .replace(/<br\s*\/?>/gi, " ")
    .replace(/<\/?(p|div)[^>]*>/gi, " ");

  return withSpaces
    .replace(/<[^>]*>/g, "")
    .replace(/&nbsp;/g, " ")
    .replace(/&amp;/g, "&")
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&quot;/g, '"')
    .replace(/\s+/g, " ")
    .trim();
};

const UserList: React.FC<Props> = ({ users, onUserSelect, selectedUserId }) => {
  const memoizedUsers = useMemo(() => users, [users]);

  return (
    <div className="overflow-y-auto pb-12 custom-scrollbar h-full">
      {memoizedUsers?.map((user) => (
        <UserListItem
          key={user.id}
          user={user}
          isSelected={user.otherUserId === selectedUserId}
          onSelect={() => onUserSelect(user.otherUserId)}
        />
      ))}
    </div>
  );
};

type UserListItemProps = {
  user: User;
  isSelected: boolean;
  onSelect: () => void;
};

const UserListItem: React.FC<UserListItemProps> = React.memo(
  ({ user, isSelected, onSelect }) => {
    const isMessageRead = user?.lastMessage?.read || false;
    const isMyMessage = user?.otherUserId !== user?.lastMessage?.sender;

    const messagePreview = stripHtmlTags(user?.lastMessage?.text || "");

    return (
      <div
        className={`relative py-2 px-4 flex justify-between items-center border-b gap-2 cursor-pointer ${
          isSelected ? "bg-[#f0f0f0]" : "hover:bg-[#fcfcfc]"
        }`}
        onClick={onSelect}
      >
        {!isMessageRead && !isMyMessage && (
          <div className="absolute top-1/2 -translate-y-1/2 w-1 h-1 bg-[#007AFF] left-[5px] rounded-full" />
        )}
        <div className="flex items-center gap-2">
          <Avatar
            size="md"
            src={user.avatar}
            name={getName(user)}
            ImgComponent={Image}
            imgProps={{ width: 32, height: 32 }}
            aria-label="User Avatar"
          />
          <div>
            <p
              className={`text-sm text-black ${
                !isMessageRead || isMyMessage ? "font-semibold" : ""
              }`}
            >
              {getName(user)}
            </p>
            <div className="flex gap-2 items-center">
              <p
                className={`text-sm text-[#8E8E8E] truncate max-w-[200px] ${
                  (!isMessageRead && !isMyMessage) || isSelected
                    ? "font-semibold"
                    : ""
                }`}
                title={`${isMyMessage ? "You: " : ""} ${messagePreview}`}
              >
                {isMyMessage ? "You: " : ""} {messagePreview}
              </p>
              <p className="text-xs text-[#8E8E8E] whitespace-nowrap">
                {getTimeDifference(user?.lastMessage?.sent || 0)}
              </p>
            </div>
          </div>
        </div>
        {!isMessageRead && !isMyMessage && (
          <p className="text-xs text-[#007AFF] font-semibold">New</p>
        )}
      </div>
    );
  }
);

UserListItem.displayName = "UserListItem";

export default UserList;
