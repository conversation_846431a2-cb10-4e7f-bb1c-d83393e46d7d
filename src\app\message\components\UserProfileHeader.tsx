import React from "react";
import { Avatar } from "@nextui-org/react";
import Image from "next/image";

type UserProfileHeaderProps = {
  avatar: string;
  username: string;
};

export const UserProfileHeader: React.FC<UserProfileHeaderProps> = ({
  avatar,
  username,
}) => {
  return (
    <div className="pb-4 flex flex-col justify-center items-center">
      <Avatar
        src={avatar}
        size="lg"
        name={username}
        className="mb-2"
        alt="User Avatar"
        aria-label="User Avatar"
        ImgComponent={Image}
        imgProps={{ width: 32, height: 32 }}
      />
      <p className="text-base font-bold">{username}</p>
    </div>
  );
};
