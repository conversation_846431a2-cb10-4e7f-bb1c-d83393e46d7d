import React from 'react';
import { getFileIcon, openFileInNewTab } from '@/app/message/utils/fileUtils';
import { ExternalLinkIcon } from 'lucide-react';

interface GenericFileMessageProps {
  file: {
    id: string;
    url: string;
    name: string;
  };
  isCurrentUser: boolean;
}

const GenericFileMessage: React.FC<GenericFileMessageProps> = ({ file }) => {
  const handleFileClick = () => {
    openFileInNewTab(file.url);
  };


  return (
    <div
      onClick={handleFileClick}
      className="flex items-center gap-3 p-3 bg-gray-50 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors border border-gray-200 max-w-[280px]"
    >
      {/* File icon */}
      <div className="text-2xl flex-shrink-0">
        {getFileIcon(file.name)}
      </div>
      
      {/* File info */}
      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm text-gray-900 truncate">
          {file.name}
        </div>
        <div className="text-xs text-gray-500 mt-0.5">
          Click to open
        </div>
      </div>
      
      {/* External link icon */}
      <ExternalLinkIcon className="text-gray-400 flex-shrink-0" size={16} />
    </div>
  );
};

export default GenericFileMessage;

