import React, { useState } from 'react';
import { openFileInNewTab } from '../../utils/fileUtils';

interface ImageFileMessageProps {
  file: {
    id: string;
    url: string;
    name: string;
  };
  isCurrentUser: boolean;
}

const ImageFileMessage: React.FC<ImageFileMessageProps> = ({ file }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  if (imageError) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <span>🖼️</span>
        <span>Image unavailable</span>
        <button
          onClick={() => window.open(file.url, '_blank')}
          className="text-blue-500 hover:underline"
        >
          View original
        </button>
      </div>
    );
  }

  return (
    <>
      <div className="relative">
        {/* Image thumbnail */}
        <img
          src={file.url}
          alt={file.name}
          className="max-w-[250px] max-h-[200px] rounded-lg cursor-pointer transition-opacity hover:opacity-90"
          style={{
            objectFit: 'cover',
            opacity: imageLoaded ? 1 : 0.5,
          }}
          onLoad={handleImageLoad}
          onError={handleImageError}
          onClick={() => openFileInNewTab(file.url)}
        />
        
        {/* Loading state */}
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-200 rounded-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>
    </>
  );
};

export default ImageFileMessage;

