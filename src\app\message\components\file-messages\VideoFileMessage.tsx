import React, { useState, useRef } from 'react';

interface VideoFileMessageProps {
  file: {
    id: string;
    url: string;
    name: string;
  };
  isCurrentUser: boolean;
}

const VideoFileMessage: React.FC<VideoFileMessageProps> = ({ file }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const [showControls, setShowControls] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handleVideoError = () => {
    setVideoError(true);
  };

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVideoClick = () => {
    togglePlay();
  };

  if (videoError) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-500 bg-gray-100 p-3 rounded-lg">
        <span>🎥</span>
        <div className="flex-1">
          <div className="font-medium">Video unavailable</div>
        </div>
        <button
          onClick={() => window.open(file.url, '_blank')}
          className="text-blue-500 hover:underline text-sm"
        >
          Open
        </button>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Video player */}
      <div className="relative max-w-[300px] rounded-lg overflow-hidden bg-black">
        <video
          ref={videoRef}
          src={file.url}
          className="w-full h-auto max-h-[200px] object-contain cursor-pointer"
          onError={handleVideoError}
          onClick={handleVideoClick}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onMouseEnter={() => setShowControls(true)}
          onMouseLeave={() => setShowControls(false)}
          controls={showControls}
          preload="metadata"
        />
        
        {/* Play button overlay */}
        {!isPlaying && (
          <div
            className="absolute inset-0 flex items-center justify-center cursor-pointer bg-black bg-opacity-20 hover:bg-opacity-30 transition-all"
            onClick={togglePlay}
          >
            <div className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg">
              <div className="w-0 h-0 border-l-[8px] border-l-gray-800 border-y-[6px] border-y-transparent ml-1"></div>
            </div>
          </div>
        )}
      </div>
      
      {/* Fallback link for mobile or if video fails */}
      <button
        onClick={() => window.open(file.url, '_blank')}
        className="mt-1 text-xs text-blue-500 hover:underline"
      >
        Open in new tab
      </button>
    </div>
  );
};

export default VideoFileMessage;