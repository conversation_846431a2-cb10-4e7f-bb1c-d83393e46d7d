import { useMemo, useCallback } from "react";
import dayjs from "dayjs";
import isToday from "dayjs/plugin/isToday";
import isYesterday from "dayjs/plugin/isYesterday";
import { Message } from "../types/messageTypes";

// Ensure dayjs plugins are extended
dayjs.extend(isToday);
dayjs.extend(isYesterday);

export const useMessageGroups = (messages: Message[]) => {
  const formatDate = useCallback((timestamp: number) => {
    const date = dayjs.unix(timestamp);
    if (date.isToday()) return "Today";
    if (date.isYesterday()) return "Yesterday";
    return date.format("MMMM D, YYYY");
  }, []);

  const groupedMessages = useMemo(() => {
    const groups: Record<number, Message[]> = {};
    messages.forEach((message) => {
      const dateKey = dayjs.unix(message.sent).startOf("day").unix();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });
    return groups;
  }, [messages]);

  const sortedDates = useMemo(
    () =>
      Object.keys(groupedMessages)
        .map(Number)
        .sort((a, b) => a - b),
    [groupedMessages]
  );

  return {
    groupedMessages,
    sortedDates,
    formatDate,
  };
}; 