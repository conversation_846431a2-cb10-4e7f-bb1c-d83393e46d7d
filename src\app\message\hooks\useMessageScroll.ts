import { useCallback, useEffect, RefObject } from "react";

export const useMessageScroll = (
  messagesEndRef: RefObject<HTMLDivElement>,
  chatBoxContainerRef: RefObject<HTMLDivElement>,
  hasMoreMessages?: boolean,
  loadMoreMessages?: () => void,
  messages?: any[]
) => {
  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, messagesEndRef]);

  // Handle scroll to top to load more messages
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop } = e.currentTarget;
      if (scrollTop === 0 && hasMoreMessages && loadMoreMessages) {
        loadMoreMessages();
      }
    },
    [hasMoreMessages, loadMoreMessages]
  );

  return { handleScroll };
}; 