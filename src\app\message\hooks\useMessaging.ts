import { debounce } from "lodash";
import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import {
  useGetDialogsQuery,
  useGetMessagesWithUserQuery,
} from "@/lib/redux/slices/messaging/messagingApi";
import { useSessionData } from "@/lib/hooks/useSession";
import { refreshTokenAction } from "@/lib/actions/auth/refreshTokenAction";
import { getCookie } from "@/lib/utils/cookieUtils";
import { clearUser } from "@/lib/redux/slices/auth/authSlice";
import { store } from "@/lib/redux/store";
import { Logout } from "@/lib/actions/auth/Logout";
import {
  MESSAGE_TYPES,
  MESSAGE_TYPE_NAMES,
  MESSAGE_TIMEOUT,
  TYPING_INDICATOR_TIMEOUT,
  RECONNECTION_DELAY,
} from "../utils/constants";
import {
  COOKIE_NAMES,
  AUTH_ERROR_MESSAGES,
  TOKEN_REFRESH_ERRORS,
  WEBSOCKET_CODES,
  WEBSOCKET_REASONS,
  HTTP_STATUS,
} from "@/lib/utils/constants";

interface PendingMessage {
  content: string;
  timestamp: number;
  timeoutId: NodeJS.Timeout;
}

export const useMessaging = (initialUserId: string | null) => {
  const [currentDialog, setCurrentDialog] = useState<string | null>(
    initialUserId
  );
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [pendingMessages, setPendingMessages] = useState<
    Record<number, PendingMessage>
  >({});
  const [error, setError] = useState<string | null>(null);
  const [messagesPageCount, setMessagesPageCount] = useState(1);
  const [dialogsPageCount, setDialogsPageCount] = useState(1);
  const [combinedMessages, setCombinedMessages] = useState<any[]>([]);
  const [messageContent, setMessageContent] = useState("");
  const [isInitiatingConversation, setIsInitiatingConversation] =
    useState(false);

  const { data: userData } = useSessionData();
  const wsRef = useRef<WebSocket | null>(null);
  const chatBoxContainerRef = useRef<HTMLDivElement>(null);

  const {
    data: dialogs = [],
    refetch: refetchDialogs,
    isLoading: isDialogsLoading,
  } = useGetDialogsQuery(
    {},
    { skip: !userData, refetchOnMountOrArgChange: true }
  );

  const {
    data: messages = [],
    refetch: refetchMessages,
    isFetching: isMessagesFetching,
    isLoading: isMessagesLoading,
  } = useGetMessagesWithUserQuery(
    { userId: currentDialog },
    { skip: !currentDialog || !userData, refetchOnMountOrArgChange: true }
  );

  const fetchMessages = useCallback(
    (dialogId: string) => {
      if (currentDialog !== dialogId) {
        setCurrentDialog(dialogId);
        setCombinedMessages([]);
        setIsInitiatingConversation(false);
        setMessagesPageCount(1);
      }
    },
    [currentDialog]
  );

  useEffect(() => {
    if (currentDialog && userData) {
      refetchMessages();
    }
  }, [currentDialog, userData, refetchMessages]);

  const generateRandomId = useCallback(
    () => -Math.floor(Math.random() * 1000000000),
    []
  );

  const showAlert = useCallback((message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000);
  }, []);

  const initiateConversation = useCallback((dialogId: string) => {
    setCurrentDialog(dialogId);
    setIsInitiatingConversation(true);
    setCombinedMessages([]);
    setMessagesPageCount(1);
  }, []);

  const sendMessage = useCallback(
    (content: string, targetUserId: string) => {
      if (wsRef.current?.readyState === WebSocket.OPEN && userData) {
        const randomId = generateRandomId();
        const message = {
          msg_type: MESSAGE_TYPES.TEXT_MESSAGE,
          text: content,
          user_pk: targetUserId,
          random_id: randomId,
        };

        wsRef.current.send(JSON.stringify(message));

        const timeoutId = setTimeout(() => {
          setPendingMessages((prev) => {
            const { [randomId]: _, ...rest } = prev;
            return rest;
          });
        }, MESSAGE_TIMEOUT);

        setPendingMessages((prev) => ({
          ...prev,
          [randomId]: {
            content,
            timestamp: Date.now(),
            timeoutId,
          },
        }));

        refetchDialogs();
        refetchMessages();
      } else {
        showAlert(AUTH_ERROR_MESSAGES.SEND_MESSAGE_ERROR);
      }
    },
    [userData, showAlert, isInitiatingConversation, refetchDialogs, generateRandomId]
  );

  const sendReadReceipt = useCallback(
    (dialogId: string) => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        const unreadMessageIds = messages?.results
          ?.filter((message) => message?.sender === dialogId && !message?.read)
          .map((message) => message?.id);

        if (unreadMessageIds?.length) {
          wsRef.current.send(
            JSON.stringify({
              msg_type: MESSAGE_TYPES.BULK_MESSAGE_READ,
              user_pk: dialogId,
              message_ids: unreadMessageIds,
            })
          );
        }
      }
    },
    [messages]
  );

  const sendTypingIndicator = useCallback((dialogId: string) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(
        JSON.stringify({
          msg_type: MESSAGE_TYPES.IS_TYPING,
          user_pk: dialogId,
        })
      );
    }
  }, []);

  const debouncedSendTypingIndicator = useMemo(
    () =>
      debounce(() => {
        if (currentDialog) {
          sendTypingIndicator(currentDialog);
        }
      }, 300),
    [currentDialog, sendTypingIndicator]
  );

  useEffect(() => {
    if (messageContent.length > 0) {
      debouncedSendTypingIndicator();
    }
    return () => {
      debouncedSendTypingIndicator.cancel();
    };
  }, [messageContent, debouncedSendTypingIndicator]);

  const handleTokenRefresh = useCallback(async () => {
    try {
      const refreshToken = getCookie(COOKIE_NAMES.REFRESH_TOKEN);
      const csrfToken = getCookie(COOKIE_NAMES.CSRF_TOKEN);

      if (!refreshToken || !csrfToken) {
        showAlert(AUTH_ERROR_MESSAGES.AUTH_ERROR);
        // Trigger logout if tokens are missing
        store.dispatch(clearUser());
        await Logout();
        return null;
      }

      const result = await refreshTokenAction(refreshToken, csrfToken);
      
      if ('error' in result) {
        // If refresh token is invalid (401-like error), trigger logout
        if (result.error === TOKEN_REFRESH_ERRORS.INVALID_REFRESH_TOKEN || 
            (result.error === TOKEN_REFRESH_ERRORS.REFRESH_API_CALL_FAILED && 
             (result.status === HTTP_STATUS.UNAUTHORIZED || result.message?.includes('401') || result.message?.includes('token_not_valid') || result.message?.includes(AUTH_ERROR_MESSAGES.UNAUTHORIZED)))) {
          showAlert(AUTH_ERROR_MESSAGES.SESSION_EXPIRED);
          
          // Trigger logout for invalid refresh tokens
          store.dispatch(clearUser());
          await Logout();
          return null;
        }
        
        showAlert(result.message || AUTH_ERROR_MESSAGES.TOKEN_REFRESH_ERROR);
        return null;
      }

      return result.accessToken;
    } catch (error) {
      console.error("Error refreshing token:", error);
      showAlert(AUTH_ERROR_MESSAGES.TOKEN_REFRESH_ERROR);
      return null;
    }
  }, [showAlert]);

  const reconnectWebSocket = useCallback(async (token: string | undefined) => {
    if (!token) {
      showAlert(AUTH_ERROR_MESSAGES.NO_AUTH_TOKEN);
      return;
    }

    if (wsRef.current) {
      wsRef.current.close();
    }
    
    const ws = new WebSocket(
      `${process.env.NEXT_PUBLIC_WEB_SOCKET_URL}?token=${token}`
    );

    ws.onopen = () => {
      if (currentDialog && userData) {
        refetchMessages();
      }
    };

    ws.onmessage = async (event) => {
      try {
        const data = JSON.parse(event.data as string);
        
        if (data.error === WEBSOCKET_REASONS.SIGNATURE_EXPIRED || data.code === WEBSOCKET_REASONS.SIGNATURE_EXPIRED) {
          showAlert(AUTH_ERROR_MESSAGES.SESSION_EXPIRED);
          const newToken = await handleTokenRefresh();
          if (newToken) {
            reconnectWebSocket(newToken);
          }
          // If handleTokenRefresh returns null, it has already triggered logout
          return;
        }

        switch (MESSAGE_TYPE_NAMES[data.msg_type as keyof typeof MESSAGE_TYPE_NAMES]) {
          case MESSAGE_TYPE_NAMES[MESSAGE_TYPES.TEXT_MESSAGE]:
          case MESSAGE_TYPE_NAMES[MESSAGE_TYPES.MESSAGE_READ]:
          case MESSAGE_TYPE_NAMES[MESSAGE_TYPES.NEW_UNREAD_COUNT]:
          case MESSAGE_TYPE_NAMES[MESSAGE_TYPES.BULK_MESSAGE_READ]:
            setMessagesPageCount(1);
            refetchDialogs();
            if (
              data.sender === currentDialog ||
              data.recipient === currentDialog
            ) {
              refetchMessages();
            }
            break;
          case MESSAGE_TYPE_NAMES[MESSAGE_TYPES.IS_TYPING]:
            setTypingUsers((prev) => new Set(prev).add(data.user_pk));
            setTimeout(() => {
              setTypingUsers((prev) => {
                const newSet = new Set(prev);
                newSet.delete(data.user_pk);
                return newSet;
              });
            }, TYPING_INDICATOR_TIMEOUT);
            break;
          case MESSAGE_TYPE_NAMES[MESSAGE_TYPES.MESSAGE_ID_CREATED]:
            setPendingMessages((prev) => {
              const { [data.random_id]: pendingMessage, ...rest } = prev;
              if (pendingMessage) {
                clearTimeout(pendingMessage.timeoutId);
              }
              return rest;
            });
            setMessagesPageCount(1);
            refetchDialogs();
            refetchMessages();
            break;
          case MESSAGE_TYPE_NAMES[MESSAGE_TYPES.ERROR_OCCURRED]:
            showAlert(`Error: ${data.error_message}`);
            break;
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      showAlert(AUTH_ERROR_MESSAGES.CONNECTION_ERROR);
    };

    ws.onclose = async (event) => {
      if (event.code === WEBSOCKET_CODES.TOKEN_EXPIRED && event.reason === WEBSOCKET_REASONS.SIGNATURE_EXPIRED) {
        showAlert(AUTH_ERROR_MESSAGES.SESSION_EXPIRED);
        const newToken = await handleTokenRefresh();
        if (newToken) {
          reconnectWebSocket(newToken);
        }
        // If handleTokenRefresh returns null, it has already triggered logout
      } else {
        showAlert(AUTH_ERROR_MESSAGES.CONNECTION_LOST);
        setTimeout(async () => {
          if (wsRef.current?.readyState === WebSocket.CLOSED) {
            // Only try to refresh if we're still in a valid session
            try {
              const refreshToken = getCookie(COOKIE_NAMES.REFRESH_TOKEN);
              const csrfToken = getCookie(COOKIE_NAMES.CSRF_TOKEN);
              
              if (refreshToken && csrfToken) {
                const newToken = await handleTokenRefresh();
                if (newToken) {
                  reconnectWebSocket(newToken);
                }
              }
            } catch (error) {
              console.error("Error during reconnection attempt:", error);
            }
          }
        }, RECONNECTION_DELAY);
      }
    };

    wsRef.current = ws;
  }, [currentDialog, refetchMessages, showAlert, handleTokenRefresh]);

  useEffect(() => {
    if (!userData?.accessToken) return;

    reconnectWebSocket(userData.accessToken);

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [userData, reconnectWebSocket]);

  useEffect(() => {
    if (messages?.results && !isMessagesFetching && !isMessagesLoading) {
      setCombinedMessages((prev) => {
        const newMessages = [...messages.results, ...prev];
        const uniqueMessages = newMessages.filter(
          (value, index, self) =>
            index === self.findIndex((m) => m.id === value.id)
        );
        return uniqueMessages.sort((a, b) => b.sent - a.sent);
      });
      chatBoxContainerRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isMessagesFetching, isMessagesLoading]);

  return {
    dialogs,
    messages: combinedMessages,
    typingUsers: Array.from(typingUsers),
    pendingMessages,
    error,
    fetchMessages,
    initiateConversation,
    sendMessage,
    sendReadReceipt,
    selectedUser: currentDialog,
    chatBoxContainerRef,
    isDialogsLoading,
    isMessagesLoading,
    isMessagesFetching,
    setMessagesPageCount,
    hasMoreMessages: !!messages?.next,
    messageContent,
    setMessageContent,
    dialogsPageCount,
    setDialogsPageCount,
  };
};
