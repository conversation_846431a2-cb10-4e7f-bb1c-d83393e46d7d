"use client";

import React, { useState, useCallback, useEffect, useMemo } from "react";
import { useMessaging } from "./hooks/useMessaging";
import { useSessionData } from "@/lib/hooks/useSession";
import { useRouter, useSearchParams } from "next/navigation";
import { useGetUserQuery } from "@/lib/redux/slices/user/userApi";
import MessageSidebar from "./components/MessageSidebar";
import MessageMainArea from "./components/MessageMainAreaProps";
import { getName } from "@/lib/utils/string";

const MessagePage: React.FC = () => {
  const [isNewDialog, setIsNewDialog] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    "connected" | "disconnected" | "reconnecting"
  >("connected");
  const [isMobileChat, setIsMobileChat] = useState(false);

  const router = useRouter();
  const { data: sessionData } = useSessionData();
  const searchParams = useSearchParams();
  const initialUserId = searchParams.get("userId");

  const {
    dialogs,
    messages: combinedMessages,
    typingUsers,
    pendingMessages,
    initiateConversation,
    error,
    fetchMessages,
    sendMessage,
    selectedUser: currentDialog,
    chatBoxContainerRef,
    isDialogsLoading,
    isMessagesLoading,
    setMessagesPageCount,
    hasMoreMessages,
    messageContent,
    setMessageContent,
    sendReadReceipt,
  } = useMessaging(initialUserId);

  const { data: newUserData, isLoading: isNewUserLoading } = useGetUserQuery(
    { userId: currentDialog },
    {
      skip:
        !currentDialog ||
        dialogs?.results?.dialogs?.some(
          (dialog) => dialog.otherUserId === currentDialog
        ),
    }
  );

  useEffect(() => {
    setConnectionStatus(error ? "disconnected" : "connected");
  }, [error]);

  useEffect(() => {
    const handleResize = () => {
      setIsMobileChat(window.innerWidth < 768 && !!initialUserId);
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [initialUserId]);

  const handleSendMessage = useCallback(
    (content: string) => {
      if (currentDialog) {
        sendMessage(content, currentDialog);
      }
    },
    [sendMessage, currentDialog]
  );

  const handleInitiateConversation = useCallback(
    async (user: User, initialMessage: string) => {
      initiateConversation(user.id);
      setIsNewDialog(false);

      if (initialMessage.trim()) {
        try {
          await sendMessage(initialMessage, user.id);
        } catch (error) {
          console.error("Error sending initial message:", error);
        }
      }
      router.push(`/message?userId=${user.id}`);
      setIsMobileChat(true);
    },
    [initiateConversation, sendMessage, router]
  );

  const toggleInitiatePanel = useCallback(() => {
    setIsNewDialog((prev) => !prev);
    if (isNewDialog) {
      router.push("/message");
    }
  }, [isNewDialog, router]);

  const handleUserSelect = useCallback(
    (userId: string) => {
      router.push(`/message?userId=${userId}`);
      setIsNewDialog(false);
      fetchMessages(userId);
      setIsMobileChat(true);
    },
    [router, fetchMessages]
  );

  const handleBackToList = useCallback(() => {
    setIsMobileChat(false);
    setIsNewDialog(false);
    router.push("/message");
  }, [router]);

  useEffect(() => {
    if (currentDialog && !isMessagesLoading) {
      sendReadReceipt(currentDialog);
    }
  }, [currentDialog, isMessagesLoading, sendReadReceipt]);

  const selectedUser = useMemo(() => {
    return (
      dialogs?.results?.dialogs?.find(
        (dialog) => dialog.otherUserId === currentDialog
      ) ||
      (newUserData && {
        otherUserId: newUserData.id,
        avatar: newUserData.avatar,
        name: getName(newUserData),
      })
    );
  }, [dialogs, currentDialog, newUserData]);

  useEffect(() => {
    if (currentDialog && !isDialogsLoading && !isNewDialog) {
      const userInDialogs = dialogs?.results?.dialogs?.some(
        (dialog) => dialog.otherUserId === currentDialog
      );
      if (userInDialogs) {
        fetchMessages(currentDialog);
      } else if (newUserData) {
        handleInitiateConversation(
          {
            id: newUserData.id,
            username: newUserData.username,
            name: getName(newUserData),
            avatar: newUserData.avatar,
          },
          ""
        );
      }
    }
  }, [
    currentDialog,
    isDialogsLoading,
    dialogs,
    newUserData,
    fetchMessages,
    isNewDialog,
    handleInitiateConversation,
  ]);

  if (!sessionData) {
    return <div>Loading...</div>;
  }

  const unreadCount =
    dialogs?.results?.dialogs?.filter(
      (dialog) =>
        !dialog?.lastMessage?.read &&
        dialog?.otherUserId === dialog?.lastMessage?.sender
    ).length ?? 0;

  return (
    <div className="max-w-[1500px] w-full px-4 sm:w-[90%] pt-[65px] mx-auto h-full">
      <h2 className="mb-5 text-xl font-semibold">Messages</h2>
      <div className="flex flex-col md:flex-row h-[85%] md:h-[95%] gap-5">
        <MessageSidebar
          connectionStatus={connectionStatus}
          unreadCount={unreadCount}
          isDialogsLoading={isDialogsLoading}
          dialogs={dialogs?.results?.dialogs}
          currentDialog={currentDialog}
          isMobileChat={isMobileChat}
          isNewDialog={isNewDialog}
          toggleInitiatePanel={toggleInitiatePanel}
          handleUserSelect={handleUserSelect}
        />
        <MessageMainArea
          isNewDialog={isNewDialog}
          currentDialog={currentDialog}
          isMobileChat={isMobileChat}
          isMessagesLoading={isMessagesLoading}
          isNewUserLoading={isNewUserLoading}
          handleBackToList={handleBackToList}
          toggleInitiatePanel={toggleInitiatePanel}
          handleInitiateConversation={handleInitiateConversation}
          combinedMessages={combinedMessages}
          sessionData={sessionData}
          handleSendMessage={handleSendMessage}
          typingUsers={typingUsers}
          pendingMessages={pendingMessages}
          chatBoxContainerRef={chatBoxContainerRef}
          messageContent={messageContent}
          setMessageContent={setMessageContent}
          hasMoreMessages={hasMoreMessages}
          setMessagesPageCount={setMessagesPageCount}
          selectedUser={selectedUser}
        />
      </div>
    </div>
  );
};

export default React.memo(MessagePage);
