import React from "react";

export type Message = {
  id: number | string;
  text: string;
  sent: number;
  sender: string;
};

export type PendingMessage = {
  content: string;
  timestamp: number;
};

export type MessagePanelProps = {
  messages: Message[];
  currentUserId: string;
  onSendMessage: (content: string) => void;
  typingUsers: string[];
  pendingMessages: Record<number, PendingMessage>;
  chatBoxContainerRef: React.RefObject<HTMLDivElement>;
  messageContent: string;
  setMessageContent: (content: string) => void;
  selectedUserAvatar: string;
  selectedUserName: string;
  hasMoreMessages?: boolean;
  loadMoreMessages?: () => void;
}; 