export const MESSAGE_TYPES = {
  WENT_ONLINE: 1,
  WENT_OFFLINE: 2,
  TEXT_MESSAGE: 3,
  FILE_MESSAGE: 4,
  IS_TYPING: 5,
  MESSAGE_READ: 6,
  ERROR_OCCURRED: 7,
  MESSAGE_ID_CREATED: 8,
  NEW_UNREAD_COUNT: 9,
  TYPING_STOPPED: 10,
  BULK_MESSAGE_READ: 11,
} as const;

export const MESSAGE_TYPE_NAMES = {
  [MESSAGE_TYPES.WENT_ONLINE]: 'WentOnline',
  [MESSAGE_TYPES.WENT_OFFLINE]: 'WentOffline',
  [MESSAGE_TYPES.TEXT_MESSAGE]: 'TextMessage',
  [MESSAGE_TYPES.FILE_MESSAGE]: 'FileMessage',
  [MESSAGE_TYPES.IS_TYPING]: 'IsTyping',
  [MESSAGE_TYPES.MESSAGE_READ]: 'MessageRead',
  [MESSAGE_TYPES.ERROR_OCCURRED]: 'ErrorOccurred',
  [MESSAGE_TYPES.MESSAGE_ID_CREATED]: 'MessageIdCreated',
  [MESSAGE_TYPES.NEW_UNREAD_COUNT]: 'NewUnreadCount',
  [MESSAGE_TYPES.TYPING_STOPPED]: 'TypingStopped',
  [MESSAGE_TYPES.BULK_MESSAGE_READ]: 'BulkMessageRead',
} as const;

export const MESSAGE_TIMEOUT = 10000;
export const TYPING_INDICATOR_TIMEOUT = 3000;
export const RECONNECTION_DELAY = 5000;

export const WEBSOCKET_EVENTS = {
  OPEN: 'open',
  MESSAGE: 'message',
  ERROR: 'error',
  CLOSE: 'close',
} as const;
