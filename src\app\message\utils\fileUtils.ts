export enum FileType {
  IMAGE = 'image',
  VIDEO = 'video',
  OTHER = 'other'
}

/**
 * Determines if a file is an image based on its filename
 */
export const isImageFile = (filename: string): boolean => {
  const imageExtensions = [
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico', 'tiff', 'tif'
  ];
  
  const extension = filename.split('.').pop()?.toLowerCase();
  return extension ? imageExtensions.includes(extension) : false;
};

/**
 * Determines if a file is a video based on its filename
 */
export const isVideoFile = (filename: string): boolean => {
  const videoExtensions = [
    'mp4', 'mov', 'avi', 'mkv', 'webm', 'm4v', 'flv', 'wmv', '3gp', 'ogv'
  ];
  
  const extension = filename.split('.').pop()?.toLowerCase();
  return extension ? videoExtensions.includes(extension) : false;
};

/**
 * Gets the file type category for display purposes
 */
export const getFileType = (filename: string): FileType => {
  if (isImageFile(filename)) return FileType.IMAGE;
  if (isVideoFile(filename)) return FileType.VIDEO;
  return FileType.OTHER;
};

/**
 * Formats file size in human readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Gets appropriate icon for file type
 */
export const getFileIcon = (filename: string): string => {
  const extension = filename.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'pdf':
      return '📄';
    case 'doc':
    case 'docx':
      return '📝';
    case 'xls':
    case 'xlsx':
      return '📊';
    case 'ppt':
    case 'pptx':
      return '📋';
    case 'zip':
    case 'rar':
    case '7z':
      return '🗜️';
    case 'mp3':
    case 'wav':
    case 'ogg':
      return '🎵';
    default:
      return '📎';
  }
};

/**
 * Safely opens a URL in a new tab
 */
export const openFileInNewTab = (url: string): void => {
  const link = document.createElement('a');
  link.href = url;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

