import DOMPurify from "isomorphic-dompurify";

/**
 * Checks if a string contains HTML tags or entities
 */
export const containsHTML = (text: string): boolean => {
  if (!text) return false;

  const htmlTagsRegex = /<\/?[a-z][\s\S]*?>/i;
  const commonEntitiesRegex = /&[a-z]+;|&#[0-9]+;/i;

  return htmlTagsRegex.test(text) || commonEntitiesRegex.test(text);
};

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export const sanitizeHTML = (html: string): string => {
  if (typeof window === "undefined") {
    return DOMPurify.sanitize(html);
  }

  return DOMPurify.sanitize(html, {
    ADD_ATTR: ["target"],
    ADD_TAGS: ["iframe"],
    ALLOW_DATA_ATTR: true,
  });
};

/**
 * Extracts image URLs from text
 */
export const extractImageUrl = (text: string): string | null => {
  const imageRegex = /(https?:\/\/\S+\.(jpg|jpeg|png|gif|webp)(\?\S*)?)/i;
  const match = text.match(imageRegex);
  return match ? match[0] : null;
}; 