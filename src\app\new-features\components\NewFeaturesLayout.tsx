import React from 'react'
import { featuresData } from '../constants'
import { FeatureCard } from './feature-card'

const NewFeaturesLayout = () => {
  return (
    <div>
        <div className="mb-6">
          <h2 className="!text-[25px] font-medium mb-4">New features</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-y-8 md:gap-x-6">
          {featuresData.map((feature) => (
            <FeatureCard
              key={feature.id}
              image={feature.image}
              imageAlt={feature.imageAlt}
              title={feature.title}
              description={feature.description}
              newFeature={feature.newFeature}
              date={feature.date}
            />
          ))}
        </div>
      </div>
  )
}

export default NewFeaturesLayout