import clsx from "clsx";
import React from "react";
import NewBadge from "@/components/NewBadge";
import "./feature-card.css";

interface FeatureCardProps {
  image: string;
  imageAlt: string;
  title: string;
  description: string;
  className?: string;
  newFeature?: boolean;
  date: string;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  image,
  imageAlt,
  title,
  description,
  className = "",
  newFeature = false,
  date,
}) => {
  return (
    <div
      className={clsx(
        "bg-white rounded-[20px] border border-[#dddddd] flex flex-col overflow-hidden feature-card-shadow transition-shadow duration-200",
        className
      )}
    >
      <div className="flex-shrink-0">
        <img
          src={image}
          alt={imageAlt}
          className="w-full h-full object-contain"
        />
      </div>

      <div className="flex flex-col flex-grow pt-3 pb-[9px] px-3">
        {/* Title */}
        <div className="flex gap-2 mb-1 leading-[15px] content-baseline">
        <h3 className="text-[15px] font-semibold text-[#303030]">
          {title}
          {newFeature && (
            <NewBadge className="relative top-[-2px] !ml-[6px]"/>
          )}
        </h3>
        </div>

        {/* Description */}
        <p className="text-[12.5px] text-[#686a6d] font-normal leading-[15px] tracking-[0px] flex-grow break-words">
          {description}
        </p>
        <div className={clsx("pt-2.5 flex justify-end items-end w-full")}>
          <p className="text-[11px] text-[#E3E3E3] font-normal leading-[0px] tracking-[0px] mb-1">
          {date}
        </p>
        </div>
      </div>
    </div>
  );
};
