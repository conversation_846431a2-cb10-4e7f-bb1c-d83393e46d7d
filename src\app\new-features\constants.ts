export const featuresData = [
    {
      id: "edit-event-web-page",
      image: "/features/edit-event-details.png",
      imageAlt: "Edit Event Web Page",
      title: "Edit Event Web Page",
      description: "Customize the content shown on your event details page",
      date: "7/21/25",
      newFeature: true
    },
    {
      id: "in-depth-event-analytics",
      image: "/features/paid-events-table.png",
      imageAlt: "Paid Events Table",
      title: "In-Depth Event Analytics",
      description: "View tickets sold and earnings for any date range by ticket type",
      date: "7/19/25",
      newFeature: true
    },
    {
      id: "find-tickets",
      image: "/features/need-help.png",
      imageAlt: "Need Help Button",
      title: "Need Help Button",
      description: "Allows customers to resend their tickets automatically or send a direct message to your inbox",
      date: "7/17/25",
      newFeature: true
    },
    {
      id: "find-tickets",
      image: "/features/find-tickets.png",
      imageAlt: "Find My Tickets",
      title: "Find My Tickets",
      description: "Add a button to your website for customers needing their tickets resent",
      date: "7/12/25",
      newFeature: true
    },
    {
      id: "edit-email-notifications",
      image: "/features/edit-emails.png",
      imageAlt: "Edit Email Notifications",
      title: "Edit Email Notifications",
      description: "Customize the automated emails to say whatever you want",
      date: "7/10/25",
      newFeature: true
    },
    {
      id: "referrer-domain",
      image: "/features/referrer-chart.png",
      imageAlt: "Referring domain",
      title: "Referring Domain",
      description:
        "View top referring domains driving traffic to your event pages",
      date: "7/8/25",
      newFeature: true
    },
    {
      id: "gender-analytics",
      image: "/features/gender-chart.png",
      imageAlt: "Gender Analytics",
      title: "Gender Analytics",
      description:
        "Understand your audience better with gender analytics",
      date: "7/6/25",
      newFeature: true
    },
    {
      id: "email-attendees",
      image: "/features/email-attendees.png",
      imageAlt: "Email attendees",
      title: "Email Attendees",
      description: "Send emails directly to attendees that purchased a specific ticket, or make custom groups using tags",
      date: "7/4/25",
      newFeature: true
    },
    {
      id: "collab-revenue-split",
      image: "/features/collab-share.png",
      imageAlt: "Collab revenue split",
      title: "Collab Revenue Split",
      description: "Now event collaborators can see exactly how much revenue each organizer has received.",
      date: "7/3/25",
      newFeature: true
    },
    {
      id: "highway-live-stream",
      image: "/features/highway-live.png",
      imageAlt: "Highway live stream",
      title: "Highway Live Stream",
      description: "View live CCTV street camera footage from 750+ locations in California.",
      date: "7/2/25",
      newFeature: true
    },
    {
      id: "select-teams-on-vehicle-form",
      image: "/features/team-name.png",
      imageAlt: "Select teams on vehicle form",
      title: "Select Teams on Vehicle Form",
      description: "Awards can now be added to team accounts, also fixes the issue with inconsistent team name spelling.",
      date: "7/1/25",
      newFeature: true
    },
    {
      id: "new-features-list",
      image: "/features/new-features.png",
      imageAlt: "New features list",
      title: "New Features List",
      description: "The new, new features section displays info about new features",
      date: "6/30/25",
      newFeature: true
    },
    {
      id: "mobile-ticket-delivery",
      image: "/features/ticket-phone.png",
      imageAlt: "Mobile phone showing ticket delivery interface",
      title: "Mobile Ticket Delivery",
      description:
        "Get tickets instantly delivered to your phone. No more digging through your email.",
      date: "6/29/25",
    },
    {
      id: "pixels",
      image: "/features/pixels.png",
      imageAlt: "Pixels tracking interface",
      title: "Pixels",
      description:
        "Add GA4, Snap Pixel, and TikTok Pixel to improve your marketing ads",
      date: "6/28/25"
    },
    {
      id: "customer-behavior",
      image: "/features/customer-behavior.png",
      imageAlt: "Customer behavior analytics chart",
      title: "Customer Behavior",
      description: "Now you can track cart and checkout conversions",
      date: "6/27/25"
    },
    {
      id: "new-vs-returning",
      image: "/features/new-returning-customers.png",
      imageAlt: "New vs returning customers pie chart",
      title: "New vs Returning Customers",
      description:
        "Data comparing customer types has been added to the analytics page",
      date: "6/26/25"
    },
  ];