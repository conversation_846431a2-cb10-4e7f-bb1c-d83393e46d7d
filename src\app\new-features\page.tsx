"use client";
import React, { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import NewFeaturesLayout from "./components/NewFeaturesLayout";
import { useSendFeatureRequestMutation } from "@/lib/redux/slices/user/userApi";
import toast from "react-hot-toast";
import { useSessionData } from "@/lib/hooks/useSession";
import { Loader2 } from "lucide-react";

const NewFeatures: React.FC = () => {
  const { data: user } = useSessionData();
  const [featureText, setFeatureText] = useState("");
  const [sendFeatureRequest, { isLoading }] = useSendFeatureRequestMutation();

  const handleSubmit = async () => {
    if (!user) {
      toast("Please login to submit a feature request");
      return;
    }

    if (!featureText.trim()) {
      toast.error("Please describe the feature you'd like to suggest");
      return;
    }
    try {
      await sendFeatureRequest({ message: featureText }).unwrap();
      toast.success("Feature request submitted successfully!");
      setFeatureText(""); // Reset the form
    } catch (error: any) {
      toast.error(
        error?.data?.message ||
          "Failed to submit feature request. Please try again."
      );
    }
  };

  return (
    <div className="max-w-[1200px] mx-auto mt-24 pb-24 md:pb-48 px-5">
      <div className="w-full max-w-4xl mx-auto mb-16">
        <h1 className="text-[20px] font-normal text-gray-900 mb-2">
          Suggest a new feature
        </h1>
        <div className="relative">
          <Textarea
            value={featureText}
            onChange={(e) => setFeatureText(e.target.value)}
            placeholder="Describe feature, or a problem you want solved"
            className="w-full min-h-[100px]  text-base border border-[#8A8A8A] placeholder:text-[#ACACAC] rounded-[8px] resize-none"
            disabled={isLoading}
          />
          <div className="flex justify-end mt-4">
            <Button
              onClick={handleSubmit}
              disabled={isLoading || !featureText.trim()}
            >
              {isLoading && <Loader2 className="w-4 h-4 mr-2" />}
              Submit
            </Button>
          </div>
        </div>
      </div>

      <NewFeaturesLayout />
    </div>
  );
};

export default NewFeatures;
