"use client";
import { But<PERSON> } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { MdCancel } from "react-icons/md";

const Cancel = () => {
  const router = useRouter();
  const handleClick = () => {
    router.push("/events");
  };
  return (
    <div className="h-screen w-screen flex items-center justify-center">
      <div className="flex flex-col items-center justify-center gap-4 md:max-w-[400px]">
        <MdCancel size="100" color="#EF4444" />
        <h1 className="text-3xl font-bold">Payment Canceled</h1>
        <p className="text-center px-3 text-[#86868B] ">
          Your payment has been sucessfully canceled. We're sorry for any
          inconvenience this may have caused.
        </p>
        <Button
          className="w-11/12 rounded-md bg-black text-white mt-3"
          onPress={handleClick}
        >
          Go to Events
        </Button>
      </div>
    </div>
  );
};

export default Cancel;
