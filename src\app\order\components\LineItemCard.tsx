import { LINE_ITEM_TYPES } from "@/lib/utils/constants";
import { dateToFormattedString } from "@/lib/utils/date";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { Card, CardBody } from "@nextui-org/react";
import Image from "next/image";
import React from "react";

const LineItemCard = ({ lineItem }: { lineItem: any }) => {
  const isProduct = lineItem?.type === LINE_ITEM_TYPES.PRODUCT;
  const imageUrl = isProduct
    ? lineItem?.variant?.media?.[0]?.image
    : lineItem?.eventDetails?.eventImage?.[0]?.photo;

  const eventName = lineItem?.eventDetails?.name;
  const name = isProduct
    ? lineItem.productName
    : lineItem?.eventTicketDetails?.name;
  const formattedDate = dateToFormattedString(
    lineItem?.eventDetails?.startDate,
    lineItem?.eventDetails?.timezone
  );

  return (
    <Card className="!bg-[#FCFCFC] !rounded-2xl !border-[#F2F2F2] !border-1 !shadow-none">
      <CardBody className="md:px-7 py-5">
        <div className="flex gap-4 items-start">
          {imageUrl && (
            <Image
              src={imageUrl || IMAGE_LINKS.NO_IMG}
              alt={name || ""}
              width={100}
              height={100}
              className="w-12 h-12 md:w-14 md:h-14 rounded-[10px] object-cover aspect-square"
            />
          )}
          <div className="flex justify-between w-full mt-[-6px]">
            <div className="flex flex-col pl-2 md:pl-3.5 ">
              {eventName && (
                <p className="font-medium text-[#898989] max-w-[250px] text-[12px] md:text-[13px] truncate">
                  {eventName}
                </p>
              )}
              <h2 className="font-semibold text-[14px] max-w-[250px] md:text-[15.5px] truncate">
                {name}
              </h2>
              <p className="font-medium text-[#898989] text-[12px] md:text-[14px] truncate mt-[2px]">
                {formattedDate}
              </p>

              {!isProduct && lineItem?.selectedVariants?.length > 0 && (
                <div className="text-[#898989] text-[10px] md:text-[11px] mt-[5px]">
                  <div className="flex flex-col gap-1">
                    {lineItem?.selectedVariants?.map((merch, index) => (
                      <p key={index} className="">
                        {merch?.productDetails?.name}: {merch?.name}
                      </p>
                    ))}
                  </div>
                </div>
              )}
              {isProduct && lineItem?.variant && (
                <div className="text-[#898989] text-[10px] md:text-[11px] mt-[5px]">
                  <p>SKU: {lineItem?.variant?.sku}</p>
                </div>
              )}
            </div>
            <div className="flex gap-2 md:gap-[10px] font-medium text-[#232323] text-[14px] md:text-[15px] mt-[18px]">
              <p>x</p>
              <p>{lineItem?.quantity}</p>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default LineItemCard;
