import Image from "next/image";
import { <PERSON><PERSON>, <PERSON>, CardBody, Checkbox } from "@nextui-org/react";
import { useMemo, useState, useEffect } from "react";
import Link from "next/link";
import { Spinner } from "@nextui-org/react";
import { LINE_ITEM_TYPES, ORDER_STATUS } from "@/lib/utils/constants";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import LineItemCard from "./LineItemCard";
import { FaLock } from "react-icons/fa";

export default function OrderConfirmation({ data }: { data: any }) {
  const [isAppleWalletSupported, setIsAppleWalletSupported] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const tickets = useMemo(() => data?.order?.lines, [data]);
  const customerMetadata = useMemo(
    () =>
      data?.order?.customerMetadata || data?.order?.customer?.customerMetadata,
    [data]
  );

  const customerEmail = useMemo(
    () =>
      data?.order?.customer?.email ||
      data?.order?.userEmail ||
      data?.order?.lines?.[0]?.userDetails?.email,
    [data]
  );

  // Check for Apple Wallet support (primarily Safari browsers)
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Check if browser is Safari
      const isSafari = /^((?!chrome|android).)*safari/i.test(
        navigator.userAgent
      );
      // Check if iOS device
      const isIOS =
        /iPad|iPhone|iPod/.test(navigator.userAgent) &&
        !(window as any).MSStream;
      // Check if macOS device
      const isMac = navigator.platform.toUpperCase().indexOf("MAC") >= 0;

      setIsAppleWalletSupported(isSafari && (isIOS || isMac));

      // Check if mobile
      const checkIfMobile = () => {
        setIsMobile(window.innerWidth < 768);
      };

      checkIfMobile();
      window.addEventListener("resize", checkIfMobile);

      return () => {
        window.removeEventListener("resize", checkIfMobile);
      };
    }
  }, []);

  // Function to handle PDF download
  const handlePdfDownload = (pdfUrl: string, ticketName: string) => {
    if (!pdfUrl) return;

    try {
      // Create the API URL with proper parameters
      const apiUrl = `/api/download-pdf?url=${encodeURIComponent(
        pdfUrl
      )}&filename=${encodeURIComponent(ticketName || "ticket")}.pdf`;

      const downloadFrame = document.createElement("iframe");
      downloadFrame.style.display = "none";
      downloadFrame.src = apiUrl;
      document.body.appendChild(downloadFrame);

      downloadFrame.onload = () => {
        setTimeout(() => {
          document.body.removeChild(downloadFrame);
        }, 1000);
      };

      // Add a small delay then open for viewing in browser
      setTimeout(() => {
        window.open(pdfUrl, "_blank");
      }, 500);
    } catch (error) {
      console.error("Error downloading PDF:", error);
      window.open(pdfUrl, "_blank");
    }
  };

  // Function to handle downloading all PDFs
  const handleDownloadAll = () => {
    if (!tickets?.length) return;

    setIsDownloading(true);

    const ticketsWithPdf = tickets.filter((ticket) => ticket?.pdfTicketUrl);
    let completedDownloads = 0;

    ticketsWithPdf.forEach((ticket, index) => {
      if (ticket?.pdfTicketUrl) {
        // Add a small delay between downloads to prevent browser blocking
        setTimeout(() => {
          try {
            handlePdfDownload(
              ticket.pdfTicketUrl,
              ticket?.eventTicketDetails?.name
            );
          } catch (err) {
            console.error("Failed to download ticket:", err);
          } finally {
            completedDownloads++;

            if (completedDownloads === ticketsWithPdf.length) {
              setIsDownloading(false);
            }
          }
        }, index * 500); // 500ms delay between each download
      }
    });

    // Safety timeout in case something goes wrong
    setTimeout(() => {
      if (isDownloading) {
        setIsDownloading(false);
      }
    }, ticketsWithPdf.length * 500 + 5000);
  };

  // Function to handle adding tickets to Apple Wallet
  const handleAddToAppleWallet = () => {
    if (!tickets?.length) return;
    try {
      if (tickets?.length > 1) {
        tickets?.forEach((ticket) => {
          if (ticket?.walletPassesUrl) {
            setTimeout(() => {
              window.open(ticket?.walletPassesUrl, "_blank");
            }, 500);
          }
        });
      } else {
        window.open(tickets?.[0]?.walletPassesUrl, "_blank");
      }
    } catch (err) {
      console.error("Error adding to Apple Wallet:", err);
    }
  };

  return (
    <div className="w-full space-y-4 md:space-y-5 bg-white mb-5 md:mb-10">
      {/* Confirmation Header */}
      <div className="flex flex-col items-center space-y-4 md:space-y-5 mb-6 md:mb-16">
        <Image
          src="/checkmark-circle.svg"
          className="w-[4.5rem] h-[4.5rem]"
          alt="AutoLNK order checkmark-circle"
          width={100}
          height={100}
        />
        {data?.order?.status === ORDER_STATUS.FULFILLED ? (
          <div className="flex flex-col gap-2 justify-center items-center">
            <h1 className="text-2xl font-semibold">
              Thank you, {customerMetadata?.firstName}{" "}
              {customerMetadata?.lastName}!
            </h1>
            <p className="text-sm text-[#707070]">
              Order #{data?.order?.number}
            </p>
          </div>
        ) : (
          <div className="flex flex-col gap-2 justify-center items-center text-center">
            <h1 className="text-[22px] md:text-2xl font-semibold">
              Hold Tight - Your Registration is Under Review
            </h1>
            <p className="text-sm text-[#707070]">
              You'll get a confirmation email with your tickets
              <span className="font-semibold ml-1">
                after you've been approved.
              </span>
            </p>
          </div>
        )}
      </div>

      {tickets?.map((ticket, index) => (
        <div key={index}>
          <LineItemCard lineItem={ticket} />
        </div>
      ))}

      {/* Order Summary */}
      <Card className="!bg-[#FCFCFC] !rounded-2xl !border-[#F2F2F2] !border-1 !shadow-none">
        <CardBody className="p-0">
          <div className="px-5 md:px-7 py-4 border-b border-gray-200">
            <h2 className=" text-[#060606] font-medium">Order Summary</h2>
          </div>

          <div className="px-5 md:px-7 md:py-5 py-3 flex flex-col gap-3 text-[14px]">
            <div className="grid grid-cols-2 gap-4">
              <span className="text-[#898989]">Email</span>
              <span className="text-[#4F4F4F] text-right truncate">
                {customerEmail}
              </span>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <span className="text-[#898989]">Fingerprint</span>
              <span className="text-[#4F4F4F] text-right truncate">
                {data?.order?.payment?.fingerprint || "-"}
              </span>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <span className="text-[#898989]">Address</span>
              <span className="text-[#4F4F4F] text-right truncate">
                {data?.order?.billingAddress?.streetAddress_1},
                {data?.order?.billingAddress?.city},
                {data?.order?.billingAddress?.countryArea}
              </span>
            </div>

            <div className="flex justify-between gap-4">
              <span className="text-[#898989]">
                Fraud Protection Verification
              </span>
              <span className="text-[#4F4F4F] flex items-center gap-2 justify-end">
                Passed
                <svg
                  width="12"
                  height="15"
                  viewBox="0 0 12 15"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5.90161 14.5801C5.79224 14.5801 5.62134 14.5391 5.4436 14.4434C1.56079 12.2559 0.220947 11.3398 0.220947 8.84473V3.61523C0.220947 2.89746 0.5354 2.66504 1.10962 2.42578C1.91626 2.09766 4.52759 1.10645 5.33423 0.874023C5.51196 0.826172 5.71021 0.771484 5.90161 0.771484C6.09302 0.771484 6.29126 0.8125 6.47583 0.874023C7.28247 1.1543 9.88696 2.09082 10.6936 2.42578C11.2747 2.67188 11.5823 2.89746 11.5823 3.61523V8.84473C11.5823 11.3398 10.2493 12.2627 6.35962 14.4434C6.18872 14.5391 6.01099 14.5801 5.90161 14.5801ZM5.90161 13.3428C6.01099 13.3428 6.1272 13.3018 6.32544 13.1787C9.48364 11.2578 10.4954 10.7041 10.4954 8.5918V3.82715C10.4954 3.59473 10.4543 3.49902 10.2698 3.43066C9.21021 3.06836 7.20728 2.34375 6.17505 1.93359C6.06567 1.89258 5.97681 1.87207 5.90161 1.87207C5.82642 1.87207 5.73755 1.88574 5.62817 1.93359C4.59595 2.34375 2.57935 3.02051 1.54028 3.43066C1.34888 3.50586 1.30786 3.59473 1.30786 3.82715V8.5918C1.30786 10.7041 2.31958 11.2646 5.47778 13.1787C5.68286 13.3018 5.79224 13.3428 5.90161 13.3428ZM5.16333 10.5879C4.93091 10.5879 4.75317 10.499 4.57544 10.2734L2.91431 8.22266C2.8186 8.09277 2.75708 7.94238 2.75708 7.79883C2.75708 7.49121 2.98267 7.25195 3.28345 7.25195C3.46802 7.25195 3.62524 7.31348 3.78247 7.53223L5.13599 9.27539L7.9729 4.72266C8.09595 4.51758 8.26685 4.41504 8.44458 4.41504C8.73169 4.41504 9.00513 4.61328 9.00513 4.91406C9.00513 5.07129 8.91626 5.22168 8.84106 5.35156L5.71021 10.2734C5.58032 10.4785 5.38892 10.5879 5.16333 10.5879Z"
                    fill="#64A63C"
                  />
                </svg>
              </span>
            </div>
          </div>
        </CardBody>
      </Card>

      <div className="flex justify-center mt-10 pb-1 md:pb-2">
        {data?.order?.status !== ORDER_STATUS.FULFILLED ? (
          <Link href="/download" target="_blank" className="w-full px-5">
            <Button color="primary" className={`w-full h-12 text-[18px]`}>
              Download app
            </Button>
          </Link>
        ) : (
          <div className="flex items-center gap-5 w-full px-5 justify-center">
            <div className="w-full flex justify-end">
              <Button
                color="primary"
                onPress={handleDownloadAll}
                disabled={isDownloading}
                className={`w-full h-[43px] sm:h-12 text-[18px] ${
                  isAppleWalletSupported ? "md:w-[80%]" : ""
                }`}
              >
                {isDownloading ? (
                  <div className="flex items-center gap-2">
                    <Spinner size="sm" color="white" />
                  </div>
                ) : (
                  "View tickets"
                )}
              </Button>
            </div>

            {isAppleWalletSupported && (
              <button
                className="flex items-start justify-start w-full"
                onClick={() => handleAddToAppleWallet()}
              >
                <img
                  src={IMAGE_LINKS.APPLE_WALLET}
                  alt="Autolnk Add to Apple Wallet"
                  className={"h-[50px]"}
                />
              </button>
            )}
          </div>
        )}
      </div>

      <div className="flex items-center gap-2 justify-center w-full">
        <FaLock style={{ color: "#61A36A", fontSize: "12px" }} />
        <p className="text-[12px] text-[#61A36A]">
          {data?.order?.status !== ORDER_STATUS.FULFILLED
            ? "Payment was authorized securely"
            : "Payment was processed securely"}
        </p>
      </div>
    </div>
  );
}
