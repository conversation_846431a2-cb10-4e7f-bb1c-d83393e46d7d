"use client";
import { <PERSON><PERSON> } from "@nextui-org/react";
import Link from "next/link";
import { SiTicktick } from "react-icons/si";

const MerchSuccess = () => {
  return (
    <div className="h-screen flex items-center justify-center">
      <div className="flex flex-col items-center justify-center gap-4 md:max-w-[400px]">
        <SiTicktick size={60} color="#21C55D" />
        <h1 className="text-2xl font-bold">Order Successful</h1>
        <p className="text-center text-[#86868B] px-3">
          Thank you for your purchase. Your order has been successfully placed
          and will be processed soon.
        </p>
        <Link href="/profile/settings/orders" className="w-11/12">
          <Button className="w-full rounded-md bg-black text-white">
            View Your Orders
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default MerchSuccess;
