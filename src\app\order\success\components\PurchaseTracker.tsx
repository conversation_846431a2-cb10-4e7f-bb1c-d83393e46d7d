"use client";

import { useEffect } from "react";
import { useMetaPixel } from "@/lib/hooks/useMetaPixel";
import { useThirdPartyPixelTracking } from "@/lib/hooks/useThirdPartyPixelTracking";
import {
  TrackingData,
  TrackingEventData,
  TrackingTicketData,
} from "@/lib/utils/extractTrackingData";
import { CHECKOUT_STORAGE_KEYS } from "@/app/checkout/constants/checkoutConstants";

interface PurchaseTrackerProps {
  orderId: string;
  pixels: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

interface StoredTrackingData {
  event: TrackingEventData;
  tickets: TrackingTicketData[];
  transactionId: string;
}

export default function PurchaseTracker({
  orderId,
  pixels,
}: PurchaseTrackerProps) {
  const { trackPurchase: trackMetaPurchase } = useMetaPixel();
  const { trackPurchase: trackThirdPartyPurchase } = useThirdPartyPixelTracking();

  useEffect(() => {
    try {
      // Retrieve tracking data from sessionStorage
      const storedData = sessionStorage.getItem(CHECKOUT_STORAGE_KEYS.PIXEL_PURCHASE_DATA);
      if (!storedData) return;

      const { event, tickets, transactionId } = JSON.parse(
        storedData
      ) as StoredTrackingData;

      // Use the actual order ID if available
      const finalTransactionId = orderId || transactionId;

  
      if (pixels.meta) {
        trackMetaPurchase(event, tickets, finalTransactionId, pixels);
      }

      // Track third-party pixel purchases
      const hasThirdPartyPixels = pixels.ga4 || pixels.snap || pixels.tiktok;
      if (hasThirdPartyPixels) {
        // Create event data with pixels for third-party tracking
        const eventWithPixels = {
          ...event,
          pixels: pixels,
        };
        
        trackThirdPartyPurchase(eventWithPixels, tickets, finalTransactionId);
      }

      // Clear the tracking data after it's been used
      sessionStorage.removeItem(CHECKOUT_STORAGE_KEYS.PIXEL_PURCHASE_DATA);
    } catch (error) {
      console.error("[PurchaseTracker] Failed to track purchase:", error);
    }
  }, [orderId, trackMetaPurchase, trackThirdPartyPurchase, pixels]);

  // This component doesn't render anything
  return null;
}
