"use client";
import { useCheckoutCompleteMutation } from "@/lib/redux/slices/cart/cartApi";
import { useGetOrderByIdQuery } from "@/lib/redux/slices/orders/ordersApi";
import { useEffect, useState, useRef } from "react";
import { SuccessData } from "./types";
import { STORAGE_KEYS } from "@/lib/constants/storage";
import OrderConfirmation from "../components/OrderConfirmation";
import PurchaseTracker from "./components/PurchaseTracker";
import { Spinner } from "@nextui-org/react";
import { useRouter, useSearchParams } from "next/navigation";
import { CHECKOUT_STORAGE_KEYS } from "@/app/checkout/constants/checkoutConstants";
import Image from "next/image";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import Link from "next/link";

const Success = () => {
  const [successData, setSuccessData] = useState<SuccessData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [checkoutComplete] = useCheckoutCompleteMutation();
  const searchParams = useSearchParams();
  const router = useRouter();

  const checkoutTokenFromUrl = searchParams.get("checkoutToken");
  const orderIdFromUrl = searchParams.get("orderId");

  const isApprovalRequiredCheckout =
    localStorage.getItem(
      CHECKOUT_STORAGE_KEYS.IS_APPROVAL_REQUIRED_CHECKOUT
    ) === "true";

  const isRequestPendingRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const { data: orderData } = useGetOrderByIdQuery(
    { orderId: orderIdFromUrl! },
    { skip: !orderIdFromUrl }
  );

  useEffect(() => {
    if (orderIdFromUrl && orderData?.checkoutToken) {
      localStorage.setItem(STORAGE_KEYS.CART_TOKEN, orderData.checkoutToken);
    } else if (checkoutTokenFromUrl) {
      localStorage.setItem(STORAGE_KEYS.CART_TOKEN, checkoutTokenFromUrl);
    }
  }, [checkoutTokenFromUrl, orderIdFromUrl, orderData]);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;
    abortControllerRef.current = new AbortController();

    const completeCheckout = async () => {
      try {
        if (orderIdFromUrl && !orderData?.checkoutToken) {
          return;
        }

        const checkoutToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);
        if (!checkoutToken) {
          router.replace("/");
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, 8000));

        const attemptCheckout = async () => {
          if (isRequestPendingRef.current) {
            return false;
          }

          isRequestPendingRef.current = true;
          try {
            const response = await checkoutComplete({
              checkoutToken,
              paymentType: "web",
              verify: true,
            } as any).unwrap();

            if (response?.order) {
              setSuccessData(response);
              setIsLoading(false);
              const audio = new Audio("/success.mp3");
              audio.play().catch(console.error);
              return true;
            }
            return false;
          } catch (error) {
            console.error("Checkout attempt error:", error);
            return false;
          } finally {
            isRequestPendingRef.current = false;
            localStorage.removeItem(STORAGE_KEYS.CART_TOKEN);
            localStorage.removeItem(STORAGE_KEYS.CLIENT_SECRET);
            localStorage.removeItem(STORAGE_KEYS.CONNECT_ID);
            localStorage.removeItem(STORAGE_KEYS.IS_COLLABORATOR_CHECKOUT);
            sessionStorage.removeItem(STORAGE_KEYS.CHECKOUT_FORM_DATA);
          }
        };

        const initialSuccess = await attemptCheckout();

        if (!initialSuccess && !abortControllerRef?.current?.signal?.aborted) {
          const poll = async () => {
            if (abortControllerRef?.current?.signal?.aborted) return;

            const success = await attemptCheckout();
            if (success) return;

            timeoutId = setTimeout(poll, 2000);
          };

          poll();
        }
      } catch (error) {
        console.error("Checkout completion setup error:", error);
        setIsLoading(false);
      }
    };

    completeCheckout();
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [checkoutComplete, router, orderIdFromUrl, orderData]);

  if (orderIdFromUrl && !orderData?.checkoutToken) {
    return (
      <div className="h-screen w-screen flex items-center justify-center text-xl">
        <div className="flex flex-col gap-5 items-center justify-center">
          <Spinner size="lg" />
          <p className="text-lg">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center text-xl">
        <div className="flex flex-col gap-5 items-center justify-center">
          <Spinner size="lg" />
          {!isApprovalRequiredCheckout && (
            <p className="text-lg">Generating your tickets</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="sm:min-h-screen my-16 bg-white">
      {successData?.order?.id && (
        <PurchaseTracker
          orderId={successData?.order?.id}
          pixels={successData?.order?.organization?.pixels || {}}
        />
      )}
      <div className="flex items-center gap-10 justify-center bg-white">
        <div className="w-[90%] md:max-w-[440px] mt-7 md:mt-10">
          <OrderConfirmation data={successData} />
        </div>
        <div className="mt-7 md:mt-10 hidden md:flex">
          <Image
            src="/registration-success.png"
            alt="A registration success image"
            width={440}
            height={440}
            className="w-full h-full object-cover"
          />
        </div>
      </div>
      <div className="mt-8 flex-col items-center justify-center gap-5 hidden md:flex">
        <p className="text-gray-700 text-lg sm:text-xl lg:text-[20px] text-center font-normal mb-6 leading-relaxed">
          Download AutoLNK to track the current
          <br className="hidden sm:block" /> status of your registration
        </p>
        <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-2">
          <Link
            href="https://apps.apple.com/in/app/autolnk/id6478376890"
            target="_blank"
            className="w-full sm:w-36"
          >
            <Image
              src={IMAGE_LINKS.DOWNLOAD_ON_APPSTORE}
              alt="AutoLNK Download on App Store"
              width={135}
              height={40}
              className="h-10 w-auto object-contain mx-auto"
              style={{
                imageRendering: "auto",
                shapeRendering: "geometricPrecision",
              }}
              priority
            />
          </Link>
          <Link
            href="https://play.google.com/store/apps/details?id=com.xcelerate.xcelerate"
            target="_blank"
            className="w-full sm:w-36"
          >
            <Image
              src={IMAGE_LINKS.DOWNLOAD_ON_GOOGLE}
              alt="Autolnk Download on Google Play"
              width={135}
              height={40}
              className="h-10 w-auto object-contain mx-auto"
              style={{
                imageRendering: "auto",
                shapeRendering: "geometricPrecision",
              }}
              priority
            />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Success;
