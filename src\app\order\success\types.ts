export interface EventTicketDetails {
  id: string;
  name: string;
  description?: string;
}

export interface VehicleImage {
  id: number;
  image: string;
  tag: string;
  type: number;
}

export interface Vehicle {
  year: number;
  makeName: string;
  customMakeName?: string;
  customModelName?: string;
  modificationText?: string;
  modifications: any[];
  name: string;
  images: VehicleImage[];
}

export interface EventDetails {
  id: string;
  name: string;
  eventName: string;
  eventImage: Array<{
    photo: string;
  }>;
  startTime: string;
  description?: string;
}

export interface ProductVariant {
  id: number;
  sku: string;
  name: string;
  price: string;
  currency: string;
  media: Array<{
    image: string;
    type: string;
  }>;
}

export interface OrderLine {
  id: string;
  type: 'product' | 'event_ticket';
  quantity: number;
  eventTicketDetails?: EventTicketDetails;
  eventDetails?: EventDetails;
  vehicle?: Vehicle;
  variant?: ProductVariant;
  productName: string;
  variantName: string;
  productSku: string;
  totalPrice: {
    amount: string;
    currency: string;
  };
  unitPrice: {
    amount: string;
    currency: string;
  };
  requiresVehicleApproval?: boolean;
}

export interface Customer {
  email: string;
  isGuest: boolean;
  customerMetadata?: {
    teamName?: string;
    socialMedia?: {
      instagram?: string;
    };
    privateMetadata?: {
      location?: string;
      phoneNumber?: string;
    };
  };
}

export interface MoneyField {
  amount: string;
  currency: string;
}

export interface Order {
  organization: any;
  id: string;
  number: number;
  orderReference: string;
  createdAt: string;
  updatedAt: string;
  status: string;
  customer: Customer;
  userEmail: string;
  lines: OrderLine[];
  total: MoneyField;
  subtotal: MoneyField;
  totalEventAmount: MoneyField;
  totalProductAmount: MoneyField;
  totalQuantity: number;
  totalEventTickets: number;
  totalProducts: number;
  customerMetadata?: {
    teamName?: string;
    socialMedia?: {
      instagram?: string;
    };
    privateMetadata?: {
      location?: string;
      phoneNumber?: string;
    };
  };
}

export interface SuccessData {
  confirmationNeeded: boolean;
  confirmationData: any;
  order: Order;
} 