export interface EventTicketDetails {
  id: string;
  name: string;
  description?: string;
}

export interface VehicleImage {
  id: number;
  image: string;
  tag: string;
  type: number;
}

export interface Vehicle {
  year: number;
  makeName: string;
  customMakeName?: string;
  customModelName?: string;
  modificationText?: string;
  modifications: any[];
  name: string;
  images: VehicleImage[];
}

export interface EventDetails {
  id: string;
  name: string;
  eventName: string;
  eventImage: Array<{
    photo: string;
  }>;
  startTime: string;
  description?: string;
}

export interface ProductVariant {
  id: number;
  sku: string;
  name: string;
  price: string;
  currency: string;
  media: Array<{
    image: string;
    type: string;
  }>;
}

export interface OrderLine {
  id: string;
  type?: 'product' | 'event_ticket';
  quantity: number;
  eventTicketDetails?: EventTicketDetails;
  eventDetails?: EventDetails;
  vehicle?: Vehicle;
  variant?: ProductVariant;
  productName: string;
  variantName: string;
  productSku: string;
  totalPrice: {
    amount: string;
    currency: string;
  };
  unitPrice: {
    amount: string;
    currency: string;
  };
  requiresVehicleApproval?: boolean;
  approvalRequired?: boolean;
  selectedVariants?: any[];
  productVariantId: string;
  privateMetadata?: Record<string, any>;
  formResponseData?: any;
  event?: {
    id: string;
    name: string;
    slug: string;
    startDate: string;
    endDate: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    timezone: string;
    status: string;
    organization: {
      slug: string;
      owner: any;
    };
    image: string;
  };
}

export interface Customer {
  email: string;
  isGuest: boolean;
  customerMetadata?: {
    teamName?: string;
    socialMedia?: {
      instagram?: string;
    };
    privateMetadata?: {
      location?: string;
      phoneNumber?: string;
    };
  };
}

export interface MoneyField {
  amount: string;
  currency: string;
}

export interface Order {
  organization: {
    id: string;
    isActive: boolean;
    isVerified: boolean;
    owner: {
      id: string;
      name: string;
      avatar: string;
      username: string;
      bio: string;
      coverPhoto: string;
      firstName: string;
      lastName: string;
      userTimezone: string;
      lastActive: string;
    };
    pixels: {
      ga4: string;
      meta: string;
      snap: string;
      tiktok: string;
    };
    slug: string;
    privateMetadata: Record<string, any>;
  };
  id: string;
  number: number;
  orderReference?: string;
  createdAt: string;
  updatedAt?: string;
  status: string;
  customer?: Customer;
  userEmail?: string;
  lines: OrderLine[];
  total: MoneyField;
  subtotal: MoneyField;
  totalEventAmount?: MoneyField;
  totalProductAmount?: MoneyField;
  totalQuantity?: number;
  totalEventTickets?: number;
  totalProducts?: number;
  processingFee?: MoneyField;
  platformFee?: MoneyField;
  checkoutToken?: string;
  payment?: {
    id: number;
    chargeStatus: string;
    paymentLinkExpiresAt: number;
  };
  linesCount?: number;
  customerMetadata?: {
    teamName?: string;
    socialMedia?: {
      instagram?: string;
    };
    privateMetadata?: {
      location?: string;
      phoneNumber?: string;
    };
  };
}

export interface SuccessData {
  confirmationNeeded: boolean;
  confirmationData: any;
  order: Order;
} 