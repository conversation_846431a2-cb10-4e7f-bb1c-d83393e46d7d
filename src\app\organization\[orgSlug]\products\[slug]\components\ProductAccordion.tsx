import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Product, ProductVariant } from '../../types';
import { ACCORDION_SECTIONS, PRODUCT_ERROR_MESSAGES } from '../../constants';
import parse from "html-react-parser";
import DOMPurify from 'isomorphic-dompurify';

interface ProductAccordionProps {
  product: Product;
  currentVariant: ProductVariant | null;
}

const ProductAccordion: React.FC<ProductAccordionProps> = ({
  product,
  currentVariant,
}) => {
  return (
    <div className="border-t pt-6">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value={ACCORDION_SECTIONS.DETAILS} className="border-b border-gray-200">
          <AccordionTrigger className="text-xl font-semibold text-gray-900 hover:no-underline py-4">
            Details
          </AccordionTrigger>
          <AccordionContent className="text-sm text-gray-600 space-y-2">
            <p><strong>Category:</strong> {product.category?.name}</p>
            <p><strong>Weight:</strong> {product.weight?.value}{product.weight?.unit}</p>
            <p><strong>SKU:</strong> {currentVariant?.sku}</p>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value={ACCORDION_SECTIONS.SHIPPING} className="border-b border-gray-200">
          <AccordionTrigger className="text-xl font-semibold text-gray-900 hover:no-underline py-4">
            Shipping & Returns
          </AccordionTrigger>
          <AccordionContent className="text-sm text-gray-600 space-y-2">
            Shipping costs are calculated at checkout.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value={ACCORDION_SECTIONS.DESCRIPTION} className="border-b-0">
          <AccordionTrigger className="text-xl font-semibold text-gray-900 hover:no-underline py-4">
            Description
          </AccordionTrigger>
          <AccordionContent className="text-sm text-gray-600">
            {product?.descriptionPlaintext ? (
              <div>{parse(DOMPurify.sanitize(product.descriptionPlaintext))}</div>
            ) : (
              <p>{PRODUCT_ERROR_MESSAGES.NO_DESCRIPTION}</p>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default ProductAccordion; 
