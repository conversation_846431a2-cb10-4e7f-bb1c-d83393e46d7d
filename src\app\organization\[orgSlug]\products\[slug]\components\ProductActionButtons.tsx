import React from 'react';
import { Button } from '@/components/ui/button';

interface ProductActionButtonsProps {
  isAddToCartDisabled: boolean;
  isBuyNowDisabled: boolean;
  onAddToCart: () => void;
  onBuyNow: () => void;
}

const ProductActionButtons: React.FC<ProductActionButtonsProps> = ({
  isAddToCartDisabled,
  isBuyNowDisabled,
  onAddToCart,
  onBuyNow,
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
      <Button
        variant="outline"
        size="lg"
        className="w-full h-14 text-base font-medium border-gray-900 text-gray-900 hover:bg-gray-50 rounded-none border-1.5"
        disabled={isAddToCartDisabled}
        onClick={onAddToCart}
      >
        Add to cart
      </Button>
      <Button
        size="lg"
        className="w-full h-14 text-base font-medium bg-black hover:bg-gray-800 text-white rounded-none"
        disabled={isBuyNowDisabled}
        onClick={onBuyNow}
      >
        Buy it now
      </Button>
    </div>
  );
};

export default ProductActionButtons; 
