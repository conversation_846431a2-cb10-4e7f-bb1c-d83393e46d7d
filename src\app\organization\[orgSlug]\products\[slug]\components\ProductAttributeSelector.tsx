import React from 'react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { SelectedAttributes } from '../../types';
import { getColorStyle, isColorAttribute } from '../../utils';
import { clsx } from 'clsx';

interface ProductAttributeSelectorProps {
  attributeTypes: string[];
  selectedAttributes: SelectedAttributes;
  onAttributeChange: (attributeType: string, value: string) => void;
  getAttributeValues: (attributeType: string) => string[];
}

const ProductAttributeSelector: React.FC<ProductAttributeSelectorProps> = ({
  attributeTypes,
  selectedAttributes,
  onAttributeChange,
  getAttributeValues,
}) => {
  if (!attributeTypes.length) return null;

  return (
    <>
      {attributeTypes.map((attributeType) => (
        <div key={attributeType} className="mb-6">
          <Label className="text-sm font-medium text-gray-900 mb-3 block">
            Select {attributeType}
          </Label>
          <RadioGroup
            value={selectedAttributes[attributeType] || ''}
            onValueChange={(value) => onAttributeChange(attributeType, value)}
            className={clsx('flex', {
              'gap-3': isColorAttribute(attributeType),
              'flex-wrap gap-2': !isColorAttribute(attributeType),
            })}
          >
            {getAttributeValues(attributeType).map((value) => {
              const isSelected = selectedAttributes[attributeType] === value;
              const isColor = isColorAttribute(attributeType);
              
              return (
                <div key={value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={value}
                    id={`${attributeType}-${value}`}
                    className="sr-only"
                  />
                  <Label
                    htmlFor={`${attributeType}-${value}`}
                    className={clsx('cursor-pointer transition-all', {
                      'w-10 h-10 rounded-full border-2': isColor,
                      'border-gray-900 ring-2 ring-gray-900 ring-offset-2': isColor && isSelected,
                      'border-gray-300 hover:border-gray-400': isColor && !isSelected,
                      'px-8 py-3 border text-sm font-medium': !isColor,
                      'border-gray-900 border-1.5': !isColor && isSelected,
                      'border-gray-300 hover:border-gray-400 bg-white text-gray-900': !isColor && !isSelected,
                    })}
                    style={
                      isColor
                        ? { backgroundColor: getColorStyle(attributeType, value) }
                        : undefined
                    }
                  >
                    {!isColor && value}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
        </div>
      ))}
    </>
  );
};

export default ProductAttributeSelector;
