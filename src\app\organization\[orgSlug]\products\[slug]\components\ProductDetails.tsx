"use client";

import React from "react";
import parse from "html-react-parser";
import { ProductDetailsProps } from "../../types";
import { PRODUCT_ERROR_MESSAGES } from "../../constants";
import ProductImageGallery from "./ProductImageGallery";
import ProductAttributeSelector from "./ProductAttributeSelector";
import ProductQuantitySelector from "./ProductQuantitySelector";
import ProductActionButtons from "./ProductActionButtons";
import ProductAccordion from "./ProductAccordion";
import { useProductDetails } from "../hooks/useProductDetails";
import DOMPurify from "isomorphic-dompurify";

const ProductDetails: React.FC<ProductDetailsProps> = ({ product }) => {
  const {
    selectedAttributes,
    quantity,
    currentImageIndex,
    stockError,
    attributeTypes,
    currentVariant,
    currentPrice,
    currentStock,
    allImages,
    isAddToCartBtnDisabled,
    isBuyNowBtnDisabled,
    handleAttributeChange,
    handleQuantityChange,
    nextImage,
    prevImage,
    handleAddToCart,
    handleBuyNow,
    getAttributeValues,
    setCurrentImageIndex,
  } = useProductDetails({ product });

  if (!product) {
    return (
      <div className="p-4 border border-gray-200 rounded-lg">
        <p className="text-gray-600">
          {PRODUCT_ERROR_MESSAGES.PRODUCT_NOT_FOUND}
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-[95%] md:max-w-[1250px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16">
        {/* Image Gallery */}
        <ProductImageGallery
          images={allImages}
          currentImageIndex={currentImageIndex}
          productName={product.name}
          onImageSelect={setCurrentImageIndex}
          onNextImage={nextImage}
          onPrevImage={prevImage}
        />

        {/* Product Information */}
        <div className="flex flex-col">
          {/* Title and Rating */}
          <div className="mb-4">
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
              {product.name}
            </h1>
          </div>

          {/* Price */}
          <div className="mb-6">
            <div className="text-2xl font-semibold text-gray-900 mb-2">
              ${currentPrice}
            </div>
          </div>

          {/* Dynamic Attribute Selection */}
          <ProductAttributeSelector
            attributeTypes={attributeTypes}
            selectedAttributes={selectedAttributes}
            onAttributeChange={handleAttributeChange}
            getAttributeValues={getAttributeValues}
          />

          {/* Quantity */}
          <ProductQuantitySelector
            quantity={quantity}
            maxQuantity={currentStock}
            onQuantityChange={handleQuantityChange}
          />

          {/* Stock Error */}
          {stockError && (
            <div className="mb-4 text-red-500 text-sm">{stockError}</div>
          )}

          {/* Action Buttons */}
          <ProductActionButtons
            isAddToCartDisabled={isAddToCartBtnDisabled}
            isBuyNowDisabled={isBuyNowBtnDisabled}
            onAddToCart={handleAddToCart}
            onBuyNow={handleBuyNow}
          />

          {/* Stock Status */}
          {currentStock === 0 && (
            <div className="mb-6 text-red-600 text-sm">
              {PRODUCT_ERROR_MESSAGES.OUT_OF_STOCK}
            </div>
          )}

          {/* Product Description */}
          {product?.descriptionPlaintext && (
            <div className="text-gray-600 mb-6">
              <div>
                {parse(DOMPurify.sanitize(product.descriptionPlaintext))}
              </div>
            </div>
          )}

          {/* Accordion Sections */}
          <ProductAccordion product={product} currentVariant={currentVariant} />
        </div>
      </div>
    </div>
  );
};

export default ProductDetails;
