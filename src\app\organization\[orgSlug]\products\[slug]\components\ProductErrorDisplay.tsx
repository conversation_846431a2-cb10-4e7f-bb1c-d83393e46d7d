import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, Search, ArrowLeft, RefreshCw } from "lucide-react";
import Link from "next/link";
import { safeReload } from "@/lib/utils/safeReload";

interface ProductErrorDisplayProps {
  error?: string;
  status?: number;
  orgSlug: string;
}

const ProductErrorDisplay: React.FC<ProductErrorDisplayProps> = ({
  error = "An unexpected error occurred",
  status,
  orgSlug,
}) => {
  const is404 = status === 404;

  const handleTryAgain = () => {
    const reloadSuccess = safeReload(
      "product-error-retry",
      `/organization/${orgSlug}`
    );
    if (!reloadSuccess) {
      // If reload is blocked, redirect to organization page as fallback
      window.location.href = `/organization/${orgSlug}`;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[500px] px-4 text-center">
      <div className="max-w-md mx-auto">
        <div className="mb-8">
          {is404 ? (
            <Search className="w-20 h-20 text-gray-400 mx-auto" />
          ) : (
            <AlertTriangle className="w-20 h-20 text-red-500 mx-auto" />
          )}
        </div>

        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {is404 ? "Product Not Found" : "Something Went Wrong"}
        </h1>

        <p className="text-gray-600 mb-8 leading-relaxed">
          {is404
            ? "We couldn't find the product you're looking for. It might have been moved, deleted, or the link might be incorrect."
            : "We're experiencing some technical difficulties. Please try again in a moment."}
        </p>

        {!is404 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <p className="text-red-700 text-sm font-medium">Error: {error}</p>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href={`/`}>
            <Button
              variant="outline"
              className="flex items-center gap-2 px-6 py-3 w-full sm:w-auto"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </Button>
          </Link>

          {!is404 && (
            <Button
              onClick={handleTryAgain}
              className="flex items-center gap-2 px-6 py-3 w-full sm:w-auto"
            >
              <RefreshCw className="w-4 h-4" />
              Try Again
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductErrorDisplay;
