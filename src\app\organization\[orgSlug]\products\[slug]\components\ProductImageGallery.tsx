import React from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { ProductMedia } from '../../types';
import { PRODUCT_ERROR_MESSAGES } from '../../constants';
import { clsx } from 'clsx';

interface ProductImageGalleryProps {
  images: ProductMedia[];
  currentImageIndex: number;
  productName: string;
  onImageSelect: (index: number) => void;
  onNextImage: () => void;
  onPrevImage: () => void;
}

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  images,
  currentImageIndex,
  productName,
  onImageSelect,
  onNextImage,
  onPrevImage,
}) => {

  return (
    <div className="flex gap-4 flex-col-reverse md:flex-row">
      <div className="flex md:flex-col flex-row gap-2 md:w-20 md:flex-shrink-0 overflow-x-auto md:overflow-x-visible py-2 md:py-0">
        {images.map((image, index) => {
          const isSelected = currentImageIndex === index;
          
          return (
            <button
              key={image.id}
              onClick={() => onImageSelect(index)}
              className={clsx(
                'relative aspect-square md:w-auto w-20 h-20 flex-shrink-0 bg-gray-100 overflow-hidden border-2 transition-colors',
                {
                  'border-gray-900': isSelected,
                  'border-transparent hover:border-gray-300': !isSelected,
                }
              )}
            >
              <Image
                src={image.image}
                alt={image.alt || productName}
                fill
                className="object-cover"
              />
            </button>
          );
        })}
      </div>

      {/* Main Image */}
      <div className="relative bg-gray-100 overflow-hidden w-full h-[500px] md:h-[600px]">
        {images.length > 0 ? (
          <>
            <Image
              src={images[currentImageIndex]?.image}
              alt={images[currentImageIndex]?.alt || productName}
              fill
              className="object-cover"
              priority
            />
            {images.length > 1 && (
              <>
                <button
                  onClick={onPrevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-colors"
                  aria-label="Previous image"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <button
                  onClick={onNextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-colors"
                  aria-label="Next image"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </>
            )}
          </>
        ) : (
          <div className="flex items-center justify-center h-full">
            <span className="text-gray-400">{PRODUCT_ERROR_MESSAGES.NO_IMAGE_AVAILABLE}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductImageGallery;
