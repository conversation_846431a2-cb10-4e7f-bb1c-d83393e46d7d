import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Minus, Plus } from 'lucide-react';
import { formatQuantity } from '../../utils';

interface ProductQuantitySelectorProps {
  quantity: number;
  maxQuantity: number;
  onQuantityChange: (change: number) => void;
}

const ProductQuantitySelector: React.FC<ProductQuantitySelectorProps> = ({
  quantity,
  maxQuantity,
  onQuantityChange,
}) => {
  return (
    <div className="mb-6">
      <Label className="text-sm font-medium text-gray-900 mb-3 block">
        Quantity
      </Label>
      <div className="flex items-center gap-4 border border-gray-300 max-w-fit">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onQuantityChange(-1)}
          disabled={quantity <= 1}
          className="h-10 w-10"
          aria-label="Decrease quantity"
        >
          <Minus className="w-4 h-4" />
        </Button>
        <span className="text-lg font-medium min-w-[3rem] text-center">
          {formatQuantity(quantity)}
        </span>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onQuantityChange(1)}
          disabled={quantity >= maxQuantity}
          className="h-10 w-10"
          aria-label="Increase quantity"
        >
          <Plus className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default ProductQuantitySelector;
 