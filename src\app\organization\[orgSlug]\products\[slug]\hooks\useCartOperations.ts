import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { Product, ProductVariant, LocalVariants } from '../../types';
import { useAddToCartMutation } from '@/lib/redux/slices/cart/cartApi';
import { useSessionData } from '@/lib/hooks/useSession';
import { CART_ERRORS, CART_STORAGE_KEYS } from '@/lib/utils/constants';
import { handleInvalidCartToken } from '@/app/cart/utils/cartUtils';
import { PRODUCT_ERROR_MESSAGES, PRODUCT_SUCCESS_MESSAGES } from '../../constants';
import { safeJsonParse } from '../../utils';

interface UseCartOperationsProps {
  product: Product;
  currentVariant: ProductVariant | null;
  quantity: number;
}

interface UseCartOperationsReturn {
  isBuyingNow: boolean;
  isAddingToCart: boolean;
  handleAddToCart: () => Promise<void>;
  handleBuyNow: () => Promise<void>;
}

export const useCartOperations = ({ 
  product, 
  currentVariant, 
  quantity 
}: UseCartOperationsProps): UseCartOperationsReturn => {
  const [isBuyingNow, setIsBuyingNow] = useState<boolean>(false);

  const { data: sessionData } = useSessionData();
  const [addToCart, { isLoading: isAddingToCart }] = useAddToCartMutation();
  const router = useRouter();

  const addProductToCart = useCallback(async () => {
    const variant = currentVariant;
    if (!variant) return null;

    // Check current inventory before proceeding
    if ((variant.stock?.quantity || 0) < quantity) {
      toast.error(PRODUCT_ERROR_MESSAGES.STOCK_REDUCE_QUANTITY(variant.stock?.quantity || 0));
      return null;
    }

    let response;
    try {
      response = await addToCart({
        type: "product",
        id: variant.id,
        quantity,
      });
      
      if (
        response?.error?.data?.error === CART_ERRORS.INVALID_CART_TOKEN ||
        response?.error?.status === CART_ERRORS.PARSING_ERROR
      ) {
        handleInvalidCartToken();
        return addProductToCart();
      }
    } catch (error: any) {
      throw error;
    }

    // Store variant info in localStorage with safe JSON parsing
    try {
      const getLocalVariants = localStorage.getItem(CART_STORAGE_KEYS.CART_VARIANTS);
      const localVariants: LocalVariants = safeJsonParse(getLocalVariants, {});

      localVariants[variant.id] = {
        id: variant.id,
        image: variant.media?.[0]?.image ?? product?.media?.[0]?.image,
      };

      localStorage.setItem(
        CART_STORAGE_KEYS.CART_VARIANTS,
        JSON.stringify(localVariants)
      );

      if (!sessionData?.user) {
        const checkoutToken = response?.data?.checkout?.token;
        if (checkoutToken) {
          localStorage.setItem(CART_STORAGE_KEYS.CART_TOKEN, checkoutToken);
        }
      }
    } catch (error) {
      console.error('Error storing cart data in localStorage:', error);
    }

    if (response?.error) {
      toast.error(PRODUCT_ERROR_MESSAGES.ADD_TO_CART_FAILED);
      return null;
    }

    return response;
  }, [currentVariant, quantity, addToCart, sessionData?.user, product?.media]);

  const handleAddToCart = useCallback(async () => {
    try {
      const response = await addProductToCart();
      if (response) {
        toast.success(PRODUCT_SUCCESS_MESSAGES.ADDED_TO_CART);
        router.push('/cart');
      }
    } catch (error) {
      console.error("Failed to add to cart:", error);
      toast.error(PRODUCT_ERROR_MESSAGES.GENERAL_ERROR);
    }
  }, [addProductToCart, router]);

  const handleBuyNow = useCallback(async () => {
    setIsBuyingNow(true);
    try {
      const response = await addProductToCart();
      if (response) {
        toast.success(PRODUCT_SUCCESS_MESSAGES.BUY_NOW_SUCCESS);
        router.push('/checkout');
      }
    } catch (error) {
      console.error("Failed to buy now:", error);
      toast.error(PRODUCT_ERROR_MESSAGES.GENERAL_ERROR);
    } finally {
      setIsBuyingNow(false);
    }
  }, [addProductToCart, router]);

  return {
    isBuyingNow,
    isAddingToCart,
    handleAddToCart,
    handleBuyNow,
  };
};
 