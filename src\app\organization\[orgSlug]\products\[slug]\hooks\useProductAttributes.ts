import { useState, useEffect, useCallback } from 'react';
import { Product, ProductVariant, SelectedAttributes } from '../../types';

interface UseProductAttributesProps {
  product: Product;
}

interface UseProductAttributesReturn {
  selectedAttributes: SelectedAttributes;
  attributeTypes: string[];
  currentVariant: ProductVariant | null;
  handleAttributeChange: (attributeType: string, value: string) => void;
  getAttributeValues: (attributeType: string) => string[];
}

export const useProductAttributes = ({ product }: UseProductAttributesProps): UseProductAttributesReturn => {
  const [selectedAttributes, setSelectedAttributes] = useState<SelectedAttributes>({});

  // Get unique attribute types from variants
  const getAttributeTypes = useCallback((): string[] => {
    if (!product?.variants?.length) return [];
    
    const attributeTypes = new Set<string>();
    product.variants.forEach((variant: ProductVariant) => {
      variant.attributes?.forEach((attr) => {
        attributeTypes.add(attr.attribute.name);
      });
    });
    return Array.from(attributeTypes);
  }, [product?.variants]);

  // Get available values for a specific attribute type
  const getAttributeValues = useCallback((attributeType: string): string[] => {
    if (!product?.variants?.length) return [];
    
    const values = new Set<string>();
    product.variants.forEach((variant: ProductVariant) => {
      variant.attributes?.forEach((attr) => {
        if (attr.attribute.name === attributeType) {
          attr.values?.forEach((value) => {
            values.add(value.value);
          });
        }
      });
    });
    return Array.from(values);
  }, [product?.variants]);

  // Find matching variant based on selected attributes
  const getMatchingVariant = useCallback((): ProductVariant | null => {
    if (!product?.variants?.length) return null;

    // Get all attribute types that have been selected
    const selectedAttributeTypes = Object.keys(selectedAttributes);
    
    // If no attributes are selected, return the default variant or first variant
    if (selectedAttributeTypes.length === 0) {
      return product.variants.find(variant => variant.attributes?.length === 0) || product.variants[0] || null;
    }

    // Find variant that matches all selected attributes
    const matchingVariant = product.variants.find((variant: ProductVariant) => {
      // Skip variants with no attributes if we have selected attributes
      if (!variant.attributes || variant.attributes.length === 0) {
        return false;
      }

      // Check if this variant has all the required attributes and they match
      return selectedAttributeTypes.every(attributeType => {
        const selectedValue = selectedAttributes[attributeType];
        
        // Find if this variant has this attribute type with the selected value
        return variant.attributes.some(attr => 
          attr.attribute.name === attributeType && 
          attr.values?.some(value => value.value === selectedValue)
        );
      });
    }) || null;
    
    return matchingVariant;
  }, [product?.variants, selectedAttributes]);

  // Initialize default attributes when component mounts or product changes
  useEffect(() => {
    if (!product?.variants?.length) return;
    
    const defaultAttributes: SelectedAttributes = {};
    const attributeTypes = getAttributeTypes();
    
    attributeTypes.forEach((type) => {
      const values = getAttributeValues(type);
      if (values.length > 0) {
        defaultAttributes[type] = values[0];
      }
    });
    
    setSelectedAttributes(defaultAttributes);
  }, [product, getAttributeTypes, getAttributeValues]);

  const handleAttributeChange = useCallback((attributeType: string, value: string) => {
    setSelectedAttributes(prev => ({
      ...prev,
      [attributeType]: value
    }));
  }, []);

  // Computed values
  const attributeTypes = getAttributeTypes();
  const currentVariant = getMatchingVariant();

  return {
    selectedAttributes,
    attributeTypes,
    currentVariant,
    handleAttributeChange,
    getAttributeValues,
  };
}; 
