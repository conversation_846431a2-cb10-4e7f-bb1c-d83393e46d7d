import { useEffect } from 'react';
import { Product } from '../../types';
import { useProductAttributes } from './useProductAttributes';
import { useProductStock } from './useProductStock';
import { useProductImages } from './useProductImages';
import { useCartOperations } from './useCartOperations';

interface UseProductDetailsProps {
  product: Product;
}

interface UseProductDetailsReturn {
  // State
  selectedAttributes: any;
  quantity: number;
  currentImageIndex: number;
  stockError: string;
  isBuyingNow: boolean;
  
  // Computed values
  attributeTypes: string[];
  currentVariant: any;
  currentPrice: string;
  currentStock: number;
  allImages: any[];
  isAddToCartBtnDisabled: boolean;
  isBuyNowBtnDisabled: boolean;
  
  // Actions
  handleAttributeChange: (attributeType: string, value: string) => void;
  handleQuantityChange: (change: number) => void;
  nextImage: () => void;
  prevImage: () => void;
  handleAddToCart: () => Promise<void>;
  handleBuyNow: () => Promise<void>;
  getAttributeValues: (attributeType: string) => string[];
  setCurrentImageIndex: (index: number) => void;
}

export const useProductDetails = ({ product }: UseProductDetailsProps): UseProductDetailsReturn => {
  // Use the smaller, focused hooks
  const {
    selectedAttributes,
    attributeTypes,
    currentVariant,
    handleAttributeChange: originalHandleAttributeChange,
    getAttributeValues,
  } = useProductAttributes({ product });

  const {
    quantity,
    stockError,
    handleQuantityChange,
    resetQuantity,
  } = useProductStock({ currentVariant });

  const {
    currentImageIndex,
    allImages,
    nextImage,
    prevImage,
    setCurrentImageIndex,
    resetImageIndex,
  } = useProductImages({ product, currentVariant });

  const {
    isBuyingNow,
    isAddingToCart,
    handleAddToCart,
    handleBuyNow,
  } = useCartOperations({ product, currentVariant, quantity });

  // Enhanced attribute change handler that also resets quantity and image index
  const handleAttributeChange = (attributeType: string, value: string) => {
    originalHandleAttributeChange(attributeType, value);
    resetQuantity();
    resetImageIndex();
  };

  // Computed values
  const currentPrice = currentVariant?.price || product?.price || '0';
  const currentStock = currentVariant?.stock?.quantity || product?.stock?.quantity || 0;

  const isAddToCartBtnDisabled = !currentVariant || stockError !== "" || currentStock === 0 || isAddingToCart;
  const isBuyNowBtnDisabled = !currentVariant || stockError !== "" || currentStock === 0 || isBuyingNow || isAddingToCart;

  return {
    // State
    selectedAttributes,
    quantity,
    currentImageIndex,
    stockError,
    isBuyingNow,
    
    // Computed values
    attributeTypes,
    currentVariant,
    currentPrice,
    currentStock,
    allImages,
    isAddToCartBtnDisabled,
    isBuyNowBtnDisabled,
    
    // Actions
    handleAttributeChange,
    handleQuantityChange,
    nextImage,
    prevImage,
    handleAddToCart,
    handleBuyNow,
    getAttributeValues,
    setCurrentImageIndex,
  };
}; 
