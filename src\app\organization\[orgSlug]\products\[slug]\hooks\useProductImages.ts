import { useState, useCallback } from 'react';
import { Product, ProductVariant } from '../../types';

interface UseProductImagesProps {
  product: Product;
  currentVariant: ProductVariant | null;
}

interface UseProductImagesReturn {
  currentImageIndex: number;
  allImages: any[];
  nextImage: () => void;
  prevImage: () => void;
  setCurrentImageIndex: (index: number) => void;
  resetImageIndex: () => void;
}

export const useProductImages = ({ product, currentVariant }: UseProductImagesProps): UseProductImagesReturn => {
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);

  // Get all images (product media + variant media)
  const allImages = [
    ...(product?.media?.filter(m => !m.isVariantImage) || []),
    ...(currentVariant?.media || [])
  ].sort((a, b) => a.sortOrder - b.sortOrder);

  const nextImage = useCallback(() => {
    setCurrentImageIndex((prev) => 
      prev === allImages.length - 1 ? 0 : prev + 1
    );
  }, [allImages.length]);

  const prevImage = useCallback(() => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? allImages.length - 1 : prev - 1
    );
  }, [allImages.length]);

  const resetImageIndex = useCallback(() => {
    setCurrentImageIndex(0);
  }, []);

  return {
    currentImageIndex,
    allImages,
    nextImage,
    prevImage,
    setCurrentImageIndex,
    resetImageIndex,
  };
};
 