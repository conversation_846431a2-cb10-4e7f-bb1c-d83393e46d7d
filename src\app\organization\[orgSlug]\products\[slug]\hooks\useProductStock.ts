import { useState, useEffect, useCallback } from 'react';
import { ProductVariant } from '../../types';
import { UI_CONSTANTS, PRODUCT_ERROR_MESSAGES } from '../../constants';

interface UseProductStockProps {
  currentVariant: ProductVariant | null;
}

interface UseProductStockReturn {
  quantity: number;
  stockError: string;
  handleQuantityChange: (change: number) => void;
  resetQuantity: () => void;
}

export const useProductStock = ({ currentVariant }: UseProductStockProps): UseProductStockReturn => {
  const [quantity, setQuantity] = useState<number>(UI_CONSTANTS.DEFAULT_QUANTITY);
  const [stockError, setStockError] = useState<string>("");

  // Check stock availability and update error message
  const checkStock = useCallback((variant: ProductVariant | null, requestedQuantity: number): boolean => {
    if (!variant) return false;

    const availableStock = variant.stock?.quantity || 0;
    if (availableStock < requestedQuantity) {
      setStockError(PRODUCT_ERROR_MESSAGES.STOCK_INSUFFICIENT(availableStock));
      return false;
    }
    setStockError("");
    return true;
  }, []);

  // Check stock whenever quantity or current variant changes
  useEffect(() => {
    if (currentVariant) {
      checkStock(currentVariant, quantity);
    }
  }, [quantity, currentVariant, checkStock]);

  const handleQuantityChange = useCallback((change: number) => {
    const newQuantity = quantity + change;
    if (currentVariant) {
      const availableStock = currentVariant.stock?.quantity || 0;
      if (newQuantity >= UI_CONSTANTS.MIN_QUANTITY && newQuantity <= availableStock) {
        setQuantity(newQuantity);
      }
    }
  }, [quantity, currentVariant]);

  const resetQuantity = useCallback(() => {
    setQuantity(UI_CONSTANTS.DEFAULT_QUANTITY);
    setStockError("");
  }, []);

  return {
    quantity,
    stockError,
    handleQuantityChange,
    resetQuantity,
  };
};
