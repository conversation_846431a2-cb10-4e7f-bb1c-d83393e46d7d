import React from 'react';

const ProductDetailsSkeleton = () => {
  return (
    <div className="max-w-[95%] mt-16 md:max-w-[80%] mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-pulse">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16">
        {/* Image Gallery Skeleton */}
        <div className="flex gap-4 flex-col-reverse md:flex-row">
          {/* Thumbnail Container */}
          <div className="flex md:flex-col flex-row gap-2 md:w-20 md:flex-shrink-0 overflow-x-auto md:overflow-x-visible py-2 md:py-0">
            {[...Array(4)].map((_, index) => (
              <div
                key={index}
                className="relative aspect-square md:w-auto w-20 h-20 flex-shrink-0 bg-gray-200 rounded"
              />
            ))}
          </div>

          {/* Main Image Skeleton */}
          <div className="relative bg-gray-200 overflow-hidden w-full h-[500px] md:h-[600px] rounded" />
        </div>

        {/* Product Information Skeleton */}
        <div className="flex flex-col">
          {/* Title Skeleton */}
          <div className="mb-4">
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-2" />
            <div className="h-4 bg-gray-200 rounded w-1/2" />
          </div>

          {/* Price Skeleton */}
          <div className="mb-6">
            <div className="h-8 bg-gray-200 rounded w-1/4" />
          </div>

          {/* Attribute Selection Skeleton */}
          <div className="mb-6">
            <div className="h-4 bg-gray-200 rounded w-1/3 mb-3" />
            <div className="flex gap-2">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="w-10 h-10 bg-gray-200 rounded-full" />
              ))}
            </div>
          </div>

          {/* Size Selection Skeleton */}
          <div className="mb-6">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-3" />
            <div className="flex gap-2">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="w-16 h-12 bg-gray-200 rounded" />
              ))}
            </div>
          </div>

          {/* Quantity Skeleton */}
          <div className="mb-6">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-3" />
            <div className="flex items-center gap-4 border border-gray-200 max-w-fit">
              <div className="h-10 w-10 bg-gray-200 rounded" />
              <div className="h-6 w-12 bg-gray-200 rounded" />
              <div className="h-10 w-10 bg-gray-200 rounded" />
            </div>
          </div>

          {/* Action Buttons Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            <div className="h-14 bg-gray-200 rounded" />
            <div className="h-14 bg-gray-200 rounded" />
          </div>

          {/* Description Skeleton */}
          <div className="mb-6">
            <div className="h-4 bg-gray-200 rounded w-full mb-2" />
            <div className="h-4 bg-gray-200 rounded w-5/6 mb-2" />
            <div className="h-4 bg-gray-200 rounded w-4/6" />
          </div>

          {/* Accordion Skeleton */}
          <div className="border-t pt-6">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="border-b border-gray-200 py-4">
                <div className="h-6 bg-gray-200 rounded w-1/3" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsSkeleton; 