import React from 'react';
import { Metadata } from 'next';
import ProductDetails from './components/ProductDetails';
import ProductErrorDisplay from './components/ProductErrorDisplay';
import { getProductDetails } from '@/lib/actions/products';

interface PageProps {
  params: { slug: string; orgSlug: string };
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug, orgSlug } = params;
  const details = await getProductDetails(slug, orgSlug);
  
  const defaultMetadata: Metadata = {
    title: 'Product Not Found | AutoLNK',
    description: 'The requested product could not be found | AutoLNK',
    robots: 'noindex, nofollow',
  };
  
  if ('error' in details) {
    return defaultMetadata;
  }
  
  const { data: product } = details;
  
  if (!product) {
    return defaultMetadata;
  }
  
  const firstImage = product.media?.[0]?.image || product.variants?.[0]?.media?.[0]?.image;
  
  const description = product.descriptionPlaintext 
    ? product?.descriptionPlaintext?.slice(0, 160)?.replace(/<[^>]*>/g, '') 
    : `${product?.name} - Available for purchase`;
  
  return {
    title: `${product.name} | AutoLNK`,
    description,
    keywords: [
      product?.name,
      product?.category?.name,
      product?.productType?.name,
      'events',
      'products',
      ...(product?.tags?.map(tag => tag.name) || [])
    ].filter(Boolean).join(', '),
    openGraph: {
      title: product?.name,
      description,
      type: 'website',
      ...(firstImage && {
        images: [
          {
            url: firstImage,
            alt: product?.name,
            width: 800,
            height: 600,
          }
        ]
      }),
      siteName: 'AutoLNK',
    },
    twitter: {
      card: 'summary_large_image',
      title: product?.name,
      description,
      ...(firstImage && {
        images: [firstImage]
      }),
    },
    other: {
      "Cache-Control": "public, max-age=3600, stale-while-revalidate=86400",
      "link": [
        `rel=preload; as=image; href=${firstImage}; fetchpriority=high`,
      ],
    },
    alternates: {
      canonical: `/organization/${orgSlug}/products/${slug}`,
    },
  };
}

const Page = async ({ params }: PageProps) => {
  const { slug, orgSlug } = params;
  const details = await getProductDetails(slug, orgSlug);
  
  if ('error' in details) {
    return (
      <div className="mt-20 md:mt-12 min-h-[calc(100vh-100px)] md:mb-20">
        <ProductErrorDisplay 
          error={details.error}
          status={details.status}
          orgSlug={orgSlug}
        />
      </div>
    );
  }
  
  const { data: product } = details;
  return (
    <div className='mt-20 md:mt-12 min-h-[calc(100vh-100px)] md:mb-20'>
      <ProductDetails product={product} />
    </div>
  );
};

export default Page;
