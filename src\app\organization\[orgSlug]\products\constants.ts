// UI Constants
export const UI_CONSTANTS = {
  DEFAULT_QUANTITY: 1,
  MIN_QUANTITY: 1,
  IMAGE_HEIGHTS: {
    MOBILE: 500,
    DESKTOP: 600,
  },
  THUMBNAIL_SIZE: {
    WIDTH: 20,
    HEIGHT: 20,
  },
} as const;

// Error Messages
export const PRODUCT_ERROR_MESSAGES = {
  PRODUCT_NOT_FOUND: 'Product not found',
  NO_IMAGE_AVAILABLE: 'No image available',
  STOCK_INSUFFICIENT: (available: number) => `Only ${available} items available`,
  STOCK_REDUCE_QUANTITY: (available: number) => 
    `Only ${available} items available. Please reduce quantity.`,
  ADD_TO_CART_FAILED: 'Failed to add item to cart. Please try again later.',
  GENERAL_ERROR: 'Something went wrong. Please try again later.',
  OUT_OF_STOCK: 'Out of stock',
  NO_DESCRIPTION: 'No description available',
} as const;

// Success Messages
export const PRODUCT_SUCCESS_MESSAGES = {
  ADDED_TO_CART: 'Item added to cart successfully!',
  BUY_NOW_SUCCESS: 'Item added to cart! Redirecting to checkout...',
} as const;

// Attribute Types
export const ATTRIBUTE_TYPES = {
  COLOR: 'color',
} as const;

// Accordion sections
export const ACCORDION_SECTIONS = {
  DETAILS: 'details',
  SHIPPING: 'shipping',
  DESCRIPTION: 'description',
} as const;
 