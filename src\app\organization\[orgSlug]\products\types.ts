export interface ProductAttributeValue {
  id: number;
  name: string;
  slug: string;
  value: string;
  fileUrl: string | null;
  contentType: string | null;
  richText: string | null;
  plainText: string | null;
  boolean: boolean | null;
  dateTime: string | null;
}

export interface ProductAttribute {
  id: number;
  name: string;
  slug: string;
  inputType: string;
}

export interface ProductVariantAttribute {
  attribute: ProductAttribute;
  values: ProductAttributeValue[];
}

export interface ProductStock {
  quantity: number;
  quantityAllocated: number;
}

export interface ProductMedia {
  id: number;
  alt: string;
  type: string;
  sortOrder: number;
  image: string;
  isVariantImage: boolean;
  variantId: number | null;
}

export interface ProductVariantMedia extends Omit<ProductMedia, 'variantId'> {
  variantId: number;
}

export interface ProductWeight {
  value: number;
  unit: string;
}

export interface ProductType {
  id: number;
  name: string;
}

export interface ProductCategory {
  id: number;
  name: string;
}

export interface ProductDetails {
  id: number;
  name: string;
  slug: string;
  defaultVariantPrice: string | null;
}

export interface ProductVariant {
  id: number;
  sku: string;
  name: string;
  weight: any;
  attributes: ProductVariantAttribute[];
  media: ProductVariantMedia[];
  createdAt: string;
  updatedAt: string;
  price: string;
  currency: string;
  stock: ProductStock;
  productDetails: ProductDetails;
}

export interface Product {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  descriptionPlaintext: string;
  productType: ProductType;
  category: ProductCategory;
  weight: ProductWeight;
  defaultVariant: any;
  status: string;
  defaultVariantPrice: string | null;
  createdAt: string;
  updatedAt: string;
  variants: ProductVariant[];
  media: ProductMedia[];
  metadata: Record<string, any>;
  price: string;
  stock: ProductStock;
  isShippingRequired: boolean;
  tags: any[];
}

export interface ProductDetailsProps {
  product: Product;
}

export interface SelectedAttributes {
  [key: string]: string;
}

export interface LocalVariant {
  id: number;
  image?: string;
}

export interface LocalVariants {
  [key: number]: LocalVariant;
}
 