import tinycolor from "tinycolor2";
import { ATTRIBUTE_TYPES } from "./constants";

/**
 * Get color style for color attributes using tinycolor2
 * @param attributeType - The type of attribute
 * @param value - The attribute value
 * @returns The color hex code or undefined
 */
export const getColorStyle = (
  attributeType: string,
  value: string
): string | undefined => {
  try {
    if (attributeType.toLowerCase() === ATTRIBUTE_TYPES.COLOR) {
      const color = tinycolor(value);

      // If tinycolor successfully parsed the color, return its hex value
      if (color.isValid()) {
        return color.toHexString();
      }

      // If tinycolor couldn't parse it, return undefined (will show as text)
      return undefined;
    }
    return undefined;
  } catch (error) {
    console.error("Error getting color style:", error);
    return undefined;
  }
};

/**
 * Format quantity with leading zeros
 * @param quantity - The quantity number
 * @returns Formatted quantity string
 */
export const formatQuantity = (quantity: number): string => {
  try {
    return quantity.toString().padStart(2, "0");
  } catch (error) {
    console.error("Error formatting quantity:", error);
    return "00";
  }
};

/**
 * Check if attribute is color type
 * @param attributeType - The attribute type to check
 * @returns Boolean indicating if it's a color attribute
 */
export const isColorAttribute = (attributeType: string): boolean => {
  try {
    return attributeType.toLowerCase() === ATTRIBUTE_TYPES.COLOR;
  } catch (error) {
    console.error("Error checking if attribute is color:", error);
    return false;
  }
};

/**
 * Safely parse JSON with fallback
 * @param jsonString - The JSON string to parse
 * @param fallback - Fallback value if parsing fails
 * @returns Parsed object or fallback
 */
export const safeJsonParse = <T>(jsonString: string | null, fallback: T): T => {
  if (!jsonString) return fallback;

  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error("Error parsing JSON:", error);
    return fallback;
  }
};
