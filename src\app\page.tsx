import type { Metadata } from "next";
import dynamic from "next/dynamic";
import Hero from "@/components/HomeSections/Hero";
import Features from "@/components/HomeSections/Features";
import Comparison from "@/components/HomeSections/Comparison";
import BentoGrid from "@/components/HomeSections/BentoGrid";
import Hosts from "@/components/HomeSections/Hosts";

// Dynamic import TopNavBar to reduce initial bundle size
const TopNavBar = dynamic(() => import("@/features/TopNavBar/TopNavBar"), {
  ssr: false,
  loading: () => (
    <div className="fixed top-0 left-0 right-0 h-16 bg-white/90 backdrop-blur-sm border-b border-gray-200 z-50">
      <div className="max-w-7xl mx-auto px-4 h-full flex items-center justify-between">
        <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
        <div className="flex gap-4">
          <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  ),
});

export const metadata: Metadata = {
  alternates: {
    canonical: "/", // Explicit canonical for homepage
  },
};

const Home = async () => {
  return (
    <TopNavBar>
      <div className="bg-[#ffffff] pt-20 md:pt-[60px] flex flex-col min-h-screen items-center px-1.5 md:px-0">
        <Hero />
        <BentoGrid />
        <Features />
        <Hosts />
        <Comparison />
      </div>
    </TopNavBar>
  );
};

export default Home;
