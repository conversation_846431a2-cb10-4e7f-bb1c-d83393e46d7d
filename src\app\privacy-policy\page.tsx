import React from "react";
import fs from "fs";
import path from "path";

// Function to read the HTML file content
const getPrivacyPolicyHtml = () => {
  const filePath = path.join(process.cwd(), "public", "privacy-policy.html");
  return fs.readFileSync(filePath, "utf8");
};

const Page = () => {
  const privacyPolicyHtml = getPrivacyPolicyHtml();
  return (
    <div className="relative w-full mt-24 md:mt-16 lg:mt-20 px-10 pb-20 xl:max-w-[1200px] m-auto min-h-screen">
      {/* Render the HTML content */}
      <div
        dangerouslySetInnerHTML={{ __html: privacyPolicyHtml }}
      />
    </div>
  );
};

export default Page;
