import React from "react";
import { RadioGroupItem } from "@/components/ui/radio-group";
import { Check } from "lucide-react";
import { AccountType } from "../hooks/useAccountTypeSelection";

interface AccountTypeOptionProps {
  id: AccountType;
  title: string;
  description: string;
  isSelected: boolean;
  onSelect?: () => void;
}

export const AccountTypeOption: React.FC<AccountTypeOptionProps> = ({
  id,
  title,
  description,
  isSelected,
  onSelect,
}) => {
  return (
    <div
      className={`relative border rounded-lg p-3 md:p-4 ${
        isSelected ? "border-blue-500" : "border-gray-700"
      } cursor-pointer transition-colors hover:border-blue-400`}
      onClick={() => onSelect?.()}
    >
      {isSelected && (
        <div className="absolute top-3 md:top-4 right-3 md:right-4 bg-blue-500 rounded-full p-0.5 md:p-1">
          <Check className="h-3 w-3 md:h-4 md:w-4 text-white" />
        </div>
      )}
      <div className="flex items-start">
        <div className="sr-only">
          <RadioGroupItem value={id} id={id} />
        </div>
        <div>
          <label
            htmlFor={id}
            className="text-white text-sm md:text-base font-medium cursor-pointer block"
          >
            {title}
          </label>
          <p className="text-gray-400 text-xs md:text-sm mt-1">{description}</p>
        </div>
      </div>
    </div>
  );
};
