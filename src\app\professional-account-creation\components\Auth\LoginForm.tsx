import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "../Button";
import { loginFormSchema, LoginFormValues } from "./authSchemas";
import { useLoginErrorReporting } from "../../hooks/useLoginErrorReporting";

type LoginFormProps = {
  onSubmit: (values: LoginFormValues) => void;
  onToggle: () => void;
  isSubmitting: boolean;
};

export const LoginForm: React.FC<LoginFormProps> = ({
  onSubmit,
  onToggle,
  isSubmitting,
}) => {
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  useLoginErrorReporting({ form });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-3 md:space-y-4 max-w-[365px]"
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Email or Username"
                  className="border border-gray-600 rounded-md bg-black text-white h-10 md:h-12 focus:border-gray-400 focus:ring-0 text-sm md:text-base"
                  {...field}
                  onChange={(e) => {
                    e.target.value = e.target.value.toLowerCase();
                    field.onChange(e);
                  }}
                />
              </FormControl>
              <FormMessage className="text-red-500 text-xs md:text-sm" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="password"
                  placeholder="Enter your password"
                  className="border border-gray-600 rounded-md bg-black text-white h-10 md:h-12 focus:border-gray-400 focus:ring-0 text-sm md:text-base"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-red-500 text-xs md:text-sm" />
              <div className="text-[#FFFFFF] font-medium text-right text-sm cursor-pointer">
                Forgot password?
              </div>
            </FormItem>
          )}
        />
        <div className="!mt-8">
          <Button
            className="w-full"
            text="Login"
            type="submit"
            disable={isSubmitting}
          />
        </div>
        <div className="text-white text-center text-sm">
          Don't have an account?{" "}
          <span
            onClick={onToggle}
            className="text-blue-500 font-semibold cursor-pointer"
          >
            Sign up
          </span>
        </div>
      </form>
    </Form>
  );
};
