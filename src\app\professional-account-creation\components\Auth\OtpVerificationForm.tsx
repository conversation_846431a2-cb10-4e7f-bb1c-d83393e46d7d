import React, { useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "../Button";
import { useOtpErrorReporting } from "../../hooks/useOtpErrorReporting";

type OtpVerificationFormProps = {
  otp: string;
  setOtp: (value: string) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  handleResendOtp: () => Promise<void>;
  canResendOtp: boolean;
  timeLeft: number;
  error?: string | null;
};

export const OtpVerificationForm: React.FC<OtpVerificationFormProps> = ({
  otp,
  setOtp,
  onSubmit,
  isSubmitting,
  handleResendOtp,
  canResendOtp,
  timeLeft,
  error,
}) => {
  // Use our custom error reporting hook
  useOtpErrorReporting({ otpError: error || null });

  return (
    <div className="space-y-4 max-w-[365px]">
      <h2 className="text-white text-xl font-semibold">Verify Your Email</h2>
      <p className="text-gray-400 text-sm">
        Enter the 6-digit code sent to your email
      </p>

      <div className="flex gap-2 justify-between">
        {[...Array(6)].map((_, index) => (
          <Input
            key={index}
            type="text"
            maxLength={1}
            className="w-12 h-12 text-center text-white bg-black border border-gray-600 rounded-md"
            value={otp[index] || ""}
            onChange={(e) => {
              const value = e.target.value;
              if (value && /^[0-9]$/.test(value)) {
                const newOtp = otp.split("");
                newOtp[index] = value;
                setOtp(newOtp.join(""));

                // Auto-focus next input
                if (index < 5 && value) {
                  const nextInput = document.querySelector(
                    `input[name="otp-${index + 1}"]`
                  ) as HTMLInputElement;
                  if (nextInput) nextInput.focus();
                }
              }
            }}
            onKeyDown={(e) => {
              // Handle backspace
              if (e.key === "Backspace") {
                if (otp[index]) {
                  // If current input has value, clear it
                  const newOtp = otp.split("");
                  newOtp[index] = "";
                  setOtp(newOtp.join(""));
                } else if (index > 0) {
                  // If current input is empty, move to previous and clear it
                  const newOtp = otp.split("");
                  newOtp[index - 1] = "";
                  setOtp(newOtp.join(""));
                  const prevInput = document.querySelector(
                    `input[name="otp-${index - 1}"]`
                  ) as HTMLInputElement;
                  if (prevInput) prevInput.focus();
                }
              }
            }}
            name={`otp-${index}`}
          />
        ))}
      </div>

      <div className="text-center">
        {canResendOtp ? (
          <button
            type="button"
            onClick={handleResendOtp}
            className="text-blue-500 text-sm hover:underline"
            disabled={isSubmitting}
          >
            Resend code
          </button>
        ) : (
          <p className="text-gray-400 text-sm">
            Resend code in {timeLeft} seconds
          </p>
        )}
      </div>

      <Button
        className="w-full"
        text="Verify"
        type="button"
        onClick={onSubmit}
        disable={isSubmitting || otp.length !== 6}
      />
    </div>
  );
};
