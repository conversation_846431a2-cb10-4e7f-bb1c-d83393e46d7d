import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "../Button";
import { signupFormSchema, SignupFormValues } from "./authSchemas";
import { useSignupErrorReporting } from "../../hooks/useSignupErrorReporting";

type SignupFormProps = {
  onSubmit: (values: SignupFormValues) => void;
  onToggle: () => void;
  isSubmitting: boolean;
  isEmailAvailable: boolean | null;
  isUsernameAvailable: boolean | null;
  isCheckingEmail: boolean;
  isCheckingUsername: boolean;
  onEmailChange: (value: string) => void;
  onUsernameChange: (value: string) => void;
};

export const SignupForm: React.FC<SignupFormProps> = ({
  onSubmit,
  onToggle,
  isSubmitting,
  isEmailAvailable,
  isUsernameAvailable,
  isCheckingEmail,
  isCheckingUsername,
  onEmailChange,
  onUsernameChange,
}) => {
  const form = useForm<SignupFormValues>({
    resolver: zodResolver(signupFormSchema),
    defaultValues: {
      email: "",
      password: "",
      fullName: "",
      username: "",
    },
  });

  const email = form.watch("email");
  const username = form.watch("username");

  // Use our custom error reporting hook
  useSignupErrorReporting({
    form,
    email,
    username,
    isCheckingEmail,
    isCheckingUsername,
    isEmailAvailable,
    isUsernameAvailable,
  });

  // Only disable the button if we know for sure the email or username is unavailable
  const isFormValid =
    form.formState.isValid &&
    isEmailAvailable !== false &&
    isUsernameAvailable !== false;

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-3 md:space-y-4 max-w-[365px]"
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="email"
                  placeholder="Email"
                  className="border border-gray-600 rounded-md bg-black text-white h-10 md:h-12 focus:border-gray-400 focus:ring-0 text-sm md:text-base"
                  {...field}
                  onChange={(e) => {
                    // Convert to lowercase
                    e.target.value = e.target.value.toLowerCase();
                    field.onChange(e);
                    onEmailChange(e.target.value);
                  }}
                />
              </FormControl>
              <FormMessage className="text-red-500 text-xs md:text-sm" />
              {email && !form.formState.errors.email && (
                <>
                  {isCheckingEmail && (
                    <p className="text-xs text-gray-400 mt-1">
                      Checking email availability...
                    </p>
                  )}
                  {!isCheckingEmail && isEmailAvailable === false && (
                    <p className="text-xs text-red-500 mt-1">
                      This email is already registered. Please use a different
                      email or sign in.
                    </p>
                  )}
                </>
              )}
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="password"
                  placeholder="Password"
                  className="border border-gray-600 rounded-md bg-black text-white h-10 md:h-12 focus:border-gray-400 focus:ring-0 text-sm md:text-base"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-red-500 text-xs md:text-sm" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  placeholder="Organization name"
                  className="border border-gray-600 rounded-md bg-black text-white h-10 md:h-12 focus:border-gray-400 focus:ring-0 text-sm md:text-base"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-red-500 text-xs md:text-sm" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  placeholder="Username"
                  className="border border-gray-600 rounded-md bg-black text-white h-10 md:h-12 focus:border-gray-400 focus:ring-0 text-sm md:text-base"
                  {...field}
                  onChange={(e) => {
                    // Convert to lowercase and filter invalid characters
                    const value = e.target.value.toLowerCase();
                    const filteredValue = value.replace(/[^a-z0-9_.]/g, "");
                    e.target.value = filteredValue;
                    field.onChange(e);
                    onUsernameChange(e.target.value);
                  }}
                />
              </FormControl>
              <FormMessage className="text-red-500 text-xs md:text-sm" />
              {username && !form.formState.errors.username && (
                <>
                  {isCheckingUsername && (
                    <p className="text-xs text-gray-400 mt-1">
                      Checking username availability...
                    </p>
                  )}
                  {!isCheckingUsername && isUsernameAvailable === false && (
                    <p className="text-xs text-red-500 mt-1">
                      This username is already taken. Please choose a different
                      one.
                    </p>
                  )}
                </>
              )}
            </FormItem>
          )}
        />

        <div className="!mt-8">
          <Button
            className="w-full"
            text="Create Account"
            type="submit"
            disable={isSubmitting || !isFormValid}
          />
        </div>
        <div className="text-white text-center text-sm">
          Already have an account?{" "}
          <span
            onClick={onToggle}
            className="text-blue-500 font-semibold cursor-pointer"
          >
            Sign in
          </span>
        </div>
      </form>
    </Form>
  );
};
