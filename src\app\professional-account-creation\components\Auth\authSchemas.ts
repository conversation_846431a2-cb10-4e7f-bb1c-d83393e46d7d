import { z } from "zod";

// Define signup form schema with Zod
export const signupFormSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters" })
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])(?=.*[0-9]).{8,}$/, {
      message:
        "Password must contain at least 8 characters, 1 uppercase letter, 1 lowercase letter, and 1 special character",
    }),
  fullName: z.string().min(2, { message: "Please enter your full name" }),
  username: z
    .string()
    .min(3, { message: "Userna<PERSON> must be at least 3 characters" })
    .regex(/^[a-z0-9_.]+$/, {
      message:
        "Username can only contain lowercase letters, numbers, underscores, and dots",
    }),
});

// Define login form schema with Zod
export const loginFormSchema = z.object({
  email: z.string().min(1, { message: "Email or username is required" }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters" }),
});

export type SignupFormValues = z.infer<typeof signupFormSchema>;
export type LoginFormValues = z.infer<typeof loginFormSchema>; 