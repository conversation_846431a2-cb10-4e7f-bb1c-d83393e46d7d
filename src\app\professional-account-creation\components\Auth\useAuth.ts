import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAppDispatch } from '@/lib/redux/hooks';
import { setUser } from '@/lib/redux/slices/auth/authSlice';
import { getSession } from 'next-auth/react';
import { mapToSignupResponse } from '@/lib/utils/modification';
import { Login } from '@/lib/actions/auth/Login';
import { SignUp } from '@/lib/actions/auth/SignUp';
import { isValidEmail } from '@/lib/utils/string';
import { 
  useGenerateOtpMutation, 
} from '@/lib/redux/slices/auth/authApi';
import { SignupFormValues, LoginFormValues } from './authSchemas';
import { reportError } from '@/lib/utils/sentryErrorLogs';
import { PROF_ACCOUNT_ERRORS } from '../../constants/errorMessages';

export const useAuth = (referralCode: string | null) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [signupData, setSignupData] = useState<SignupFormValues | null>(null);
  const [otp, setOtp] = useState<string>('');
  const router = useRouter();
  const dispatch = useAppDispatch();

  // API hooks
  const [generateOtp] = useGenerateOtpMutation();

  // Function to report API errors
  const reportApiError = (errorMessage: string, context: Record<string, any>) => {
    reportError(errorMessage, {
      page: 'professional-account-creation',
      ...context
    });
  };

  const handleSignupSubmit = async (values: SignupFormValues, startCooldown: () => void) => {
    setSignupData(values);
    setIsSubmitting(true);
    setError(null);

    try {
      // Generate OTP for email verification
      await generateOtp({ email: values.email }).unwrap();
      startCooldown();
      return true;
    } catch (err: any) {
      const errorMessage = err?.data?.message || PROF_ACCOUNT_ERRORS.SIGNUP_ERROR;
      setError(errorMessage);
      
      // Report to Sentry
      reportApiError(errorMessage, {
        field: 'email',
        type: 'otp_generation_error'
      });
      
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLoginSubmit = async (values: LoginFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const result = await Login({
        value: values.email,
        field: isValidEmail(values.email) ? 'email' : 'username',
        password: values.password,
        redirect: false,
      });

      if (result?.error) {
        setError(result.error);
        
        // Report login error to Sentry
        reportApiError(result.error, {
          field: 'login',
          type: 'auth_error'
        });
        
        return false;
      } else {
        const session = await getSession();
        const mappedResponse = mapToSignupResponse(session);
        dispatch(setUser(mappedResponse));

        if(referralCode){
          // Navigate to the select account type screen
          router.push('/professional-account-creation?refferal=' + referralCode + '&screen=2');
          return true;
        } else {
          // Navigate to the select account type screen
          router.push('/professional-account-creation?screen=2');
          return true;
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = (error as Error).message || PROF_ACCOUNT_ERRORS.LOGIN_ERROR;
      setError(errorMessage);
      
      // Report to Sentry
      reportApiError(errorMessage, {
        field: 'login',
        type: 'api_error'
      });
      
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendOtp = async (startCooldown: () => void) => {
    if (!signupData?.email) return false;

    try {
      await generateOtp({ email: signupData.email }).unwrap();
      startCooldown();
      return true;
    } catch (err: any) {
      const errorMessage = err?.data?.message || PROF_ACCOUNT_ERRORS.OTP_RESEND_ERROR;
      setError(errorMessage);
      
      // Report to Sentry
      reportApiError(errorMessage, {
        field: 'otp',
        type: 'resend_error'
      });
      
      return false;
    }
  };

  const handleVerifyOtp = async () => {
    if (!signupData || otp.length !== 6) return false;

    setIsSubmitting(true);
    setError(null);

    try {
      const [firstName, lastName] = signupData.fullName.split(' ');

      // Then use the SignUp server action to create a session
      const result = await SignUp({
        email: signupData.email,
        password: signupData.password,
        name: signupData.fullName,
        first_name: firstName || '',
        last_name: lastName || '',
        username: signupData.username,
        code: otp,
        redirect: false,
      });

      if (result?.error) {
        // If there's an error with the session creation, but the user was created
        // Just log in manually
        const loginResult = await Login({
          value: signupData.email,
          field: 'email',
          password: signupData.password,
          redirect: false,
        });

        if (loginResult?.error) {
          setError(loginResult.error);
          
          // Report to Sentry
          reportApiError(loginResult.error, {
            field: 'login',
            type: 'post_signup_login_error'
          });
          
          return false;
        }
      }

      // Get the session after successful signup
      const session = await getSession();
      if (session) {
        const mappedResponse = mapToSignupResponse(session);
        dispatch(setUser(mappedResponse));

        if(referralCode){
          // Navigate to the select account type screen
          router.push('/professional-account-creation?refferal=' + referralCode + '&screen=2');
          return true;
        } else {
          // Navigate to the select account type screen
          router.push('/professional-account-creation?screen=2');
          return true;
        }
      } else {
        // If session is still not available, try to refresh the page
        window.location.href = '/professional-account-creation?screen=2';
        return true;
      }
    } catch (err: any) {
      console.error('Signup error:', err);
      const errorMessage = err?.data?.message || 
        err?.data?.error_message || 
        PROF_ACCOUNT_ERRORS.OTP_VERIFICATION_ERROR;
      
      setError(errorMessage);
      
      // Report to Sentry
      reportApiError(errorMessage, {
        field: 'otp',
        type: 'verification_error'
      });
      
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    error,
    setError,
    signupData,
    otp,
    setOtp,
    handleSignupSubmit,
    handleLoginSubmit,
    handleResendOtp,
    handleVerifyOtp,
    reportApiError
  };
}; 