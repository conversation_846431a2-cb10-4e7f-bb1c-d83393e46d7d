import { useState, useEffect } from 'react';
import { isValidEmail } from '@/lib/utils/string';
import { useEmailCheckMutation } from '@/lib/redux/slices/auth/authApi';
import { useCheckUsernameMutation } from '@/lib/redux/slices/user/userApi';

export const useFormValidation = () => {
  // Form values for real-time validation
  const [emailValue, setEmailValue] = useState<string>('');
  const [usernameValue, setUsernameValue] = useState<string>('');

  // API hooks
  const [checkEmail, { isLoading: isCheckingEmail }] = useEmailCheckMutation();
  const [checkUsername, { isLoading: isCheckingUsername }] = useCheckUsernameMutation();

  // State for email and username availability
  const [isEmailAvailable, setIsEmailAvailable] = useState<boolean | null>(null);
  const [isUsernameAvailable, setIsUsernameAvailable] = useState<boolean | null>(null);

  // Check email availability when email changes
  useEffect(() => {
    if (!emailValue || !isValidEmail(emailValue)) return;

    const timer = setTimeout(async () => {
      try {
        const result = await checkEmail({ email: emailValue }).unwrap();
        setIsEmailAvailable(!result?.existing);
      } catch (err) {
        console.error('Failed to check email:', err);
        setIsEmailAvailable(null);
      }
    }, 600);

    return () => clearTimeout(timer);
  }, [emailValue, checkEmail]);

  // Check username availability when username changes
  useEffect(() => {
    if (!usernameValue || usernameValue.length < 3) return;

    const timer = setTimeout(async () => {
      try {
        const result = await checkUsername({
          username: usernameValue,
        }).unwrap();
        setIsUsernameAvailable(false);
      } catch (err) {
        console.error('Failed to check username:', err);
        setIsUsernameAvailable(true);
      }
    }, 600);

    return () => clearTimeout(timer);
  }, [usernameValue, checkUsername]);

  return {
    emailValue,
    setEmailValue,
    usernameValue,
    setUsernameValue,
    isEmailAvailable,
    isUsernameAvailable,
    isCheckingEmail,
    isCheckingUsername
  };
}; 