import { useState, useEffect } from 'react';

export const useOtpTimer = (initialCooldown: number = 30) => {
  const [canResendOtp, setCanResendOtp] = useState<boolean>(true);
  const [timeLeft, setTimeLeft] = useState<number>(0);

  // OTP resend timer handler
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && !canResendOtp) {
      setCanResendOtp(true);
    }
  }, [timeLeft, canResendOtp]);

  const startCooldown = () => {
    setCanResendOtp(false);
    setTimeLeft(initialCooldown);
  };

  return {
    canResendOtp,
    timeLeft,
    startCooldown
  };
}; 