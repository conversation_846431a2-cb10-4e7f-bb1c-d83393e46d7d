import { Button as ButtonComponent } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

type Props = {
  text: string;
  onClick?: () => void;
  className?: string;
  type?: "button" | "submit" | "reset";
  disable?: boolean;
  loading?: boolean;
};

export const Button = ({
  text,
  onClick,
  className,
  type = "button",
  disable = false,
  loading = false,
}: Props) => {
  return (
    <ButtonComponent
      disabled={disable || loading}
      onClick={onClick}
      type={type}
      className={`bg-[#DADADA] max-w-[365px] h-[50px] text-[16px] text-black px-4 py-2 rounded-full hover:bg-gray-300 transition-colors duration-200 ${className}`}
    >
      {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {text}
    </ButtonComponent>
  );
};
