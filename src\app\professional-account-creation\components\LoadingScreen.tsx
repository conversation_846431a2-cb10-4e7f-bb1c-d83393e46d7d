import Image from "next/image";
import { Loader2 } from "lucide-react";

export const LoadingScreen = () => {
  return (
    <div className="flex flex-col gap-6 md:gap-8 w-full max-w-md mx-auto md:mx-0 px-4 md:px-0 py-8 md:py-0 items-center justify-center min-h-[400px]">
      <div className="flex items-center gap-2">
        <Image
          src="/logo-white.svg"
          alt="logo"
          width={125}
          height={100}
          className="w-[100px] md:w-[125px]"
        />
        <p className="text-white text-xl md:text-[27px] font-bold text-center md:text-left">
          for Professionals
        </p>
      </div>

      <div className="flex flex-col items-center gap-4">
        <Loader2 className="h-8 w-8 animate-spin text-[#367AFF]" />
        <p className="text-gray-400 text-sm text-center">
          Loading your account...
        </p>
      </div>
    </div>
  );
};
