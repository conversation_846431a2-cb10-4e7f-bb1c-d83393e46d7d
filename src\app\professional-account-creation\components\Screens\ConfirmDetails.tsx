import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "../Button";
import { useConfirmDetails } from "../../hooks/useConfirmDetails";

type Props = {
  onNextScreen: () => void;
  userDetails: any;
};

export default function ConfirmDetails({ onNextScreen, userDetails }: Props) {
  const {
    form,
    isUsernameAvailable,
    isCheckingUsername,
    isLoading,
    isFormValid,
    username,
    handleNext,
    handleUsernameChange,
  } = useConfirmDetails({
    userDetails,
    onNextScreen,
  });

  return (
    <div className="w-full max-w-md mx-auto md:mx-0 px-4 md:px-0 py-8 md:py-0 flex flex-col gap-6 !max-w-[325px]">
      <div className="flex flex-col gap-2 mb-4">
        <h1 className="text-white text-xl md:text-2xl font-bold">
          Confirm account details
        </h1>
        <p className="text-gray-400 text-xs md:text-sm">
          Make sure your name reflects your new account
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleNext)} className="">
          <div className="bg-[#242628] pl-3 pr-2 rounded-lg border border-[#3A3C3E]">
            <div className="flex items-center border-b border-[#3A3C3E] pb-1">
              <Label
                htmlFor="name"
                className="w-[25%] text-gray-300 text-sm font-regular"
              >
                Name
              </Label>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="w-[70%]">
                    <FormControl>
                      <Input
                        id="name"
                        type="text"
                        className="bg-transparent border-none text-white placeholder:text-none focus:border-none focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 !border-none !focus:border-none"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex items-center pb-1">
              <Label
                htmlFor="username"
                className="w-[25%] text-gray-300 text-sm font-regular"
              >
                Username
              </Label>
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem className="w-[70%]">
                    <FormControl>
                      <Input
                        id="username"
                        type="text"
                        className="bg-transparent border-none text-white placeholder:text-none focus:border-none focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 !border-none !focus:border-none"
                        {...field}
                        onChange={(e) => handleUsernameChange(e, field)}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            {username && username !== userDetails.username && (
              <div className="pb-2">
                {isCheckingUsername && (
                  <p className="text-xs text-gray-400">
                    Checking username availability...
                  </p>
                )}
                {!isCheckingUsername && isUsernameAvailable === false && (
                  <p className="text-xs text-red-500">
                    This username is already taken. Please choose a different
                    one.
                  </p>
                )}
                {!isCheckingUsername && isUsernameAvailable === true && (
                  <p className="text-xs text-green-500">
                    Username is available!
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Display form validation errors */}
          {(form.formState.errors.name || form.formState.errors.username) && (
            <div className="space-y-1">
              {form.formState.errors.name && (
                <FormMessage className="text-red-500 text-xs">
                  {form.formState.errors.name.message}
                </FormMessage>
              )}
              {form.formState.errors.username && (
                <FormMessage className="text-red-500 text-xs">
                  {form.formState.errors.username.message}
                </FormMessage>
              )}
            </div>
          )}

          <Button
            className="w-full mt-[70px]"
            text={isLoading ? "Saving..." : "Next"}
            type="submit"
            loading={isLoading}
            disable={isLoading || isCheckingUsername || !isFormValid}
          />
        </form>
      </Form>
    </div>
  );
}
