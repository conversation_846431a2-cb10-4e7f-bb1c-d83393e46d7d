import Image from "next/image";
import { But<PERSON> } from "../Button";
import { useRouter } from "next/navigation";
import { useOrganization } from "@/lib/hooks/useOrganization";
import { useSessionData } from "@/lib/hooks/useSession";
import { useAccountTypeSelection } from "../../hooks/useAccountTypeSelection";
import { useState } from "react";
import { getSession } from "next-auth/react";
import type { Session } from "next-auth";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";

type Props = {
  onNextScreen: () => void;
};

export const FinishScreen = ({ onNextScreen }: Props) => {
  const { selectedAccountType, clearSelectedAccountType } =
    useAccountTypeSelection();
  const router = useRouter();
  const { getDashboardUrl } = useOrganization();
  const { data: sessionData, update } = useSessionData();
  const [isLoading, setIsLoading] = useState(false);

  const handleGoToDashboard = async () => {
    try {
      setIsLoading(true);

      // Try to update the session
      let updatedSession = await update();

      // If update() returns undefined, fall back to the current session data
      if (!updatedSession) {
        updatedSession = keysToCamel(sessionData);

        // If we still don't have session data, try to get it directly
        if (!updatedSession?.user) {
          const nextAuthSession = await getSession();
          if (nextAuthSession) {
            // Use type assertion to handle the type mismatch
            updatedSession = keysToCamel(nextAuthSession as unknown as Session);
          }
        }
      }

      if (selectedAccountType === "eventOrganizer") {
        // Use a type assertion to access the properties we need
        const user = updatedSession?.user as any;
        let orgSlug = user?.orgDetails?.org?.slug;

        if (orgSlug) {
          // Get the dashboard URL
          const dashboardUrl = getDashboardUrl({ orgSlug: orgSlug });

          // If getDashboardUrl returns an empty string, create our own URL
          if (dashboardUrl) {
            window.location.href = dashboardUrl;
          } else {
            // Get the base URL from environment or use a default
            const baseUrl = process.env.NEXT_PUBLIC_DASHBOARD_URL;

            // Create auth params if we have tokens
            let authParams = "";
            if (updatedSession?.accessToken && updatedSession?.refreshToken) {
              authParams = `accessToken=${updatedSession.accessToken}&refreshToken=${updatedSession.refreshToken}&csrfToken=${updatedSession.csrfToken}`;
            }

            // Create the full URL
            const customDashboardUrl = `${baseUrl}organization/${orgSlug}/${
              authParams ? `?${authParams}` : ""
            }`;

            // Navigate to the dashboard
            window.location.href = customDashboardUrl;
          }
        } else {
          router.push("/");
        }
      } else {
        router.push("/");
      }
    } catch (error) {
      router.push("/");
    } finally {
      clearSelectedAccountType();
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto md:mx-0 px-4 md:px-0 py-8 md:py-0 flex flex-col gap-6 md:gap-8">
      <div className="flex flex-wrap items-center gap-2">
        <p className="text-white text-xl md:text-[27px] font-bold text-center md:text-left">
          Welcome to
        </p>
        <Image
          src="/logo-white.svg"
          alt="logo"
          width={125}
          height={100}
          className="w-[100px] md:w-[125px]"
        />
        <p className="text-white text-xl md:text-[27px] font-bold text-center md:text-left">
          for Professionals
        </p>
      </div>
      <p className="text-gray-400 text-sm md:text-left">
        Now you can access more tools to better connect with your customers and
        grow your brand
      </p>

      <Button
        className="w-full mt-6 md:mt-12"
        text={
          selectedAccountType === "eventOrganizer"
            ? "Go to dashboard"
            : "Go to homepage"
        }
        onClick={handleGoToDashboard}
        loading={isLoading}
      />
    </div>
  );
};
