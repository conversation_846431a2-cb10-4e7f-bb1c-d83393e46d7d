import React, { useState } from "react";
import {
  LoginForm,
  SignupForm,
  OtpVerificationForm,
  useAuth,
  useFormValidation,
  useOtpTimer,
  SignupFormValues,
  LoginFormValues,
} from "../Auth";

type Props = {
  onNextScreen: () => void;
  referralCode: string | null;
};

export const LoginOrSignupScreen = ({ onNextScreen, referralCode }: Props) => {
  const [currentScreen, setCurrentScreen] = useState<
    "login" | "signup" | "otp"
  >("signup");
  const [previousScreen, setPreviousScreen] = useState<"login" | "signup">(
    "signup"
  );

  // Custom hooks
  const {
    isSubmitting,
    error,
    setError,
    otp,
    setOtp,
    handleSignupSubmit,
    handleLoginSubmit,
    handleResendOtp,
    handleVerifyOtp,
  } = useAuth(referralCode);

  const {
    emailValue,
    setEmailValue,
    usernameValue,
    setUsernameValue,
    isEmailAvailable,
    isUsernameAvailable,
    isCheckingEmail,
    isCheckingUsername,
  } = useFormValidation();

  const { canResendOtp, timeLeft, startCooldown } = useOtpTimer();

  // Handle form submissions
  const onSignupSubmit = async (values: SignupFormValues) => {
    const success = await handleSignupSubmit(values, startCooldown);
    if (success) {
      setPreviousScreen("signup");
      setCurrentScreen("otp");
    }
  };

  const onLoginSubmit = async (values: LoginFormValues) => {
    await handleLoginSubmit(values);
  };

  const onResendOtp = async () => {
    if (!canResendOtp) return;
    await handleResendOtp(startCooldown);
  };

  const onVerifyOtp = async () => {
    await handleVerifyOtp();
  };

  const toggleScreen = () => {
    setCurrentScreen(currentScreen === "login" ? "signup" : "login");
    setError(null);
  };

  const handleBackFromOtp = () => {
    setCurrentScreen(previousScreen);
    setError(null);
  };

  return (
    <div className="w-full max-w-md mx-auto md:mx-0 px-4 md:px-6 py-8 md:py-6 flex flex-col">
      <div className="flex flex-col gap-2 !mb-6">
        <h1 className="text-white text-[30px] font-bold">
          {currentScreen === "login" || currentScreen === "signup"
            ? "Account details"
            : "Verify Email"}
        </h1>
        {error && (
          <div className="bg-red-500/10 border border-red-500 rounded-md p-3 text-red-500 text-sm">
            {error}
          </div>
        )}
      </div>

      {currentScreen === "signup" && (
        <SignupForm
          onSubmit={onSignupSubmit}
          onToggle={toggleScreen}
          isSubmitting={isSubmitting}
          isEmailAvailable={isEmailAvailable}
          isUsernameAvailable={isUsernameAvailable}
          isCheckingEmail={isCheckingEmail}
          isCheckingUsername={isCheckingUsername}
          onEmailChange={setEmailValue}
          onUsernameChange={setUsernameValue}
        />
      )}

      {currentScreen === "login" && (
        <LoginForm
          onSubmit={onLoginSubmit}
          onToggle={toggleScreen}
          isSubmitting={isSubmitting}
        />
      )}

      {currentScreen === "otp" && (
        <>
          <button
            onClick={handleBackFromOtp}
            className="text-white flex items-center mb-4 hover:text-gray-300 transition-colors"
            type="button"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Back
          </button>
          <OtpVerificationForm
            otp={otp}
            setOtp={setOtp}
            onSubmit={onVerifyOtp}
            isSubmitting={isSubmitting}
            handleResendOtp={onResendOtp}
            canResendOtp={canResendOtp}
            timeLeft={timeLeft}
            error={error}
          />
        </>
      )}
    </div>
  );
};
