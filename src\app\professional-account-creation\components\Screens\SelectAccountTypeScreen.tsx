import React, { useState, useEffect } from "react";
import { Button } from "../Button";
import { RadioGroup } from "@/components/ui/radio-group";
import {
  useAccountTypeSelection,
  AccountType,
} from "../../hooks/useAccountTypeSelection";
import { AccountTypeOption } from "../AccountTypeOption";
import {
  useOrganizationCreationMutation,
  useReferralRegisterMutation,
  useTeamCreationMutation,
} from "@/lib/redux/slices/organization/api";
import { useRouter } from "next/navigation";
import { useSessionData } from "@/lib/hooks/useSession";
import { getSession } from "next-auth/react";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import { toast } from "react-hot-toast";
import { getErrorMessage } from "@/lib/utils/errorUtils";
import { TOASTS } from "@/lib/utils/constants";
import { XIcon } from "lucide-react";
import { LoadingScreen } from "../LoadingScreen";

type Props = {
  onNextScreen: () => void;
  referralCode: string | null;
};

export const SelectAccountTypeScreen = ({
  onNextScreen,
  referralCode,
}: Props) => {
  const [createOrganization] = useOrganizationCreationMutation();
  const [createTeam] = useTeamCreationMutation();
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { data: sessionData, status: sessionStatus } = useSessionData();
  const [referralRegister] = useReferralRegisterMutation();

  const {
    selectedAccountType,
    setSelectedAccountType,
    isEventOrganizerSelected,
    isTeamClubSelected,
    canProceed,
  } = useAccountTypeSelection();

  useEffect(() => {
    if (!sessionData) {
      getSession().then((session) => {
        const user = keysToCamel(session?.user);
        if (user?.orgDetails || user?.teamDetails) {
          onNextScreen();
        }
      });
    } else {
      if (sessionData?.user?.orgDetails || sessionData?.user?.teamDetails) {
        onNextScreen();
      }
    }
  }, [sessionData]);

  const handleNext = async () => {
    try {
      setIsLoading(true);

      let response;

      if (selectedAccountType === "eventOrganizer") {
        response = await createOrganization({}).unwrap();
      } else if (selectedAccountType === "teamClub") {
        response = await createTeam({}).unwrap();
      }

      if (referralCode) {
        await referralRegister({
          orgSlug: response?.slug,
          body: {
            referralCode,
          },
        });
      }

      onNextScreen();
    } catch (error) {
      console.error("Error creating organization:", error);
      const errorMessage = getErrorMessage(error);

      if (errorMessage === TOASTS.ORG_NAME_DUPLICATE) {
        toast.custom((t) => (
          <div className={`${t.visible ? "animate-in" : "animate-out"}`}>
            <div className="bg-red-600 text-white gap-3 sm:gap-0 p-4 rounded-md flex flex-col sm:flex-row items-center justify-between">
              <p>{errorMessage}</p>
              <button
                onClick={() => router.push("/profile/settings")}
                className="sm:ml-4 bg-white text-black py-1 px-3 rounded-md text-sm font-medium hover:bg-gray-100 transition-colors whitespace-nowrap"
              >
                Go to Settings
              </button>
            </div>
          </div>
        ));
      } else {
        toast.error(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectOption = (type: AccountType) => {
    setSelectedAccountType(type);
  };

  if (sessionStatus === "loading") {
    return <LoadingScreen />;
  }

  return (
    <div className="flex flex-col w-full max-w-md mx-auto md:mx-0 px-4 md:px-6 py-8 md:py-6">
      <div className="flex flex-col gap-2 mb-4">
        <h1 className="text-white text-xl md:text-2xl font-bold">
          Select an account type
        </h1>
        <p className="text-gray-400 text-xs md:text-sm">
          Choose the one that best suits you
        </p>
      </div>

      <RadioGroup
        className="flex flex-col gap-3 md:gap-4 mt-3 md:mt-4 w-full"
        value={selectedAccountType || ""}
        onValueChange={(value) => setSelectedAccountType(value as AccountType)}
      >
        <AccountTypeOption
          id="eventOrganizer"
          title="Event Organizer"
          description="Create, manage, and sell tickets for automotive events with an all-in-one dashboard"
          isSelected={isEventOrganizerSelected}
          onSelect={() => handleSelectOption("eventOrganizer")}
        />

        <AccountTypeOption
          id="teamClub"
          title="Team / Club"
          description="Add members, and manage your car club or team. Host private drives, chatrooms, and more!"
          isSelected={isTeamClubSelected}
          onSelect={() => handleSelectOption("teamClub")}
        />
      </RadioGroup>

      <Button
        className="mt-8 md:mt-16 w-full"
        text="Next"
        onClick={handleNext}
        disable={!canProceed}
        loading={isLoading}
      />
    </div>
  );
};
