import Image from "next/image";
import { Button } from "../Button";

type Props = {
  onNextScreen: () => void;
};

export const StartScreen = ({ onNextScreen }: Props) => {
  return (
    <div className="flex flex-col gap-6 md:gap-8 w-full max-w-md mx-auto md:mx-0 px-4 md:px-0 py-8 md:py-0">
      <div className="flex items-center gap-2">
        <Image
          src="/logo-white.svg"
          alt="logo"
          width={125}
          height={100}
          className="w-[100px] md:w-[125px]"
        />
        <p className="text-white text-xl md:text-[27px] font-bold text-center md:text-left">
          for Professionals
        </p>
      </div>
      <div className="pl-2 md:pl-6 flex flex-col gap-4 md:gap-6">
        <div className="flex items-center gap-3">
          <div className="w-[26px] h-[26px] flex items-center justify-center">
            <Image
              src="/ticket.svg"
              alt="event ticket"
              width={22}
              height={22}
              className=""
            />
          </div>
          <p className="text-white text-[14px] md:text-[15.5px] font-bold">
            Create events
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="w-[26px] h-[26px] flex items-center justify-center">
            <Image
              src="/money.svg"
              alt="money"
              width={22}
              height={22}
              className=""
            />
          </div>
          <p className="text-white text-[14px] md:text-[15.5px] font-bold">
            Instant payouts
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="w-[26px] h-[26px] flex items-center justify-center">
            <Image
              src="/award.svg"
              alt="award"
              width={19}
              height={20}
              className=""
            />
          </div>
          <p className="text-white text-[14px] md:text-[15.5px] font-bold">
            Assign awards
          </p>
        </div>
      </div>
      <Button
        className="w-full mt-6 md:mt-12"
        text="Agree and continue"
        onClick={onNextScreen}
      />
    </div>
  );
};
