/**
 * Professional account creation error message constants
 */
export const PROF_ACCOUNT_ERRORS = {
  // Form validation errors
  EMAIL_VALIDATION_ERROR: "The email is invalid or improperly formatted.",
  PASSWORD_VALIDATION_ERROR: "The password does not meet requirements.",
  NAME_VALIDATION_ERROR: "The name field is invalid or improperly formatted.",
  USERNAME_VALIDATION_ERROR: "The username is invalid or improperly formatted.",
  
  // Availability errors
  EMAIL_AVAILABILITY_ERROR: "This email is already registered. Please use a different email or sign in.",
  USERNAME_AVAILABILITY_ERROR: "This username is already taken. Please choose a different one.",
  
  // OTP errors
  OTP_INVALID_ERROR: "The verification code is invalid. Please try again.",
  OTP_EXPIRED_ERROR: "The verification code has expired. Please request a new one.",
  
  // API errors
  SIGNUP_ERROR: "There was an error creating your account. Please try again.",
  LOGIN_ERROR: "There was an error signing in. Please check your credentials and try again.",
  OTP_VERIFICATION_ERROR: "There was an error verifying your email. Please try again.",
  OTP_RESEND_ERROR: "There was an error sending a new verification code. Please try again."
}; 