import { useState, useEffect } from 'react';

export type AccountType = "eventOrganizer" | "teamClub";

interface UseAccountTypeSelectionReturn {
  selectedAccountType: AccountType | null;
  setSelectedAccountType: (type: AccountType) => void;
  isEventOrganizerSelected: boolean;
  isTeamClubSelected: boolean;
  canProceed: boolean;
  clearSelectedAccountType: () => void;
}

const SESSION_STORAGE_KEY = 'selectedAccountType';

export const useAccountTypeSelection = (): UseAccountTypeSelectionReturn => {
  // Initialize state from session storage if available
  const [selectedAccountType, setSelectedAccountTypeState] = useState<AccountType | null>(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      const savedType = sessionStorage.getItem(SESSION_STORAGE_KEY);
      return savedType ? (savedType as AccountType) : null;
    }
    return null;
  });

  // Custom setter that updates both state and session storage
  const setSelectedAccountType = (type: AccountType) => {
    setSelectedAccountTypeState(type);
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(SESSION_STORAGE_KEY, type);
    }
  };

  // Function to clear the selected account type from session storage
  const clearSelectedAccountType = () => {
    setSelectedAccountTypeState(null);
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(SESSION_STORAGE_KEY);
    }
  };

  const isEventOrganizerSelected = selectedAccountType === "eventOrganizer";
  const isTeamClubSelected = selectedAccountType === "teamClub";
  const canProceed = selectedAccountType !== null;

  return {
    selectedAccountType,
    setSelectedAccountType,
    isEventOrganizerSelected,
    isTeamClubSelected,
    canProceed,
    clearSelectedAccountType
  };
}; 