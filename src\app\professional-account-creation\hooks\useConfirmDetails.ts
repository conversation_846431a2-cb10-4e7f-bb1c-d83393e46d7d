import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  useMeMutation,
  useCheckUsernameMutation,
} from "@/lib/redux/slices/user/userApi";

const confirmDetailsSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  username: z
    .string()
    .min(3, { message: "Username must be at least 3 characters" })
    .regex(/^[a-z0-9_.]+$/, {
      message:
        "Username can only contain lowercase letters, numbers, underscores, and dots",
    }),
});

export type ConfirmDetailsFormValues = z.infer<typeof confirmDetailsSchema>;

interface UseConfirmDetailsProps {
  userDetails: any;
  onNextScreen: () => void;
}

interface UseConfirmDetailsReturn {
  form: ReturnType<typeof useForm<ConfirmDetailsFormValues>>;
  isUsernameAvailable: boolean | null;
  isCheckingUsername: boolean;
  isLoading: boolean;
  isFormValid: boolean;
  username: string;
  handleNext: (values: ConfirmDetailsFormValues) => Promise<void>;
  handleUsernameChange: (e: React.ChangeEvent<HTMLInputElement>, field: any) => void;
}

export const useConfirmDetails = ({
  userDetails,
  onNextScreen,
}: UseConfirmDetailsProps): UseConfirmDetailsReturn => {
  const form = useForm<ConfirmDetailsFormValues>({
    resolver: zodResolver(confirmDetailsSchema),
    defaultValues: {
      name: userDetails.name || "",
      username: userDetails.username || "",
    },
  });

  const [updateUserMutation, { isLoading }] = useMeMutation();
  const [checkUsername, { isLoading: isCheckingUsername }] =
    useCheckUsernameMutation();

  const [isUsernameAvailable, setIsUsernameAvailable] = useState<
    boolean | null
  >(null);

  const username = form.watch("username");

  useEffect(() => {
    if (!username || username.length < 3) {
      setIsUsernameAvailable(null);
      return;
    }

    if (username === userDetails.username) {
      setIsUsernameAvailable(null);
      return;
    }

    const timer = setTimeout(async () => {
      try {
        const result = await checkUsername({
          username: username,
        }).unwrap();
        setIsUsernameAvailable(false);
      } catch (err) {
        setIsUsernameAvailable(true);
      }
    }, 600);

    return () => clearTimeout(timer);
  }, [username, checkUsername, userDetails.username]);

  const handleNext = async (values: ConfirmDetailsFormValues) => {
    if (isUsernameAvailable === false) {
      return;
    }

    if (
      values?.name === userDetails?.name &&
      values?.username === userDetails?.username
    ) {
      onNextScreen();
      return;
    }

    try {
      await updateUserMutation({
        name: values.name,
        username: values.username,
      });
    } catch (error) {
      console.log(error);
    } finally {
      onNextScreen();
    }
  };

  const handleUsernameChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: any
  ) => {
    const value = e.target.value.toLowerCase();
    const filteredValue = value.replace(/[^a-z0-9_.]/g, "");
    e.target.value = filteredValue;
    field.onChange(e);
  };

  const isFormValid = form.formState.isValid && isUsernameAvailable !== false;

  return {
    form,
    isUsernameAvailable,
    isCheckingUsername,
    isLoading,
    isFormValid,
    username,
    handleNext,
    handleUsernameChange,
  };
}; 
