import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { UseFormReturn } from "react-hook-form";
import { PROF_ACCOUNT_ERRORS } from "../constants/errorMessages";
import { LoginFormValues } from "../components/Auth/authSchemas";

interface UseLoginErrorReportingProps {
  form: UseFormReturn<LoginFormValues>;
  page?: string;
}

/**
 * Custom hook to handle error reporting for login form
 */
export function useLoginErrorReporting({
  form,
  page = "professional-account-creation"
}: UseLoginErrorReportingProps) {
  const { formState } = form;

  // Report email validation errors to Sentry
  useEffect(() => {
    const emailError = formState?.errors?.email?.message;
    if (emailError) {
      reportError(String(emailError), { 
        page, 
        field: "email",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.email?.message, page]);

  // Report password validation errors to Sentry
  useEffect(() => {
    const passwordError = formState?.errors?.password?.message;
    if (passwordError) {
      reportError(String(passwordError), { 
        page, 
        field: "password",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.password?.message, page]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page, 
      ...context
    });
  };

  return { reportApiError };
} 