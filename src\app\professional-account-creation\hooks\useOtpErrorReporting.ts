import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { PROF_ACCOUNT_ERRORS } from "../constants/errorMessages";

interface UseOtpErrorReportingProps {
  otpError: string | null;
  page?: string;
}

/**
 * Custom hook to handle error reporting for OTP verification
 */
export function useOtpErrorReporting({
  otpError,
  page = "professional-account-creation"
}: UseOtpErrorReportingProps) {
  
  // Report OTP validation errors to Sentry
  useEffect(() => {
    if (otpError) {
      reportError(otpError, { 
        page, 
        field: "otp",
        type: "validation_error" 
      });
    }
  }, [otpError, page]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page, 
      ...context
    });
  };

  return { reportApiError };
} 