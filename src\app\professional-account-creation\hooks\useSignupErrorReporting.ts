import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { UseFormReturn } from "react-hook-form";
import { PROF_ACCOUNT_ERRORS } from "../constants/errorMessages";
import { SignupFormValues } from "../components/Auth/authSchemas";

interface UseSignupErrorReportingProps {
  form: UseFormReturn<SignupFormValues>;
  email: string;
  username: string;
  isCheckingEmail: boolean;
  isCheckingUsername: boolean;
  isEmailAvailable: boolean | null;
  isUsernameAvailable: boolean | null;
  page?: string;
}

/**
 * Custom hook to handle error reporting for signup form
 */
export function useSignupErrorReporting({
  form,
  email,
  username,
  isCheckingEmail,
  isCheckingUsername,
  isEmailAvailable,
  isUsernameAvailable,
  page = "professional-account-creation"
}: UseSignupErrorReportingProps) {
  const { formState } = form;

  // Report email validation errors to Sentry
  useEffect(() => {
    const emailError = formState?.errors?.email?.message;
    if (emailError) {
      reportError(String(emailError), { 
        page, 
        field: "email",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.email?.message, page]);

  // Report password validation errors to Sentry
  useEffect(() => {
    const passwordError = formState?.errors?.password?.message;
    if (passwordError) {
      reportError(String(passwordError), { 
        page, 
        field: "password",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.password?.message, page]);

  // Report name validation errors to Sentry
  useEffect(() => {
    const nameError = formState?.errors?.fullName?.message;
    if (nameError) {
      reportError(String(nameError), { 
        page, 
        field: "fullName",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.fullName?.message, page]);

  // Report username validation errors to Sentry
  useEffect(() => {
    const usernameError = formState?.errors?.username?.message;
    if (usernameError) {
      reportError(String(usernameError), { 
        page, 
        field: "username",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.username?.message, page]);

  // Report availability errors to Sentry
  useEffect(() => {
    if (email && !formState?.errors?.email && !isCheckingEmail && isEmailAvailable === false) {
      reportError(PROF_ACCOUNT_ERRORS.EMAIL_AVAILABILITY_ERROR, { 
        page, 
        field: "email",
        type: "availability_error" 
      });
    }
  }, [email, formState?.errors?.email, isCheckingEmail, isEmailAvailable, page]);

  useEffect(() => {
    if (username && !formState?.errors?.username && !isCheckingUsername && isUsernameAvailable === false) {
      reportError(PROF_ACCOUNT_ERRORS.USERNAME_AVAILABILITY_ERROR, { 
        page, 
        field: "username",
        type: "availability_error" 
      });
    }
  }, [username, formState?.errors?.username, isCheckingUsername, isUsernameAvailable, page]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page, 
      ...context
    });
  };

  return { reportApiError };
} 