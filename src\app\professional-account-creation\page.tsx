"use client";

import Image from "next/image";
import { StartScreen } from "./components/Screens/StartScreen";
import { SelectAccountTypeScreen } from "./components/Screens/SelectAccountTypeScreen";
import { FinishScreen } from "./components/Screens/FinishScreen";
import { LoginOrSignupScreen } from "./components/Screens/LoginOrSignupScreen";
import { useState, useEffect } from "react";
import { useSessionData } from "@/lib/hooks/useSession";
import { useRouter, useSearchParams } from "next/navigation";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import ConfirmDetails from "./components/Screens/ConfirmDetails";
import { LoadingScreen } from "./components/LoadingScreen";

const ProfessionalAccountCreation = () => {
  const { data: sessionData, status: sessionStatus } = useSessionData();
  const [currentScreen, setCurrentScreen] = useState<number>(0);
  const searchParams = useSearchParams();
  const router = useRouter();

  const referralCode = searchParams.get("refferal");

  useEffect(() => {
    const screenParam = searchParams.get("screen");
    if (screenParam) {
      const screenNumber = parseInt(screenParam, 10);
      if (!isNaN(screenNumber) && screenNumber >= 0 && screenNumber <= 3) {
        setCurrentScreen(screenNumber);
      }
    }
  }, [searchParams]);

  const updateUrlWithScreen = (screen: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("screen", screen.toString());

    // Explicitly preserve referral code if it exists
    if (referralCode) {
      params.set("refferal", referralCode);
    }

    router.push(`?${params.toString()}`, { scroll: false });
  };

  const navigateToScreen = (screen: number) => {
    setCurrentScreen(screen);
    updateUrlWithScreen(screen);
  };

  const handleNextScreen = () => {
    const nextScreen = currentScreen + 1;
    navigateToScreen(nextScreen);
  };

  if (sessionStatus === "loading") {
    return (
      <div className="bg-black min-h-screen flex items-center justify-center">
        <div className="grid grid-cols-1 md:grid-cols-2 items-center w-full max-w-6xl px-4">
          <LoadingScreen />
          <div className="hidden md:block">
            <Image
              src={IMAGE_LINKS.MOBILE_APP_EVENT_VIEW}
              alt="Autolnk Professional Account Creation"
              width={500}
              height={500}
            />
          </div>
        </div>
      </div>
    );
  }

  const RenderScreens = () => {
    if (sessionData?.user) {
      switch (currentScreen) {
        case 0:
          return <StartScreen onNextScreen={handleNextScreen} />;
        case 1:
          return (
            <SelectAccountTypeScreen
              referralCode={referralCode}
              onNextScreen={handleNextScreen}
            />
          );
        case 2:
          return (
            <ConfirmDetails
              onNextScreen={handleNextScreen}
              userDetails={sessionData.user}
            />
          );
        case 3:
          return <FinishScreen onNextScreen={handleNextScreen} />;
        default:
          return null;
      }
    } else {
      switch (currentScreen) {
        case 0:
          return <StartScreen onNextScreen={handleNextScreen} />;
        case 1:
          return (
            <LoginOrSignupScreen
              onNextScreen={handleNextScreen}
              referralCode={referralCode}
            />
          );
        case 2:
          return (
            <SelectAccountTypeScreen
              referralCode={referralCode}
              onNextScreen={handleNextScreen}
            />
          );
        case 3:
          return <FinishScreen onNextScreen={handleNextScreen} />;
        default:
          return null;
      }
    }
  };
  return (
    <div className="bg-black min-h-screen flex items-center justify-center">
      <div className="grid grid-cols-1 md:grid-cols-2 items-center w-full max-w-6xl px-4">
        <RenderScreens />
        <div className="hidden md:block">
          <Image
            src={IMAGE_LINKS.MOBILE_APP_EVENT_VIEW}
            alt="Autolnk Professional Account Creation"
            width={500}
            height={500}
          />
        </div>
      </div>
    </div>
  );
};

export default ProfessionalAccountCreation;
