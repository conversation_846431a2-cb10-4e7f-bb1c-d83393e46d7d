import React from 'react';
import { Skeleton } from "@nextui-org/react";

const CalendarSkeletonLoader = () => {
  return (
    <div className="w-full max-w-md md:max-w-full mx-auto bg-white rounded-lg shadow-lg p-4 animate-pulse">
      <div className="flex justify-between items-center mb-4">
        <Skeleton className="w-1/3 h-8 rounded-lg" />
        <div className="flex gap-2">
          <Skeleton className="w-8 h-8 rounded-full" />
          <Skeleton className="w-8 h-8 rounded-full" />
        </div>
      </div>
      <div className="grid grid-cols-7 gap-1 mb-2">
        {Array.from({ length: 7 }).map((_, index) => (
          <Skeleton key={`day-${index}`} className="h-6 rounded-md" />
        ))}
      </div>
      <div className="grid grid-cols-7 gap-1">
        {Array.from({ length: 35 }).map((_, index) => (
          <Skeleton key={`date-${index}`} className="h-10 w-10 rounded-full mx-auto" />
        ))}
      </div>
    </div>
  );
};

export default CalendarSkeletonLoader;