import React from "react";
import MonthCalendar from "./MonthCalendar";
import { months } from "../constants";

const DesktopCalendar = ({ currentDate, events, hasEvent }) => {
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Generate array of next 12 months starting from current month
  const nextTwelveMonths = Array.from({ length: 12 }, (_, index) => {
    const futureDate = new Date(currentYear, currentMonth + index, 1);
    return {
      month: months[futureDate.getMonth()],
      monthIndex: futureDate.getMonth(),
      year: futureDate.getFullYear(),
    };
  });

  // Get unique years from the next twelve months
  const uniqueYears = [...new Set(nextTwelveMonths.map(({ year }) => year))];
  const titleYears = uniqueYears.join("-");

  return (
    <div className="hidden md:block w-full max-w-[70rem] mx-auto p-4">
      <div className="text-center mb-4">
        <h2 className="text-2xl font-bold">{titleYears}</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 border rounded-2xl shadow-xl p-1">
        {nextTwelveMonths.map(({ month, monthIndex, year }) => (
          <MonthCalendar
            key={`${month}-${year}`}
            year={year}
            month={month}
            monthIndex={monthIndex}
            hasEvent={hasEvent}
          />
        ))}
      </div>
    </div>
  );
};

export default DesktopCalendar;
