import React, { useState } from "react";
import DesktopCalendar from "./DesktopCalendar";
import MobileCalendar from "./MobileCalendar";
import { months, daysOfWeek, isLeapYear, getDaysInMonth } from "../constants";

const EventCalendar = ({ events }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();

  // Updated hasEvent function to accept year parameter
  const hasEvent = (month, day, eventYear = year) => {
    const dateString = `${eventYear}-${(month + 1)
      .toString()
      .padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
    const eventDetails = events?.filter((event) => event?.start === dateString);
    const eventExists = eventDetails.length > 0;
    return {
      eventDate: eventExists,
      id: eventDetails[0]?.id,
      name: eventDetails[0]?.name,
      image: eventDetails[0]?.image,
      slug: eventDetails[0]?.slug,
    };
  };

  const prevMonth = () => setCurrentDate(new Date(year, month - 1, 1));
  const nextMonth = () => setCurrentDate(new Date(year, month + 1, 1));

  const calendarProps = {
    currentDate,
    events,
    hasEvent,
    prevMonth,
    nextMonth,
  };

  return (
    <>
      <DesktopCalendar {...calendarProps} />
      <MobileCalendar {...calendarProps} />
    </>
  );
};

export default EventCalendar;
