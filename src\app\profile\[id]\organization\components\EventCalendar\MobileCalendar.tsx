import React from "react";
import Link from "next/link";
import clsx from "clsx";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { months, daysOfWeek, getDaysInMonth, isPastDate } from "../constants";

const MobileCalendar = ({ currentDate, hasEvent, prevMonth, nextMonth }) => {
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const firstDayOfMonth = new Date(year, month, 1).getDay();
  return (
    <div className="md:hidden w-full max-w-md mx-auto bg-white rounded-2xl shadow-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">
          {months[month]} {year}
        </h2>
        <div className="flex gap-2">
          <button
            onClick={prevMonth}
            className="p-1 rounded-full hover:bg-gray-200"
          >
            <FaChevronLeft className="w-6 h-6 text-blue-500" />
          </button>
          <button
            onClick={nextMonth}
            className="p-1 rounded-full hover:bg-gray-200"
          >
            <FaChevronRight className="w-6 h-6 text-blue-500" />
          </button>
        </div>
      </div>
      <div className="grid grid-cols-7 gap-1 mb-2">
        {daysOfWeek.map((day) => (
          <div
            key={day}
            className="text-center text-gray-500 text-xs font-medium"
          >
            {day}
          </div>
        ))}
      </div>
      <div className="grid grid-cols-7 gap-1">
        {Array.from({ length: firstDayOfMonth }).map((_, index) => (
          <div key={`empty-${index}`} className="h-10"></div>
        ))}
        {Array.from({ length: getDaysInMonth(year, month) }).map((_, index) => {
          const day = index + 1;
          const { eventDate, id, slug } = hasEvent(month, day);
          const isDateInPast = isPastDate(year, month, day);

          return (
            <Link
              href={eventDate && !isDateInPast ? `/e/${slug}` : "#"}
              key={day}
              className={clsx(
                "h-10 flex items-center justify-center cursor-pointer",
                {
                  "text-[#017aff] font-bold border-b-2 border-[#017aff]":
                    eventDate && !isDateInPast,
                  "text-gray-400": isDateInPast,
                  "text-gray-700": !eventDate && !isDateInPast,
                }
              )}
            >
              {day}
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default MobileCalendar;
