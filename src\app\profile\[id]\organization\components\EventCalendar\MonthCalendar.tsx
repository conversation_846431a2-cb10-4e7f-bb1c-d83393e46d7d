import React from "react";
import Link from "next/link";
import clsx from "clsx";
import { Tooltip } from "@nextui-org/react";
import { daysOfWeek, getDaysInMonth, isPastDate } from "../constants";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

const MonthCalendar = ({ year, month, monthIndex, hasEvent }) => {
  return (
    <div className="p-2">
      <h3 className="text-center font-semibold mb-4">
        {month} {year}
      </h3>
      <div className="grid grid-cols-7 gap-[2px] text-xs">
        {daysOfWeek.map((day) => (
          <div key={day} className="text-center font-medium">
            {day}
          </div>
        ))}
        {Array.from({
          length: new Date(year, monthIndex, 1).getDay(),
        }).map((_, i) => (
          <div key={`empty-${i}`} className="h-9"></div>
        ))}
        {Array.from({ length: getDaysInMonth(year, monthIndex) }).map(
          (_, day) => {
            const date = day + 1;
            const { eventDate, id, name, image, slug } = hasEvent(
              monthIndex,
              date,
              year
            );
            const isDateInPast = isPastDate(year, monthIndex, date);
            
            return (
              <Link
                href={eventDate && !isDateInPast ? `/events/${slug}` : "#"}
                key={date}
                className={clsx(
                  "h-9 flex items-center justify-center rounded-lg text-base",
                  {
                    "bg-[#017aff] text-white": eventDate && !isDateInPast,
                    "text-gray-400 cursor-default": isDateInPast,
                    "hover:bg-gray-100": !eventDate && !isDateInPast,
                  }
                )}
              >
                {eventDate && !isDateInPast ? (
                  <Tooltip
                    content={
                      <div className="flex flex-col items-center p-3">
                        <img
                          src={image ?? IMAGE_LINKS.NO_IMG}
                          alt={name + " Autolnk Event Image"}
                          className="w-28 h-28 object-cover rounded-lg"
                        />
                        <p className="text-sm font-semibold truncate max-w-full mt-1">
                          {name}
                        </p>
                      </div>
                    }
                  >
                    {date}
                  </Tooltip>
                ) : (
                  date
                )}
              </Link>
            );
          }
        )}
      </div>
    </div>
  );
};

export default MonthCalendar;
