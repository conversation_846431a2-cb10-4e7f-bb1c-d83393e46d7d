"use client";

import Image from "next/image";
import { Gallery, Item } from "react-photoswipe-gallery";
import { useState, useEffect } from "react";
import "photoswipe/dist/photoswipe.css";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { Avatar } from "@nextui-org/react";

export function HeroGallery({ coverSrc, avatarSrc, children }) {
  const [coverDimensions, setCoverDimensions] = useState({
    width: 1024,
    height: 768,
  });
  const [avatarDimensions, setAvatarDimensions] = useState({
    width: 600,
    height: 600,
  });

  const loadImage = (
    src: string
  ): Promise<{ width: number; height: number }> => {
    return new Promise((resolve) => {
      const img = new window.Image();
      img.onload = () => {
        resolve({ width: img.naturalWidth, height: img.naturalHeight });
      };
      img.src = src;
    });
  };

  useEffect(() => {
    if (coverSrc) loadImage(coverSrc).then(setCoverDimensions);
    if (avatarSrc) loadImage(avatarSrc).then(setAvatarDimensions);
  }, [coverSrc, avatarSrc]);

  return (
    <Gallery>
      <div className="w-full px-4 sm:px-0">
        {coverSrc ? (
          <Item
            original={coverSrc}
            thumbnail={coverSrc}
            width={coverDimensions.width}
            height={coverDimensions.height}
            cropped={false}
          >
            {({ ref, open }) => (
              <div
                ref={ref}
                onClick={open}
                className="cursor-pointer rounded-2xl overflow-hidden"
              >
                <Image
                  src={coverSrc}
                  width={1400}
                  height={560}
                  alt="Autolnk Cover Photo"
                  blurDataURL={IMAGE_LINKS.PLACEHOLDER_BIG}
                  placeholder="blur"
                  priority={true}
                  className="w-full max-h-[20vh] md:max-h-[32rem] rounded-2xl object-cover"
                />
              </div>
            )}
          </Item>
        ) : (
          <div className="w-full h-[20vh] md:h-[32rem] rounded-3xl object-cover border-2 border-gray-200"></div>
        )}
      </div>
      <div className="ml-8 md:ml-12 -mt-6 sm:-mt-5 md:-mt-20">
        <div className="flex gap-x-4 items-end">
          <div className="relative w-28 sm:w-[200px] aspect-square">
            {avatarSrc ? (
              <Item
                original={avatarSrc}
                thumbnail={avatarSrc}
                width={avatarDimensions.width}
                height={avatarDimensions.height}
                cropped={false}
              >
                {({ ref, open }) => (
                  <div
                    ref={ref}
                    onClick={open}
                    className="cursor-pointer h-full"
                  >
                    <Avatar
                      src={avatarSrc}
                      alt="Autolnk Avatar Photo"
                      className="w-full h-full text-large"
                      size="lg"
                      radius="full"
                      isBordered
                      showFallback
                    />
                  </div>
                )}
              </Item>
            ) : (
              <Avatar
                className="w-full h-full text-large"
                size="lg"
                radius="full"
                isBordered
                showFallback
              />
            )}
          </div>
          {children}
        </div>
      </div>
    </Gallery>
  );
}
