"use client";
import { EditUserProfile } from "../../../user/components/EditUserProfile";
import { APP_ROLES } from "../constants";
import { RenderActions } from "./render-action";
import { HeroGallery } from "./hero-gallery";
import { Image } from "@nextui-org/react";
import NextImage from "next/image";
import { useSessionData } from "@/lib/hooks/useSession";
import { CALICREAMING_ORGANIZER_ID } from "@/lib/utils/constants";

export async function Hero({ details }: any) {
  const { data: sessionData } = await useSessionData();
  const avatarSrc = details?.orgDetails?.org?.owner?.avatar;
  const coverSrc = details?.orgDetails?.org?.owner?.coverPhoto;
  const isOwner = details?.orgDetails?.org?.owner?.id === sessionData?.user?.id;
  const isCrownShow =
    details?.orgDetails?.org?.owner?.id === CALICREAMING_ORGANIZER_ID;

  return (
    <div className="flex flex-col">
      <HeroGallery coverSrc={coverSrc} avatarSrc={avatarSrc}>
        <div className="flex md:flex-row flex-col w-full">
          <div className="sm:mb-2 w-[85%] md:w-[100%] md:mb-2 md:mt-7 flex-col gap-0 md:gap-2 flex justify-center mt-2">
            <div className="flex justify-between items-end pt-5 mr-2 md:mr-4">
              <div className="flex mt-1 md:mt-12 items-center gap-[6px]">
                <h1 className="font-semibold text-lg sm:text-2xl md:text-3xl font-sfPro text-ellipsis">
                  {details?.orgDetails?.org?.owner?.name}
                </h1>
                {details?.orgDetails?.org?.isVerified && (
                  <Image
                    src={isCrownShow ? "/crown.svg" : "/verified.svg"}
                    alt="AutoLNK Verified"
                    width={30}
                    height={30}
                    className="w-4 h-4 md:w-6 md:h-6"
                    as={NextImage}
                  />
                )}
              </div>
              <div className="hidden md:flex mt-12">
                {isOwner ? (
                  <div className="mr-2 md:mr-0 md:mb-1">
                    <EditUserProfile title="Edit" />
                  </div>
                ) : (
                  <div className="flex flex-row gap-x-2 mr-2 md:mr-3">
                    <RenderActions
                      userId={details?.orgDetails?.org?.owner?.id}
                      orgSlug={details?.orgDetails?.org?.slug}
                    />
                  </div>
                )}
              </div>
            </div>
            <p className="text-xs sm:text-sm md:text-xl font-normal text-[#1D1D1F7A]">
              {details?.orgDetails?.org?.upcomingEventsCount ?? "No"} upcoming
              events
            </p>
          </div>
          {isOwner ? (
            <div className="mr-2 flex md:hidden md:mr-0 md:mb-1 mt-2">
              <EditUserProfile title="Edit" />
            </div>
          ) : (
            <div className="flex md:hidden flex-row gap-x-2 mr-2 mt-3">
              <RenderActions
                userId={details?.orgDetails?.org?.owner?.id}
                orgSlug={details?.orgDetails?.org?.slug}
              />
            </div>
          )}
        </div>
      </HeroGallery>
    </div>
  );
}
