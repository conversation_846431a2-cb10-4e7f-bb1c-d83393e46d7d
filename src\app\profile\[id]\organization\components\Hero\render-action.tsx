"use client";
import OrganizationFollow from "@/app/events/[slug]/components/OrganizationFollow";
import { Authenticator } from "@/components/Authenticator";
import { useSessionData } from "@/lib/hooks/useSession";
import { AUTH_STATUS } from "@/lib/utils/constants";
import { Button } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import React, { ReactNode } from "react";
import Link from "next/link";

export function RenderActions({
  userId,
  orgSlug,
}: {
  userId: string;
  orgSlug: string;
}) {
  const { status } = useSessionData();
  const navigate = useRouter();
  const isAuthenticated: boolean = status === AUTH_STATUS.AUTHENTICATED;

  function onMessage(): void {
    navigate.push(`/message?userId=${userId}`);
  }

  const renderButton = (
    label: string,
    onClick: () => void,
    className: string,
    requireAuth: boolean = false
  ): ReactNode => {
    const button = (
      <Button
        variant="solid"
        className={className}
        radius="sm"
        size="sm"
        onPress={isAuthenticated ? onClick : undefined}
      >
        {label}
      </Button>
    );

    return requireAuth && !isAuthenticated ? (
      <Link href="/auth/signin">{button}</Link>
    ) : (
      button
    );
  };

  const actionButtons = (
    <>
      {/* {renderButton(
        "LNK",
        createLNKs,
        "w-full h-[2rem] bg-[#009aff] font-normal text-gray-50"
      )} */}
      <OrganizationFollow orgSlug={orgSlug} />
      {renderButton(
        "Message",
        onMessage,
        "w-fit h-[2rem] font-semibold text-gray-800 bg-gray-200"
      )}
    </>
  );

  return isAuthenticated ? (
    actionButtons
  ) : (
    <>
      {/* {renderButton(
        "LNK",
        createLNKs,
        "w-full h-[2rem] bg-[#009aff] font-normal text-gray-50",
        true
      )} */}
      <OrganizationFollow orgSlug={orgSlug} />
      {renderButton(
        "Message",
        onMessage,
        "w-fit h-[2rem] font-semibold text-gray-800 bg-gray-200",
        true
      )}
    </>
  );
}
