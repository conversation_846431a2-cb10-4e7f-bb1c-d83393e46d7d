import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import React from "react";
import { NO_DATA_AVAILABLE_IMAGE } from "../constants";

export function EmptyState({ title }: { title: string }) {
  return (
    <div className="flex flex-col justify-center items-center">
      <Card isFooterBlurred radius="lg" className="border-none max-w-[340px]">
        <Image
          alt="No content"
          className="object-cover"
          height={300}
          src={NO_DATA_AVAILABLE_IMAGE}
          width={300}
        />
        <CardFooter className="justify-center text-center before:bg-white/10 border-white/20 border-1 overflow-hidden py-1 absolute before:rounded-xl rounded-large bottom-1 w-[calc(100%_-_8px)] shadow-small ml-1 z-10">
          <p className="text-tiny text-black/80">{title}</p>
        </CardFooter>
      </Card>
    </div>
  );
}
