"use client";
import { Tab, Tabs } from "@nextui-org/react";
import React, { useState, useEffect } from "react";
import { RenderTabBody } from "./renderTabBody";
import { useGetEventsByOrgIdOrSlugQuery } from "@/lib/redux/slices/events/eventsApi";
import { useGetProductsListQuery } from "@/lib/redux/slices/products/productsApi";

const LoadingSkeleton = () => (
  <div className="max-w-[1400px] xl:max-w-[1600px] flex flex-col flex-wrap gap-4 items-center justify-center md:w-full animate-pulse">
    <div className="mx-4 w-full md:w-auto">
      <div className="flex gap-2 mb-4">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="h-10 bg-gray-200 rounded-lg w-32 md:w-56"
          ></div>
        ))}
      </div>
    </div>
    <div className="w-full rounded-xl p-2 mb-3 md:mb-5">
      <div className="h-64 bg-gray-200 rounded-lg"></div>
    </div>
  </div>
);

export function SegmentedControl({ details }: any) {
  const orgSlug = details?.orgDetails?.org?.slug;

  // Fetch events data
  const {
    data: activeEvents,
    isLoading: isLoadingActiveEvents,
    isFetching: isFetchingActiveEvents,
  } = useGetEventsByOrgIdOrSlugQuery(
    { orgIdOrSlug: orgSlug, status: "active", cursor: "10:0:0", perPage: 10 },
    { skip: !orgSlug }
  );

  const {
    data: pastEvents,
    isLoading: isLoadingPastEvents,
    isFetching: isFetchingPastEvents,
  } = useGetEventsByOrgIdOrSlugQuery(
    { orgIdOrSlug: orgSlug, status: "expired", cursor: "10:0:0", perPage: 10 },
    { skip: !orgSlug }
  );

  // Fetch products data
  const {
    data: products,
    isLoading: isLoadingProducts,
    isFetching: isFetchingProducts,
  } = useGetProductsListQuery({ orgSlug }, { skip: !orgSlug });

  const isLoading =
    isLoadingActiveEvents || isLoadingPastEvents || isLoadingProducts;
  const isFetching =
    isFetchingActiveEvents || isFetchingPastEvents || isFetchingProducts;

  const allTabs = [
    {
      title: "Events",
      key: "events",
      hasData: !!activeEvents?.results?.length,
      priority: 1,
    },
    {
      title: "Past Events",
      key: "past-events",
      hasData: !!pastEvents?.results?.length,
      priority: 2,
    },
    {
      title: "Merchandise",
      key: "merchandise",
      hasData: !!products?.results?.length,
      priority: 3,
    },
    {
      title: "Calendar",
      key: "calender",
      hasData: !!activeEvents?.results?.length,
      priority: 4,
    },
    {
      title: "About",
      key: "about",
      hasData: !!details?.orgDetails?.org?.description,
      priority: 5,
    },
  ];

  const OrgTabs = allTabs
    .filter((tab) => tab.hasData)
    .sort((a, b) => a.priority - b.priority);

  const defaultTab = OrgTabs[0]?.key || "events";

  const [selection, setSelection] = useState(defaultTab);

  useEffect(() => {
    const newDefaultTab = OrgTabs[0]?.key || "events";
    setSelection(newDefaultTab);
  }, [activeEvents, pastEvents, products, details]);

  function handleSelection(key) {
    setSelection(key);
  }

  if (!orgSlug || isLoading) {
    return <LoadingSkeleton />;
  }

  if (OrgTabs.length === 0) {
    return null;
  }

  return (
    <div className="max-w-[1400px] xl:max-w-[1600px] flex flex-col flex-wrap gap-4 items-center justify-center md:w-full">
      <div className="mx-4 w-full md:w-auto overflow-x-auto scrollbar-hide">
        <Tabs
          aria-label="Tabs variants"
          placement="top"
          onSelectionChange={(key: React.Key) => handleSelection(key)}
          fullWidth
          className="w-full"
          selectedKey={selection}
          classNames={{
            tabList:
              "flex-nowrap min-w-full [&>button]:mr-2 [&>button:last-child]:mr-0",
            tab: "flex-shrink-0 font-medium text-medium",
            cursor: "w-full",
          }}
        >
          {OrgTabs.map((tab) => (
            <Tab
              key={tab.key}
              title={tab.title}
              className="w-30 sm:w-32 md:w-56"
            />
          ))}
        </Tabs>
      </div>
      <div className="w-full rounded-xl p-2 mb-3 md:mb-5">
        <RenderTabBody currentTab={selection} details={details} />
      </div>
    </div>
  );
}
