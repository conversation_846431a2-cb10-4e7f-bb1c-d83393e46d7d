import { Card, CardBody } from "@nextui-org/react";
import parse from "html-react-parser";
import React from "react";

export function RenderAbout({ details }) {
  return (
    <Card className="bg-gray-50 md:mx-5">
      <CardBody className="min-h-36 md:min-h-80 p-5">
        <p className="text-gray-800 font-sfPro">
          {parse(
            details?.orgDetails?.org?.description || "No description available"
          )}
        </p>
      </CardBody>
    </Card>
  );
}
