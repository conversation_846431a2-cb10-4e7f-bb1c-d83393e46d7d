"use client";
import { memo, useState, useEffect, useRef } from "react";
import EventCard from "@/components/EventCard/EventCard";
import ShimmerCard from "@/components/ShimmerCard/ShimmerCard";
import { useGetEventsByOrgIdOrSlugQuery } from "@/lib/redux/slices/events/eventsApi";
import { EmptyState } from "./emptyState";
import type { Event } from "@/lib/redux/slices/events/eventTypes";
import { EventImage } from "@/app/events/types";

interface OrgDetails {
  org: {
    id: string;
  };
}

interface RenderEventsProps {
  details?: {
    orgDetails?: OrgDetails;
  };
  status?: string;
}

const SHIMMER_COUNT = 8;
const GRID_CLASSES =
  "w-full grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 justify-items-center";

const LoadingSkeleton = memo(() => (
  <div className={GRID_CLASSES}>
    {Array.from({ length: SHIMMER_COUNT }).map((_, index) => (
      <ShimmerCard key={`shimmer-${index}`} />
    ))}
  </div>
));

LoadingSkeleton.displayName = "LoadingSkeleton";

const EventCardMemo = memo(({ event }: { event: Event }) => {
  const mainEventImg = event?.images?.find(
    (img: EventImage) => Number(img?.type) === 1
  );
  return (
    <EventCard
      title={event.name}
      date={event.startDate}
      city={event.city}
      state={event.state}
      imgSrc={mainEventImg?.photo}
      href={`/events/${event.slug}`}
      eventId={event.id}
      timezone={event?.timezone}
      tag={event?.metadata?.tag}
      pixels={event?.organization?.pixels || {}}
    />
  );
});

EventCardMemo.displayName = "EventCardMemo";

export const RenderEvents = memo(function RenderEvents({
  details,
  status = "active",
}: RenderEventsProps) {
  const orgSlug = details?.orgDetails?.org?.slug;
  const [allEvents, setAllEvents] = useState<Event[]>([]);
  const [currentCursor, setCurrentCursor] = useState<string>("10:0:0");
  const [hasMore, setHasMore] = useState<boolean>(true);
  const loadingRef = useRef<HTMLDivElement>(null);
  const shouldLoadMore = useRef<boolean>(false);

  const {
    data: events,
    isLoading,
    isFetching,
  } = useGetEventsByOrgIdOrSlugQuery(
    {
      // @ts-ignore - We're skipping the query if orgSlug is not present
      orgIdOrSlug: orgSlug,
      status,
      cursor: currentCursor,
      perPage: 10,
      orderBy: status === "active" ? "start_date" : "-start_date",
    },
    {
      skip: !orgSlug,
      refetchOnMountOrArgChange: true,
    }
  );

  // Process events data when it arrives from RTK Query
  useEffect(() => {
    if (!events || !events.results) return;

    if (currentCursor === "10:0:0") {
      // Initial load - set events
      setAllEvents(events.results);
    } else {
      // Pagination - add new events
      const existingIds = new Set(allEvents.map((event) => event.id));
      const newEvents = events.results.filter(
        (event) => !existingIds.has(event.id)
      );

      if (newEvents.length > 0) {
        setAllEvents((prev) => [...prev, ...newEvents]);
      }
    }

    // Update pagination state
    setHasMore(events.nextPageResults || false);
    shouldLoadMore.current = false;
  }, [events, currentCursor, allEvents]);

  useEffect(() => {
    if (
      events?.nextCursor &&
      hasMore &&
      !isFetching &&
      shouldLoadMore.current
    ) {
      setCurrentCursor(events.nextCursor);
      shouldLoadMore.current = false;
    }
  }, [events?.nextCursor, hasMore, isFetching]);

  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    if (isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (
          entry.isIntersecting &&
          hasMore &&
          !isFetching &&
          events?.nextCursor
        ) {
          console.log("Loading element is intersecting, should load more");
          shouldLoadMore.current = true;
          setCurrentCursor(events.nextCursor);
        }
      },
      { threshold: 0.1 } // 10% of the element is visible
    );

    const currentLoadingRef = loadingRef.current;
    if (currentLoadingRef) {
      observer.observe(currentLoadingRef);
    }

    return () => {
      if (currentLoadingRef) {
        observer.unobserve(currentLoadingRef);
      }
    };
  }, [events?.nextCursor, hasMore, isFetching, isLoading]);

  useEffect(() => {
    if (!hasMore || isLoading) return;

    let lastScrollY = window.scrollY;

    const handleScroll = () => {
      if (isFetching || !events?.nextCursor) return;

      const currentScrollY = window.scrollY;
      const isScrollingDown = currentScrollY > lastScrollY;
      const scrollPercentage =
        (currentScrollY + window.innerHeight) / document.body.offsetHeight;
      const bottom = scrollPercentage > 0.8; // Only trigger when scrolled 80% down the page

      // Only load more if scrolling down AND near bottom
      if (bottom && isScrollingDown) {
        shouldLoadMore.current = true;
        setCurrentCursor(events.nextCursor);
      }

      lastScrollY = currentScrollY;
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [events?.nextCursor, hasMore, isFetching, isLoading]);

  if (isLoading && allEvents.length === 0) {
    return <LoadingSkeleton />;
  }

  if (!allEvents.length && !isLoading) {
    return (
      <div className="min-h-[80vh] flex justify-center items-center">
        <EmptyState title="No events available!" />
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center mb-12 w-full">
      <div className={GRID_CLASSES}>
        {allEvents.map((event) => (
          <EventCardMemo key={event.id} event={event} />
        ))}
      </div>

      {hasMore && (
        <div ref={loadingRef} className="w-full flex justify-center p-6 mt-4">
          {isFetching ? (
            <div className="flex space-x-3">
              <div
                className="w-3 h-3 bg-primary rounded-full animate-bounce"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="w-3 h-3 bg-primary rounded-full animate-bounce"
                style={{ animationDelay: "150ms" }}
              ></div>
              <div
                className="w-3 h-3 bg-primary rounded-full animate-bounce"
                style={{ animationDelay: "300ms" }}
              ></div>
            </div>
          ) : (
            <div className="h-10 flex items-center justify-center">
              {/* This invisible element ensures the ref has height even when not loading */}
              <span className="sr-only">Load more</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
});
