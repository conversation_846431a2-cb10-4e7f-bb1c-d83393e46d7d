import React, { useState, useEffect, useMemo } from "react";
import { formatIsoDate } from "@/lib/utils/date";
import EventCalendar from "../EventCalendar/EventCalendar";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import CalendarSkeletonLoader from "../EventCalendar/CalendarSkeleton";
import { useSessionData } from "@/lib/hooks/useSession";

interface RenderEventsCalendarProps {
  details: {
    orgDetails: {
      org: {
        id: string;
      };
    };
  };
}

const RenderEventsCalendar: React.FC<RenderEventsCalendarProps> = ({
  details,
}) => {
  const [allEvents, setAllEvents] = useState<IEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const orgSlug = details?.orgDetails?.org?.slug;

  const { data: session } = useSessionData();
  const getCurrentAndNext12MonthsDates = () => {
    const today = new Date();
    const currentMonth = today?.getMonth();
    const currentYear = today?.getFullYear();

    const firstDate = new Date(currentYear, currentMonth, 1);

    const lastDate = new Date(currentYear + 1, currentMonth, 0);

    // Format dates to YYYY-MM-DD
    const formatDate = (date: Date) => {
      return date?.toISOString()?.split("T")[0];
    };

    return {
      firstDate: formatDate(firstDate),
      lastDate: formatDate(lastDate),
    };
  };

  const { firstDate, lastDate } = getCurrentAndNext12MonthsDates();
  const dateQueryString = `${firstDate};after,${lastDate};before`;

  useEffect(() => {
    // this is a workaround to fetch all events for the year cuz there;s a possibility events count are more than 100 for the year and we need to fetch all events for the year
    const fetchAllEvents = async () => {
      if (!orgSlug) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      const userTimeZone =
        session?.user?.userTimezone ||
        Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone;

      let cursor: string | null = null;
      let hasMore = true;
      const fetchedEvents: IEvent[] = [];

      while (hasMore) {
        try {
          const url = `${
            process.env.NEXT_PUBLIC_API_URL
          }api/channels/${orgSlug}/events/public/?include_collab_events=true&status=active&${
            cursor ? `cursor=${cursor}&` : ""
          }date=${dateQueryString}`;
          const response = await fetch(url, {
            method: "GET",
            cache: "no-store",
            headers: {
              "X-Timezone": userTimeZone,
            },
            credentials: "include",
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response?.status}`);
          }

          const data: IEventsResponse = keysToCamel(await response.json());
          fetchedEvents.push(...data?.results);

          if (data?.nextPageResults && data?.nextCursor) {
            cursor = data.nextCursor;
          } else {
            hasMore = false;
          }
        } catch (e) {
          setError(
            e instanceof Error ? e.message : "An unknown error occurred"
          );
          hasMore = false;
        }
      }

      setAllEvents(fetchedEvents);
      setIsLoading(false);
    };

    fetchAllEvents();
  }, [orgSlug]);

  const formattedEventsData = useMemo(() => {
    return allEvents.map((event) => ({
      id: event.id,
      name: event.name,
      start: formatIsoDate(event.startDate),
      end: formatIsoDate(event.endDate),
      image: event.images[0]?.photo,
      slug: event.slug,
    }));
  }, [allEvents]);

  if (isLoading) {
    return <CalendarSkeletonLoader />;
  }

  if (error) {
    return (
      <div className="text-red-500 text-center w-full m-5">
        Error loading events: {error}
      </div>
    );
  }

  return (
    <div>
      <EventCalendar events={formattedEventsData} />
    </div>
  );
};

export default RenderEventsCalendar;
