import { useGetProductsListQuery } from "@/lib/redux/slices/products/productsApi";
import { useState } from "react";
import { Card, Skeleton, useDisclosure } from "@nextui-org/react";
import { ProductCard } from "@/app/events/[slug]/components/merch/ProductCard";
import { Product } from "@/app/events/types";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import { EmptyState } from "./emptyState";
import { useRouter } from "next/navigation";

export function RenderMerchandise({ details }: any) {
  const { data, isLoading } = useGetProductsListQuery(
    {
      orgSlug: details?.orgDetails?.org?.slug,
    },
    { skip: !details?.orgDetails?.org?.slug }
  );
  const router = useRouter();

  const handleProductClick = (product: Product) => {
    router.push(`/organization/${details?.orgDetails?.org?.slug}/products/${product.slug}`);
  };

  if (isLoading) {
    const SHIMMER_COUNT = 10;

    return (
      <div className="w-full grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 justify-items-center">
        {Array.from({ length: SHIMMER_COUNT }).map((_, index) => (
          <div key={`shimmer-${index}`} className="w-full h-full">
            <Card className="shadow-none border-1.5 lg:w-full" radius="lg">
              <Skeleton className="rounded-lg rounded-b-none aspect-square">
                <div className="rounded-lg bg-secondary"></div>
              </Skeleton>
            </Card>
            <div className="w-full mt-3 grid gap-3 md:flex md:justify-between">
              <div className="w-full flex flex-col gap-2">
                <Skeleton className="h-3 w-3/5 rounded-lg" />
                <Skeleton className="h-3 w-4/5 rounded-lg" />
              </div>
              <div>
                <Skeleton className="flex rounded-md w-full h-6" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!data?.results?.length) {
    return (
      <div className="min-h-[80vh] flex justify-center items-center">
        <EmptyState title="No merchandise available!" />
      </div>
    );
  }

  const camelCaseProducts = keysToCamel(data?.results);

  return (
    <div className="mt-6 lg:mt-12">
      <div className="mt-3">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {camelCaseProducts.map((product: Product) => (
            <ProductCard
              key={product.id}
              product={product}
              onClick={handleProductClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
