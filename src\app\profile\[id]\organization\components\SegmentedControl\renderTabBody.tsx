import React from "react";
import { RenderEvents } from "./renderEvents";
import { RenderAbout } from "./renderAbout";
import RenderEventsCalendar from "./renderEventsCalender";
import { RenderMerchandise } from "./renderMerchandise";
import { EmptyState } from "./emptyState";
import { useGetEventsByOrgIdOrSlugQuery } from "@/lib/redux/slices/events/eventsApi";
import { useGetProductsListQuery } from "@/lib/redux/slices/products/productsApi";

export function RenderTabBody({ currentTab, details }) {
  const orgSlug = details?.orgDetails?.org?.slug;

  // Fetch data for each tab
  const { data: activeEvents, isLoading: isLoadingActiveEvents } =
    useGetEventsByOrgIdOrSlugQuery(
      { orgIdOrSlug: orgSlug, status: "active", cursor: "10:0:0", perPage: 10 },
      { skip: !orgSlug }
    );

  const { data: pastEvents, isLoading: isLoadingPastEvents } =
    useGetEventsByOrgIdOrSlugQuery(
      { orgIdOrSlug: orgSlug, status: "expired", cursor: "10:0:0", perPage: 10 },
      { skip: !orgSlug }
    );

  const { data: products, isLoading: isLoadingProducts } =
    useGetProductsListQuery({ orgSlug }, { skip: !orgSlug });

  if (!details?.orgDetails?.org) {
    return (
      <div className="min-h-[80vh] flex justify-center items-center">
        <EmptyState title="No organization data available!" />
      </div>
    );
  }

  switch (currentTab) {
    case "about":
      return <RenderAbout details={details} />;
    case "calender":
      if (isLoadingActiveEvents) {
        return (
          <div className="min-h-[80vh] flex justify-center items-center">
            Loading calendar...
          </div>
        );
      }
      if (!activeEvents?.results?.length) {
        return (
          <div className="min-h-[80vh] flex justify-center items-center">
            <EmptyState title="No events available for calendar view!" />
          </div>
        );
      }
      return (
        <div>
          <RenderEventsCalendar details={details} />
        </div>
      );
    case "past-events":
      if (isLoadingPastEvents) {
        return (
          <div className="min-h-[80vh] flex justify-center items-center">
            Loading past events...
          </div>
        );
      }
      if (!pastEvents?.results?.length) {
        return (
          <div className="min-h-[80vh] flex justify-center items-center">
            <EmptyState title="No past events available!" />
          </div>
        );
      }
      return (
        <div>
          <RenderEvents details={details} status="expired" />
        </div>
      );
    case "merchandise":
      if (isLoadingProducts) {
        return (
          <div className="min-h-[80vh] flex justify-center items-center">
            Loading merchandise...
          </div>
        );
      }
      if (!products?.results?.length) {
        return (
          <div className="min-h-[80vh] flex justify-center items-center">
            <EmptyState title="No merchandise available!" />
          </div>
        );
      }
      return <RenderMerchandise details={details} />;
    default:
      if (isLoadingActiveEvents) {
        return (
          <div className="min-h-[80vh] flex justify-center items-center">
            Loading events...
          </div>
        );
      }
      if (!activeEvents?.results?.length) {
        return (
          <div className="min-h-[80vh] flex justify-center items-center">
            <EmptyState title="No events available!" />
          </div>
        );
      }
      return <RenderEvents details={details} status="active" />;
  }
}
