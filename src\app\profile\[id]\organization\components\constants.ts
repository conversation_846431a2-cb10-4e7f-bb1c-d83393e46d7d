export const EDIT_STATUS = {
  ABOUT: "ABOUT",
  PROFILE_PICTURE: "PROFILE_PICTURE",
  COVER_PHOTO: "COVER_PHOTO",
  DEFAULT: "DEFAULT",
};

export const APP_ROLES = {
  OWNER: "owner",
  MEMBER: "member",
};

export const NO_DATA_AVAILABLE_IMAGE =
  "https://cdni.iconscout.com/illustration/premium/thumb/product-is-empty-illustration-download-in-svg-png-gif-file-formats--no-records-list-record-emply-data-user-interface-pack-design-development-illustrations-6430781.png?f=webp";



  export const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  export const daysOfWeek = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];

  export const isLeapYear = (year) =>
    (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;

  export const getDaysInMonth = (year, month) => {
    const daysInMonth = [
      31,
      isLeapYear(year) ? 29 : 28,
      31,
      30,
      31,
      30,
      31,
      31,
      30,
      31,
      30,
      31,
    ];
    return daysInMonth[month];
  };

  export const isPastDate = (checkYear, checkMonth, checkDay) => {
    const today = new Date();
    const checkDate = new Date(checkYear, checkMonth, checkDay);
    return checkDate < new Date(today.getFullYear(), today.getMonth(), today.getDate());
  };