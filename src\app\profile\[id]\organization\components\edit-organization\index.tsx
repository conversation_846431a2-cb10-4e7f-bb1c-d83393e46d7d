"use client";
import { EditProfileModal } from "@/components/EditProfileModal";
import { useSessionData } from "@/lib/hooks/useSession";
import { useUpdateOrganizationMutation } from "@/lib/redux/slices/organization/api";

interface EditOrganizationProps {
  title: string;
}

export function EditOrganization({ title }: EditOrganizationProps) {
  const { data: sessionData, update } = useSessionData();
  const [updateOrgMutation, { isLoading }] = useUpdateOrganizationMutation();
  const orgSlug = sessionData?.user?.orgDetails?.org?.slug;

  const data = {
    coverPhoto: sessionData?.user?.orgDetails?.org?.coverPhoto || "_",
    profilePhoto: sessionData?.user?.orgDetails?.org?.avatar || "_",
    bio: sessionData?.user?.orgDetails?.org?.description || "",
  };

  const handleSave = async (payload: any, actionType: string) => {
    try {
      const updatedPayload = {
        orgSlug,
        body: payload,
      };
      const response = await updateOrgMutation(updatedPayload);
      return response;
    } catch (err) {
      console.error(`${actionType} save error:`, err);
    }
  };

  const controller = {
    onCoverPhotoSave: (payload: any) => handleSave(payload, "CoverPhoto"),
    onProfilePhotoSave: (payload: any) => handleSave(payload, "ProfilePhoto"),
    onBioSave: (payload: any) => handleSave(payload, "Bio"),
  };

  return (
    <EditProfileModal
      title={title}
      data={data}
      controller={controller}
      isLoading={isLoading}
    />
  );
}
