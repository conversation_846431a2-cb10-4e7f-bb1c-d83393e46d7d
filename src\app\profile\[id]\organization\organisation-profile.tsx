import React from "react";
import { auth } from "@/auth.config";
import { <PERSON> } from "./components/Hero";
import { SegmentedControl } from "./components/SegmentedControl";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import { StoreSessions } from "@/features/Analytics";
import { getUserTimeZone } from "@/lib/utils/getUserTimeZone";
import ErrorBoundary from "@/components/ErrorBoundary";

interface OrganizationProfileProps {
  orgDetails: any;
}
const getCustomOrgPermissions = async (
  accessToken: string | undefined,
  orgSlug: string
): Promise<{ responseObj: any }> => {
  const userTimeZone = await getUserTimeZone();
  const getCustomOrg = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}api/channels/${orgSlug}/channels-members/me/`,
    {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "X-Timezone": userTimeZone,
      },
      credentials: "include",
    }
  );

  const headers: HeadersInit = accessToken
    ? { Authorization: `Bearer ${accessToken}`, "X-Timezone": userTimeZone }
    : {};

  const getOrgById = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}api/channels/${orgSlug}/`,
    {
      method: "GET",
      headers: headers,
      credentials: "include",
    }
  );
  const getOrgByIdResponse = getOrgById.ok
    ? await getOrgById.json()
    : { slug: orgSlug };

  const getCustomOrgResponse = getCustomOrg.ok
    ? await getCustomOrg.json()
    : { role: "member" };

  let responseObj = {};

  responseObj = {
    org_details: {
      org: {
        ...getOrgByIdResponse,
      },
      role: getCustomOrgResponse?.role, // Assigning the role
    },
  };

  return { responseObj: keysToCamel(responseObj) };
};
const OrganizationProfile: React.FC<OrganizationProfileProps> = async ({
  orgDetails,
}: OrganizationProfileProps) => {
  const session = await auth();

  const { responseObj: orgDetailsById } = await getCustomOrgPermissions(
    session?.accessToken,
    orgDetails?.slug
  );

  return (
    <main>
      <ErrorBoundary>
        <StoreSessions orgSlug={orgDetails?.slug} />
      </ErrorBoundary>
      <div className="max-w-[1200px] xl:max-w-[1400px] min-h-screen mx-auto mb-4 mt-20 md:px-5 px-0 flex flex-col gap-y-10">
        <Hero details={orgDetailsById} />
        <SegmentedControl details={orgDetailsById} />
      </div>
    </main>
  );
};

export default OrganizationProfile;
