import React from "react";
import { redirect, useParams } from "next/navigation";
import { auth } from "@/auth.config";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import { USER_ENDPOINTS } from "@/lib/redux/slices/user/userApiEndpoints";
import { getUserTimeZoneWithSession } from "@/lib/utils/getUserTimeZone";
import OrganizationProfile from "./organization/organisation-profile";
import UserProfile from "./user/user-profile";

async function GetUserDetails(id, retryCount = 0, maxRetries = 3) {
  try {
    const session = await auth();
    const userTimeZone = await getUserTimeZoneWithSession(session);
    const endpoint = `${process.env.NEXT_PUBLIC_API_URL}api${USER_ENDPOINTS.profileById}/${id}/profile/`;
    const vehiclesList = await fetch(endpoint, {
      headers: {
        "X-Timezone": userTimeZone,
      },
      cache: "no-store",
      credentials: "include",
    });

    if (vehiclesList.ok) {
      const data = keysToCamel(await vehiclesList.json());
      return { data };
    } else {
      // If we haven't reached max retries, try again
      if (retryCount < maxRetries) {
        console.log(
          `Retrying fetch attempt ${retryCount + 1} of ${maxRetries}`
        );
        // Wait for 1 second before retrying
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return GetUserDetails(id, retryCount + 1, maxRetries);
      }
      return {
        error: vehiclesList.statusText || "Error fetching profile",
      };
    }
  } catch (e: any) {
    // If we haven't reached max retries, try again
    if (retryCount < maxRetries) {
      console.log(`Retrying fetch attempt ${retryCount + 1} of ${maxRetries}`);
      // Wait for 1 second before retrying
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return GetUserDetails(id, retryCount + 1, maxRetries);
    }
    console.error("Error fetching profile details:", e);
    return { error: e?.cause?.err?.message || "Error fetching profile" };
  }
}

const Profile = async ({ params }) => {
  // Show loading state while fetching data
  const loadingState = (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <h1 className="text-2xl font-bold text-gray-600">Loading profile...</h1>
      </div>
    </div>
  );

  const { data, error } = await GetUserDetails(params?.id);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Error</h1>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return loadingState;
  }

  // If it's an organization profile, render it without requiring authentication
  if (data?.orgDetails?.id) {
    return <OrganizationProfile orgDetails={data?.orgDetails} />;
  }

  // For user profiles, require authentication
  const session = await auth();
  if (!session) {
    redirect("/");
  }

  return <UserProfile data={data} session={session} />;
};

export default Profile;
