
interface Price {
    amount: string;
    currency: string;
  }


export interface EventTicket {
    id: string;
    event: {
      id: string;
      name: string;
      slug: string;
      startDate: string;
      endDate: string;
      address: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
      timezone: string;
      status: string;
      organization: {
        slug: string;
        owner: {
          id: string;
          name: string;
          avatar: string;
          username: string;
          bio: string;
          coverPhoto: string;
          firstName: string;
          lastName: string;
        };
      };
      image: string;
    };
    order: {
      id: string;
      number: number;
      createdAt: string;
      status: string;
      organization: {
        slug: string;
        owner: {
          id: string;
          name: string;
          avatar: string;
          username: string;
          bio: string;
          coverPhoto: string;
          firstName: string;
          lastName: string;
        };
      };
      userDetails: {
        email: string;
        name: string;
      };
      total: Price;
      subtotal: Price;
      platformFee: Price;
      processingFee: Price;
    };
    approvalRequired: boolean;
    pdfTicketUrl: string;
    qrTicketUrl: {
      url: string;
      ticketNumber: string;
    }[];
    walletPassUrl: string[];
    walletPassesUrl: string | null;
    merchPdfUrl: string | null;
    isVip: boolean;
    ticketType: string;
    quantity: number;
    productName: string;
    variantName: string;
    productSku: string;
    productVariantId: string;
  }

  