const DottedDivider = ({ className }: { className?: string }) => (
  <div
    className={`border-black border-dotted self-stretch ${className} relative`}
  >
    <div className="hidden sm:block bg-gray-100 w-5 h-5 absolute -top-6 -right-2 rounded-full" />
    <div className="hidden sm:block bg-gray-100 w-5 h-5 absolute -bottom-6 -right-2 rounded-full" />
    <div className="block sm:hidden  bg-gray-100 w-5 h-5 absolute -top-3 -left-2 rounded-full" />
    <div className="block sm:hidden  bg-gray-100 w-5 h-5 absolute -top-3 -right-2 rounded-full" />
  </div>
);

export default DottedDivider;
