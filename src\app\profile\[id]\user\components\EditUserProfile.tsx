"use client";
import { EditProfileModal } from "@/components/EditProfileModal";
import { useSessionData } from "@/lib/hooks/useSession";
import {
  useMeMutation,
  useUpdateUserProfileMutation,
} from "@/lib/redux/slices/user/userApi";

interface EditUserProfileProps {
  title: string;
}

export function EditUserProfile({ title }: EditUserProfileProps) {
  const { data: sessionData, update } = useSessionData();
  const [updateUserMutation, { isLoading }] = useMeMutation();
  const [updateUserDetails, { isLoading: profileUploading }] =
    useUpdateUserProfileMutation();
  const data = {
    coverPhoto: sessionData?.user?.coverPhoto || "_",
    profilePhoto: sessionData?.user?.avatar || "_",
    bio: sessionData?.user?.bio || "",
  };

  const controller = {
    onCoverPhotoSave: async (payload: any) => {
      // Implement the save logic
      try {
        const reupload = await updateUserMutation(payload);
        return reupload;
      } catch (err) {
        console.log(err);
      }
    },
    onProfilePhotoSave: async (payload: any) => {
      try {
        console.log("coverphotosave", payload);
        const reupload = await updateUserMutation(payload);
        return reupload;
      } catch (err) {
        console.log(err);
      }
    },
    onBioSave: async (payload: any) => {
      try {
        console.log("coverphotosave", payload);
        const reupload = await updateUserMutation({ bio: payload.description });
        return reupload;
      } catch (err) {
        console.log(err);
      }
    },
  };
  return (
    <EditProfileModal
      title={title}
      data={data}
      controller={controller}
      isLoading={isLoading}
    />
  );
}
