import { useSessionData } from "@/lib/hooks/useSession";
import { useMeMutation } from "@/lib/redux/slices/user/userApi";
import { getName } from "@/lib/utils/string";
import { useState } from "react";
import { MdEdit } from "react-icons/md";

export function EditUserName() {
  const [isEditing, setIsEditing] = useState(false);
  const { data: session, status } = useSessionData();
  const [name, setName] = useState(
    getName(session?.user) || session?.user?.display_name
  );
  const [updateUserMutation] = useMeMutation();
  const [inputValue, setInputValue] = useState(name);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleInputChange = (e: any) => {
    setInputValue(e.target.value);
  };

  const handleSaveClick = async () => {
    try {
      const formData = new FormData();
      formData.append("name", inputValue);
      const reupload = await updateUserMutation(formData);
      setName(inputValue);
      setIsEditing(false);
    } catch (error) {
      console.error("Error updating name:", error);
    }
  };

  const handleCancelClick = () => {
    setInputValue(name);
    setIsEditing(false);
  };

  const handleInputBlur = async () => {
    if (!inputValue) return handleCancelClick();
    return await handleSaveClick();
  };
  return isEditing ? (
    <>
      <input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        className="outline-none border-b-2 border-b-gray-300 px-2 py-1"
        autoFocus
      />
    </>
  ) : (
    <>
      <h2
        className="group font-bold text-lg md:text-xl text-slate-800 dark:text-gray-50 relative cursor-pointer capitalize"
        onClick={handleEditClick}
      >
        {name}
        <MdEdit className="absolute top-0 -right-5 mt-1 ml-2 hidden group-hover:block" />
      </h2>
    </>
  );
}
