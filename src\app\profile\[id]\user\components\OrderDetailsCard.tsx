import { Card, CardBody } from "@nextui-org/react";
import Image from "next/image";
import React from "react";
import { EventTicket } from "./TicketsTable";
import { CART_STORAGE_KEYS } from "@/lib/utils/constants";

const OrderDetailsCard = ({
  selectedTicket,
}: {
  selectedTicket: EventTicket;
}) => {
  const getLocalVariants = localStorage.getItem(
    CART_STORAGE_KEYS.CART_VARIANTS
  );

  const localVariants = JSON.parse(getLocalVariants || "{}");

  const isProduct = selectedTicket.type === "product";
  const imageUrl = isProduct
    ? selectedTicket?.variant?.media?.[0]?.image ||
      localVariants[selectedTicket?.variant?.id]?.image
    : selectedTicket?.eventDetails?.eventImage?.[0]?.photo;
  const name = isProduct
    ? selectedTicket.productName
    : selectedTicket?.eventDetails?.name;
  const subtitle = isProduct
    ? selectedTicket.variantName
    : selectedTicket?.eventTicketDetails?.name;

  return (
    <div className="w-full">
      <Card>
        <CardBody className="px-7 py-5">
          <div className="flex gap-4 items-start">
            {imageUrl && (
              <Image
                src={imageUrl}
                alt={name || ""}
                width={100}
                height={100}
                className="w-20 h-20 rounded-lg object-cover aspect-square"
              />
            )}
            <div className="flex justify-between w-full">
              <div className="flex flex-col">
                <h2 className="font-semibold">{name}</h2>
                {subtitle && (
                  <div className="mt-[1px] text-[#616161] text-[13px]">
                    {subtitle}
                  </div>
                )}
                <div className="text-[#616161] text-[13px]">
                  ${Number(selectedTicket?.unitPrice?.amount)?.toFixed(2)}
                </div>
                {!isProduct && selectedTicket?.selectedVariants?.length > 0 && (
                  <div className="text-[#616161] text-[12px] mt-[5px]">
                    <div className="flex flex-col gap-1">
                      {selectedTicket?.selectedVariants?.map((merch, index) => (
                        <p key={index} className="">
                          {merch?.productDetails?.name}: {merch?.name}
                        </p>
                      ))}
                    </div>
                  </div>
                )}
                {isProduct && selectedTicket.variant && (
                  <div className="text-[#616161] text-[12px] mt-[5px]">
                    <p>SKU: {selectedTicket.variant.sku}</p>
                  </div>
                )}
              </div>
              <div className="flex gap-2 md:gap-[10px] text-[#616161] text-[14px] mt-1">
                <p>${Number(selectedTicket?.unitPrice?.amount)?.toFixed(2)}</p>
                <p>x</p>
                <p>{selectedTicket.quantity}</p>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default OrderDetailsCard;
