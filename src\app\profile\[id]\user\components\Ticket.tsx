"use client";

import { <PERSON><PERSON>, Divider } from "@nextui-org/react";
import Image from "next/image";
import { MdOutlineFileDownload } from "react-icons/md";
import DottedDivider from "./DottedDivider";
import { capitalizeAndSplitSnakeCase } from "@/lib/utils/string";
import { isZero } from "@/lib/utils/numberUtil";
import { handleApiError } from "@/lib/utils/errorUtils";
import QRCode from "react-qr-code";
import toast from "react-hot-toast";
import { useMemo } from "react";

interface EventTicketProps {
  bookingId: string | number;
  totalPrice: number;
  eventName: string;
  ticketCount: number;
  ticketPrice: string;
  eventImage: string | null;
  eventDateTime: string;
  status: string;
  pdfTicketUrl: string;
  platformFee: string;
  processingFee: string;
}

const Ticket: React.FC<EventTicketProps> = ({
  eventName,
  ticketCount,
  eventImage,
  ticketPrice,
  totalPrice,
  bookingId,
  eventDateTime,
  status,
  pdfTicketUrl,
  platformFee,
  processingFee,
}) => {
  const isPaidTicketWithTicketUrl = useMemo(
    () => totalPrice > 0 && pdfTicketUrl,
    [pdfTicketUrl, totalPrice]
  );
  const isPaidTicketWithoutTicketUrl = useMemo(
    () => totalPrice > 0 && !pdfTicketUrl,
    [pdfTicketUrl, totalPrice]
  ); // then this means the ticket is not yet generated
  const handleDownloadPDF = () => {
    try {
      if (isPaidTicketWithoutTicketUrl) {
        toast("Ticket not yet generated. Please wait!!", {
          icon: "🎟️",
        });
        return;
      }
      if (!pdfTicketUrl) throw new Error("Ticket not found");
      window.open(pdfTicketUrl, "_blank");
    } catch (error: any) {
      handleApiError(error, "Error downloading pdf ticket");
    }
  };
  return (
    <div className="bg-[#D9D9D9] rounded-lg p-1 sm:p-2 flex flex-col sm:flex-row items-start sm:items-center gap-2 justify-around overflow-x-hidden sm:overflow-x-auto  overflow-y-hidden">
      <div className="flex flex-row gap-4 relative">
        <Image
          src={eventImage || "/placeholder-image.jpg"}
          width={80}
          height={80}
          loading="lazy"
          alt="ticket"
          className="w-[35%] h-[35%] mx-auto sm:w-[90px] sm:h-[90px] object-cover rounded-lg"
        />
        <div className="w-[220px] flex flex-col gap-3 sm:gap-5 tracking-wider overflow-x-auto">
          <div className="flex flex-col gap-y-0 font-thin">
            <p className="bg-[#f6f6f69f] rounded-xl text-[11px] font-bold px-2 py-1 w-fit">
              {capitalizeAndSplitSnakeCase(status)}
            </p>
            <h1 className="text-base sm:text-[14px] font-semibold truncate">
              {eventName}
            </h1>
            <p className="text-xs">Date: {eventDateTime}</p>
            <p className="text-xs">Quantity: {ticketCount}</p>
          </div>
          <div className="w-full">
            <div className="flex justify-between">
              <p className="text-xs">Ticket Price</p>
              <p className="text-xs">${parseFloat(ticketPrice).toFixed(2)}</p>
            </div>
            {!isZero(totalPrice) && (
              <>
                <div className="flex justify-between">
                  <p className="text-xs">Platform Fee</p>
                  <p className="text-xs">${platformFee}</p>
                </div>
                <div className="flex justify-between">
                  <p className="text-xs">Card Processing Fee</p>
                  <p className="text-xs">${processingFee}</p>
                </div>
              </>
            )}
            <Divider className="border-l-1 border-black border-dotted my-2" />
            <div className="flex justify-between font-semibold">
              <p className="text-xs">Total paid</p>
              <p className="text-xs">${totalPrice}</p>
            </div>
          </div>
        </div>
      </div>

      <DottedDivider className="hidden sm:block border-l-1 my-[5px]" />
      <DottedDivider className="block sm:hidden border-t-1 mx-[-5px]" />

      <div className="flex flex-row md:flex-col items-center justify-between w-full sm:w-auto min-w-[5rem]">
        {isPaidTicketWithTicketUrl && (
          <div
            style={{
              height: "auto",
              maxWidth: 55,
              width: "100%",
            }}
            className="mb-2 ml-2 sm:ml-0"
          >
            <QRCode
              bgColor="#D9D9D9"
              style={{ height: "auto", maxWidth: "100%", width: "100%" }}
              value={pdfTicketUrl || ""}
              viewBox={`0 0 256 256`}
            />
          </div>
        )}

        <div className="text-center">
          <p className="text-xs">Order:</p>
          <p className="text-xs sm:text-sm font-medium">{bookingId}</p>
        </div>
        {totalPrice > 0 && (
          <>
            <Button
              variant="flat"
              size="sm"
              className="hidden sm:flex items-center text-xs p-2 sm:p-0 font-medium mt-4"
              startContent={<MdOutlineFileDownload size={15} />}
              onPress={handleDownloadPDF}
            >
              Download PDF
            </Button>
            <MdOutlineFileDownload
              size={24}
              className="sm:hidden cursor-pointer"
              onClick={handleDownloadPDF}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default Ticket;
