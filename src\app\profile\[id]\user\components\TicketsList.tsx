"use client";

import { useMemo } from "react";
import { Divide<PERSON>, Spinner } from "@nextui-org/react";
import { useSessionData } from "@/lib/hooks/useSession";
import { useGetBookedEventsQuery } from "@/lib/redux/slices/user/userApi";
import { formatDateTime } from "@/lib/utils/date";
import Ticket from "./Ticket";
import { TICKET_LIST_VARIANTS } from "@/lib/utils/constants";
import TicketsTable from "./TicketsTable";
import { EventTicket } from "../../types";

interface TicketsListProps {
  variant?: "card" | "table";
  showPastTickets?: boolean;
  selectedTicket?: EventTicket | null;
  setSelectedTicket?: (ticket: EventTicket | null) => void;
}

const TicketsList: React.FC<TicketsListProps> = ({
  variant = TICKET_LIST_VARIANTS.CARD,
  showPastTickets = true,
  selectedTicket = null,
  setSelectedTicket = () => {},
}) => {
  const { data: sessionData } = useSessionData();
  const { data: bookedEvents, isLoading } = useGetBookedEventsQuery(
    {},
    { skip: !sessionData?.user, refetchOnMountOrArgChange: true }
  );


  const { filteredTickets, eventTickets } = useMemo(() => {
    const allTickets = (bookedEvents?.results ?? []) as unknown as EventTicket[];
    const onlyEventTickets = allTickets?.filter(
      (ticket) => ticket.event
    ) as unknown as EventTicket[];

    if (showPastTickets) {
      return {
        filteredTickets: allTickets as unknown as EventTicket[],
        eventTickets: onlyEventTickets,
      };
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const filteredAll = allTickets?.filter((ticket) => {
      if (!ticket?.event?.startDate) {
        return true;
      }

      const eventDate = new Date(ticket?.event?.startDate);
      eventDate.setHours(0, 0, 0, 0);
      return eventDate >= today;
    }) as unknown as EventTicket[];

    const filteredEvents = onlyEventTickets.filter((ticket) => {
      if (!ticket?.event?.startDate) {
        return true;
      }

      const eventDate = new Date(ticket?.event?.startDate);
      eventDate.setHours(0, 0, 0, 0);
      return eventDate >= today;
    });

    return {
      filteredTickets: filteredAll,
      eventTickets: filteredEvents,
    };
  }, [bookedEvents, showPastTickets]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spinner />
      </div>
    );
  }

  if (variant === TICKET_LIST_VARIANTS.TABLE) {
    const columns = [
      { key: "bookingId", label: "Order" },
      { key: "eventName", label: "Event Name" },
      { key: "totalPrice", label: "Total Price" },
    ];
    return (
      <>
        {filteredTickets?.length === 0 && (
          <div className="mt-5 text-[#B4B4B4] text-xl sm:text-[22px] text-center">
            No Orders
          </div>
        )}
        {filteredTickets?.length > 0 && (
          <TicketsTable
            finalTickets={filteredTickets}
            columns={columns}
            selectedTicket={selectedTicket}
            setSelectedTicket={setSelectedTicket}
          />
        )}
      </>
    );
  }

  if (variant === TICKET_LIST_VARIANTS.CARD) {
    return (
      <div className="min-h-[200px] h-full w-full bg-[#F6F6F6] rounded-2xl border-2 border-gray-200 shadow-md py-4 px-4 sm:px-4">
        <div className="p-2 sm:p-2">
          <h1 className="text-[#0D0D0D] text-xl sm:text-[22px] font-semibold mb-3">
            Your tickets
          </h1>
          <Divider />
        </div>

        <div className="flex flex-col gap-4">
          {eventTickets?.length === 0 && !isLoading ? (
            <div className="mt-5 text-[#B4B4B4] text-xl sm:text-[22px] text-center">
              No tickets
            </div>
          ) : (
            eventTickets?.map((ticket) => (
              <Ticket
                key={`tickets-${ticket?.id}`}
                bookingId={
                  ticket?.order?.number
                }
                eventDateTime={formatDateTime(ticket?.order?.createdAt)}
                status={ticket?.order?.status || ""}
                totalPrice={Number(ticket?.order?.total?.amount)}
                eventName={ticket?.event?.name || ticket?.productName || ""}
                platformFee={ticket?.order?.platformFee?.amount || "0"}
                processingFee={ticket?.order?.processingFee?.amount || "0"}
                ticketCount={ticket?.quantity}
                ticketPrice={ticket?.order?.subtotal?.amount}
                eventImage={
                  ticket?.event?.image ?? null
                }
                pdfTicketUrl={ticket?.pdfTicketUrl || ""}
              />
            ))
          )}
        </div>
      </div>
    );
  }

  return <></>;
};

export default TicketsList;
