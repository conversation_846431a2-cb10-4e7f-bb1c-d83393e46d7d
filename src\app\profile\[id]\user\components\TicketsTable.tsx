import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from "@nextui-org/react";
import OrderDetailsCard from "./OrderDetailsCard";
import { EventTicket } from "../../types";


interface Column {
  key: string;
  label: string;
}

interface TicketsTableProps {
  finalTickets: EventTicket[];
  columns: Column[];
  selectedTicket: EventTicket | null;
  setSelectedTicket: (ticket: EventTicket | null) => void;
}

const TicketsTable: React.FC<TicketsTableProps> = ({
  finalTickets,
  columns,
  selectedTicket,
  setSelectedTicket,
}) => {
  if (selectedTicket) {
    return <OrderDetailsCard selectedTicket={selectedTicket} />;
  }

  return (
    <Table className="w-full">
      <TableHeader columns={columns}>
        {(column) => (
          <TableColumn className="font-semibold text-lg" key={column?.key}>
            {column?.label}
          </TableColumn>
        )}
      </TableHeader>
      <TableBody>
        {finalTickets?.map((ticket, idx) => (
          <TableRow
            key={ticket?.id}
            className="cursor-pointer hover:bg-gray-100"
          >
            <TableCell>#{ticket?.order?.number}</TableCell>
            <TableCell className="truncate">
              {ticket?.event?.name || ticket?.productName || ""}
            </TableCell>
            <TableCell>
              ${Number(ticket?.order?.total?.amount).toFixed(2)}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default TicketsTable;
