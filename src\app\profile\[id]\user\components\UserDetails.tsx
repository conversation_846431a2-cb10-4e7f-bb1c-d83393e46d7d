"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { BiLinkAlt } from "react-icons/bi";
import parse from "html-react-parser";
import { EditUserProfile } from "./EditUserProfile";
import NextLink from "next/link";
import Link from "next/link";
import { Gallery, Item } from "react-photoswipe-gallery";
import { Avatar } from "@nextui-org/react";
import "photoswipe/dist/photoswipe.css";
import { getName } from "@/lib/utils/string";

interface WebLink {
  url: string;
  title?: string;
}

export const UserDetails = ({ data, session }) => {
  const avatarSrc = data?.avatar;
  const coverSrc = data?.coverPhoto;
  const [coverDimensions, setCoverDimensions] = useState({
    width: 1200,
    height: 800,
  });
  const [avatarDimensions, setAvatarDimensions] = useState({
    width: 600,
    height: 600,
  });

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const loadImage = (
    src: string
  ): Promise<{ width: number; height: number }> => {
    return new Promise((resolve) => {
      const img = new window.Image();
      img.onload = () => {
        resolve({ width: img.naturalWidth, height: img.naturalHeight });
      };
      img.src = src;
    });
  };

  useEffect(() => {
    if (coverSrc) {
      loadImage(coverSrc).then(setCoverDimensions);
    }
    if (avatarSrc) {
      loadImage(avatarSrc).then(setAvatarDimensions);
    }
  }, [coverSrc, avatarSrc]);

  const hasEditableProfile = session?.user?.id === data?.id;
  const webLinks = data?.socialMedia?.webLinks || [];

  function RenderActions() {
    return hasEditableProfile ? (
      <EditUserProfile title="Edit" />
    ) : (
      <>
        <Link
          className="w-full px-8 py-1 h-[2rem] font-semibold text-gray-800 bg-gray-200 rounded-lg text-center"
          href="/message"
        >
          Message
        </Link>
      </>
    );
  }

  const renderCoverImage = () => {
    if (coverSrc) {
      return (
        <Item
          original={coverSrc}
          thumbnail={coverSrc}
          width={coverDimensions.width}
          height={coverDimensions.height}
          cropped={false}
        >
          {({ ref, open }) => (
            <div ref={ref} onClick={open} className="cursor-pointer">
              <Image
                src={coverSrc}
                width={1200}
                height={100}
                alt="Cover photo"
                className="rounded-2xl max-h-[45vh] overflow-hidden object-cover shadow-lg"
              />
            </div>
          )}
        </Item>
      );
    }

    return (
      <div className="w-full h-[20vh] md:h-[32rem] rounded-3xl object-cover border-2 border-gray-200"></div>
    );
  };

  const renderAvatarImage = () => {
    if (avatarSrc) {
      return (
        <Item
          original={avatarSrc}
          thumbnail={avatarSrc}
          width={avatarDimensions.width}
          height={avatarDimensions.height}
          cropped={false}
        >
          {({ ref, open }) => (
            <div
              ref={ref}
              onClick={open}
              className="cursor-pointer w-full h-full"
            >
              <Image
                src={avatarSrc}
                alt="avatar photo"
                width={100}
                height={100}
                className="rounded-full object-cover border-3 border-gray-50 shadow"
                style={{
                  width: "100%",
                  height: "100%",
                }}
              />
            </div>
          )}
        </Item>
      );
    }

    return (
      <Avatar className="w-full h-full border-3 border-gray-50 shadow">
        <span className="text-2xl font-medium">
          {getInitials(data?.name || getName(data))}
        </span>
      </Avatar>
    );
  };

  return (
    <Gallery>
      <div className="flex flex-col mx-auto">
        <div className="px-4 rounded-2xl overflow-hidden">
          {renderCoverImage()}
        </div>
        <div className="mx-4 md:mx-12 -mt-6 sm:-mt-10 md:-mt-10">
          <div className="flex gap-x-4 items-end sm:items-center justify-between">
            <div className="flex flex-col gap-y-3">
              <div className="relative w-20 h-20 sm:w-28 sm:h-28 md:w-[150px] md:h-[150px] aspect-square">
                {renderAvatarImage()}
              </div>
            </div>
            <div className="flex gap-x-4 md:gap-x-10">
              <div className="flex flex-col gap-y-0 items-center">
                <p className="text-base font-semibold text-gray-900">
                  {data?.activeEventBookingsCount || 0}
                </p>
                <p className="text-base font-semibold text-gray-900">events</p>
              </div>
              <div className="flex flex-col gap-y-0 items-center">
                <p className="text-base font-semibold text-gray-900">
                  {data?.points}
                </p>
                <p className="text-base font-semibold text-gray-900">points</p>
              </div>
            </div>

            <div className="flex-col gap-y-2 hidden sm:flex md:gap-x-2 md:flex-row">
              <RenderActions />
            </div>
          </div>
          <div className="sm:w-[55%] text-ellipsis mb-2">
            <h1 className="text-base md:text-lg font-bold text-gray-900">
              {data?.name || getName(data) || ""}
            </h1>
            <p className="text-md md:text-base font-normal text-gray-400">
              @{data?.username}
            </p>
          </div>
          <div className="flex flex-col gap-y-1 md:gap-y-2">
            {data?.bio && data.bio.trim() !== "" && (
              <p className="text-sm sm:text-base leading-1 font-medium text-gray-800">
                {parse(data.bio)}
              </p>
            )}
            {webLinks?.length > 0 && (
              <div className="flex gap-x-1 items-center">
                {webLinks?.map((link: WebLink, index: number) => (
                  <div
                    key={index}
                    className="flex gap-x-1 items-center cursor-pointer"
                  >
                    <BiLinkAlt color="#3b82f6" size="20" />
                    <NextLink href={link?.url} passHref target="_blank">
                      <p
                        rel="noopener noreferrer"
                        className="text-base font-normal text-blue-500 cursor-pointer"
                      >
                        {link?.title || link?.url}
                      </p>
                    </NextLink>
                    {index + 1 !== webLinks?.length && <span>,</span>}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="w-full sm:hidden flex gap-x-2 flex-row py-2">
            <RenderActions />
          </div>
        </div>
      </div>
    </Gallery>
  );
};
