"use client";

import { Card, CardBody, Divider } from "@nextui-org/react";
import Image from "next/image";
import Link from "next/link";
import { MdOutlineSettings } from "react-icons/md";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
const Vehicle: React.FC<IVehicle> = ({
  modifications,
  name,
  vehicleImages,
  coverPhoto = {
    photo: IMAGE_LINKS.NO_IMG,
  },
  year,
  points,
  id,
}) => {
  return (
    <Link
      href={`/profile/vehicles/${id}`}
      className="cursor-pointer hover:opacity-90"
    >
      <div className="bg-[#D9D9D9] relative rounded-lg p-2 sm:p-3 flex flex-row items-start sm:items-center gap-2 sm:gap-4 justify-between">
        <div className="flex flex-row gap-4">
          <Image
            src={coverPhoto?.photo}
            width={80}
            height={80}
            loading="lazy"
            alt="Autolnk Vehicle Image"
            className="w-[35%] h-[35%] mx-auto sm:w-[90px] sm:h-[90px] object-cover rounded-lg"
          />
          <div className="w-[220px] flex flex-col justify-center gap-3 sm:gap-5 tracking-wider truncate">
            <div className="flex flex-col gap-y-0 font-thin">
              <p className="text-sm font-light">{year}</p>
              <h1 className="font-bold text-gray-700 text-lg">{name}</h1>
              <div className="flex gap-1 mt-1 text-sm items-center">
                <MdOutlineSettings />
                <p>{modifications?.length || 0} modifications</p>
              </div>
            </div>
          </div>
        </div>
        <div className="absolute right-3 top-1">
          <p className="text-base font-medium">
            {points} <span className="text-sm">PTS</span>
          </p>
        </div>
      </div>
    </Link>
  );
};

export default Vehicle;
