import { Skeleton } from "@nextui-org/react";

const VehiclesSkeleton = () => {
  return (
    <div className="min-h-[200px] h-full w-full bg-[#F6F6F6] rounded-2xl shadow-md py-4 px-4 sm:px-8">
      <div className="p-2 sm:p-4">
        <Skeleton className="h-8 sm:h-10 w-32 rounded-lg mb-3" />
        <Skeleton className="h-[1px] w-full" />
      </div>

      <div className="flex flex-col gap-4 mt-5">
        {Array.from({ length: 2 }).map((_, idx) => (
          <div key={idx} className="flex items-center gap-3">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="flex-1">
              <Skeleton className="h-4 w-[60%] rounded-lg mb-2" />
              <Skeleton className="h-3 w-[40%] rounded-lg" />
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end w-full mt-5">
        <Skeleton className="h-10 w-32 rounded-lg" />
      </div>
    </div>
  );
};

export default VehiclesSkeleton;
