import { auth } from "@/auth.config";
import { Divider } from "@nextui-org/react";
import React from "react";
import Vehicle from "./Vehicle";
import Link from "next/link";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import { VEHICLES_ENDPOINTS } from "@/lib/redux/slices/vehicles/vehiclesApiEndpoint";
import { getUserTimeZoneWithSession } from "@/lib/utils/getUserTimeZone";

// function for fetching user vehicles list
async function GetUserVehiclesList() {
  try {
    const session = await auth();
    if (!session) {
      return { error: "Session not found" };
    }
    const userTimeZone = await getUserTimeZoneWithSession(session);
    const endpoint = `${process.env.NEXT_PUBLIC_API_URL}api/${VEHICLES_ENDPOINTS.vehiclesList}`;
    const vehiclesList = await fetch(endpoint, {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        "X-Timezone": userTimeZone,
      },
      cache: "no-store",
      credentials: "include",
    });

    if (vehiclesList?.ok) {
      const data = keysToCamel(await vehiclesList?.json());
      const sortedData = data.sort((a, b) => b.points - a.points);
      return { data: sortedData, sessionId: session.user.id };
    } else {
      return {
        error: vehiclesList?.statusText || "Error fetching vehicles list",
      };
    }
  } catch (e: any) {
    console.log("error", e);
    return { error: e?.cause?.err?.message || "Error fetching vehicles list" };
  }
}

const VehiclesList = async ({ id }) => {
  const { data: vehiclesList, sessionId, error } = await GetUserVehiclesList();
  const canAbleAddVehicles = id === sessionId;
  return (
    <div className="min-h-[200px] w-full bg-[#F6F6F6] rounded-2xl border-2 border-gray-200 shadow-md py-4 px-4">
      <div className="p-2 sm:py-4">
        <h1 className="text-[#0D0D0D] text-xl sm:text-[22px] font-semibold mb-3">
          Vehicles
        </h1>
        <Divider />
      </div>

      {error && (
        <div className="mt-5 text-red-500 text-center">Error: {error}</div>
      )}

      {vehiclesList && (
        <div className="flex flex-col gap-4">
          {vehiclesList?.length === 0 ? (
            <div className="mt-5 text-[#B4B4B4] text-xl sm:text-[22px] text-center">
              No Vehicles Added
            </div>
          ) : (
            vehiclesList?.map((vehicle) => (
              <Vehicle key={vehicle.id} {...vehicle} />
            ))
          )}
        </div>
      )}
      {canAbleAddVehicles && (
        <div className="flex justify-end w-full">
          <Link
            href="/profile/vehicles/add"
            className="bg-blue-500 text-white py-2 px-4 rounded-lg mt-4 text-center"
          >
            Add Vehicle
          </Link>
        </div>
      )}
    </div>
  );
};

export default VehiclesList;
