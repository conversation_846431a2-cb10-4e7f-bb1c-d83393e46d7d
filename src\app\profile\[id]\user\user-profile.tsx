import React, { Suspense } from "react";
import { UserDetails } from "./components";
import TicketsList from "./components/TicketsList";
import VehiclesList from "./components/VehiclesList";
import VehiclesSkeleton from "./components/VehicleListSkeleton";

export default function UserProfile({ data, session }) {
  if (!data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-600">Loading...</h1>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="max-w-[1200px] mx-auto mb-4 mt-20 relative">
        <UserDetails data={data} session={session} />
        <div className="p-8 md:p-12 w-full">
          <div className="grid md:grid-cols-2 items-start gap-9 w-full">
            <Suspense fallback={<VehiclesSkeleton />}>
              <VehiclesList id={data?.id} />
            </Suspense>
            <TicketsList showPastTickets={false} />
          </div>
        </div>
      </div>
    </div>
  );
}
