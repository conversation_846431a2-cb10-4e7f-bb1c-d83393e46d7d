/**
 * Profile page error message constants
 */
export const PROFILE_ERRORS = {
  // Form validation errors
  USERNAME_VALIDATION_ERROR: "The username is invalid or improperly formatted.",
  EMAIL_VALIDATION_ERROR: "The email is invalid or improperly formatted.",
  PH<PERSON><PERSON>_VALIDATION_ERROR: "The phone number is invalid or improperly formatted.",
  PASSWORD_VALIDATION_ERROR: "The password does not meet requirements.",
  PASSWORD_MATCH_ERROR: "The passwords do not match.",
  NAME_VALIDATION_ERROR: "The name field is invalid or improperly formatted.",
  BIO_VALIDATION_ERROR: "The bio is too long or contains invalid characters.",
  
  // Address validation errors
  ADDRESS_LINE_ERROR: "The address line is invalid or missing.",
  CITY_ERROR: "The city field is invalid or missing.",
  STATE_ERROR: "The state field is invalid or missing.",
  POSTAL_CODE_ERROR: "The postal code is invalid or improperly formatted.",
  COUNTRY_ERROR: "The country field is invalid or missing.",
  
  // Social media validation errors
  SOCIAL_URL_ERROR: "The social media URL is invalid or improperly formatted.",
  
  // API errors
  PROFILE_UPDATE_ERROR: "There was an error updating your profile. Please try again.",
  PASSWORD_UPDATE_ERROR: "There was an error updating your password. Please try again.",
  ADDRESS_UPDATE_ERROR: "There was an error updating your address. Please try again.",
  SOCIAL_UPDATE_ERROR: "There was an error updating your social links. Please try again.",
  AVATAR_UPDATE_ERROR: "There was an error updating your avatar. Please try again."
}; 