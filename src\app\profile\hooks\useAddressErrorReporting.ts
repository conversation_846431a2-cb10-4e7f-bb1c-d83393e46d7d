import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { UseFormReturn } from "react-hook-form";
import { PROFILE_ERRORS } from "../constants/errorMessages";
import { AddressFormValues } from "../settings/components/AddressModal";

interface UseAddressErrorReportingProps {
  form: UseFormReturn<AddressFormValues>;
  page?: string;
}

/**
 * Custom hook to handle error reporting for address forms
 */
export function useAddressErrorReporting({
  form,
  page = "profile-address"
}: UseAddressErrorReportingProps) {
  const { formState } = form;

  // Report name validation errors
  useEffect(() => {
    const firstNameError = formState?.errors?.firstName?.message;
    if (firstNameError) {
      reportError(String(firstNameError), { 
        page, 
        field: "firstName",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.firstName?.message, page]);

  useEffect(() => {
    const lastNameError = formState?.errors?.lastName?.message;
    if (lastNameError) {
      reportError(String(lastNameError), { 
        page, 
        field: "lastName",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.lastName?.message, page]);

  // Report phone validation errors
  useEffect(() => {
    const phoneError = formState?.errors?.phone?.message;
    if (phoneError) {
      reportError(PROFILE_ERRORS.PHONE_VALIDATION_ERROR, { 
        page, 
        field: "phone",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.phone?.message, page]);

  // Report address validation errors
  useEffect(() => {
    const addressLine1Error = formState?.errors?.addressLine1?.message;
    if (addressLine1Error) {
      reportError(PROFILE_ERRORS.ADDRESS_LINE_ERROR, { 
        page, 
        field: "addressLine1",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.addressLine1?.message, page]);

  useEffect(() => {
    const cityError = formState?.errors?.city?.message;
    if (cityError) {
      reportError(PROFILE_ERRORS.CITY_ERROR, { 
        page, 
        field: "city",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.city?.message, page]);

  useEffect(() => {
    const stateError = formState?.errors?.state?.message;
    if (stateError) {
      reportError(PROFILE_ERRORS.STATE_ERROR, { 
        page, 
        field: "state",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.state?.message, page]);

  useEffect(() => {
    const postalCodeError = formState?.errors?.postalCode?.message;
    if (postalCodeError) {
      reportError(PROFILE_ERRORS.POSTAL_CODE_ERROR, { 
        page, 
        field: "postalCode",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.postalCode?.message, page]);

  useEffect(() => {
    const countryError = formState?.errors?.country?.message;
    if (countryError) {
      reportError(PROFILE_ERRORS.COUNTRY_ERROR, { 
        page, 
        field: "country",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.country?.message, page]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page, 
      ...context
    });
  };

  return { reportApiError };
} 