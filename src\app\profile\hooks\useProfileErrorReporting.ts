import { useEffect } from "react";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { UseFormReturn } from "react-hook-form";
import { PROFILE_ERRORS } from "../constants/errorMessages";

interface UseProfileErrorReportingProps {
  form: UseFormReturn<any>;
  page?: string;
}

/**
 * Custom hook to handle error reporting for profile pages
 */
export function useProfileErrorReporting({
  form,
  page = "profile"
}: UseProfileErrorReportingProps) {
  const { formState } = form;

  // Report name validation errors to Sentry
  useEffect(() => {
    const nameError = formState?.errors?.name?.message 
      || formState?.errors?.firstName?.message 
      || formState?.errors?.lastName?.message;
    
    if (nameError) {
      reportError(String(nameError), { 
        page, 
        field: "name",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.name?.message, formState?.errors?.firstName?.message, formState?.errors?.lastName?.message, page]);

  // Report username validation errors to Sentry
  useEffect(() => {
    const usernameError = formState?.errors?.username?.message;
    if (usernameError) {
      reportError(String(usernameError), { 
        page, 
        field: "username",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.username?.message, page]);

  // Report email validation errors to Sentry
  useEffect(() => {
    const emailError = formState?.errors?.email?.message;
    if (emailError) {
      reportError(String(emailError), { 
        page, 
        field: "email",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.email?.message, page]);

  // Report phone validation errors to Sentry
  useEffect(() => {
    const phoneError = formState?.errors?.phone?.message;
    if (phoneError) {
      reportError(String(phoneError), { 
        page, 
        field: "phone",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.phone?.message, page]);

  // Report bio validation errors to Sentry
  useEffect(() => {
    const bioError = formState?.errors?.bio?.message;
    if (bioError) {
      reportError(String(bioError), { 
        page, 
        field: "bio",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.bio?.message, page]);

  // Report password validation errors to Sentry
  useEffect(() => {
    const currentPasswordError = formState?.errors?.currentPassword?.message;
    const newPasswordError = formState?.errors?.newPassword?.message;
    const confirmPasswordError = formState?.errors?.confirmPassword?.message;
    
    if (currentPasswordError) {
      reportError(String(currentPasswordError), { 
        page, 
        field: "currentPassword",
        type: "validation_error" 
      });
    }
    
    if (newPasswordError) {
      reportError(String(newPasswordError), { 
        page, 
        field: "newPassword",
        type: "validation_error" 
      });
    }
    
    if (confirmPasswordError) {
      reportError(PROFILE_ERRORS.PASSWORD_MATCH_ERROR, { 
        page, 
        field: "confirmPassword",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.currentPassword?.message, formState?.errors?.newPassword?.message, formState?.errors?.confirmPassword?.message, page]);

  // Report address validation errors to Sentry
  useEffect(() => {
    const addressLine1Error = formState?.errors?.addressLine1?.message;
    const cityError = formState?.errors?.city?.message;
    const stateError = formState?.errors?.state?.message;
    const postalCodeError = formState?.errors?.postalCode?.message;
    const countryError = formState?.errors?.country?.message;
    
    if (addressLine1Error) {
      reportError(PROFILE_ERRORS.ADDRESS_LINE_ERROR, { 
        page, 
        field: "addressLine1",
        type: "validation_error" 
      });
    }
    
    if (cityError) {
      reportError(PROFILE_ERRORS.CITY_ERROR, { 
        page, 
        field: "city",
        type: "validation_error" 
      });
    }
    
    if (stateError) {
      reportError(PROFILE_ERRORS.STATE_ERROR, { 
        page, 
        field: "state",
        type: "validation_error" 
      });
    }
    
    if (postalCodeError) {
      reportError(PROFILE_ERRORS.POSTAL_CODE_ERROR, { 
        page, 
        field: "postalCode",
        type: "validation_error" 
      });
    }
    
    if (countryError) {
      reportError(PROFILE_ERRORS.COUNTRY_ERROR, { 
        page, 
        field: "country",
        type: "validation_error" 
      });
    }
  }, [formState?.errors?.addressLine1?.message, formState?.errors?.city?.message, formState?.errors?.state?.message, formState?.errors?.postalCode?.message, formState?.errors?.country?.message, page]);

  // Function to report API/runtime errors
  const reportApiError = (errorMessage: string, context?: Record<string, any>) => {
    reportError(errorMessage, { 
      page, 
      ...context
    });
  };

  return { reportApiError };
} 