import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON>ooter,
  Input,
  Select,
  SelectItem,
  Checkbox,
  Switch,
} from "@nextui-org/react";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Country, State, ICountry, IState } from "country-state-city";
import { useEffect, useState } from "react";
import <PERSON><PERSON>ield from "@/components/FormField/FormField";
import { useAddressErrorReporting } from "../../hooks/useAddressErrorReporting";
import { PROFILE_ERRORS } from "../../constants/errorMessages";

interface Country {
  code: string;
  name: string;
}

interface AddressResponse {
  id: string;
  firstName: string;
  lastName: string;
  companyName: string;
  streetAddress_1: string;
  streetAddress_2: string;
  city: string;
  cityArea: string;
  postalCode: string;
  country: Country;
  countryArea: string;
  phone: string;
  isShipping?: boolean;
}

const addressSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  company: z.string().optional(),
  phone: z.string().min(1, "Phone number is required"),
  countryCode: z.string().optional().default("+1"),
  addressLine1: z.string().min(1, "Address is required"),
  addressLine2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  postalCode: z.string().regex(/^\d{4,10}$/, "Invalid zip code"),
  country: z.string().min(1, "Country is required"),
  isShipping: z.boolean().optional().default(true),
});

export type AddressFormValues = z.infer<typeof addressSchema>;

interface AddressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AddressFormValues) => void;
  initialData?: AddressResponse | null;
  title?: string;
  isEdit?: boolean;
}

const AddressModal: React.FC<AddressModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  title = "Add New Address",
  isEdit = false,
}) => {
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [states, setStates] = useState<IState[]>([]);
  const [selectedCountryCode, setSelectedCountryCode] = useState<string>("US");

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
    setValue,
  } = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      company: "",
      phone: "",
      countryCode: "+1",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      postalCode: "",
      country: "United States",
      isShipping: true,
    },
  });

  const { reportApiError } = useAddressErrorReporting({
    form: { formState: { errors } } as any,
  });

  const watchCountry = watch("country");

  useEffect(() => {
    setCountries(Country.getAllCountries());
  }, []);

  useEffect(() => {
    if (watchCountry) {
      const selectedCountry = countries?.find((c) => c.name === watchCountry);
      if (selectedCountry) {
        setSelectedCountryCode(selectedCountry.isoCode);
        setStates(State.getStatesOfCountry(selectedCountry.isoCode));
      } else {
        setStates([]);
      }
    } else {
      setStates([]);
    }
  }, [watchCountry, countries]);

  useEffect(() => {
    if (initialData) {
      setValue("firstName", initialData.firstName);
      setValue("lastName", initialData.lastName);
      setValue("company", initialData.companyName);
      setValue("phone", initialData.phone);
      setValue("countryCode", initialData.country.code);
      setValue("addressLine1", initialData.streetAddress_1);
      setValue("addressLine2", initialData.streetAddress_2);
      setValue("city", initialData.city);
      setValue("state", initialData.countryArea);
      setValue("postalCode", initialData.postalCode);
      setValue("country", initialData.country.name);
      setValue("isShipping", initialData.isShipping ?? true);
      setSelectedCountryCode(initialData.country.code);
    }
  }, [initialData, setValue]);

  const handleClose = () => {
    reset();
    onClose();
  };

  const onFormSubmit = (data: AddressFormValues) => {
    try {
      onSubmit({
        ...data,
        countryCode: selectedCountryCode,
        id: initialData?.id,
      });
      reset();
    } catch (error: any) {
      const errorMessage =
        error?.message || PROFILE_ERRORS.ADDRESS_UPDATE_ERROR;
      reportApiError(errorMessage, {
        type: "api_error",
      });
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      placement="center"
      scrollBehavior="outside"
      size="xl"
    >
      <ModalContent>
        <form onSubmit={handleSubmit(onFormSubmit)}>
          <ModalHeader>{title}</ModalHeader>
          <ModalBody className="gap-4">
            {!isEdit && (
              <div className="flex items-center justify-between">
                <FormField
                  label="Address Type"
                  errors={errors}
                  name="isShipping"
                  control={control}
                  render={(field) => (
                    <div className="flex items-center gap-2">
                      <span className="text-sm">Billing</span>
                      <Switch
                        isSelected={field.value}
                        onValueChange={field.onChange}
                        color="primary"
                      />
                      <span className="text-sm">Shipping</span>
                    </div>
                  )}
                />
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <FormField
                label="First Name"
                errors={errors}
                name="firstName"
                control={control}
                render={(field) => (
                  <Input
                    {...field}
                    variant="bordered"
                    placeholder="Enter first name"
                  />
                )}
              />

              <FormField
                label="Last Name"
                errors={errors}
                name="lastName"
                control={control}
                render={(field) => (
                  <Input
                    {...field}
                    variant="bordered"
                    placeholder="Enter last name"
                  />
                )}
              />
            </div>

            <FormField
              label="Phone Number"
              errors={errors}
              name="phone"
              control={control}
              render={(field) => (
                <Input
                  {...field}
                  variant="bordered"
                  placeholder="Enter phone number"
                  type="tel"
                />
              )}
            />

            <FormField
              label="Address Line 1"
              errors={errors}
              name="addressLine1"
              control={control}
              render={(field) => (
                <Input
                  {...field}
                  variant="bordered"
                  placeholder="Enter street address"
                />
              )}
            />

            <FormField
              label="Address Line 2"
              errors={errors}
              name="addressLine2"
              control={control}
              render={(field) => (
                <Input
                  {...field}
                  variant="bordered"
                  placeholder="Apartment, suite, unit, etc. (optional)"
                />
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                label="City"
                errors={errors}
                name="city"
                control={control}
                render={(field) => (
                  <Input
                    {...field}
                    variant="bordered"
                    placeholder="Enter city"
                  />
                )}
              />

              <FormField
                label="State/Province"
                errors={errors}
                name="state"
                control={control}
                render={(field) => (
                  <Select
                    {...field}
                    variant="bordered"
                    placeholder="Select state"
                    selectedKeys={field.value ? [field.value] : []}
                    onChange={(e) => field.onChange(e.target.value)}
                  >
                    {states.map((state) => (
                      <SelectItem key={state.name} value={state.name}>
                        {state.name}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                label="Postal Code"
                errors={errors}
                name="postalCode"
                control={control}
                render={(field) => (
                  <Input
                    {...field}
                    variant="bordered"
                    placeholder="Enter postal code"
                  />
                )}
              />

              <FormField
                label="Country"
                errors={errors}
                name="country"
                control={control}
                render={(field) => (
                  <Select
                    {...field}
                    variant="bordered"
                    placeholder="Select country"
                    selectedKeys={field.value ? [field.value] : []}
                    onChange={(e) => field.onChange(e.target.value)}
                  >
                    {countries.map((country) => (
                      <SelectItem key={country.name} value={country.name}>
                        {country.name}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              />
            </div>

            <FormField
              label="Company Name"
              errors={errors}
              name="company"
              control={control}
              render={(field) => (
                <Input
                  {...field}
                  variant="bordered"
                  placeholder="Enter company name (optional)"
                />
              )}
            />
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onPress={handleClose}>
              Cancel
            </Button>
            <Button color="primary" type="submit">
              Save
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default AddressModal;
