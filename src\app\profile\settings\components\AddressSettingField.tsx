import {
  useGetUserAddressesQuery,
  useDeleteUserAddressMutation,
  useAddUserAddressMutation,
  useUpdateUserAddressMutation,
  useSetDefaultAddressesMutation,
} from "@/lib/redux/slices/user/userApi";
import { TOASTS } from "@/lib/utils/constants";
import { Button, Chip } from "@nextui-org/react";
import React, { useState } from "react";
import toast from "react-hot-toast";
import AddressModal, { AddressFormValues } from "./AddressModal";

interface Country {
  code: string;
  name: string;
}

interface AddressResponse {
  id: string;
  firstName: string;
  lastName: string;
  companyName: string;
  streetAddress_1: string;
  streetAddress_2: string;
  city: string;
  cityArea: string;
  postalCode: string;
  country: Country;
  countryArea: string;
  phone: string;
  isDefault: boolean;
}

const AddressSettingField: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState<AddressResponse | null>(
    null
  );
  const { data: addresses, isLoading } = useGetUserAddressesQuery();
  const [deleteAddress] = useDeleteUserAddressMutation();
  const [addAddress] = useAddUserAddressMutation();
  const [updateAddress] = useUpdateUserAddressMutation();
  const [setDefaultAddress] = useSetDefaultAddressesMutation();

  const handleSubmit = async (formData: AddressFormValues) => {
    const addressType = formData.isShipping ? "shipping" : "billing";
    try {
      if (editingAddress?.id) {
        const updateData = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          companyName: formData.company,
          phone: formData.phone,
          streetAddress_1: formData.addressLine1,
          streetAddress_2: formData.addressLine2,
          city: formData.city,
          countryArea: formData.state,
          postalCode: formData.postalCode,
          country: formData.countryCode,
        };

        await updateAddress({
          id: editingAddress.id,
          data: { ...updateData },
        }).unwrap();

        toast.success(TOASTS.ADDRESS_UPDATED);
      } else {
        const addressDetails = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          companyName: formData.company,
          phone: formData.phone,
          streetAddress_1: formData.addressLine1,
          streetAddress_2: formData.addressLine2,
          city: formData.city,
          countryArea: formData.state,
          postalCode: formData.postalCode,
          country: formData.countryCode,
        };
        await addAddress({ addressType, addressDetails }).unwrap();
        toast.success(TOASTS.ADDRESS_ADDED);
      }
      setIsModalOpen(false);
      setEditingAddress(null);
    } catch (error) {
      console.error("Error submitting address:", error);
      toast.error(TOASTS.ERROR);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteAddress(id).unwrap();
      toast.success(TOASTS.ADDRESS_DELETED);
    } catch (error) {
      console.error("Error deleting address:", error);
      toast.error(TOASTS.ERROR);
    }
  };

  const handleEdit = (address: AddressResponse) => {
    setEditingAddress(address);
    setIsModalOpen(true);
  };

  const handleSetDefault = async (id: string) => {
    const addressType = "shipping";
    try {
      await setDefaultAddress({ addressId: id, addressType });
      toast.success(TOASTS.ADDRESS_SET_DEFAULT);
    } catch (error) {
      console.error("Error setting default address:", error);
      toast.error(TOASTS.ADDRESS_SET_DEFAULT_ERROR);
    }
  };

  return (
    <div className="flex flex-col">
      <div className="flex justify-between items-center p-4 border w-full">
        <div className="max-w-[80%]">
          <h3 className="font-medium">Mailing Addresses</h3>
          <p className="text-gray-600">
            {addresses?.length
              ? `${addresses.length} address${
                  addresses.length > 1 ? "es" : ""
                } saved`
              : "No addresses saved"}
          </p>
        </div>
        <Button
          size="sm"
          className="font-bold"
          color="primary"
          variant="flat"
          onPress={() => {
            setEditingAddress(null);
            setIsModalOpen(true);
          }}
        >
          Add Address
        </Button>
      </div>

      {isLoading ? (
        <div className="p-4">Loading addresses...</div>
      ) : (
        <div className="">
          {addresses?.map((address: AddressResponse) => (
            <div
              key={address.id}
              className="flex justify-between items-center p-4 border"
            >
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <p className="font-medium">
                    {address.firstName} {address.lastName}
                  </p>
                  {address.isDefault && (
                    <Chip
                      size="sm"
                      color="primary"
                      variant="flat"
                      className="text-xs"
                    >
                      Default
                    </Chip>
                  )}
                </div>
                <p className="text-gray-600">{address.phone}</p>
                <p className="font-medium">{address.streetAddress_1}</p>
                {address.streetAddress_2 && (
                  <p className="text-gray-600">{address.streetAddress_2}</p>
                )}
                <p className="text-gray-600">
                  {`${address.city}, ${address.countryArea} ${address.postalCode}`}
                </p>
                <p className="text-gray-600">{address.country.name}</p>
              </div>
              <div className="flex flex-col gap-2">
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="flat"
                    onPress={() => handleEdit(address)}
                  >
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    color="danger"
                    variant="light"
                    onPress={() => handleDelete(address.id)}
                  >
                    Delete
                  </Button>
                </div>
                {!address?.isDefault && (
                  <Button
                    size="sm"
                    variant="bordered"
                    onPress={() => handleSetDefault(address.id)}
                  >
                    Set Default
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <AddressModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingAddress(null);
        }}
        isEdit={!!editingAddress?.id}
        onSubmit={handleSubmit}
        initialData={editingAddress}
        title={editingAddress ? "Edit Address" : "Add New Address"}
      />
    </div>
  );
};

export default AddressSettingField;
