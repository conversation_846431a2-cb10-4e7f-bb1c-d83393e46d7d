import { getInitials, getName } from "@/lib/utils/string";
import { Button } from "@nextui-org/react";
import Image from "next/image";

const AvatarSection: React.FC<{ user: any; onEdit: () => void }> = ({
  user,
  onEdit,
}) => (
  <div className="flex justify-between items-center p-4 border w-full">
    <div>
      {user?.avatar ? (
        <Image
          src={user?.avatar}
          alt="User avatar"
          height={80}
          width={80}
          className="object-cover rounded-full aspect-square w-20 h-20"
        />
      ) : (
        <div className="rounded-full aspect-square w-20 h-20 bg-blue-500 flex items-center justify-center">
          <p className="text-[50px] text-white">{getInitials(getName(user))}</p>
        </div>
      )}
    </div>
    <Button
      size="sm"
      className="underline font-bold"
      variant="light"
      onPress={onEdit}
    >
      Edit
    </Button>
  </div>
);

export default AvatarSection;
