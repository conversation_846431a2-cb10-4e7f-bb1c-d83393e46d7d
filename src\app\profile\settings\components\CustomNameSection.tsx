import FormField from "@/components/FormField/FormField";
import { Input } from "@nextui-org/react";
import React, { useEffect } from "react";

export function CustomNameSection({
  control,
  errors,
  setValue,
  user,
}: {
  control: any;
  errors: any;
  user: any;
  setValue:any
}) {
  useEffect(() => {
    setValue("firstName", user?.firstName||"");
    setValue("lastName", user?.lastName||"");
  }, []);
  return (
    <div className="flex gap-x-2">
      <FormField
        label="First Name"
        errors={errors}
        name="firstName"
        control={control}
        render={(field) => (
          <Input
            variant="bordered"
            {...field}
            type="text"
          />
        )}
      />
      <FormField
        label="Last Name"
        errors={errors}
        name="lastName"
        control={control}
        render={(field) => (
          <Input
            variant="bordered"
            {...field}
            type="text"
          />
        )}
      />
    </div>
  );
}
