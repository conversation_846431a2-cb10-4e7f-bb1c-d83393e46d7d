import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  Input,
  Textarea,
  Select,
  SelectItem,
} from "@nextui-org/react";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { UserProfile } from "@/components/UserProfile";
import { Country, State, City, ICountry, IState } from "country-state-city";
import { useEffect, useState } from "react";
import FormField from "@/components/FormField/FormField";
import { SETTINGS_FIELDS } from "../constants";
import { SocialMediaSection } from "./SocialMediaSection";
import PasswordRequirementsText from "@/components/Authenticator/components/PasswordRequirementsText";
import { CustomNameSection } from "./CustomNameSection";
import { useProfileErrorReporting } from "../../hooks/useProfileErrorReporting";
import { PROFILE_ERRORS } from "../../constants/errorMessages";

interface EditModalProps {
  field: string;
  fieldSchemas: any;
  initialValue: any;
  onClose: () => void;
  onSave: (value: any) => void;
  user: any;
}

const EditModal: React.FC<EditModalProps> = ({
  field,
  fieldSchemas,
  initialValue,
  onClose,
  onSave,
  user,
}) => {
  const schema =
    field === SETTINGS_FIELDS.NAME
      ? z.object({
          [SETTINGS_FIELDS.FIRST_NAME]:
            fieldSchemas[SETTINGS_FIELDS.FIRST_NAME],
          [SETTINGS_FIELDS.LAST_NAME]: fieldSchemas[SETTINGS_FIELDS.LAST_NAME],
        })
      : fieldSchemas[field];
  type FormValues = z.infer<typeof schema>;
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    getValues,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: initialValue ? initialValue : { [field]: initialValue },
  });
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [states, setStates] = useState<IState[]>([]);
  const watchCountry = watch("country");
  const watchSocial = watch("socialMedia");

  // Use the error reporting hook
  const { reportApiError } = useProfileErrorReporting({
    form: { formState: { errors } } as any,
    page: `profile-${field}`
  });

  useEffect(() => {
    if (field === SETTINGS_FIELDS.ADDRESS) {
      setCountries(Country.getAllCountries());
    }
  }, []);

  useEffect(() => {
    if (field === SETTINGS_FIELDS.ADDRESS) {
      if (watchCountry) {
        const selectedCountry = countries?.find((c) => c.name === watchCountry);
        if (selectedCountry) {
          setStates(State.getStatesOfCountry(selectedCountry.isoCode));
        } else {
          setStates([]);
        }
      } else {
        setStates([]);
      }
    }
  }, [watchCountry, countries]);

  const renderFields = () => {
    switch (field) {
      case SETTINGS_FIELDS.NAME:
        return (
          <CustomNameSection
            errors={errors}
            control={control}
            setValue={setValue}
            user={user}
          />
        );
      case SETTINGS_FIELDS.USERNAME:
      case SETTINGS_FIELDS.EMAIL:
      case SETTINGS_FIELDS.PHONE:
        return (
          <FormField
            errors={errors}
            name={field as any}
            control={control}
            render={(field) => (
              <Input variant="bordered" {...field} type={"text"} />
            )}
          />
        );
      case SETTINGS_FIELDS.BIO:
        return (
          <FormField
            errors={errors}
            name={field as any}
            control={control}
            render={(field) => <Textarea variant="bordered" {...field} />}
          />
        );
      case SETTINGS_FIELDS.PASSWORD:
        return (
          <>
            <FormField
              errors={errors}
              name="currentPassword"
              control={control}
              render={(field) => (
                <Input
                  {...field}
                  variant="bordered"
                  type="password"
                  placeholder="Current Password"
                />
              )}
            />
            <FormField
              errors={errors}
              name="newPassword"
              control={control}
              render={(field) => (
                <Input
                  variant="bordered"
                  {...field}
                  type="password"
                  placeholder="New Password"
                />
              )}
            />
            <FormField
              errors={errors}
              name="confirmPassword"
              control={control}
              render={(field) => (
                <Input
                  variant="bordered"
                  {...field}
                  type="password"
                  placeholder="Confirm New Password"
                />
              )}
            />
            <PasswordRequirementsText className="max-w-xs mb-0" />
          </>
        );
      case SETTINGS_FIELDS.SOCIAL_MEDIA:
        return (
          <SocialMediaSection
            control={control}
            errors={errors}
            name="socialMedia"
            setValue={setValue}
            getValues={getValues}
          />
        );
      case SETTINGS_FIELDS.ADDRESS:
        return (
          <>
            {["addressLine1", "addressLine2", "city", "postalCode"].map(
              (addressField) => (
                <FormField
                  errors={errors}
                  key={addressField}
                  name={addressField as any}
                  control={control}
                  render={(field) => (
                    <Input {...field} variant="bordered" type="text" />
                  )}
                />
              )
            )}
            <FormField
              errors={errors}
              name="state"
              control={control}
              render={(field) => (
                <Select
                  {...field}
                  variant="bordered"
                  placeholder="Select State"
                  selectedKeys={field.value ? [field.value] : []}
                  isDisabled={!watchCountry}
                >
                  {states.map((state) => (
                    <SelectItem key={state.name} value={state.name}>
                      {state.name}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
            <FormField
              errors={errors}
              name="country"
              control={control}
              render={(field) => (
                <Select
                  {...field}
                  variant="bordered"
                  placeholder="Select Country"
                  selectedKeys={field.value ? [field.value] : []}
                >
                  {countries.map((country) => (
                    <SelectItem key={country.name} value={country.name}>
                      {country.name}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
          </>
        );
      default:
        return null;
    }
  };

  function onSocialMediaSave() {
    const formatedLinks = watchSocial.map((link) => ({
      ...link,
      title: link.title ? link.title : link.url,
      url: link.url ? link.url : "#",
    }));
    onSave({ socialMedia: { webLinks: formatedLinks } });
  }
  function onCustomNameSave() {
    const firstName = watch("firstName");
    const lastName = watch("lastName");
    onSave({ name: { firstName, lastName } });
  }

  function onSubmit(data: FormValues) {
    try {
      if (field === SETTINGS_FIELDS.SOCIAL_MEDIA) {
        onSocialMediaSave();
      } else if (field === SETTINGS_FIELDS.NAME) {
        onCustomNameSave();
      } else {
        onSave(data);
      }
    } catch (error: any) {
      const errorMessage = error?.message || PROFILE_ERRORS.PROFILE_UPDATE_ERROR;
      reportApiError(errorMessage, {
        field,
        type: "api_error"
      });
    }
  }

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      placement="center"
      scrollBehavior="outside"
    >
      <ModalContent>
        <ModalHeader className="capitalize">{field}</ModalHeader>
        {field !== "image" ? (
          <form
            onSubmit={handleSubmit(onSubmit)}
          >
            <ModalBody className="mb-3">{renderFields()}</ModalBody>
            <ModalFooter className="flex justify-start">
              <Button color="primary" type="submit">
                Save
              </Button>
              <Button variant="light" onPress={onClose}>
                Cancel
              </Button>
            </ModalFooter>
          </form>
        ) : (
          <ModalBody className="mb-3">
            <UserProfile />
          </ModalBody>
        )}
      </ModalContent>
    </Modal>
  );
};

export default EditModal;
