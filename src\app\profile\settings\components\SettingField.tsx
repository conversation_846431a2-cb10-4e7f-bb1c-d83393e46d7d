import { Button, Switch } from "@nextui-org/react";

interface SettingsFieldProps {
  label: string;
  value: any;
  onEdit: (val) => any;
  switchInput?: boolean;
}

const SettingsField: React.FC<SettingsFieldProps> = ({
  label,
  value,
  onEdit,
  switchInput = false,
}) => {
  const renderEditButton = () => {
    // we are restricting the edit for email address for now
    if (label === "Email address") return null;
    return switchInput ? (
      <Switch size="sm" isSelected={value} onValueChange={onEdit} />
    ) : (
      <Button
        size="sm"
        className="underline font-bold"
        variant="light"
        onPress={onEdit}
      >
        Edit
      </Button>
    );
  };
  return (
    <div className="flex justify-between items-center p-4 border w-full">
      <div className="max-w-[80%]">
        <h3 className="font-medium">{label}</h3>
        <p className="text-gray-600 truncate">{value}</p>
      </div>
      {renderEditButton()}
    </div>
  );
};

export default SettingsField;
