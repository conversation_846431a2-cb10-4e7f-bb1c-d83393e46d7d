import { Input } from "@nextui-org/react";
import { MdDelete } from "react-icons/md";
import React, { useState, useMemo, useEffect } from "react";
import { useForm, Controller } from "react-hook-form";

export function SocialMediaSection({
  control,
  name,
  setValue,
  getValues,
}) {
  const [tempLinks, setTempLinks] = useState(
    getValues(name)?.webLinks || [{ title: "", url: "" }]
  );

  // Update setValue whenever tempLinks changes
  useEffect(() => {
    setValue(name, tempLinks);
  }, [tempLinks, setValue, name]);

  const handleTitleChange = (index: number, title: string) => {
    setTempLinks((prevLinks) =>
      prevLinks.map((link, i) => (i === index ? { ...link, title } : link))
    );
  };

  const handleURLChange = (index: number, url: string) => {
    setTempLinks((prevLinks) =>
      prevLinks.map((link, i) => (i === index ? { ...link, url } : link))
    );
  };

  const onAddMedia = () => {
    setTempLinks((prevLinks) => [...prevLinks, { title: "", url: "" }]);
  };

  // Use useMemo to memoize the rendering of tempLinks to avoid unnecessary re-renders
  const renderedLinks = useMemo(() => {
    return tempLinks.map((link, index) => (
      <div
        key={index}
        className="group flex flex-row justify-evenly gap-x-2 items-center"
      >
        <div className="w-full">
          <Controller
            control={control}
            name={name}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Title"
                radius="sm"
                size="sm"
                onChange={(e) => {
                  field.onChange(e);
                  handleTitleChange(index, e.target.value);
                }}
                value={link.title}
              />
            )}
          />
        </div>
        <div className="w-full">
          <Controller
            control={control}
            name={name}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="URL"
                radius="sm"
                size="sm"
                onChange={(e) => {
                  field.onChange(e);
                  handleURLChange(index, e.target.value);
                }}
                value={link.url}
              />
            )}
          />
        </div>
        <div className="hidden group-hover:block">
          <MdDelete
            color="red"
            className="cursor-pointer"
            size="20"
            onClick={() => {
              setTempLinks((prevLinks) =>
                prevLinks.filter((_, i) => i !== index)
              );
            }}
          />
        </div>
      </div>
    ));
  }, [tempLinks, control, name]);

  return (
    <div className="flex flex-col gap-y-5">
      <div className="flex flex-col gap-y-3">{renderedLinks}</div>
      <div
        onClick={onAddMedia}
        className="w-full cursor-pointer border-dotted rounded-md text-center py-1 bg-gray-100 text-slate-700 font-bold text-base border-gray-600 border-2"
      >
        + add
      </div>
    </div>
  );
}
