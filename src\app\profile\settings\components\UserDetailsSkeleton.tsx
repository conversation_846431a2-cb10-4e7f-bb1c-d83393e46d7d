import React from 'react';
import { Card, Skeleton } from "@nextui-org/react";

const SettingsPageSkeleton = () => {
  return (
    <div className="mx-auto mt-3 space-y-4">
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <Skeleton className="rounded-full w-16 h-16" />
          <Skeleton className="h-3 w-3/4" />
        </div>
      </Card>

      {Array.from({ length: 6 }).map((_, index) => (
        <Card key={index} className="p-4">
          <Skeleton className="h-4 w-1/4 mb-2" />
          <Skeleton className="h-4 w-3/4" />
        </Card>
      ))}
    </div>
  );
};

export default SettingsPageSkeleton;