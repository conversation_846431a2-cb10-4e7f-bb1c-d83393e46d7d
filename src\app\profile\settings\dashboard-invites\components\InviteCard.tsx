import { Invite } from "@/lib/types/organizationTypes";
import { <PERSON><PERSON>, Card, CardBody } from "@nextui-org/react";
import { RiDeleteBin6Line } from "react-icons/ri";

interface InviteCardProps {
  invite: Invite;
  onJoin: (invite: Invite) => void;
  onDecline: (invite: Invite) => void;
}

const InviteCard: React.FC<InviteCardProps> = ({
  invite,
  onJoin,
  onDecline,
}) => (
  <div className="flex gap-3 justify-center items-center mb-4">
    <Card className="w-full bg-[#F6F6F6]">
      <CardBody className="flex flex-row items-center justify-between px-8 py-4">
        <div className="flex items-center space-x-4">
          <p className="font-semibold text-lg truncate capitalize">
            {invite.organization?.owner?.name}
          </p>
        </div>
        <div className="text-right">
          <Button color="primary" onPress={() => onJoin(invite)}>
            Join
          </Button>
        </div>
      </CardBody>
    </Card>
    <RiDeleteBin6Line
      size={36}
      className="cursor-pointer p-2 w-10 h-10 text-red-500"
      onClick={() => onDecline(invite)}
    />
  </div>
);

export default InviteCard;
