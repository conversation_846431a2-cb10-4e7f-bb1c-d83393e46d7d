"use client";

import React, { useOptimistic, useCallback, useTransition } from "react";
import { useGetUserDashboardInvitesQuery } from "@/lib/redux/slices/user/userApi";
import { useSessionData } from "@/lib/hooks/useSession";
import { useUpdateUserDashboardInviteMutation } from "@/lib/redux/slices/organization/api";
import { Invite } from "@/lib/types/organizationTypes";
import InviteCard from "./components/InviteCard";
import toast from "react-hot-toast";
import { TOASTS } from "@/lib/utils/constants";
import { useRouter } from "next/navigation";

const InvitesPage: React.FC = () => {
  const { data: session } = useSessionData();
  const {
    data: dashboardInvites,
    isLoading: isDashboardInvitesLoading,
    refetch: refetchDashboardInvites,
  } = useGetUserDashboardInvitesQuery({}, { skip: !session?.user });
  const [updateUserDashboardInvite] = useUpdateUserDashboardInviteMutation();
  const [isPending, startTransition] = useTransition();
  const [optimisticInvites, updateOptimisticInvites] = useOptimistic<
    Invite[],
    string
  >(dashboardInvites || [], (currentInvites, inviteIdToRemove) => {
    return currentInvites.filter(
      (invite) => invite?.invitationId !== inviteIdToRemove
    );
  });

  const router = useRouter();

  const handleInviteAction = useCallback(
    async (invite: Invite, accepted: boolean) => {
      startTransition(async () => {
        updateOptimisticInvites(invite?.invitationId);
        try {
          await updateUserDashboardInvite({
            orgSlug: invite?.organization?.slug,
            inviteId: invite?.invitationId,
            body: {
              email: invite?.email,
              accepted,
            },
          }).unwrap();
          const successMessage = accepted
            ? `${TOASTS.INVITE_ACCEPTED}. Redirecting to the dashboard...`
            : TOASTS.INVITE_DECLINED;
          toast.success(successMessage);
          if (accepted) {
            router.replace(
              `${process.env.NEXT_PUBLIC_DASHBOARD_URL}organization/${invite?.organization?.slug}/?accessToken=${session?.accessToken}&refreshToken=${session?.refreshToken}&csrfToken=${session?.csrfToken}`
            );
          }
        } catch (error: any) {
          const errorMessage =
            error?.data?.error || error?.message || TOASTS.ERROR;
          console.error(error);
          toast.error(errorMessage);
        } finally {
          refetchDashboardInvites();
        }
      });
    },
    [
      updateOptimisticInvites,
      updateUserDashboardInvite,
      refetchDashboardInvites,
      session,
      router,
    ]
  );

  const handleJoin = useCallback(
    (invite: Invite) => handleInviteAction(invite, true),
    [handleInviteAction]
  );
  const handleDecline = useCallback(
    (invite: Invite) => handleInviteAction(invite, false),
    [handleInviteAction]
  );

  return (
    <div className="min-h-screen">
      <h1 className="text-2xl font-semibold mb-5">Invites</h1>
      {optimisticInvites.length === 0 ? (
        <p className="text-lg text-gray-500">No invites available</p>
      ) : (
        optimisticInvites.map((invite) => (
          <InviteCard
            key={invite.invitationId}
            invite={invite}
            onJoin={handleJoin}
            onDecline={handleDecline}
          />
        ))
      )}
    </div>
  );
};

export default InvitesPage;
