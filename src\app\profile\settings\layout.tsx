"use client";

import TopNavBar from "@/features/TopNavBar/TopNavBar";
import { settingsSideBarLinks } from "@/lib/utils/constants";
import { Divider } from "@nextui-org/react";
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function SettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const url = usePathname();

  return (
    <div>
      <TopNavBar>
        <section>
          <main className="mx-auto mb-10 mt-20 sm:mt-[5.5rem] w-[90%] sm:max-w-[900px] items-center flex justify-center flex-col min-h-screen ">
            <h1 className="text-2xl font-bold mb-2 text-start w-full">
              Settings
            </h1>
            <Divider />
            <div className="flex flex-col sm:flex-row gap-2 mb-8 w-full mt-3">
              <div className="w-1/4 py-5 h-full">
                <div className="flex flex-col gap-5">
                  {settingsSideBarLinks.map((link) => (
                    <Link
                      key={link.href}
                      href={link.href}
                      className={`${
                        url === link.href ? "text-blue-500" : ""
                      } hover:underline font-bold`}
                    >
                      {link.label}
                    </Link>
                  ))}
                </div>
              </div>
              <div className="sm:w-3/4 sm:p-5">{children}</div>
            </div>
          </main>
        </section>
      </TopNavBar>
    </div>
  );
}
