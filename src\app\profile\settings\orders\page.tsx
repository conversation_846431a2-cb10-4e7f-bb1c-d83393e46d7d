"use client";

import React, { useState } from "react";
import TicketsList from "../../[id]/user/components/TicketsList";
import { Button } from "@nextui-org/react";
import { IoChevronBack } from "react-icons/io5";

const Page = () => {
  const [selectedTicket, setSelectedTicket] = useState<any>(null);

  return (
    <div className="min-h-screen">
      {selectedTicket ? (
        <div className="flex gap-2 mb-6 items-center">
          <IoChevronBack
            className="w-5 h-5 cursor-pointer"
            onClick={() => setSelectedTicket(null)}
          />
          <p className="text-2xl font-semibold">
            Order #{selectedTicket.orderDetails.number}
          </p>
        </div>
      ) : (
        <h1 className="text-2xl font-semibold mb-5">Order History</h1>
      )}
      <TicketsList
        variant="table"
        selectedTicket={selectedTicket}
        setSelectedTicket={setSelectedTicket}
      />
    </div>
  );
};

export default Page;
