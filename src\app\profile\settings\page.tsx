"use client";
import React from "react";
import { AUTH_STATUS } from "@/lib/utils/constants";
import SettingsField from "./components/SettingField";
import AvatarSection from "./components/AvatarSection";
import EditModal from "./components/EditModal";
import { useProfileSettings } from "@/lib/hooks/useProfileSettings";
import SettingsPageSkeleton from "./components/UserDetailsSkeleton";
import AddressSettingField from "./components/AddressSettingField";
import { getName } from "@/lib/utils/string";

const SettingsPage: React.FC = () => {
  const {
    user,
    status,
    editingField,
    handleEdit,
    handleCloseModal,
    handleSave,
    fieldSchemas,
    getInitialValue,
  } = useProfileSettings();
  if (status === AUTH_STATUS.LOADING) return <SettingsPageSkeleton />;

  if (!user) return <div>User Not found</div>;

  return (
    <>
      <AvatarSection user={user} onEdit={() => handleEdit("image")} />
      <SettingsField
        label="Name"
        value={getName(user) || ""}
        onEdit={() => handleEdit("name")}
      />
      <SettingsField
        label="Username"
        value={user?.username || ""}
        onEdit={() => handleEdit("username")}
      />
      <SettingsField
        label="Email address"
        value={user?.email || ""}
        onEdit={() => handleEdit("email")}
      />
      <SettingsField
        label="Phone number"
        value={user?.phone || "Not provided"}
        onEdit={() => handleEdit("phone")}
      />
      <SettingsField
        label="Bio"
        value={user?.bio || "No bio added"}
        onEdit={() => handleEdit("bio")}
      />
      <SettingsField
        label="Password"
        value="*********"
        onEdit={() => handleEdit("password")}
      />
      <SettingsField
        label="Social links"
        value={
          user?.socialMedia?.webLinks?.map((item) => item.title).join(", ") ||
          ""
        }
        onEdit={() => handleEdit("socialMedia")}
      />

      <AddressSettingField />
      {editingField && (
        <EditModal
          field={editingField}
          initialValue={getInitialValue(editingField, user)}
          onClose={handleCloseModal}
          onSave={(value) => handleSave(editingField, value)}
          fieldSchemas={fieldSchemas}
          user={user}
        />
      )}
      <SettingsField
        label="Hide modifications from other users"
        value={user?.hideModifications}
        onEdit={(val) => handleSave("hideModifications", val)}
        switchInput={true}
      />
      <SettingsField
        label="Hide points from other users"
        value={user?.hidePoints}
        onEdit={(val) => handleSave("hidePoints", val)}
        switchInput={true}
      />
    </>
  );
};

export default SettingsPage;
