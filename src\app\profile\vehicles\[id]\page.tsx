"use client";
import React, { useMemo, useState } from "react";
import { <PERSON><PERSON>, Divider } from "@nextui-org/react";
import Link from "next/link";
import { MdArrowBack } from "react-icons/md";
import { useSessionData } from "@/lib/hooks/useSession";
import {
  useGetModificationTypesQuery,
  useGetVehicleDetailsQuery,
} from "@/lib/redux/slices/vehicles/vehiclesApi";
import { groupVehicleModifications } from "@/lib/utils/groupModifications";
import VehicleImageCarousel from "../components/VehicleImagesCarousel";
import ModificationTypesList from "../components/ModificationTypesList";
import VehicleHeader from "../components/VehicleHeader";
import { useRouter } from "next/navigation";
import VehicleDetailsSkeleton from "../components/VehicleDetailsSkeleton";
import CategoryWiseModifications from "../components/CategoryWiseModifications";

const VehicleDetailPage = ({ params }) => {
  const vehicleId = params?.id;
  const { data } = useSessionData();
  const [modificationDetails, setModificationDetails] = useState<any>(null);
  const { data: modificationTypes, isLoading: isLoadingModificationTypes } =
    useGetModificationTypesQuery({ token: data?.accessToken });
  const {
    data: vehicleDetails,
    isLoading: isLoadingVehicleDetails,
    error,
  } = useGetVehicleDetailsQuery({
    token: data?.accessToken,
    id: vehicleId,
  });

  const router = useRouter();
  const canEditVehicles = data?.user?.id === vehicleDetails?.user?.id;

  const groupedModificationTypesDetails = useMemo(() => {
    const vehicleDetailsAndModTypesExist = vehicleDetails && modificationTypes;
    if (vehicleDetailsAndModTypesExist) {
      const filteredModTypes = modificationTypes.filter(
        (modType) =>
          modType.vehicleType === vehicleDetails?.model?.make?.type?.id
      );
      return groupVehicleModifications(vehicleDetails, filteredModTypes);
    }
    return [];
  }, [vehicleDetails, modificationTypes]);

  if (isLoadingVehicleDetails || isLoadingModificationTypes) {
    return <VehicleDetailsSkeleton />;
  }

  return (
    <main className="min-h-screen">
      <div className="max-w-[900px] mx-auto mb-4 mt-20 px-4">
        <div className="flex gap-2 items-center w-full mb-6">
          {modificationDetails ? (
            <MdArrowBack
              size={20}
              className="cursor-pointer"
              onClick={() => setModificationDetails(null)}
            />
          ) : (
            <Link
              href={`/profile/${data?.user?.id}`}
              className="hover:opacity-80"
            >
              <MdArrowBack size={20} />
            </Link>
          )}
          <p className="font-semibold">
            {modificationDetails
              ? modificationDetails?.typeName
              : "Vehicle Details"}
          </p>
        </div>
        {modificationDetails ? (
          <CategoryWiseModifications
            modificationDetails={modificationDetails}
            vehicleDetails={vehicleDetails}
          />
        ) : (
          <>
            {vehicleDetails ? (
              <>
                <div className="hidden md:block">
                  <VehicleHeader vehicleDetails={vehicleDetails} />
                </div>
                <Divider className="mb-6" />
                <div className="relative mb-6 flex justify-between items-center flex-col md:flex-row">
                  <VehicleImageCarousel vehicleDetails={vehicleDetails} />
                  <div className="md:hidden w-full">
                    <VehicleHeader vehicleDetails={vehicleDetails} />
                  </div>
                  <ModificationTypesList
                    setDetails={setModificationDetails}
                    groupedModificationTypesDetails={
                      groupedModificationTypesDetails
                    }
                  />
                </div>
                {canEditVehicles && (
                  <div className="flex justify-end gap-5 mb-10">
                    <Button
                      onPress={() =>
                        router.push(
                          `/profile/vehicles/${vehicleId}/modifications/add`
                        )
                      }
                      color="primary"
                      className="px-8"
                    >
                      Add modifications
                    </Button>

                    <Button
                      onPress={() =>
                        router.push(`/profile/vehicles/${vehicleId}/update`)
                      }
                    >
                      Edit
                    </Button>
                  </div>
                )}
              </>
            ) : (
              <div className="max-w-[950px] mx-auto mb-4 mt-20 px-4">
                <p className="text-center">
                  {error?.data?.detail || "Something Went Wrong"}
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </main>
  );
};

export default VehicleDetailPage;
