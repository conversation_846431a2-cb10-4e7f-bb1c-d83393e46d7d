"use client";

import React from "react";
import Link from "next/link";
import { MdArrowBack } from "react-icons/md";
import { Button } from "@nextui-org/react";
import VehicleTypeSelector from "./VehicleTypeSelector";
import VehicleDetailsForm from "./VehicleDetailsForm";
import { useVehicleForm } from "@/lib/hooks/useVehicleForm";
import { ImageUpload } from "./VehicleImagesUpload";
import SkeletonForm from "./FormSkeleton";
import { useSessionData } from "@/lib/hooks/useSession";

const AddOrUpdateVehicle = ({ vehicleId = null }) => {
  const { formState, formActions, formMethods } = useVehicleForm(vehicleId);
  const { data } = useSessionData();
  const {
    isEditMode,
    isAddLoading,
    isUpdateLoading,
    isVehicleLoading,
    typesLoading,
    makesLoading,
    modelsLoading,
    photos,
    vehicleOptions,
    selectedVehicleMake,
    addUpdateVehicleError,
  } = formState;
  const { setCoverPhoto, setSecondaryPhotos, addDeletedImage } = formActions;
  const {
    control,
    errors,
    handleSubmit,
    onSubmit,
    handleDeleteVehicle,
    setValue,
    loadModelOptions,
    loadMakeOptions,
  } = formMethods;
  const isEditAndLoading = isEditMode && isVehicleLoading;

  if (isEditAndLoading) {
    return <SkeletonForm />;
  }

  return (
    <main className="mx-auto mb-10 mt-20 items-center flex justify-center flex-col min-h-screen">
      <div className="w-[90%] sm:max-w-[770px] flex gap-2 items-center mb-8">
        <Link
          href={
            isEditMode
              ? `/profile/vehicles/${vehicleId}`
              : `/profile/${data?.user?.id}`
          }
          className="hover:opacity-80"
        >
          <MdArrowBack size={20} />
        </Link>
        <p className="font-semibold">
          {isEditMode ? "Update Vehicle" : "Add Vehicle"}
        </p>
      </div>
      <div className="w-[90%] sm:max-w-[425px] items-center flex justify-center flex-col ">
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="w-full flex justify-center flex-col items-center gap-5 mb-14"
        >
          <ImageUpload
            coverPhoto={photos?.cover}
            secondaryPhotos={photos?.secondary}
            setCoverPhoto={setCoverPhoto}
            setSecondaryPhotos={setSecondaryPhotos}
            setDeletedImages={addDeletedImage}
          />

          <VehicleTypeSelector
            control={control}
            allTypes={vehicleOptions.types}
          />

          <VehicleDetailsForm
            control={control}
            errors={errors}
            filteredMakes={vehicleOptions?.makes}
            typesLoading={typesLoading}
            makesLoading={makesLoading}
            modelsLoading={modelsLoading}
            models={vehicleOptions?.models}
            setValue={setValue}
            selectedVehicleMake={selectedVehicleMake}
            loadMakeOptions={loadMakeOptions}
            loadModelOptions={loadModelOptions}
          />

          {addUpdateVehicleError && addUpdateVehicleError?.data && (
            <div>
              {Object.keys(addUpdateVehicleError?.data)?.map((key) => (
                <p key={key} className="text-red-500 text-xs">
                  {addUpdateVehicleError?.data[key][0]}
                </p>
              ))}
            </div>
          )}

          <Button
            type="submit"
            disabled={isAddLoading || isUpdateLoading || isVehicleLoading}
            className="mt-3 min-w-[200px]"
            isLoading={isAddLoading || isUpdateLoading || isVehicleLoading}
            color="primary"
          >
            {isEditMode ? "Update Vehicle" : "Add Vehicle"}
          </Button>
          {isEditMode && (
            <Button
              onPress={handleDeleteVehicle}
              variant="light"
              size="sm"
              className="text-sm mt-3 underline text-red-500"
              isLoading={isVehicleLoading}
            >
              Delete
            </Button>
          )}
        </form>
      </div>
    </main>
  );
};

export default AddOrUpdateVehicle;
