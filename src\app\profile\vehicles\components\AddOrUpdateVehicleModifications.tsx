"use client";

import React, { useMemo } from "react";
import { Select, Input, Button, SelectItem } from "@nextui-org/react";
import Form<PERSON>ield from "@/components/FormField/FormField";
import Link from "next/link";
import { MdArrowBack } from "react-icons/md";
import { useModificationForm } from "@/lib/hooks/useModificationForm";
import SkeletonForm from "./FormSkeleton";

interface AddUpdateModificationFormProps {
  vehicleId: string;
  modificationId?: string | null;
}

const AddUpdateModificationForm: React.FC<AddUpdateModificationFormProps> = ({
  vehicleId,
  modificationId = null,
}) => {
  const { formState, formData, formMethods } = useModificationForm(
    vehicleId,
    modificationId
  );
  const {
    isUpdateMode,
    isVehicleDetailsLoading,
    isCategoriesLoading,
    isSubCategoriesLoading,
    isPartsLoading,
    isSubmitting,
    selectedCategory,
    selectedSubCategory,
    addUpdateModificationError,
    vehicleDetailError,
  } = formState;
  const { categories, subCategories, parts } = formData;
  const { control, errors, handleSubmit, onSubmit, handleDeleteModification } =
    formMethods;

  const vehicleDetailsLoadedAndError =
    !isVehicleDetailsLoading && vehicleDetailError;

  const renderErrorMessages = useMemo(() => {
    if (!addUpdateModificationError?.data) return null;
    return Object.entries(addUpdateModificationError.data).map(
      ([key, value]) => (
        <p key={key} className="text-red-500 text-xs">
          {Array.isArray(value) ? value[0] : value}
        </p>
      )
    );
  }, [addUpdateModificationError]);

  if (isVehicleDetailsLoading) return <SkeletonForm />;

  return (
    <main>
      <div className="w-[90%] sm:max-w-[700px] mx-auto mb-10 mt-20 items-center flex justify-center flex-col">
        <div className="flex gap-2 items-center w-full mb-5">
          <Link
            href={`/profile/vehicles/${vehicleId}`}
            className="hover:opacity-80"
          >
            <MdArrowBack size={20} />
          </Link>
          <p className="font-semibold">
            {isUpdateMode ? "Update" : "Add"} Modification
          </p>
        </div>

        {vehicleDetailsLoadedAndError ? (
          <div>{vehicleDetailError?.data?.detail || "An error occurred"}</div>
        ) : (
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-2 w-full mx-auto mt-6 flex justify-center flex-col items-center gap-2 mb-14"
          >
            <FormField
              name="category"
              control={control}
              errors={errors}
              render={(field) => (
                <Select
                  {...field}
                  className="max-w-xs"
                  placeholder="Select modification category"
                  isLoading={isCategoriesLoading}
                  selectedKeys={field.value ? [field.value] : []}
                  required
                >
                  {categories?.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </Select>
              )}
            />

            {selectedCategory && (
              <FormField
                name="subcategory"
                control={control}
                errors={errors}
                render={(field) => (
                  <Select
                    {...field}
                    placeholder="Select subcategory"
                    className="max-w-xs"
                    isLoading={isSubCategoriesLoading}
                    selectedKeys={field.value ? [field.value] : []}
                    required
                  >
                    {subCategories?.map((subcat) => (
                      <SelectItem key={subcat.id} value={subcat.id}>
                        {subcat.name}
                      </SelectItem>
                    ))}
                  </Select>
                )}
              />
            )}

            {selectedSubCategory && (
              <>
                <FormField
                  name="part"
                  control={control}
                  errors={errors}
                  render={(field) => (
                    <Select
                      {...field}
                      placeholder="Select part"
                      isLoading={isPartsLoading}
                      className="max-w-xs"
                      selectedKeys={field.value ? [field.value] : []}
                      required
                    >
                      {parts?.map((p) => (
                        <SelectItem key={p.id} value={p.id}>
                          {p.name}
                        </SelectItem>
                      ))}
                    </Select>
                  )}
                />

                <FormField
                  name="brand"
                  control={control}
                  errors={errors}
                  render={(field) => (
                    <Input
                      {...field}
                      className="max-w-xs"
                      placeholder="Enter brand"
                    />
                  )}
                />
              </>
            )}

            {renderErrorMessages}

            <div>
              <Button
                type="submit"
                color="primary"
                isLoading={isSubmitting}
                className="mt-5 min-w-[200px]"
              >
                {isUpdateMode ? "Update" : "Add"} modification
              </Button>

              {isUpdateMode && (
                <Button
                  onPress={handleDeleteModification}
                  variant="light"
                  size="sm"
                  className="text-sm mt-3 underline text-red-500"
                  isLoading={isVehicleDetailsLoading}
                >
                  Delete
                </Button>
              )}
            </div>
          </form>
        )}
      </div>
    </main>
  );
};

export default AddUpdateModificationForm;
