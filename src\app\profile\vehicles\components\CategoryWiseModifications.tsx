import React, { useMemo } from "react";
import { <PERSON><PERSON>, Card, CardBody } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import ModificationCard from "./ModificationCard";
import ModificationHeader from "./ModificationHeader";
import { useSessionData } from "@/lib/hooks/useSession";

const CategorySection = ({
  category,
  modifications,
  vehicleId,
}: {
  category: string;
  modifications: IVehicleModification[];
  vehicleId: string;
}) => (
  <>
    <div className="flex items-center justify-center mb-8">
      <div className="border-t-3 border-black w-10 md:w-28 mr-5"></div>
      <span className="text-2xl font-semibold text-gray-800">{category}</span>
      <div className="border-t-3 border-black w-10 md:w-28 ml-5"></div>
    </div>
    <div className="grid grid-cols-1 gap-4 mb-6">
      {modifications?.map((mod: IVehicleModification) => (
        <ModificationCard key={mod?.id} mod={mod} vehicleId={vehicleId} />
      ))}
    </div>
  </>
);

const AddModificationButton = ({ onClick }) => (
  <Button
    onPress={onClick}
    className="mx-auto min-w-[200px] px-8 mt-2"
    color="primary"
  >
    Add modifications
  </Button>
);

const CategoryWiseModifications = ({ modificationDetails, vehicleDetails }) => {
  const router = useRouter();
  const { data } = useSessionData();
  const canEditVehicle = vehicleDetails?.user?.id === data?.user?.id;

  const handleAddModification = () => {
    router.push(`/profile/vehicles/${vehicleDetails?.id}/modifications/add`);
  };

  const categorySections = useMemo(() => {
    return Object.entries(modificationDetails?.modifications)?.map(
      ([category, mods]) => (
        <CategorySection
          key={category}
          category={category}
          modifications={mods as IVehicleModification[]}
          vehicleId={vehicleDetails?.id}
        />
      )
    );
  }, [modificationDetails?.modifications, vehicleDetails?.id]);

  return (
    <div>
      <div className="flex flex-col mt-8">
        <ModificationHeader
          typeName={modificationDetails?.typeName}
          totalPoints={modificationDetails?.totalPoints}
          count={modificationDetails?.count}
        />
        {categorySections}
        {canEditVehicle && (
          <AddModificationButton onClick={handleAddModification} />
        )}
      </div>
    </div>
  );
};

export default CategoryWiseModifications;
