import React from "react";
import { Skeleton } from "@nextui-org/react";

const SkeletonForm = () => {
  return (
    <div className="w-[90%] sm:max-w-[700px] mx-auto mb-10 mt-20 items-center flex justify-center flex-col">
      <div className="flex gap-2 items-center w-full mb-5">
        <Skeleton className="w-6 h-6 rounded-full" />
        <Skeleton className="w-40 h-6 rounded-md" />
      </div>
      <div className="space-y-4 w-full mx-auto mt-8 flex justify-center flex-col items-center gap-5 mb-14">
        <Skeleton className="w-full max-w-xs h-12 rounded-lg" />
        <Skeleton className="w-full max-w-xs h-12 rounded-lg" />
        <Skeleton className="w-full max-w-xs h-12 rounded-lg" />
        <Skeleton className="w-full max-w-xs h-12 rounded-lg" />
        <Skeleton className="w-full max-w-xs h-12 rounded-full" />
      </div>
    </div>
  );
};

export default SkeletonForm;