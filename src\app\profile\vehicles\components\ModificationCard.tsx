import { Card, CardBody } from "@nextui-org/react";
import Link from "next/link";

const ModificationCard = ({ mod, vehicleId } : {
    mod: IVehicleModification;
    vehicleId: string;
  }) => (
    <Link
      href={`/profile/vehicles/${vehicleId}/modifications/${mod.id}`}
      className="w-full flex justify-center flex-col items-center"
    >
      <Card className="min-w-full md:min-w-[400px] bg-[#D9D9D9] hover:opacity-85 cursor-pointer">
        <CardBody className="flex flex-row items-center justify-between px-4 py-2">
          <div className="flex items-center space-x-4">
            <div>
              <p className="font-semibold">{mod?.part?.name}</p>
              <p className="text-sm text-gray-700">{mod?.brand}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="flex flex-col font-bold text-sm md:text-base text-white items-center aspect-square justify-center text-center bg-black rounded-xl py-1 px-2 md:px-3 w-auto">
              <p>{mod?.points}</p>
              <p>PTS</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </Link>
  );

export default ModificationCard;