import { modificationTypeIcons } from "@/lib/utils/constants";
import Image from "next/image";
import { MdOutlineSettings } from "react-icons/md";

const ModificationHeader = ({
  typeName,
  totalPoints,
  count,
}: {
  typeName: string;
  totalPoints: number;
  count: number;
}) => (
  <>
    <div className="flex gap-5 sm:gap-10 justify-center items-center">
      <Image
        src={modificationTypeIcons[typeName]}
        width={80}
        height={80}
        alt="modification type icon"
        className="rounded-full"
      />
      <p className="text-3xl sm:text-4xl font-extrabold text-center truncate">
        {typeName}
      </p>
      <div className="text-right">
        <div className="flex flex-col font-bold md:text-lg text-white items-center aspect-square justify-center text-center bg-black rounded-xl py-2 px-2 md:px-4 w-auto">
          <p>{totalPoints}</p>
          <p>PTS</p>
        </div>
      </div>
    </div>
    <div className="flex justify-center items-center gap-2">
      <MdOutlineSettings className="mt-2" />
      <p className="text-center mt-8 mb-6 font-medium">{count} Modifications</p>
    </div>
  </>
);

export default ModificationHeader;
