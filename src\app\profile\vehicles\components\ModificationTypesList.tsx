import React from "react";
import { <PERSON><PERSON>, <PERSON>, CardBody, Image } from "@nextui-org/react";
import { modificationTypeIcons } from "@/lib/utils/constants";

const ModificationTypesList = ({
  groupedModificationTypesDetails,
  setDetails,
}) => {
  if (groupedModificationTypesDetails?.length === 0) {
    return (
      <div className="w-full flex justify-center items-center">
        <p className="text-center">
          No modification type found for this vehicle
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-4 mb-6 w-full">
      {groupedModificationTypesDetails.map((type) => (
        <Button
          variant="ghost"
          onPress={() => {
            setDetails(type);
          }}
          className="w-full h-full bg-[#D9D9D9] hover:opacity-85  p-0 rounded-2xl"
        >
          <Card key={type?.typeId} className="w-full bg-[#D9D9D9] ">
            <CardBody className="flex flex-row items-center justify-between px-4 py-2">
              <div className="flex items-center space-x-4">
                <Image
                  src={modificationTypeIcons[type?.typeName]}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
                <div>
                  <p className="font-semibold">{type?.typeName}</p>
                  <p className="text-sm text-gray-500">
                    {type?.count} Modification
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex flex-col font-bold text-sm md:text-base text-white items-center aspect-square justify-center text-center bg-black rounded-xl py-1 px-2 md:px-3 w-auto">
                  <p>{type?.totalPoints}</p>
                  <p>PTS</p>
                </div>
              </div>
            </CardBody>
          </Card>
        </Button>
      ))}
    </div>
  );
};

export default ModificationTypesList;
