import React, { useEffect, useState } from "react";
import { Input, Select, SelectItem } from "@nextui-org/react";
import <PERSON>Field from "../../../../components/FormField/FormField";
import FormControlledBaseVirtualisedSelect from "@/components/VirtualisedSelect/FormControlledVirtualisedSelect";
import { Option } from "@/components/VirtualisedSelect/BaseVirtualisedSelect";

function getYearsSince1950(): string[] {
  try {
    const currentYear: number = new Date().getFullYear();
    return Array.from({ length: currentYear - 1949 }, (_, i) => `${1950 + i}`);
  } catch (error) {
    console.error("Error getting years since 1950:", error);
    return [];
  }
}

const VehicleDetailsForm = ({
  control,
  errors,
  filteredMakes,
  models,
  selectedVehicleMake,
  makesLoading,
  modelsLoading,
  setValue,
  loadMakeOptions,
  loadModelOptions,
}) => {
  const years = getYearsSince1950();
  const [makeOptions, setMakeOptions] = useState<Option[]>([]);
  const [modelOptions, setModelOptions] = useState<Option[]>([]);

  useEffect(() => {
    if (filteredMakes) {
      const newMakeOptions = filteredMakes.map((make) => ({
        label: make.name,
        value: make.id,
      }));
      setMakeOptions(newMakeOptions);
    }
  }, [filteredMakes]);

  useEffect(() => {
    if (models && models?.types?.[0]?.makes?.[0]?.models) {
      const newModelOptions = models?.types?.[0]?.makes?.[0]?.models?.map(
        (model) => ({
          label: model.name,
          value: model.id,
        })
      );
      setModelOptions(newModelOptions);
    }
  }, [models]);

  return (
    <div className="flex flex-col justify-center items-center w-full gap-2">
        <FormField
          name="name"
          control={control}
          errors={errors}
          render={(field) => (
            <Input
              {...field}
              className="max-w-xs"
              placeholder="Enter Vehicle Name"
            />
          )}
        />
        <FormField
          name="year"
          control={control}
          errors={errors}
          render={(field) => (
            <Select
              {...field}
              className="max-w-xs"
              placeholder="Select Year"
              selectedKeys={[field.value]}
            >
              {years.map((year) => (
                <SelectItem key={year} value={year}>
                  {year}
                </SelectItem>
              ))}
            </Select>
          )}
        />
      <FormControlledBaseVirtualisedSelect
        name="make"
        control={control}
        label="Make"
        loadOptions={loadMakeOptions}
        onChange={(newValue) => {
          setValue("make", newValue?.value);
        }}
        defaultOptions={makeOptions}
        className="w-[20rem]"
        isLoading={makesLoading}
        isRequired={true}
        placeholder="Select Make"
        variant="filled"
        errors={errors}
      />

      <FormControlledBaseVirtualisedSelect
        name="model"
        control={control}
        label="Model"
        loadOptions={loadModelOptions}
        isDisabled={!selectedVehicleMake}
        isLoading={modelsLoading}
        placeholder="Select Model"
        onChange={(newValue) => {
          setValue("model", newValue?.value);
        }}
        defaultOptions={modelOptions}
        className="w-[20rem]"
        isRequired={true}
        variant="filled"
        errors={errors}
      />
    </div>
  );
};
export default VehicleDetailsForm;
