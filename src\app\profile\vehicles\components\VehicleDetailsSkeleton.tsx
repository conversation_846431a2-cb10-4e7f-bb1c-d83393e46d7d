import { Skeleton } from "@nextui-org/react";

const VehicleDetailsSkeleton = () => {
  return (
    <div className="min-h-screen bg-white p-8">
      <div className="max-w-[950px] mx-auto px-4 flex justify-between items-center mb-8 mt-20">
        <Skeleton className="h-8 w-1/4 rounded-lg" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-6 w-24 rounded-lg" />
          <Skeleton className="h-6 w-16 rounded-lg" />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        {Array.from({ length: 2 }).map((_, idx) => (
          <div key={idx} className="flex flex-col items-center p-4 bg-gray-100 rounded-lg">
            <Skeleton className="h-24 w-full rounded-full mb-2" />
            <Skeleton className="h-10 w-3/4 rounded-lg mb-1" />
            <Skeleton className="h-10 w-1/2 rounded-lg" />
          </div>
        ))}
      </div>

      <div className="flex justify-end mt-4">
        <Skeleton className="h-10 w-32 rounded-lg mr-2" />
        <Skeleton className="h-10 w-32 rounded-lg" />
      </div>
    </div>
  );
};

export default VehicleDetailsSkeleton;
