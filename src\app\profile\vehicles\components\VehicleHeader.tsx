import React from "react";
import { MdOutlineSettings } from "react-icons/md";

const VehicleHeader = ({ vehicleDetails }) => {
  return (
    <>
      <div className="justify-between mb-2 items-center hidden md:flex">
        <div className="flex gap-2 md:gap-5 justify-center items-center">
          <h1 className="text-2xl font-bold">{vehicleDetails?.name}</h1>
          <p className="">({vehicleDetails?.year})</p>
        </div>
        <div className="flex gap-1 justify-center items-center">
          <MdOutlineSettings />
          <p className="">
            {vehicleDetails?.modifications?.length} Modifications
          </p>
        </div>
        <div className="flex gap-2 md:gap-5 justify-center items-center">
          <h1 className="text-lg font-bold">
            {vehicleDetails?.points} PTS
          </h1>
        </div>
      </div>

      <div className="md:hidden flex flex-col justify-center items-center gap-2 w-full mb-5">
        <p className="text-xl font-bold">
          {vehicleDetails?.year} {vehicleDetails?.name}
        </p>
        <div className="w-full flex mt-2 justify-between">
          <div className="flex gap-1 justify-center">
            <MdOutlineSettings className="mt-1" />
            <p className="text-center">
              {vehicleDetails?.modifications?.length} Modifications
            </p>
          </div>
          <div className="text-right">
            <div className="flex font-bold text-white items-center aspect-square justify-center text-center bg-black rounded-xl px-2">
              <p>{vehicleDetails?.points} PTS</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default VehicleHeader;