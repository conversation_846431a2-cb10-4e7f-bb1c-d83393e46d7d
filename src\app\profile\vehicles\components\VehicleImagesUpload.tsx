import React, { useState } from "react";
import Image from "next/image";
import { HiOutlineXCircle } from "react-icons/hi2";
import { FaPlus } from "react-icons/fa";
import { RiImageAddFill } from "react-icons/ri";
import toast from "react-hot-toast";
import { TOASTS } from "@/lib/utils/constants";
import { useImageUpload } from "@/components/ImageSelector/useImageUpload";
import { processImageFile } from "@/lib/utils/heicConverter";
import { Spinner } from "@nextui-org/react";

export const ImageUpload = ({
  coverPhoto,
  secondaryPhotos,
  setCoverPhoto,
  setSecondaryPhotos,
  setDeletedImages,
}) => {
  const [loadingIndices, setLoadingIndices] = useState<number[]>([]);

  const {
    handleImageSelect: handleCoverImageSelect,
    isUploading: isCoverUploading,
  } = useImageUpload({
    fileSize: 9.5 * 1024 * 1024,
    onSuccess: (url) => {
      if (url) {
        setCoverPhoto({
          url,
          file: null,
        });
      }
    },
  });

  const {
    handleImageSelect: handleSecondaryImageSelect,
    isUploading: isSecondaryUploading,
  } = useImageUpload({
    fileSize: 9.5 * 1024 * 1024,
    onSuccess: (url) => {
      if (url) {
        setSecondaryPhotos({
          url,
          file: null,
          id: null,
        });
      }
    },
  });

  const onCoverPhotoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) {
      toast.error("No file selected");
      return;
    }

    try {
      // Process HEIC files first (convert to JPEG if needed)
      const processedFile = await processImageFile(file);
      await handleCoverImageSelect(processedFile);
    } catch (error) {
      console.error("Error uploading cover photo:", error);
      toast.error(TOASTS.IMAGE_NOT_VALID);
    }
  };

  const onSecondaryPhotoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        setLoadingIndices((prev) => [...prev, index]);
        await handleSecondaryImageSelect(file);
        setLoadingIndices((prev) => prev.filter((i) => i !== index));
      } catch (error) {
        console.error("Error uploading secondary photo:", error);
        toast.error(TOASTS.IMAGE_NOT_VALID);
        setLoadingIndices((prev) => prev.filter((i) => i !== index));
      }
    }
  };

  const onDeleteCoverPhoto = (e) => {
    e.preventDefault();
    const hasCoverPhoto = coverPhoto && coverPhoto?.id;
    if (hasCoverPhoto) {
      setDeletedImages(coverPhoto?.id);
    }
    setCoverPhoto(null);
  };

  const onDeleteSecondaryPhoto = (e, index: number) => {
    e.preventDefault();
    const photoToDelete = secondaryPhotos[index];
    const hasPhotoToDelete = photoToDelete && photoToDelete?.id;
    if (hasPhotoToDelete) {
      setDeletedImages(photoToDelete?.id);
    }
    const newSecondaryPhotos = secondaryPhotos.filter((_, i) => i !== index);
    setSecondaryPhotos(newSecondaryPhotos);
  };

  return (
    <>
      <div className="relative w-3/4 sm:w-1/2 aspect-square bg-[#EDEDED] rounded-2xl overflow-hidden">
        {coverPhoto ? (
          <>
            <Image
              src={coverPhoto?.url}
              alt="Cover photo"
              layout="fill"
              objectFit="cover"
            />
            <button
              onClick={onDeleteCoverPhoto}
              className="absolute top-2 right-2 p-1 bg-white rounded-full shadow-md"
              disabled={isCoverUploading}
            >
              <HiOutlineXCircle className="w-4 h-4 text-gray-600" />
            </button>
          </>
        ) : (
          <label className="flex items-center justify-center w-full h-full cursor-pointer">
            <input
              type="file"
              className="hidden"
              onChange={onCoverPhotoUpload}
              accept=".jpg,.jpeg,.png,.webp,.heic,.heif"
              disabled={isCoverUploading}
            />
            <div className="text-center">
              {isCoverUploading ? (
                <Spinner size="lg" color="primary" />
              ) : (
                <div className="flex justify-center">
                  <RiImageAddFill className="w-12 h-12 rounded-full text-gray-400" />
                </div>
              )}
            </div>
          </label>
        )}
      </div>

      <div className="w-full h-full grid grid-cols-4 gap-2 sm:gap-4">
        {[...Array(4)].map((_, index) => (
          <div
            key={index}
            className="relative w-full aspect-square pt-[100%] bg-[#EDEDED] rounded-lg overflow-hidden"
          >
            {secondaryPhotos[index] ? (
              <>
                <Image
                  src={secondaryPhotos[index]?.url}
                  alt={`Secondary photo ${index + 1}`}
                  layout="fill"
                  objectFit="cover"
                />
                <button
                  onClick={(e) => onDeleteSecondaryPhoto(e, index)}
                  className="absolute top-2 right-2 p-1 bg-white rounded-full shadow-md"
                  disabled={loadingIndices.includes(index)}
                >
                  <HiOutlineXCircle className="w-4 h-4 text-gray-600" />
                </button>
              </>
            ) : (
              <label className="absolute inset-0 flex items-center justify-center cursor-pointer">
                <input
                  type="file"
                  className="hidden"
                  onChange={(e) => onSecondaryPhotoUpload(e, index)}
                  accept=".jpg,.jpeg,.png"
                  disabled={loadingIndices.includes(index)}
                />
                <div className="text-center px-3">
                  {loadingIndices.includes(index) ? (
                    <Spinner size="sm" color="primary" />
                  ) : (
                    <>
                      <p className="mt-1 text-xs text-gray-400 font-normal">
                        Add more photos
                      </p>
                      <div className="flex justify-center">
                        <FaPlus className="w-3 h-3 text-gray-400 mt-1" />
                      </div>
                    </>
                  )}
                </div>
              </label>
            )}
          </div>
        ))}
      </div>
    </>
  );
};
