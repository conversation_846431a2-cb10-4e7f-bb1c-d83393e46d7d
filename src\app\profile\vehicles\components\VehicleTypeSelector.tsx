import React from "react";
import { Controller } from "react-hook-form";
import { RadioGroup } from "@nextui-org/react";
import Image from "next/image";
import { CustomRadio } from "@/components/CustomRadio/CustomRadio";

const VehicleTypeSelector = ({ control, allTypes }) => {
  return (
    <Controller
      name="vehicleType"
      control={control}
      render={({ field }) => (
        <RadioGroup
          className="my-2"
          orientation="horizontal"
          {...field}
        >
          {allTypes?.map((type) => (
            <CustomRadio
              key={type?.id}
              description={type?.name}
              value={type?.name?.toLowerCase()}
            >
              <Image
                src={`/${type?.name?.toLowerCase()}.svg`}
                alt="ticket"
                width={80}
                height={80}
                className="w-[35px] h-[35px] sm:w-[50px] sm:h-[50px]  object-contain mx-auto"
              />
            </CustomRadio>
          ))}
        </RadioGroup>
      )}
    />
  );
};

export default VehicleTypeSelector;