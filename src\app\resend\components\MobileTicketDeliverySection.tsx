import { BASE_URL } from "@/components/HomeSections/utils/constants";
import { useMediaQuery } from "@mui/material";
import clsx from "clsx";
import Image from "next/image";

export const MobileTicketDeliverySection = () => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <div className="bg-gradient-to-br from-[#F6F8F8] to-[#FBFCFC] rounded-[14px] p-6 md:p-8 py-[23px] md:py-[26px] relative overflow-hidden min-h-[305px] md:min-h-[440px] max-h-[440px] flex-1">
      <div className="relative z-10 mb-10">
        <h3 className="text-[18px] md:text-[23px] font-semibold text-[#101010] mb-3 md:mb-4 tracking-[0.4px]">
          Mobile ticket delivery
        </h3>
        <p className="text-[#6F7275] text-[14px] md:text-[20px] tracking-[0.2px] md:tracking-[0.5px] font-normal leading-[19px] md:leading-[24px] w-full md:w-[95%]">
          Get tickets instantly, straight to your phone. No more{" "}
          <br className="md:hidden" /> digging through your email.
        </p>
      </div>
      <div className={clsx("absolute bottom-0", isMobile && "left-0")}>
        <Image
          width={1000}
          height={1000}
          quality={100}
          priority={true}
          src={`${BASE_URL}/landing-page/ticket.png`}
          alt="Mobile ticket delivery"
          className="h-full w-full object-cover object-left"
        />
      </div>
    </div>
  );
}; 

