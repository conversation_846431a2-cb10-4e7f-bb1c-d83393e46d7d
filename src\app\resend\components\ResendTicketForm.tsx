import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useResendTicket } from "../hooks/useResendTicket";

const formatPhoneNumber = (value: string): string => {
  const digits = value.replace(/\D/g, "");

  const limitedDigits = digits.slice(0, 10);

  if (limitedDigits.length === 0) return "";
  if (limitedDigits.length <= 3) return `(${limitedDigits}`;
  if (limitedDigits.length <= 6)
    return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3)}`;
  return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(
    3,
    6
  )}-${limitedDigits.slice(6)}`;
};

interface ResendTicketFormProps {
  organizerSlug: string | null;
}

export const ResendTicketForm = ({ organizerSlug }: ResendTicketFormProps) => {
  const { form, onSubmit, isResending } = useResendTicket(organizerSlug);

  return (
    <div className="flex-1 w-full md:max-w-[400px] mx-auto md:mx-0">
      <div className="bg-white px-2 py-3 md:px-8 md:py-7 rounded-[8px] md:border-[1.5px] md:border-[#E8E8E8]">
        <p className="text-[#232323] text-[15px] font-normal mb-7 tracking-[0.8px]">
          Enter the information used to place your <br /> order and we will text
          your tickets to you.
        </p>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder="First name"
                      className="h-12 border-[#D9D9D9] focus:ring-1 rounded-[8px] focus:ring-primary text-sm font-inter placeholder:text-[#9A9A9A]"
                      {...field}
                      required
                    />
                  </FormControl>
                  <FormMessage className="text-red-500 text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      required
                      placeholder="Last name"
                      className="h-12 border-[#D9D9D9] focus:ring-1 rounded-[8px] focus:ring-primary text-sm font-inter placeholder:text-[#9A9A9A]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-red-500 text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder="Phone number"
                      type="tel"
                      required
                      className="h-12 border-[#D9D9D9] focus:ring-1 rounded-[8px] focus:ring-primary text-sm font-inter placeholder:text-[#9A9A9A]"
                      {...field}
                      onChange={(e) => {
                        const formattedValue = formatPhoneNumber(
                          e.target.value
                        );
                        field.onChange(formattedValue);
                      }}
                      maxLength={14}
                    />
                  </FormControl>
                  <FormMessage className="text-red-500 text-xs" />
                </FormItem>
              )}
            />

            <div className="flex items-start space-x-2 mt-3">
              <p className="text-xs text-[#9A9A9A] leading-relaxed cursor-pointer tracking-[0.6px]">
                By clicking "resend tickets" you agree to receiving <br />
                tickets via text delivery
              </p>
            </div>

            <div className="pt-4">
              <Button
                type="submit"
                disabled={isResending}
                loading={isResending}
                className="w-full h-12 bg-[#367AFF] hover:bg-[#2c66e6] text-white font-medium text-base rounded-[8px]"
              >
                Resend tickets
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};
