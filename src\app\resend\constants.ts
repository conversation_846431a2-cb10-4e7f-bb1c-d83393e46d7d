import { z } from "zod";

export const resendTicketSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    phoneNumber: z
      .string()
      .min(1, "Phone number is required")
      .regex(
        /^\(\d{3}\) \d{3}-\d{4}$/,
        "Please enter a valid US phone number"
      ),
  });
  
  export const TICKET_RESEND_SUCCESS_MESSAGE = "Ticket sent successfully";
  export const TICKET_RESEND_ERROR_MESSAGE = "Failed to send ticket";
  export const ORGANIZER_SLUG_NOT_FOUND_MESSAGE = "Organizer slug not found";
  
