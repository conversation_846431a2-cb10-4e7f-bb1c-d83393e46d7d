import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import toast from "react-hot-toast";
import { useResendTicketMutation } from "@/lib/redux/slices/organization/api";
import {  type ResendTicketFormValues } from "../types";
import { ORGANIZER_SLUG_NOT_FOUND_MESSAGE, resendTicketSchema, TICKET_RESEND_ERROR_MESSAGE, TICKET_RESEND_SUCCESS_MESSAGE } from "../constants";

const formatPhoneNumberForAPI = (formattedPhone: string): string => {
  const digits = formattedPhone.replace(/\D/g, "");
  return `+1${digits}`;
};

export const useResendTicket = (organizerSlug: string | null) => {
  const [resendTicket, { isLoading: isResending }] = useResendTicketMutation();

  const form = useForm<ResendTicketFormValues>({
    resolver: zodResolver(resendTicketSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      phoneNumber: "",
    },
  });

  const onSubmit = async (values: ResendTicketFormValues) => {
    try {
      if (!organizerSlug) {
        toast.error(ORGANIZER_SLUG_NOT_FOUND_MESSAGE);
        return;
      }
      
      const apiPayload = {
        ...values,
        phoneNumber: formatPhoneNumberForAPI(values.phoneNumber),
      };
      
      await resendTicket({
        orgSlug: organizerSlug,
        body: apiPayload,
      }).unwrap();
      toast.success(TICKET_RESEND_SUCCESS_MESSAGE);
      form.reset();
    } catch (error: any) {
      const errorMessage = error?.data?.detail || error?.data?.message || TICKET_RESEND_ERROR_MESSAGE;
      toast.error(errorMessage);
    }
  };

  return {
    form,
    onSubmit,
    isResending,
  };
};

