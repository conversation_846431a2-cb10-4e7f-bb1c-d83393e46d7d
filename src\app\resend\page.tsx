"use client";

import { useSubdomain } from "@/lib/Providers/SubdomainProvider";
import { MobileTicketDeliverySection, ResendTicketForm } from "./components";

export default function ResendPage() {
  const { isWhitelabel, organizerSlug } = useSubdomain();

  if (!isWhitelabel) {
    return <></>;
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen w-[92%] md:w-full max-w-[1050px] mx-auto p-4 pt-10">
      <div className="w-full">
        <div className="flex flex-col md:flex-row gap-5 md:gap-10 w-full">
          <MobileTicketDeliverySection />
          <ResendTicketForm organizerSlug={organizerSlug} />
        </div>
      </div>
    </div>
  );
}
