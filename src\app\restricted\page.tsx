import { Metadata } from "next";
import { cookies } from "next/headers";
import { RESTRICTION_GIF, RESTRICTION_REASON } from "@/lib/utils/constants";
import Image from "next/image";
import { redirect } from "next/navigation";
import CartDataFetcher from "./CartDataFetcher";

export const metadata: Metadata = {
  title: "Access Restricted",
  description: "Your access has been restricted",
  robots: "noindex, nofollow",
};

export default function RestrictedPage() {
  const cookieStore = cookies();
  const isRestricted = cookieStore.get("restricted")?.value;

  // Redirect to home if not restricted
  if (!isRestricted) {
    redirect("/");
  }

  const restrictionReason =
    cookieStore.get("restriction_reason")?.value ||
    RESTRICTION_REASON.RATE_LIMIT_EXCEEDED;
  const restrictionDetail = cookieStore.get("restriction_detail")?.value || "";
  const restrictionCode = cookieStore.get("restriction_code")?.value || "";

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black text-white">
      <CartDataFetcher />
      <div className="flex flex-col items-center justify-center max-w-md mx-auto text-center px-4">
        <h1 className="text-3xl font-bold mb-2">Access restricted</h1>
        <p className="text-gray-400 mb-8">Reason: {restrictionReason}</p>

        <div className="my-6">
          {/* The Mario-themed GIF */}
          <Image
            src={RESTRICTION_GIF}
            alt="You are blocked"
            width={240}
            height={240}
            className="rounded-md"
          />
        </div>

        <p className="text-gray-400 text-xs mt-20">
          Restriction lifted in: 0.0000000037 galactic years
        </p>
      </div>
    </div>
  );
}
