import { NextResponse } from 'next/server';

export async function GET() {
  const s3BucketName = process.env.S3_BUCKET_NAME;
  const s3StaticAssetsPrefix = process.env.S3_STATIC_ASSETS_PREFIX || '';
  const sitemapUrl = `https://${s3BucketName}.s3.amazonaws.com/${s3StaticAssetsPrefix}sitemap.xml`;

  try {
    const response = await fetch(sitemapUrl, {
      cache: 'no-store', // Ensure fresh fetch each time
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch sitemap: ${response.status}`);
    }

    const sitemapContent = await response.text();

    return new NextResponse(sitemapContent, {
        headers: {
            'Content-Type': 'application/xml',
            'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          },
    });
  } catch (error) {
    console.error('Error fetching sitemap from S3:', error);
    return new NextResponse('Sitemap not found', {
      status: 404,
    });
  }
}