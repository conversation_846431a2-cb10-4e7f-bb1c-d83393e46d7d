import path from "path";
import React from "react";
import fs from "fs";

// Function to read the HTML file content
const getTermsOfServicesHtml = () => {
  const filePath = path.join(
    process.cwd(),
    "public",
    "terms-and-services.html"
  );
  return fs.readFileSync(filePath, "utf8");
};

const Page = () => {
  const termsOfServicesHtml = getTermsOfServicesHtml();
  return (
    <div className="relative w-full mt-24 md:mt-16 lg:mt-20 px-10 pb-20 xl:max-w-[1200px] m-auto min-h-screen">
      <div
        dangerouslySetInnerHTML={{ __html: termsOfServicesHtml }}
      />
    </div>
  );
};

export default Page;
