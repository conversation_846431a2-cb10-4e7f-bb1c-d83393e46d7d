import { getUserTimeZone } from "@/lib/utils/getUserTimeZone";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import Image from "next/image";
import Link from "next/link";
import React from "react";

async function getOrganizationById(org_id: string) {
  try {
    const userTimeZone = await getUserTimeZone();
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}api/channels/${org_id}`,
      {
        headers: {
          "X-Timezone": userTimeZone,
        },
        cache: "no-store",
        credentials: "include",
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return keysToCamel(data);
  } catch (error) {
    console.error("Failed to fetch events:", error);
    return { results: [] };
  }
}

async function unSubscribeEmailMarketing({
  org_id,
  email,
}: {
  org_id: string;
  email: string;
}) {
  try {
    const userTimeZone = await getUserTimeZone();
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}marketing/${org_id}/unsubscribe/`,
      {
        cache: "no-store",
        method: "POST",
        headers: {
          "X-Timezone": userTimeZone,
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ email, reason: "" }),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return keysToCamel(data);
  } catch (error) {
    console.error("Failed to fetch events:", error);
    return { results: [] };
  }
}

export default async function UnsubscribePage({
  searchParams,
}: {
  searchParams: {
    organization_id?: string;
    campaign_id?: string;
    recipient_email?: string;
  };
}) {
  const organizationId = searchParams?.organization_id || "";
  const recipientEmail = searchParams?.recipient_email || "";
  const onUnsubscribe = await unSubscribeEmailMarketing({
    org_id: organizationId,
    email: recipientEmail,
  });
  const orgDetails = await getOrganizationById(organizationId);

  return (
    <div className="flex justify-center bg-gray-50 h-screen">
      <div className="flex flex-col gap-y-3 items-center  mt-8">
        <h2 className="font-bold text-xl md:text-2xl text-gray-900">
          {orgDetails?.companyName}
        </h2>
        <h1 className="font-bold text-xl md:text-3xl text-gray-800">
          YOU'VE BEEN UNSUBSCRIBED
        </h1>
        <p className="font-base font-medium text-gray-400">
          If you didn't mean to do this, you can resubscribe below.
        </p>
        <Link
          href="/"
          className="px-16 py-2 rounded-md bg-[#007AFF] hover:bg-[#007bffec] cursor-pointer text-gray-50 font-semibold"
        >
          GO TO WEBSITE
        </Link>
      </div>
    </div>
  );
}
