import React from "react";

interface AvatarProps {
  width?: number;
  height?: number;
  className?: string;
}

export const Avatar: React.FC<AvatarProps> = ({
  width = 84,
  height = 95,
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 84 95"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        opacity="0.56"
        d="M52.0523 93.4858C45.7475 94.676 39.2674 94.6559 32.9512 93.5174C11.3891 89.6248 0.746094 77.6305 0.746094 77.6305C15.9689 47.0699 40.4174 47.9928 40.4174 47.9928C77.2051 48.5706 83.2023 78.4384 83.2023 78.4384C72.7748 87.9918 60.4845 91.8931 52.0523 93.4858ZM41.9727 0.125C30.1711 0.125 20.6004 9.6928 20.6004 21.4973C20.6004 33.3018 30.1682 42.8696 41.9727 42.8696C53.7772 42.8696 63.345 33.3018 63.345 21.4973C63.345 9.6928 53.7772 0.125 41.9727 0.125Z"
        fill="#F0F4F7"
      />
    </svg>
  );
};
