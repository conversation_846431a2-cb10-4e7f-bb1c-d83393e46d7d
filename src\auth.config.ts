import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials";
import { AUTH_ENDPOINTS } from "@/lib/redux/slices/auth/authEndpoints";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import { handleRestriction } from "./lib/utils/handleRestriction";
import { cookies } from 'next/headers';

export const authOptions = {
  providers: [
    CredentialsProvider({
      id: "login",
      name: "Login",
      credentials: {
        username: { label: "Email", type: "text", placeholder: "Enter Email" },
        password: {
          label: "Password",
          type: "password",
          placeholder: "Enter Password",
        },
      },
      async authorize(credentials) {
        const formData = new FormData();
        Object.keys(credentials).forEach((key) => {
          formData.append(key, credentials[key] as string);
        });

        const res = await fetch(
          process.env.NEXT_PUBLIC_API_URL + "auth/" + AUTH_ENDPOINTS.login,
          {
            method: "POST",
            body: formData,
            credentials: "include",
          }
        );

        if (res.ok) {
          const loginData = keysToCamel(await res.json());
          
          // Set refresh token cookie on server side
          if (loginData.refreshToken) {
            const cookieStore = cookies();
            cookieStore.set('refreshToken', loginData.refreshToken, {
              expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
              secure: process.env.NODE_ENV === 'production',
              sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
              path: '/'
            });
          }

          return {
            ...loginData,
            name: loginData?.firstName,
            email: credentials?.username as string,
          };
        } else {
          const error = await res.json();
          throw new Error(error.detail === "AUTHENTICATION_FAILED_SIGN_IN" ? "Invalid credentials" : error.detail);
        }
      },
    }),
    CredentialsProvider({
      id: "signup",
      name: "Signup",
      credentials: {
        email: { label: "Email", type: "text", placeholder: "Enter Email" },
        password: {
          label: "Password",
          type: "password",
          placeholder: "Enter Password",
        },
        name: { label: "Name", type: "text", placeholder: "Enter Full Name" },
        username: { label: "Username", type: "text", placeholder: "Enter Username" },
        first_name: { label: "First Name", type: "text", placeholder: "Enter First Name" },
        last_name: { label: "Last Name", type: "text", placeholder: "Enter Last Name" },
        code: { label: "Code", type: "text", placeholder: "Enter Code" },
      },
      async authorize(credentials) {
        const formData = new FormData();

        Object.keys(credentials).forEach((key) => {
          formData.append(key, credentials[key] as string);
        });

        const res = await fetch(
          process.env.NEXT_PUBLIC_API_URL + "auth/" + AUTH_ENDPOINTS.signUpWithOtp,
          {
            method: "POST",
            body: formData,
            credentials: "include",
          }
        );

        if (res.ok) {
          const signupData = keysToCamel(await res.json());
          const loginFormData = new FormData();
          loginFormData.append("name", credentials?.name as string);

          await fetch(process.env.NEXT_PUBLIC_API_URL + AUTH_ENDPOINTS.user, {
            method: "PATCH",
            body: loginFormData,
            headers: { Authorization: `Bearer ${signupData.accessToken}` },
            credentials: "include",
          });

          // Update onboarding steps
          const onboardingResponse = await fetch(
            process.env.NEXT_PUBLIC_API_URL + AUTH_ENDPOINTS.onboard,
            {
              method: "PATCH",
              headers: { 
                Authorization: `Bearer ${signupData.accessToken}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                onboarding_step: {
                  enter_name: true,
                  enter_username: true,
                  upload_profile_picture: false,
                  enter_password: true,
                },
                is_onboarded: true,
              }),
              credentials: "include",
            }
          );
          
          if (!onboardingResponse.ok) {
            const errorData = await onboardingResponse.json();
            throw new Error(errorData.detail || 'Failed to update onboarding steps');
          }
          
          return { ...signupData, email: credentials?.email as string };
        } else {
          const error = await res.json();
          throw new Error(error.detail || "Sign up failed!");
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.accessToken = (user as any).accessToken;
        token.refreshToken = (user as any).refreshToken;
        token.user = { ...user, csrfToken: (user as any).csrfToken ?? token.csrfToken };
        token.accessTokenExpiration = (user as any).profile?.accessTokenExpiredAt ?? (user as any).accessTokenExpiration;
        token.csrfToken = (user as any).csrfToken;
      }

      const currentDate = new Date();
      const comparisonDate = token.accessTokenExpiration
        ? new Date(token.accessTokenExpiration as string)
        : null;
  
      if (comparisonDate && currentDate < comparisonDate) {
        return token;
      }

      try {
   

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}auth/token/refresh/`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Cookie": `refreshToken=${token.refreshToken}`,
            },
            credentials: "include",
            body: JSON.stringify({
              csrf_token: token.csrfToken as string,
            }),
          }
        );

 
        if (response.ok) {
          const refreshedApiData = await response.json();
         
          const refreshedTokens = keysToCamel(refreshedApiData) as {
            accessToken: string;
            refreshToken?: string;
            profile?: { accessTokenExpiredAt?: string };
            accessTokenExpiration?: string;
            csrfToken?: string;
          };

          // Update token with new values
          token.accessToken = refreshedTokens.accessToken;
          if (refreshedTokens.csrfToken) {
            token.csrfToken = refreshedTokens.csrfToken;
          }

          if (refreshedTokens.profile?.accessTokenExpiredAt) {
            token.accessTokenExpiration = refreshedTokens.profile.accessTokenExpiredAt;
          } else if (refreshedTokens.accessTokenExpiration) {
            token.accessTokenExpiration = refreshedTokens.accessTokenExpiration;
          }

          if (refreshedTokens.refreshToken) {
            token.refreshToken = refreshedTokens.refreshToken;
          }
          
          if (token.user && typeof token.user === 'object' && token.user !== null) {
            (token.user as any).accessToken = refreshedTokens.accessToken;
            if (refreshedTokens.csrfToken) {
              (token.user as any).csrfToken = refreshedTokens.csrfToken;
            }
          }

          return token;
        } else {
          const errorText = await response.text();
         
          return {
            error: "RefreshAccessTokenError",
          };
        }
      } catch (error) {
        
        return {
          error: "RefreshAccessTokenError",
        };
      }
    },
    async session({ session, token }) {
      if (token.error === "RefreshAccessTokenError") {
        return { ...session, error: "RefreshAccessTokenError", user: null, accessToken: null, refreshToken: null, accessTokenExpiration: null, csrfToken: null };
      }

      if (token.accessToken) {
        session.accessToken = token.accessToken as string;
        session.accessTokenExpiration = token.accessTokenExpiration as string | undefined;
        session.refreshToken = token.refreshToken as string | undefined;
        session.csrfToken = token.csrfToken as string | undefined;
        session.error = token.error as string | undefined;

        const userRes = await fetch(
          process.env.NEXT_PUBLIC_API_URL + "api/users/me",
          {
            headers: {
              Authorization: `Bearer ${token.accessToken}`,
            },
            credentials: "include",
          }
        );

        if (userRes.ok) {
          const userData = await userRes.json();
          session.user = {
            ...session.user,
            ...keysToCamel(userData),
            id: userData.id as string,
            email: userData.email as string || userData.username as string,
            image: userData.avatar as string || null,
            isActive: userData.isActive as boolean,
            isBot: userData.isBot as boolean,
            isPhoneVerified: userData.isPhoneVerified as boolean,
            userTimezone: userData.userTimezone as string,
            username: userData.username as string,
            profile: userData.profile,
            csrfToken: token.csrfToken as string,
          };
        } else if (userRes.status === 401) {
          // Token is invalid or expired, try to refresh
          try {
            const response = await fetch(
              `${process.env.NEXT_PUBLIC_API_URL}auth/token/refresh/`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "Cookie": `refreshToken=${token.refreshToken}`,
                },
                credentials: "include",
                body: JSON.stringify({
                  csrf_token: token.csrfToken as string,
                }),
              }
            );

            if (response.status === 401) {
              return {
                ...session,
                error: "RefreshAccessTokenError",
                user: null,
                accessToken: null,
                refreshToken: null,
                accessTokenExpiration: null,
                csrfToken: null,
              };
            }

            if (response.ok) {
              const refreshedApiData = await response.json();
              const refreshedTokens = keysToCamel(refreshedApiData) as {
                accessToken: string;
                refreshToken?: string;
                profile?: { accessTokenExpiredAt?: string };
                accessTokenExpiration?: string;
                csrfToken?: string;
              };

              // Update session and token with new tokens
              token.accessToken = refreshedTokens.accessToken;
              session.accessToken = refreshedTokens.accessToken;

              if (refreshedTokens.csrfToken) {
                token.csrfToken = refreshedTokens.csrfToken;
                session.csrfToken = refreshedTokens.csrfToken;
              }

              if (refreshedTokens.profile?.accessTokenExpiredAt) {
                token.accessTokenExpiration = refreshedTokens.profile.accessTokenExpiredAt;
                session.accessTokenExpiration = refreshedTokens.profile.accessTokenExpiredAt;
              } else if (refreshedTokens.accessTokenExpiration) {
                token.accessTokenExpiration = refreshedTokens.accessTokenExpiration;
                session.accessTokenExpiration = refreshedTokens.accessTokenExpiration;
              }

              if (refreshedTokens.refreshToken) {
                token.refreshToken = refreshedTokens.refreshToken;
                session.refreshToken = refreshedTokens.refreshToken;
              }

              // Retry the me call with new token
              const retryUserRes = await fetch(
                process.env.NEXT_PUBLIC_API_URL + "api/users/me",
                {
                  headers: {
                    Authorization: `Bearer ${refreshedTokens.accessToken}`,
                  },
                  credentials: "include",
                }
              );

              if (retryUserRes.ok) {
                const userData = await retryUserRes.json();
                session.user = {
                  ...session.user,
                  ...keysToCamel(userData),
                  id: userData.id as string,
                  email: userData.email as string || userData.username as string,
                  image: userData.avatar as string || null,
                  isActive: userData.isActive as boolean,
                  isBot: userData.isBot as boolean,
                  isPhoneVerified: userData.isPhoneVerified as boolean,
                  userTimezone: userData.userTimezone as string,
                  username: userData.username as string,
                  profile: userData.profile,
                  csrfToken: refreshedTokens.csrfToken || token.csrfToken as string,
                };
              } else {
                return {
                  error: "RefreshAccessTokenError",
                };
              }
            } else {
              return {
                error: "RefreshAccessTokenError",
              };
            }
          } catch (error) {
            return {
              error: "RefreshAccessTokenError",
            };
          }
        } else if (userRes.status === 403) {
          handleRestriction(userRes);
          session.error = "ForbiddenAccess";
        } else {
          session.error = "FetchUserError";
        }
      } 
      return session;
    },
  },
  trustHost: true,
  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup",
  },
  secret: process.env.NEXT_PUBLIC_AUTH_SECRET,
};

export const { auth, handlers, signIn, signOut, useSession } =
  NextAuth(authOptions as any);
