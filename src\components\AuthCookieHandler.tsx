"use client";

import { useEffect, useRef } from "react";
import { useSession, signOut } from "next-auth/react";
import { setCookie, removeCookie } from "@/lib/utils/cookieUtils"; // Only need set/remove
import type { Session } from "next-auth";
import { useRouter } from "next/navigation";
import { clearUser } from "@/lib/redux/slices/auth/authSlice";
import { useDispatch } from "react-redux";

// Define a type for the session object that includes our custom properties
// This should align with what's defined in src/types/next-auth.d.ts
interface ExtendedClientSession extends Session {
  csrfToken?: string;
  error?: string;
  refreshToken?: string;
  accessTokenExpiration?: string;
}

export const AuthCookieHandler = () => {
  const { data: session, status } = useSession();
  const initialCsrfSet = useRef(false);
  const router = useRouter();
  const dispatch = useDispatch();

  const handleLogout = async () => {
    removeCookie("csrfToken");
    removeCookie("refreshToken");
    dispatch(clearUser());
    initialCsrfSet.current = false;
    await signOut({ redirect: true, callbackUrl: "/" });
    window.location.href = "/";
  };

  useEffect(() => {
    const extendedSession = session as ExtendedClientSession | null;

    if (status === "loading") {
      return;
    }

    // Handle unauthenticated state
    if (status === "unauthenticated" || !extendedSession) {
      removeCookie("csrfToken");
      removeCookie("refreshToken");
      initialCsrfSet.current = false;
      return;
    }

    // Handle session errors
    if (
      extendedSession.error === "RefreshAccessTokenError" ||
      extendedSession.error === "token_expired"
    ) {
      handleLogout();
      return;
    }

    // Manage auth cookies
    if (status === "authenticated") {
      // Handle CSRF token
      if (extendedSession?.csrfToken) {
        if (!initialCsrfSet.current) {
          setCookie("csrfToken", extendedSession.csrfToken, { expires: 1 });
          initialCsrfSet.current = true;
        }
      } else {
        removeCookie("csrfToken");
        initialCsrfSet.current = false;
      }

      // Handle refresh token
      if (extendedSession?.refreshToken) {
        setCookie("refreshToken", extendedSession.refreshToken, {
          expires: 7,
          secure: process.env.NODE_ENV === "production",
          sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
        });
      } else {
        removeCookie("refreshToken");
      }
    }
  }, [session, status, router]);

  return null; // This component doesn't render anything
};
