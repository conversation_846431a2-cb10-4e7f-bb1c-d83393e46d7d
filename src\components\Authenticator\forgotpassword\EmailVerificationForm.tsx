"use client";

import { FormError } from "@/components/FormStatus/Form-error";
import { useForgotPasswordMutation } from "@/lib/redux/slices/auth/authApi";
import { forgotEmailSchema } from "@/lib/utils/validation";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { useEmailVerificationErrorReporting } from "@/app/auth/hooks/useEmailVerificationErrorReporting";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type FormFields = z.infer<typeof forgotEmailSchema>;

interface TEmailVerificationFormProps {
  onNext: (arg1: string) => void;
  handleToast?: (message: string, type: 'success' | 'error') => void;
}

function EmailVerificationForm({ onNext, handleToast }: TEmailVerificationFormProps) {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<FormFields>({
    resolver: zodResolver(forgotEmailSchema),
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [forgotPassword] = useForgotPasswordMutation();

  const email = watch("email");
  const isFormFilled = email;

  // Use the custom hook for error reporting
  const { reportApiError } = useEmailVerificationErrorReporting(errors);

  const onSubmit: SubmitHandler<FormFields> = async (data) => {
    setIsSubmitting(true);
    setError(null);
    try {
      const response = await forgotPassword({
        email: data.email,
        method: "link",
      }).unwrap();
      if (response.message) {
        onNext(data.email);
      }
    } catch (error: any) {
      const errorMessage = error?.data?.error_message;
      setError(errorMessage);
      reportApiError(errorMessage);
      if (handleToast) {
        handleToast(errorMessage, 'error');
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
      <div>
        <Input
          id="email"
          type="email"
          placeholder="Email"
          className="h-12 "
          {...register("email", {
            onChange: (e) => {
              e.target.value = e.target.value.toLowerCase();
            },
          })}
        />
        {errors?.email?.message && (
          <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
        )}
      </div>
      
      {error && <FormError message={error} />}
      
      <Button
        type="submit"
        disabled={isSubmitting || !isFormFilled}
        className="w-full h-12 text-base font-medium"
        loading={isSubmitting}
      >
        {isSubmitting ? "Sending..." : "Continue"}
      </Button>
    </form>
  );
}

export default EmailVerificationForm;
