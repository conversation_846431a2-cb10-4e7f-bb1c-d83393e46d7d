"use client";

import React, { useMemo, useState } from "react";
import { ResetPasswordModal } from "./ResetPasswordModal";
import VerificationForm from "./VerificationForm";
import EmailVerificationForm from "./EmailVerificationForm";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { TOASTS } from "@/lib/utils/constants";
import { Button } from "@nextui-org/react";
import { FaArrowLeft } from "react-icons/fa";

const FORGOT_FORM_MODE = {
  EMAIL: "EMAIL",
  VERIFICATION: "VERIFICATION",
  RESET_PASSWORD: "RESET_PASSWORD",
};

export default function ForgotPasswordState({
  setTab,
}: {
  setTab: (tab: string) => void;
}) {
  const [formMode, setFormMode] = useState<string>(FORGOT_FORM_MODE.EMAIL);
  const isEmailActive = useMemo(
    () => formMode === FORGOT_FORM_MODE.EMAIL,
    [formMode]
  );

  const isVerificationActive = useMemo(
    () => formMode === FORGOT_FORM_MODE.VERIFICATION,
    [formMode]
  );
  const isResetPasswordActive = useMemo(
    () => formMode === FORGOT_FORM_MODE.RESET_PASSWORD,
    [formMode]
  );
  const router = useRouter();
  const [email, setEmail] = useState("");

  function onChangeState(state: string) {
    setFormMode(state);
  }

  return (
    <>
      {isEmailActive && (
        <EmailVerificationForm
          onNext={(value) => {
            setEmail(value);
            onChangeState(FORGOT_FORM_MODE.VERIFICATION);
          }}
        />
      )}
      {isVerificationActive && (
        <VerificationForm
          email={email}
          onNext={() => onChangeState(FORGOT_FORM_MODE.RESET_PASSWORD)}
        />
      )}
      {isResetPasswordActive && (
        <ResetPasswordModal
          email={email}
          onNext={() => {
            toast.success(TOASTS.PASSWORD_RESET_SUCCESS);
            window.location.reload();
          }}
        />
      )}
      <Button
        className="bg-transparent w-full mt-4"
        startContent={<FaArrowLeft size={10} />}
        onPress={() => setTab("signIn")}
      >
        Back to Sign In
      </Button>
    </>
  );
}
