"use client";

import { FormError } from "@/components/FormStatus/Form-error";
import { useResetPasswordMutation } from "@/lib/redux/slices/auth/authApi";
import { IResponseProps } from "@/lib/types";
import { resetPasswordSchema } from "@/lib/utils/validation";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { useResetPasswordErrorReporting } from "@/app/auth/hooks/useResetPasswordErrorReporting";
import { AUTH_ERRORS } from "@/app/auth/constants/errorMessages";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface TResetPasswordProps {
  email: string;
  uidb64: string;
  token: string;
  onNext: () => void;
  handleToast?: (message: string, type: 'success' | 'error') => void;
}

type FormFields = z.infer<typeof resetPasswordSchema>;

export function ResetPasswordModal({ email, uidb64, token, onNext, handleToast }: TResetPasswordProps) {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<FormFields>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const [resetPassword] = useResetPasswordMutation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const newPassword = watch("newPassword");
  const confirmPassword = watch("confirmPassword");
  const isFormFilled = newPassword && confirmPassword;

  // Use the custom hook for error reporting
  const { reportApiError } = useResetPasswordErrorReporting(errors);

  const onSubmit: SubmitHandler<FormFields> = async (data) => {
    setIsSubmitting(true);
    setError(null);
    try {
      const payload = {
        password: data.newPassword,
        uidb64: uidb64,
        token: token,
      };
      
      const response: IResponseProps = await resetPassword(payload).unwrap();
      if (response?.message) {
        onNext();
      }
    } catch (error: any) {
      const errorMessage = error?.data?.error_message || AUTH_ERRORS.PASSWORD_RESET_ERROR;
      setError(errorMessage);
      reportApiError(errorMessage);
      if (handleToast) {
        handleToast(errorMessage, 'error');
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
      {/* <div>
        <Input
          id="email"
          type="email"
          value={email}
          disabled
          className="h-12  bg-gray-100 mb-3"
        />
      </div> */}
      
      <div>
        <Input
          id="newPassword"
          type="password"
          placeholder="New password"
          className="h-12 "
          {...register("newPassword")}
        />
        <p className="text-xs text-gray-500 mt-1">
          Min. 8 characters with uppercase, lowercase & special character
        </p>
        {errors?.newPassword?.message && (
          <p className="text-sm text-red-500 mt-1">{errors.newPassword.message}</p>
        )}
      </div>
      
      <div>
        <Input
          id="confirmPassword"
          type="password"
          placeholder="Confirm password"
          className="h-12 "
          {...register("confirmPassword")}
        />
        {errors?.confirmPassword?.message && (
          <p className="text-sm text-red-500 mt-1">{errors.confirmPassword.message}</p>
        )}
      </div>
      
      {error && <FormError message={error} />}
      
      <Button
        type="submit"
        disabled={isSubmitting || !isFormFilled}
        className="w-full h-12  text-base font-medium"
        loading={isSubmitting}
      >
        {isSubmitting ? "Updating..." : "Reset password"}
      </Button>
    </form>
  );
}
