"use client";

import { FormError } from "@/components/FormStatus/Form-error";
import { useVerifyOtpMutation } from "@/lib/redux/slices/auth/authApi";
import { verficationSchema } from "@/lib/utils/validation";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { IResponseProps } from "@/lib/types";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type FormFields = z.infer<typeof verficationSchema>;

interface TVerificationFormProps {
  email: string;
  onNext: () => void;
}

export default function VerificationForm({
  email,
  onNext,
}: TVerificationFormProps) {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<FormFields>({
    resolver: zodResolver(verficationSchema),
  });

  const [verifyOtp] = useVerifyOtpMutation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const verificationCode = watch("verifyOTP");
  const isFormFilled = verificationCode;

  const onSubmit: SubmitHandler<FormFields> = async (data) => {
    setIsSubmitting(true);
    setError(null);
    try {
      const response: IResponseProps = await verifyOtp({
        code: data.verifyOTP,
        email,
      }).unwrap();
      if (response?.message) {
        onNext();
      }
    } catch (error: any) {
      setError(error?.data?.error_message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
      <div>
        <Input
          id="email"
          type="email"
          value={email}
          disabled
          className="h-12  mb-3"
        />
      </div>
      
      <div>
        <Input
          id="verifyOTP"
          type="text"
          placeholder="Verification code"
          className="h-12"
          {...register("verifyOTP")}
        />
        <p className="text-xs text-gray-500 mt-1">We sent a code to your email</p>
        {errors?.verifyOTP?.message && (
          <p className="text-sm text-red-500 mt-1">{errors.verifyOTP.message}</p>
        )}
      </div>
      
      {error && <FormError message={error} />}
      
      <Button
        type="submit"
        disabled={isSubmitting || !isFormFilled}
        className="w-full h-12 text-base font-medium"
        loading={isSubmitting}
      >
        {isSubmitting ? "Verifying..." : "Continue"}
      </Button>
    </form>
  );
}
