import React from "react";

export interface BaseLabelProps
  extends React.LabelHTMLAttributes<HTMLLabelElement> {
  className?: string;
}

const BaseLabel: React.FC<BaseLabelProps> = ({
  children,
  className = "",
  ...props
}) => {
  const combinedClassName = `block text-[13px] text-[#303030] font-medium mb-1 ${className}`;

  return (
    <label className={combinedClassName} {...props}>
      {children}
    </label>
  );
};

export default BaseLabel;
