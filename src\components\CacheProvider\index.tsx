"use client";

import { createContext, useContext, useEffect, ReactNode } from "react";
import { CacheManager } from "@/lib/utils/cacheManager";

interface CacheContextType {
  clearCache: () => Promise<void>;
  diagnoseCache: () => Promise<Record<string, any>>;
}

const CacheContext = createContext<CacheContextType | undefined>(undefined);

export function CacheProvider({ children }: { children: ReactNode }) {
  // In development mode, provide a no-op context
  if (process.env.NODE_ENV !== "production") {
    const devContextValue: CacheContextType = {
      clearCache: async () => {}, // No-op in development
      diagnoseCache: async () => ({}), // Return empty object in development
    };

    return (
      <CacheContext.Provider value={devContextValue}>
        {children}
      </CacheContext.Provider>
    );
  }

  useEffect(() => {
    CacheManager.initialize();
    CacheManager.watchForErrors();
  }, []);

  const value = {
    clearCache: CacheManager.clearAllCaches.bind(CacheManager),
    diagnoseCache: CacheManager.diagnoseCache.bind(CacheManager),
  };

  return (
    <CacheContext.Provider value={value}>{children}</CacheContext.Provider>
  );
}

export const useCache = () => {
  const context = useContext(CacheContext);
  if (!context) {
    throw new Error("useCache must be used within a CacheProvider");
  }
  return context;
};
