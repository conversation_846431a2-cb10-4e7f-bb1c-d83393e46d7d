"use client";
import { Card, CardFooter } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import Image from "next/image";

interface CategoryCardProps {
  title: string;
  imgSrc: string;
}
const CategoryCard: React.FC<CategoryCardProps> = ({ title, imgSrc }) => {
  const router = useRouter();
  const handleClick = () => {
    router.push(`/events/list?category=${title.toLocaleLowerCase()}`);
  };
  return (
    <Card className="w-full h-full cursor-pointer shadow-none border-1.5">
      <div onClick={handleClick} className="w-full h-full">
        <Image
          alt={`AutoLNK ${title}`}
          className="z-0 w-full object-cover aspect-video"
          src={imgSrc}
          width={150}
          quality={100}
          height={150}
          loading="lazy"
        />
        <CardFooter className="absolute bottom-0 left-0 right-0">
          <h4 className="text-white ">{title}</h4>
        </CardFooter>
      </div>
    </Card>
  );
};

export default CategoryCard;
