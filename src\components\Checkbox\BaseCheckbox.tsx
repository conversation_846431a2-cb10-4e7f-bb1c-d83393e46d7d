import React from "react";
import { Checkbox, CheckboxProps } from "@nextui-org/react";

export interface BaseCheckboxProps extends Omit<CheckboxProps, "css"> {
  label: string;
  helperText?: string;
  className?: string;
  checkboxClassName?: string;
}

const BaseCheckbox: React.FC<BaseCheckboxProps> = ({
  label,
  helperText,
  className = "",
  checkboxClassName = "",
  ...props
}) => {
  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      <Checkbox
        {...props}
        classNames={{
          base: `inline-flex max-w-md items-center ${checkboxClassName}`,
          label: "text-sm text-gray-700 dark:text-gray-300",
        }}
      >
        {label}
      </Checkbox>
      {helperText && (
        <p className="text-xs text-gray-500 dark:text-gray-400 ml-6">
          {helperText}
        </p>
      )}
    </div>
  );
};

export default BaseCheckbox;
