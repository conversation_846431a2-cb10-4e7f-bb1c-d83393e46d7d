import React from "react";
import { Controller } from "react-hook-form";
import { CheckboxGroup, Checkbox } from "@nextui-org/react";
import BaseLabel from "../BaseLabel";

interface FormControlledCheckboxProps {
  control: any;
  name: string;
  label: string;
  options: Array<{ label: string; value: string }>;
  isRequired?: boolean;
  classNames?: {
    base?: string;
    wrapper?: string;
    label?: string;
  };
  labelClassName?: string;
}

export default function FormControlledCheckbox({
  control,
  name,
  label,
  options,
  isRequired,
  classNames,
  labelClassName = "",
}: FormControlledCheckboxProps) {
  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: isRequired ? "This field is required" : false,
      }}
      render={({ field: { onChange, value = [] }, fieldState }) => (
        <div>
          {label && (
            <BaseLabel className={labelClassName}>
              {label} {isRequired && "*"}
            </BaseLabel>
          )}
          <CheckboxGroup
            value={value}
            onChange={onChange}
            orientation="vertical"
            isRequired={isRequired}
            classNames={classNames}
            isInvalid={fieldState.invalid}
            errorMessage={fieldState.error?.message}
          >
            {options.map((option) => (
              <Checkbox key={option.value} value={option.value}>
                {option.label}
              </Checkbox>
            ))}
          </CheckboxGroup>
        </div>
      )}
    />
  );
}
