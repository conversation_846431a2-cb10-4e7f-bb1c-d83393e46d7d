import React, { ButtonHTMLAttributes } from "react";
import { twMerge } from "tailwind-merge";

interface HeavyShadowedButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  bgColor?: string;
  textColor?: string;
  hoverColor?: string;
  shadowColor?: string;
}

export default function HeavyShadowedButton({
  children,
  className = "",
  bgColor = "bg-[#EABC07]",
  textColor = "text-black",
  hoverColor = "hover:bg-yellow-500",
  onClick,
  ...props
}: HeavyShadowedButtonProps) {
  const baseClasses = `
    relative
    text-[3.2vw]
    md:text-[15px]
    font-bold
    py-[9px]
    px-[17px]
    rounded-lg
    border-1
    border-[#AB9526]
    active:border-b-2
    active:border-r-2
    active:translate-y-[2px]
    active:translate-x-[2px]
    transition-all
    duration-150
    focus:outline-none
    focus:ring-2
    focus:ring-yellow-600
    focus:ring-opacity-50
  `;

  const mergedClasses = twMerge(
    baseClasses,
    bgColor,
    textColor,
    hoverColor,
    className
  );

  return (
    <button
      className={mergedClasses}
      style={{
        boxShadow:
          "5px 5px 0px #AB9526, 1px 1px 0px #AB9526, 3px 3px 0px #AB9526",
      }}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
}
