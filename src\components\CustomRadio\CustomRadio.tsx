import React from 'react';
import { useRadio, VisuallyHidden, cn } from "@nextui-org/react";

export const CustomRadio = (props) => {
  const {
    Component,
    children,
    description,
    getBaseProps,
    getInputProps,
    getLabelProps,
    getLabelWrapperProps,
  } = useRadio(props);

  return (
    <Component
      {...getBaseProps()}
      className={cn(
        "group flex flex-col items-center justify-between hover:opacity-80 active:opacity-60 w-20 h-20 sm:w-24 sm:h-20 cursor-pointer border rounded-lg p-1 m-1 tap-highlight-transparent",
        "data-[selected=true]:border-primary bg-[#EDEDED]"
      )}
    >
      <VisuallyHidden>
        <input {...getInputProps()} />
      </VisuallyHidden>
      <div {...getLabelWrapperProps()} className="flex flex-col items-center justify-center">
        {children && <span {...getLabelProps()}>{children}</span>}
        {description && (
          <span className="text-sm font-semibold text-foreground text-center opacity-90">
            {description}
          </span>
        )}
      </div>
    </Component>
  );
};