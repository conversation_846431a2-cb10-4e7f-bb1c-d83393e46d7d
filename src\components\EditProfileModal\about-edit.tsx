import React, { useState } from "react";
import RichTextEditor from "./text-editor";
import { FormHeader } from "./header";
import { FormFooter } from "./footer";
import { useSessionData } from "@/lib/hooks/useSession";
import { EDIT_STATUS } from "@/lib/utils/constants";

export function AboutEdit({ setCurrentEditStatus, bio, onSave, isLoading }) {
  const { update } = useSessionData();
  const [richTextContent, setRichTextContent] = useState("");

  function goBack() {
    setCurrentEditStatus(EDIT_STATUS.DEFAULT);
  }

  async function handleSave() {
    await onSave({ description: richTextContent });
    update();
    goBack();
  }
  return (
    <div className="flex flex-col gap-y-1 sm:gap-y-3">
      <FormHeader title="About" goBack={goBack} />
      <div className="h-[65vh] overflow-y-auto sm:overflow-hidden">
        <RichTextEditor
          initialText={bio}
          setRichTextContent={setRichTextContent}
        />
      </div>
      <FormFooter onSave={handleSave} goBack={goBack} isLoading={isLoading} />
    </div>
  );
}
