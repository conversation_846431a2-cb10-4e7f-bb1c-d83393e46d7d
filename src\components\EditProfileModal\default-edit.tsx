import { Image } from "@nextui-org/react";
import React, { useEffect, useState } from "react";
import parse from "html-react-parser";
import { useSessionData } from "@/lib/hooks/useSession";
import { EDIT_STATUS } from "@/lib/utils/constants";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

interface DefaultEditProps {
  setCurrentEditStatus: (status: string) => void; // or the type of `status` based on your use case
  data: {
    coverPhoto: string;
    profilePhoto: string;
    bio: string;
  };
}

export function DefaultEdit({ setCurrentEditStatus, data }: DefaultEditProps) {
  const { data: sessionData } = useSessionData();
  const [avatarSrc, setAvatarSrc] = useState("");
  const [coverSrc, setCoverSrc] = useState({ src: "", isError: false });

  useEffect(() => {
    if (sessionData) {
      setAvatarSrc(data.profilePhoto);
      setCoverSrc({
        src: data.coverPhoto,
        isError: false,
      });
    }
  }, [sessionData]);

  const handleImageError = () => {
    setAvatarSrc(IMAGE_LINKS.IMAGE_PLACEHOLDER); // Fallback to placeholder image
  };

  const handleCoverImageError = () => {
    setCoverSrc({ src: IMAGE_LINKS.IMAGE_PLACEHOLDER, isError: true }); // Fallback to placeholder image
  };
  function handleEdit(status) {
    setCurrentEditStatus(status);
  }
  return (
    <>
      <div className="flex flex-col gap-y-2 pb-2 border-b-2 border-b-gray-50">
        <div className="flex justify-between">
          <h2 className="font-semibold text-medium text-gray-600">
            Profile picture
          </h2>
          <p
            className="font-medium text-blue-500 cursor-pointer"
            onClick={() => handleEdit(EDIT_STATUS.PROFILE_PICTURE)}
          >
            Edit
          </p>
        </div>
        <div className="flex items-center justify-center">
          <div className="w-20 sm:w-24 md:w-36">
            <Image
              src={avatarSrc}
              alt="Autolnk avatar photo"
              onError={handleImageError}
              className="rounded-full h-20 sm:h-24 md:h-36 w-24 md:w-36 object-cover"
            />
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-y-2">
        <div className="flex justify-between">
          <h2 className="font-semibold text-medium text-gray-600">
            Cover photo
          </h2>
          <p
            className="font-medium text-blue-500 cursor-pointer"
            onClick={() => handleEdit(EDIT_STATUS.COVER_PHOTO)}
          >
            Edit
          </p>
        </div>
        <div className="flex items-center justify-center">
          <Image
            src={coverSrc.src}
            onError={handleCoverImageError}
            className="rounded-2xl h-40 w-96 object-cover"
            width="100%"
            alt="Autolnk Cover Photo"
          />
        </div>
      </div>
      <div className="flex flex-col gap-y-2">
        <div className="flex justify-between">
          <h2 className="font-semibold text-medium text-gray-600">About</h2>
          <p
            className="font-medium text-blue-500 cursor-pointer"
            onClick={() => handleEdit(EDIT_STATUS.ABOUT)}
          >
            Edit
          </p>
        </div>
        <div className="flex items-center justify-center">
          <div className="h-48 overflow-y-auto bg-gray-100 w-full rounded-lg p-2">
            <p className="text-[#86868B] font-sfPro">
              {parse(data.bio || "Add your organization description...")}
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
