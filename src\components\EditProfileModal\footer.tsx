import { But<PERSON>, <PERSON><PERSON>Footer } from "@nextui-org/react";
import React from "react";

type FormFooterProps = {
  onSave: () => void; // Function type that takes no arguments and returns void
  goBack: () => void; // Function type that takes no arguments and returns void
  isLoading?: boolean; // Optional boolean for loading state
};
export function FormFooter({ onSave, goBack, isLoading }: FormFooterProps) {
  return (
    <ModalFooter>
      <Button color="primary" onPress={onSave} isLoading={isLoading}>
        {isLoading ? "Saving..." : "Save"}
      </Button>
    </ModalFooter>
  );
}
