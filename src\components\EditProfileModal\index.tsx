"use client";
import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DefaultEdit } from "./default-edit";
import { AboutEdit } from "./about-edit";
import { EditCoverPhoto } from "./cover-edit";
import { EditProfilePhoto } from "./profile-edit";
import { EDIT_STATUS } from "@/lib/utils/constants";

interface EditProfileModalProps {
  title: string;
  data: {
    coverPhoto: string;
    profilePhoto: string;
    bio: string;
  };
  controller: {
    onCoverPhotoSave: (arg1: any) => void;
    onProfilePhotoSave: (arg1: any) => void;
    onBioSave: (arg1: any) => void;
  };
  isLoading?: boolean;
}

export const EditProfileModal: React.FC<EditProfileModalProps> = ({
  title,
  data,
  controller,
  isLoading = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentEditStatus, setCurrentEditStatus] = useState(
    EDIT_STATUS.DEFAULT
  );

  function handleEdit() {
    setIsOpen(true);
  }

  function onCloseHandle() {
    setCurrentEditStatus(EDIT_STATUS.DEFAULT);
    setIsOpen(false);
    window.location.reload();
  }

  const EditRenderer = useMemo(() => {
    switch (currentEditStatus) {
      case EDIT_STATUS.DEFAULT:
        return (
          <DefaultEdit
            setCurrentEditStatus={setCurrentEditStatus}
            data={data}
          />
        );
      case EDIT_STATUS.COVER_PHOTO:
        return (
          <EditCoverPhoto
            setCurrentEditStatus={setCurrentEditStatus}
            coverPhoto={data.coverPhoto}
            onSave={controller.onCoverPhotoSave}
            isLoading={isLoading}
          />
        );
      case EDIT_STATUS.ABOUT:
        return (
          <AboutEdit
            setCurrentEditStatus={setCurrentEditStatus}
            bio={data.bio}
            onSave={controller.onBioSave}
            isLoading={isLoading}
          />
        );
      case EDIT_STATUS.PROFILE_PICTURE:
        return (
          <EditProfilePhoto
            setCurrentEditStatus={setCurrentEditStatus}
            profilePhoto={data.profilePhoto}
            onSave={controller.onProfilePhotoSave}
            isLoading={isLoading}
          />
        );
      default:
        return null;
    }
  }, [currentEditStatus, data, controller, isLoading]);

  return (
    <>
      <Button
        onClick={handleEdit}
        variant="outline"
        size="sm"
        className="mr-1 md:mr-3 md:mt-1 text-[15px] w-[95px] font-medium bg-[#EFEFEF]"
      >
        {title}
      </Button>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) onCloseHandle();
          setIsOpen(open);
        }}
      >
        <DialogContent className="max-w-[600px] md:max-h-[85vh] md:min-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-center">Edit Page</DialogTitle>
          </DialogHeader>
          <div className="md:min-h-[80vh] overflow-y-auto">{EditRenderer}</div>
        </DialogContent>
      </Dialog>
    </>
  );
};
