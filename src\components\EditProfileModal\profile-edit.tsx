import React, { useEffect, useRef, useState, useCallback } from "react";
import { FormHeader } from "./header";
import { Button, Image, Slider } from "@nextui-org/react";
import { FormFooter } from "./footer";
import Cropper from "react-easy-crop";
import { useSessionData } from "@/lib/hooks/useSession";
import { EDIT_STATUS } from "@/lib/utils/constants";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { useImageUpload } from "@/components/ImageSelector/useImageUpload";
import { processImageFile } from "@/lib/utils/heicConverter";
import toast from "react-hot-toast";

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export function EditProfilePhoto({
  setCurrentEditStatus,
  profilePhoto,
  onSave,
  isLoading,
}) {
  const { data: sessionData, update } = useSessionData();
  const fileRef = useRef<HTMLInputElement>(null);

  const [previewImage, setPreviewImage] = useState<string>("");
  const [image, setImage] = useState<File | null>(null);
  const [showCropper, setShowCropper] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<CropArea | null>(
    null
  );

  const { handleImageSelect, isUploading } = useImageUpload({
    fileSize: 9.5 * 1024 * 1024,
    onSuccess: async (url) => {
      if (url) {
        await onSave({ avatar: url });
        update();
        goBack();
      }
    },
  });

  useEffect(() => {
    if (sessionData) {
      setPreviewImage(profilePhoto);
    }
  }, [sessionData]);

  function goBack() {
    setCurrentEditStatus(EDIT_STATUS.DEFAULT);
    setShowCropper(false);
  }

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0) {
      toast.error("No file selected");
      return;
    }

    const file = files[0];
    if (!file) {
      toast.error("Invalid file selected");
      return;
    }

    try {
      // Process HEIC files first (convert to JPEG if needed)
      const processedFile = await processImageFile(file);
      setImage(processedFile);
      const imageUrl = URL.createObjectURL(processedFile);
      setPreviewImage(imageUrl);
      setShowCropper(true);
      // Reset controls when new image is selected
      setZoom(1);
    } catch (error) {
      console.error("Error processing HEIC file:", error);
      toast.error("Failed to process image. Please try a different format.");
    }
  };

  const handleFileUpload = () => {
    if (fileRef.current) {
      fileRef.current.click();
    }
  };

  const onCropComplete = useCallback(
    (croppedArea: any, croppedAreaPixels: CropArea) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const createImage = (url: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
      const image = document.createElement("img");
      image.addEventListener("load", () => resolve(image));
      image.addEventListener("error", (error) => reject(error));
      image.setAttribute("crossOrigin", "anonymous");
      image.src = url;
    });

  const getCroppedImage = async (
    imageSrc: string,
    pixelCrop: CropArea
  ): Promise<Blob> => {
    const image = await createImage(imageSrc);
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      throw new Error("No 2d context");
    }

    // Set the canvas size to the cropped size
    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    // Calculate scaling
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    // Draw the cropped image
    ctx.drawImage(
      image,
      pixelCrop.x * scaleX,
      pixelCrop.y * scaleY,
      pixelCrop.width * scaleX,
      pixelCrop.height * scaleY,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height
    );

    return new Promise((resolve) => {
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            throw new Error("Canvas is empty");
          }
          resolve(blob);
        },
        "image/jpeg",
        1
      );
    });
  };

  async function handleSave() {
    try {
      if (!croppedAreaPixels || !previewImage) return;

      const croppedImageBlob = await getCroppedImage(
        previewImage,
        croppedAreaPixels
      );

      const croppedImageFile = new File(
        [croppedImageBlob],
        "cropped-profile.jpg",
        {
          type: "image/jpeg",
        }
      );

      await handleImageSelect(croppedImageFile);
    } catch (err) {
      console.log(err);
    }
  }

  const handleProfileImageError = () => {
    setPreviewImage(IMAGE_LINKS.IMAGE_PLACEHOLDER);
  };

  const handleCancelCrop = () => {
    // Reset preview image to the original profile photo
    setPreviewImage(profilePhoto);
    setShowCropper(false);
    // Clean up the object URL to prevent memory leaks
    if (image) {
      URL.revokeObjectURL(previewImage);
    }
    setImage(null);
  };

  return (
    <div className="flex h-[80vh] flex-col justify-between">
      <div className="flex flex-col gap-y-3">
        <FormHeader title="Profile Photo" goBack={goBack} />

        {showCropper ? (
          <div className="flex flex-col items-center justify-center gap-4">
            <div className="relative w-full h-[300px]">
              <Cropper
                image={previewImage}
                crop={crop}
                zoom={zoom}
                aspect={1}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={onCropComplete}
                cropShape="round"
                showGrid={false}
              />
            </div>

            <div className="flex gap-2">
              <Button color="danger" variant="light" onPress={handleCancelCrop}>
                Cancel
              </Button>
              <Button
                color="primary"
                onPress={handleSave}
                isLoading={isLoading || isUploading}
              >
                Save Changes
              </Button>
            </div>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-center">
              <div className="w-20 sm:w-24 md:w-36">
                <Image
                  src={previewImage}
                  alt="Autolnk Profile photo preview"
                  className="rounded-full h-20 sm:h-24 md:h-36 w-24 md:w-36 object-cover"
                  onError={handleProfileImageError}
                />
              </div>
            </div>
            <input
              ref={fileRef}
              type="file"
              accept=".png,.jpg,.jpeg,.webp,.heic,.heif"
              onChange={handleFileChange}
              className="hidden"
            />
            <div className="items-center flex justify-center">
              <Button onPress={handleFileUpload}>Change</Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
