import React, { useState } from "react";
import dynamic from "next/dynamic";
import "react-quill/dist/quill.snow.css"; // Import Quill styles

const QuillEditor = dynamic(() => import("react-quill"), { ssr: false });

type RichTextEditorProps = {
  setRichTextContent: (content: string) => void; // Function that takes a string and returns void
  initialText?: string; // Optional string for initial text
};

export default function RichTextEditor({
  setRichTextContent,
  initialText = "",
}: RichTextEditorProps) {
  const [content, setContent] = useState(initialText);

  const quillModules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike", "blockquote"],
      [{ list: "ordered" }, { list: "bullet" }],
      ["link", "image"],
      [{ align: [] }],
      [{ color: [] }],
      ["code-block"],
      ["clean"],
    ],
  };

  const quillFormats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "blockquote",
    "list",
    "bullet",
    "link",
    "image",
    "align",
    "color",
    "code-block",
  ];

  const handleEditorChange = (newContent) => {
    setContent(newContent);
    setRichTextContent(newContent);
  };

  return (
    <main>
      <div className="flex items-center flex-col h-full sm:h-[65vh] mb-3">
        <QuillEditor
          value={content}
          onChange={handleEditorChange}
          modules={quillModules}
          formats={quillFormats}
          className="w-full h-[60vh] mt-1 bg-white"
        />
      </div>
    </main>
  );
}
