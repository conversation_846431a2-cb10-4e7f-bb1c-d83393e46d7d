"use client";

import { memo, useState, useCallback, useRef, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON>, CardFooter, CardHeader, Skeleton } from "@nextui-org/react";
import { dateToFormattedString } from "@/lib/utils/date";
import { trackEventClick } from "@/lib/utils/pixelTracking";
import { useSessionData } from "@/lib/hooks/useSession";
import { getName } from "@/lib/utils/string";
import TagBadge from "@/app/events/components/TagBadge";

interface EventCardProps {
  title: string;
  imgSrc?: string;
  date: string;
  city: string;
  state: string;
  href: string;
  eventId: string;
  timezone: string;
  tag?: string;
  lazy?: boolean;
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

const EventCard = memo(function EventCard({
  eventId,
  title,
  imgSrc,
  date,
  city,
  state,
  href,
  timezone,
  tag,
  lazy = false,
  pixels,
}: EventCardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [shouldLoadImage, setShouldLoadImage] = useState(!lazy);
  const { data: session } = useSessionData();
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!lazy || shouldLoadImage) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setShouldLoadImage(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: "50px",
        threshold: 0.1,
      }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, shouldLoadImage]);

  const handleClick = useCallback(() => {
    if (!pixels?.meta || !pixels?.ga4 || !pixels?.snap || !pixels?.tiktok)
      return;

    trackEventClick(
      {
        id: eventId,
        title,
        date,
        pixels,
      },
      {
        id: session?.user?.id,
        email: session?.user?.email,
        name: getName(session?.user),
        isAnonymous: !session?.user,
      }
    );
  }, [pixels, eventId, title, date, session]);

  const formattedDate = dateToFormattedString(date, timezone);
  const location = `${city}, ${state}`;

  return (
    <Link href={href} onClick={handleClick} className="focus:outline-none">
      <div className="relative" ref={cardRef}>
        <TagBadge
          tag={tag}
          className="top-[-10px] right-[-5px] text-[10px] sm:text-sm"
          iconClassName="w-[10px] h-[12px] sm:w-[13px] sm:h-[17px] font-medium"
        />
        <Card className="bg-transparent shadow-none w-[calc(100vw/2.8)] sm:w-[calc(100vw/2.8)] md:w-[calc(100vw/4)] lg:w-[calc(100vw/5.8)] xl:w-[calc(100vw/5.5)] 2xl:w-[335px] hover:opacity-90 transition-opacity">
          <div className="relative w-full h-[calc(100vw/2.8)] sm:h-[calc(100vw/2.8)] md:h-[calc(100vw/4)] lg:h-[calc(100vw/5.8)] xl:h-[calc(100vw/5.5)] 2xl:h-[335px]">
            {imgSrc ? (
              <>
                {(!shouldLoadImage || isLoading) && (
                  <Skeleton className="absolute inset-0 rounded-lg" />
                )}
                {shouldLoadImage && (
                  <Image
                    alt={title}
                    src={imgSrc}
                    fill
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                    className="rounded-lg object-cover"
                    onLoad={() => setIsLoading(false)}
                    priority={!lazy}
                    loading={lazy ? "lazy" : "eager"}
                    quality={75}
                  />
                )}
              </>
            ) : (
              <div className="w-full h-full bg-gray-300 rounded-lg flex items-center justify-center">
                <p className="text-gray-600 font-bold text-[12px] sm:text-[14px] md:text-[16px] lg:text-[16px] xl:text-[18px] 2xl:text-[20px]">
                  Image not available
                </p>
              </div>
            )}
          </div>
          <CardHeader className="px-0 pt-2 pb-0">
            <h4 className="font-bold text-[12px] sm:text-sm md:text-[16px] lg:text-lg xl:text-xl 2xl:text-[25px] 2xl:leading-7 md:leading-5 leading-4">
              {title}
            </h4>
          </CardHeader>
          <CardFooter className="px-0 py-2 lg:py-1 xl:py-3">
            <p className="text-[#929292] text-[11px] leading-[15px] sm:text-[12px] sm:leading-[16px] md:text-[12px] md:leading-[16px] lg:text-[12px] lg:leading-[16px] xl:text-[17px] xl:leading-[22px] 2xl:text-[20px] 2xl:leading-[26px] tracking-wide font-normal">
              {formattedDate} · {location}
            </p>
          </CardFooter>
        </Card>
      </div>
    </Link>
  );
});

export default EventCard;
