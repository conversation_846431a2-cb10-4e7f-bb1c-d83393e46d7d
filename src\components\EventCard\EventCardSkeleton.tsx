import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Skeleton } from "@nextui-org/react";

const EventCardSkeleton = () => (
  <Card className="bg-transparent shadow-none w-[calc(100vw/2.8)] sm:w-[calc(100vw/2.8)] md:w-[calc(100vw/4)] lg:w-[calc(100vw/5.8)] xl:w-[calc(100vw/5.5)] 2xl:w-[335px]">
    <Skeleton className="w-full h-[calc(100vw/2.8)] sm:h-[calc(100vw/2.8)] md:h-[calc(100vw/4)] lg:h-[calc(100vw/5.8)] xl:h-[calc(100vw/5.5)] 2xl:h-[335px] rounded-lg" />
    <CardHeader className="px-0 pt-2 pb-0">
      <Skeleton className="w-3/4 h-6" />
    </CardHeader>
    <CardFooter className="px-0 py-2 lg:py-1 xl:py-3">
      <Skeleton className="w-2/3 h-4" />
    </CardFooter>
  </Card>
);

export default EventCardSkeleton;
