import React, { useCallback, RefObject, useEffect, useRef } from "react";
import { useDropzone } from "react-dropzone";
import { Button, Image, Spinner } from "@nextui-org/react";
import FileIcon from "@/assets/fileIcon";

interface BaseFileSelectorProps {
  label: string;
  onFileSelect: (files: File[]) => void;
  onRemoveFile: (index: number) => void;
  className?: string;
  files: File[];
  fileUrls?: string[];
  fileInputRef: RefObject<HTMLInputElement>;
  error?: string;
  isRequired?: boolean;
  isLoading?: boolean;
  maxFiles?: number;
  acceptedFileTypes?: Record<string, string[]>;
}

const BaseFileSelector: React.FC<BaseFileSelectorProps> = ({
  label,
  onFileSelect,
  onRemoveFile,
  className = "",
  files = [],
  fileUrls = [],
  fileInputRef,
  error,
  isRequired = false,
  isLoading = false,
  maxFiles = 1,
  acceptedFileTypes,
}) => {
  const objectUrlsRef = useRef<Map<number, string>>(new Map());

  // Clean up all object URLs on unmount
  useEffect(() => {
    return () => {
      objectUrlsRef.current.forEach((url) => {
        URL.revokeObjectURL(url);
      });
      objectUrlsRef.current.clear();
    };
  }, []);

  // Clean up object URLs when files change
  useEffect(() => {
    // Clean up URLs for files that no longer exist
    const currentFileIndexes = new Set(files.map((_, index) => index));
    objectUrlsRef.current.forEach((url, index) => {
      if (!currentFileIndexes.has(index)) {
        URL.revokeObjectURL(url);
        objectUrlsRef.current.delete(index);
      }
    });
  }, [files]);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles?.length > 0 && onFileSelect) {
        onFileSelect(acceptedFiles);
      }
    },
    [onFileSelect]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes || undefined,
    multiple: maxFiles > 1,
    maxFiles: maxFiles || 1,
  });

  const handleFileSelect = () => {
    fileInputRef?.current?.click();
  };

  const getFilePreview = (file: File, index: number) => {
    if (!file) return null;

    let fileUrl: string | undefined = fileUrls?.[index];

    if (!fileUrl && file) {
      // Create object URL for local files
      const existingUrl = objectUrlsRef.current.get(index);
      if (existingUrl) {
        fileUrl = existingUrl;
      } else {
        try {
          const newUrl = URL.createObjectURL(file);
          objectUrlsRef.current.set(index, newUrl);
          fileUrl = newUrl;
        } catch (error) {
          console.error("Error creating object URL:", error);
          fileUrl = undefined;
        }
      }
    }

    const isImage = file.type?.startsWith("image/") || false;
    const fileName = file.name || `File ${index + 1}`;
    const fileSize = file.size || 0;

    const handleRemoveFile = (e: React.MouseEvent) => {
      e?.stopPropagation();
      if (!isLoading && onRemoveFile) {
        // Clean up object URL before removing
        const existingUrl = objectUrlsRef.current.get(index);
        if (existingUrl) {
          URL.revokeObjectURL(existingUrl);
          objectUrlsRef.current.delete(index);
        }
        onRemoveFile(index);
      }
    };

    return (
      <div key={`file-${index}-${fileName}`} className="mb-3 relative">
        <div className="w-full h-[120px] flex flex-col justify-center items-center border-1 border-dashed rounded-lg p-4">
          <div className="w-full h-full flex justify-center items-center relative">
            {isImage && fileUrl ? (
              <Image
                src={fileUrl}
                alt={`File ${index + 1}`}
                className="max-h-[90px] object-cover"
                fallbackSrc="/placeholder-image.png"
              />
            ) : (
              <div className="flex flex-col items-center justify-center">
                <FileIcon width={24} height={24} color="#6C7280" />
                <div className="mt-2 flex flex-col items-center justify-center">
                  <p
                    className="text-sm font-medium ml-2 w-[100px] truncate"
                    title={fileName}
                  >
                    {fileName}
                  </p>
                  <p className="text-xs text-gray-500 ml-2">
                    {formatFileSize(fileSize)}
                  </p>
                </div>
              </div>
            )}
            <button
              type="button"
              onClick={handleRemoveFile}
              className="absolute top-[-8px] right-[-8px] w-6 h-6 flex items-center justify-center bg-white rounded-full shadow-sm"
              aria-label={`Remove ${fileName}`}
              disabled={isLoading}
            >
              {isLoading ? (
                <Spinner size="sm" />
              ) : (
                <Image
                  src="/delete-icon.svg"
                  alt="remove file"
                  width={16}
                  height={16}
                  fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDRMNCA3TDggOEw0IDEyIiBzdHJva2U9IiM2QzcyODAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo="
                />
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const formatFileSize = (bytes: number): string => {
    if (!bytes || bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Safe check for showing files
  const hasFiles = files?.length > 0 || fileUrls?.length > 0;
  const totalFiles = Math.max(files?.length || 0, fileUrls?.length || 0);
  const canAddMore = totalFiles < (maxFiles || 1);

  const handleUrlRemove = (index: number) => (e: React.MouseEvent) => {
    e?.stopPropagation();
    if (!isLoading && onRemoveFile) {
      onRemoveFile(index);
    }
  };

  return (
    <div className={`w-full relative ${className}`}>
      <div className="mt-1">
        {hasFiles && (
          <div>
            {files?.map((file, index) =>
              file ? getFilePreview(file, index) : null
            )}
            {fileUrls?.map((url, index) => {
              if (!url || files?.[index]) return null; // Skip if file exists or URL is invalid

              // Try to determine file type from URL extension
              const getFileTypeFromUrl = (url: string) => {
                const extension = url.split(".").pop()?.toLowerCase();
                if (
                  ["jpg", "jpeg", "png", "gif", "webp", "svg"].includes(
                    extension || ""
                  )
                ) {
                  return "image";
                }
                return "document";
              };

              const fileType = getFileTypeFromUrl(url);
              const isImage = fileType === "image";
              const fileName = url.split("/").pop() || `File ${index + 1}`;

              return (
                <div key={`url-${index}`} className="mb-3 relative">
                  <div className="w-full h-[120px] flex flex-col justify-center items-center border-1 border-dashed rounded-lg p-4">
                    <div className="w-full h-full flex justify-center items-center relative">
                      {isImage ? (
                        <Image
                          src={url}
                          alt={`File ${index + 1}`}
                          className="max-h-[90px] object-cover"
                          fallbackSrc="/placeholder-image.png"
                        />
                      ) : (
                        <div className="flex flex-col items-center justify-center">
                          <FileIcon width={24} height={24} color="#6C7280" />
                          <div className="mt-2 flex flex-col items-center justify-center">
                            <p
                              className="text-sm font-medium ml-2 w-[100px] truncate"
                              title={fileName}
                            >
                              {fileName}
                            </p>
                          </div>
                        </div>
                      )}
                      <button
                        type="button"
                        onClick={handleUrlRemove(index)}
                        className="absolute top-[-8px] right-[-8px] w-6 h-6 flex items-center justify-center bg-white rounded-full shadow-sm"
                        aria-label="Remove file"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <Spinner size="sm" />
                        ) : (
                          <Image
                            src="/delete-icon.svg"
                            alt="remove file"
                            width={16}
                            height={16}
                            fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDRMNCA3TDggOEw0IDEyIiBzdHJva2U9IiM2QzcyODAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo="
                          />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {canAddMore && (
          <div
            {...getRootProps()}
            className={`h-[120px] flex flex-col justify-center items-center border-1 border-dashed rounded-lg p-4 text-center cursor-pointer
            ${isDragActive ? "border-blue-500 bg-blue-50" : ""}
            ${error ? "border-[#F31260]" : "border-[#8A8A8A]"}`}
            role="button"
            aria-label={`File upload area for ${label || "files"}`}
          >
            <input
              {...getInputProps()}
              ref={fileInputRef}
              multiple={maxFiles > 1}
              aria-label="File input"
            />
            <div className="text-center">
              <Button
                onPress={handleFileSelect}
                className="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-3 min-w-16 h-8 text-tiny gap-2 rounded-small [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none text-default-foreground data-[hover=true]:opacity-hover bg-white border-1 border-[#e3e3e3] shadow-sm"
                size="sm"
                variant="flat"
                isDisabled={isLoading}
                isLoading={isLoading}
              >
                Add File
              </Button>
              {maxFiles > 1 && (
                <p className="text-xs text-gray-400 mt-1">
                  {totalFiles}/{maxFiles} files
                </p>
              )}
            </div>
            {isDragActive && (
              <p className="mt-2 text-sm text-gray-600" role="status">
                Drop the files here ...
              </p>
            )}
          </div>
        )}
      </div>
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
};

export default BaseFileSelector;
