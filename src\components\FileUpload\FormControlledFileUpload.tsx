import React, { useRef, useEffect, useState } from "react";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import BaseFileSelector from "./BaseFileSelector";
import { useFileUpload } from "./useFileUpload";

interface FormControlledFileUploadProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>;
  control: Control<TFieldValues>;
  label?: string;
  className?: string;
  isRequired?: boolean;
  fileSize?: number;
  maxFiles?: number;
  acceptedFileTypes?: string[];
  allowSpecificTypes?: boolean;
  ticketId?: string;
  setIsFormLoading?: (isFormLoading: boolean) => void;
}

function FormControlledFileUpload<TFieldValues extends FieldValues>({
  name,
  control,
  label = "",
  className,
  isRequired,
  fileSize = 10 * 1024 * 1024, // Default 10MB
  maxFiles = 1,
  acceptedFileTypes = ["image"],
  allowSpecificTypes = false,
  ticketId,
  setIsFormLoading,
}: FormControlledFileUploadProps<TFieldValues>) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewFiles, setPreviewFiles] = useState<File[]>([]);
  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        const {
          handleFileSelect,
          handleRemoveFile,
          isUploading,
          getAcceptedMimeTypes,
        } = useFileUpload({
          fileSize,
          maxFiles,
          acceptedFileTypes,
          setIsFormLoading,
          onSuccess: (urls) => {
            if (onChange) {
              onChange(urls && urls.length > 0 ? urls[0] : null);
            }
          },
        });

        // Handle initial value and updates with better type safety
        useEffect(() => {
          try {
            if (value) {
              if (typeof value === "string" && value.trim()) {
                setUploadedUrls([value]);
                setPreviewFiles([]);
              } else if (
                value &&
                typeof value === "object" &&
                "name" in value &&
                "size" in value &&
                "type" in value
              ) {
                setPreviewFiles([value as File]);
                setUploadedUrls([]);
              } else {
                // Invalid value type, reset both arrays
                setPreviewFiles([]);
                setUploadedUrls([]);
              }
            } else {
              setPreviewFiles([]);
              setUploadedUrls([]);
            }
          } catch (error) {
            console.error("Error processing form value:", error);
            setPreviewFiles([]);
            setUploadedUrls([]);
          }
        }, [value]);

        const acceptedTypes =
          allowSpecificTypes && getAcceptedMimeTypes
            ? getAcceptedMimeTypes(acceptedFileTypes)
            : undefined;

        const safeHandleFileSelect = (files: File[]) => {
          if (files && Array.isArray(files) && files.length > 0) {
            handleFileSelect(files);
          }
        };

        const safeHandleRemoveFile = (index: number) => {
          if (typeof index === "number" && index >= 0) {
            handleRemoveFile(index);
          }
        };

        return (
          <div className={className}>
            <BaseFileSelector
              label={label || "File"}
              onFileSelect={safeHandleFileSelect}
              onRemoveFile={safeHandleRemoveFile}
              files={previewFiles}
              fileUrls={uploadedUrls}
              fileInputRef={fileInputRef}
              error={error?.message}
              isRequired={isRequired}
              isLoading={isUploading}
              maxFiles={1}
              acceptedFileTypes={acceptedTypes}
            />
          </div>
        );
      }}
    />
  );
}

export default FormControlledFileUpload;
