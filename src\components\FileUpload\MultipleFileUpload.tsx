import React, { useEffect, useState } from "react";
import {
  Control,
  FieldValues,
  FieldPath,
  Path,
  useWatch,
} from "react-hook-form";
import FormControlledFileUpload from "./FormControlledFileUpload";

interface MultipleFileUploadProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>;
  control: Control<TFieldValues>;
  label: string;
  className?: string;
  isRequired?: boolean;
  fileSize?: number;
  maxFiles: number;
  acceptedFileTypes?: string[];
  allowSpecificTypes?: boolean;
  ticketId?: string;
  setIsFormLoading?: (isFormLoading: boolean) => void;
}

function MultipleFileUpload<TFieldValues extends FieldValues>({
  name,
  control,
  label,
  className,
  isRequired,
  fileSize,
  maxFiles,
  acceptedFileTypes,
  allowSpecificTypes,
  ticketId,
  setIsFormLoading,
}: MultipleFileUploadProps<TFieldValues>) {
  const [inputCount, setInputCount] = useState(maxFiles);
  const value = useWatch({ control, name });

  useEffect(() => {
    if (Array.isArray(value) && value.length > 0) {
      setInputCount(Math.max(value.length, maxFiles));
    }
  }, [value, maxFiles]);

  // Generate array based on current input count
  const fileInputs = Array.from({ length: inputCount }, (_, index) => index);

  return (
    <div className={className}>
      <div className="text-sm font-medium text-gray-700 mb-2">
        {label} {isRequired && "*"}
      </div>
      <div className="grid grid-cols-2 gap-3">
        {fileInputs.map((index) => (
          <FormControlledFileUpload
            key={`${name}.${index}`}
            control={control}
            name={`${String(name)}[${index}]` as Path<TFieldValues>}
            isRequired={index === 0 && isRequired}
            fileSize={fileSize}
            maxFiles={1}
            acceptedFileTypes={acceptedFileTypes}
            allowSpecificTypes={allowSpecificTypes}
            ticketId={ticketId}
            setIsFormLoading={setIsFormLoading}
          />
        ))}
      </div>
    </div>
  );
}

export default MultipleFileUpload;
