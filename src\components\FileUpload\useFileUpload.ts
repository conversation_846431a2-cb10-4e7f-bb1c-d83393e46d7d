import { useState, useRef, useCallback, useEffect } from "react";
import { useImageUploadMutation } from "@/lib/redux/slices/cart/cartApi";
import { compressImage } from "@/lib/utils/imageCompression";
import { formatBytes } from "../ImageSelector/helper";
import { sanitizeFilename } from "@/lib/utils/fileUtils";
import { processImageFile } from "@/lib/utils/heicConverter";
import toast from "react-hot-toast";

// Global upload tracker to manage upload state across components
const globalUploadTracker = {
  activeUploads: 0,
  listeners: new Set<(isUploading: boolean) => void>(),
  
  startUpload() {
    this.activeUploads++;
    this.notifyListeners();
  },
  
  finishUpload() {
    if (this.activeUploads > 0) {
      this.activeUploads--;
      this.notifyListeners();
    }
  },
  
  notifyListeners() {
    const isUploading = this.activeUploads > 0;
    this.listeners.forEach(listener => listener(isUploading));
  },
  
  subscribe(listener: (isUploading: boolean) => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  },
  
  getIsUploading() {
    return this.activeUploads > 0;
  }
};

interface FileUploadResponse {
  url: string;
  contentType: string;
  path: string;
  cloudfrontUrl: string;
  headers: {
    contentDisposition: any;
    contentType: any;
  };
}

interface UseFileUploadProps {
  fileSize?: number;
  maxFiles?: number;
  onSuccess?: (urls: string[] | null) => void;
  acceptedFileTypes?: string[];
  setIsFormLoading?: (isFormLoading: boolean) => void;
}

export const useFileUpload = ({
  fileSize = 10 * 1024 * 1024, // Default 10MB
  maxFiles = 1,
  onSuccess,
  acceptedFileTypes = ["image"],
  setIsFormLoading,
}: UseFileUploadProps = {}) => {
  const [imageUpload] = useImageUploadMutation();
  const [isUploading, setIsUploading] = useState(false);
  const [previewFiles, setPreviewFiles] = useState<File[]>([]);
  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);
  
  // Synchronize global upload state with form loading state
  useEffect(() => {
    if (!setIsFormLoading) return;
    
    // Initial state sync
    setIsFormLoading(globalUploadTracker.getIsUploading());
    
    // Subscribe to global upload state changes
    const unsubscribe = globalUploadTracker.subscribe((isUploading) => {
      setIsFormLoading(isUploading);
    });
    
    // Cleanup subscription
    return unsubscribe;
  }, [setIsFormLoading]);

  const getAcceptedMimeTypes = (types: string[]): Record<string, string[]> => {
    // If acceptedFileTypes is empty or contains no valid types, accept any file
    if (!types || types.length === 0) {
      return {}; // Empty object means accept all file types
    }

    const mimeTypeMap: Record<string, string[]> = {
      image: ["image/*", "image/heic", "image/heif"],
      pdf: ["application/pdf"],
      document: [
        "application/msword", 
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.oasis.opendocument.text",
        "text/plain"
      ],
      spreadsheet: [
        "application/vnd.ms-excel", 
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.oasis.opendocument.spreadsheet"
      ],
      video: ["video/*"],
      audio: ["audio/*"],
      presentation: [
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "application/vnd.oasis.opendocument.presentation"
      ],
      drawing: [
        "image/vnd.adobe.photoshop",
        "application/illustrator",
        "image/svg+xml"
      ]
    };

    const acceptedTypes: Record<string, string[]> = {};
    
    types.forEach(type => {
      if (mimeTypeMap[type]) {
        mimeTypeMap[type].forEach(mimeType => {
          acceptedTypes[mimeType] = [];
        });
      }
    });

    return Object.keys(acceptedTypes).length ? acceptedTypes : {}; // Return empty object to allow all types if no valid types specified
  };

  const verifyUrlWithRetry = async (
    url: string,
    maxRetries = 3
  ): Promise<boolean> => {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const response = await fetch(url, { method: "HEAD" });
        if (response.ok) {
          return true;
        }
        // If not last attempt, wait before retrying
        if (attempt < maxRetries - 1) {
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, attempt) * 1000)
          );
        }
      } catch (error) {
        console.error(
          `URL verification attempt ${attempt + 1} failed:`,
          error
        );
        if (attempt < maxRetries - 1) {
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, attempt) * 1000)
          );
        }
      }
    }
    return false;
  };

  const uploadToAWS = async (file: File, response: FileUploadResponse) => {
    try {
      // Read file as binary data
      const fileData = await new Promise<ArrayBuffer>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as ArrayBuffer);
        reader.onerror = () => reject(reader.error);
        reader.readAsArrayBuffer(file);
      });

      const config = {
        method: 'PUT',
        body: fileData,
        headers: {
          'Content-Type': response.headers?.contentType,
          'Content-Disposition': response.headers?.contentDisposition,
        },
      };

      const uploadResponse = await fetch(response.url, config);

      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload to AWS: ${uploadResponse.statusText}`);
      }

      return uploadResponse;
    } catch (error) {
      console.error("Error uploading to AWS:", error);
      throw error;
    }
  };

  const uploadWithRetry = async (file: File, maxRetries = 2) => {
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const sanitizedFilename = sanitizeFilename(file);
        const response = (await imageUpload({
          filename: sanitizedFilename,
          type: file.type,
        }).unwrap()) as FileUploadResponse;

        if (!response.url || !response.cloudfrontUrl) {
          throw new Error("Invalid upload URL response");
        }

        await uploadToAWS(file, response);
        
        // Verify the CloudFront URL is accessible before returning it
        const isUrlValid = await verifyUrlWithRetry(response.cloudfrontUrl);
        if (!isUrlValid) {
          throw new Error("File URL is not accessible after multiple attempts");
        }
        
        return response.cloudfrontUrl;
      } catch (error) {
        console.error(`Upload attempt ${attempt + 1} failed:`, error);
        lastError = error;
        
        if (attempt < maxRetries) {
          // Wait for a short time before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }
    
    // If all retries failed, throw the last error
    throw lastError;
  };

  const processFile = async (file: File) => {
    if (!file) {
      toast.error("No file provided");
      return null;
    }

    try {
      // Process HEIC/HEIF files first (convert to JPEG if needed)
      let processedFile = file;
      if (file.type.startsWith('image/')) {
        try {
          processedFile = await processImageFile(file);
        } catch (error) {
          console.error("Error processing HEIC/HEIF file:", error);
          toast.error("Failed to process image. Please try a different format.");
          return null;
        }
      }

      // For images, try to compress if they are too large
      if (processedFile.type.startsWith('image/') && processedFile.size > fileSize) {
        try {
          const compressedFile = await compressImage(processedFile);
          if (compressedFile.size > fileSize) {
            toast.error(
              `Unable to compress image below ${formatBytes(fileSize)}. Please select a smaller image.`
            );
            return null;
          }
          return compressedFile;
        } catch (error) {
          console.error("Error compressing image:", error);
          toast.error(
            `File is too large. Please select an image smaller than ${formatBytes(fileSize)}.`
          );
          return null;
        }
      }
      
      // For non-image files, just check the size
      if (processedFile.size > fileSize) {
        toast.error(
          `File is too large. Please select a file smaller than ${formatBytes(fileSize)}.`
        );
        return null;
      }
      
      return processedFile;
    } catch (error) {
      console.error("Error processing file:", error);
      toast.error("Error processing file. Please try again.");
      return null;
    }
  };

  const handleFileSelect = async (files: File[]) => {
    if (!files || !Array.isArray(files) || files.length === 0) return;
    
    // Start tracking upload globally
    globalUploadTracker.startUpload();
    
    // Limit the number of files
    const filesToProcess = files.slice(0, maxFiles - (previewFiles?.length || 0));
    
    if (filesToProcess.length === 0) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      globalUploadTracker.finishUpload();
      return;
    }

    setIsUploading(true);
    const newUrls: string[] = [...(uploadedUrls || [])];
    const newFiles: File[] = [...(previewFiles || [])];
    
    try {
      for (const file of filesToProcess) {
        if (!file) continue; // Skip null/undefined files
        
        const processedFile = await processFile(file);
        if (!processedFile) continue;
        
        try {
          const cloudfrontUrl = await uploadWithRetry(processedFile);
          if (cloudfrontUrl && typeof cloudfrontUrl === "string") {
            newUrls.push(cloudfrontUrl);
            newFiles.push(processedFile);
          }
        } catch (error) {
          console.error("Error uploading file after retries:", error);
          toast.error(`Failed to upload ${file.name || "file"} after multiple attempts. Please try again later.`);
        }
      }
      
      setPreviewFiles(newFiles);
      setUploadedUrls(newUrls);
      onSuccess?.(newUrls.length > 0 ? newUrls : null);
      
      if (newUrls.length > (uploadedUrls?.length || 0)) {
        toast.success("Files uploaded successfully");
      }
    } catch (error) {
      console.error("Error processing files:", error);
      toast.error("Error processing files. Please try again.");
    } finally {
      setIsUploading(false);
      // Finish tracking upload globally
      globalUploadTracker.finishUpload();
    }
  };

  const handleRemoveFile = (index: number) => {
    if (typeof index !== "number" || index < 0) return;
    
    const newFiles = [...(previewFiles || [])];
    const newUrls = [...(uploadedUrls || [])];
    
    if (index < newFiles.length) {
      newFiles.splice(index, 1);
    }
    if (index < newUrls.length) {
      newUrls.splice(index, 1);
    }
    
    setPreviewFiles(newFiles);
    setUploadedUrls(newUrls);
    onSuccess?.(newUrls.length > 0 ? newUrls : null);
    
    toast.success("File removed successfully");
  };

  const handleRemoveAllFiles = () => {
    setPreviewFiles([]);
    setUploadedUrls([]);
    onSuccess?.(null);
    toast.success("All files removed successfully");
  };

  return {
    handleFileSelect,
    handleRemoveFile,
    handleRemoveAllFiles,
    isUploading,
    previewFiles,
    uploadedUrls,
    maxFiles,
    getAcceptedMimeTypes,
    acceptedFileTypes,
    setUploadedUrls,
  };
}; 