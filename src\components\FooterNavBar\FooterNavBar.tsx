import Link from "next/link";
import React from "react";
import Image from "next/image";
import { FOOTER_LINKS, SOCIAL_LINKS } from "@/lib/utils/constants";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

// Custom Facebook Icon Component
const FacebookIcon = ({ size = 20, color = "currentColor" }: { size?: number; color?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 320 512" 
    width={size} 
    height={size}
    fill={color}
  >
    <path d="M80 299.3V512H196V299.3h86.5l18-97.8H196V166.9c0-51.7 20.3-71.5 72.7-71.5c16.3 0 29.4 .4 37 1.2V7.9C291.4 4 256.4 0 236.2 0C129.3 0 80 50.5 80 159.4v42.1H14v97.8H80z"/>
  </svg>
);

// Custom Twitter Icon Component
const TwitterIcon = ({ size = 20, color = "currentColor" }: { size?: number; color?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 512 512" 
    width={size} 
    height={size}
    fill={color}
  >
    <path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"/>
  </svg>
);

// Custom LinkedIn Icon Component
const LinkedInIcon = ({ size = 20, color = "currentColor" }: { size?: number; color?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 448 512" 
    width={size} 
    height={size}
    fill={color}
  >
    <path d="M100.3 448H7.4V148.9h92.9zM53.8 108.1C24.1 108.1 0 83.5 0 53.8a53.8 53.8 0 0 1 107.6 0c0 29.7-24.1 54.3-53.8 54.3zM447.9 448h-92.7V302.4c0-34.7-.7-79.2-48.3-79.2-48.3 0-55.7 37.7-55.7 76.7V448h-92.8V148.9h89.1v40.8h1.3c12.4-23.5 42.7-48.3 87.9-48.3 94 0 111.3 61.9 111.3 142.3V448z"/>
  </svg>
);

// Custom Instagram Icon Component
const InstagramIcon = ({ size = 20, color = "currentColor" }: { size?: number; color?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 448 512" 
    width={size} 
    height={size}
    fill={color}
  >
    <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/>
  </svg>
);

const FooterNavBar = () => {
  const navLinks = [
    {
      title: "Pages",
      items: [
        { label: "Events ", href: "/events" },
        { label: "Community", href: "/community" },
        { label: "Company", href: "/about" },
        { label: "Search", href: "/" },
      ],
    },
    {
      title: "Events",
      items: [
        { label: "Meets", href: "/events/list?category=meet" },
        { label: "Shows", href: "/events/list?category=show" },
        { label: "Drives", href: "/events/list?category=drives" },
        { label: "Motorsports", href: "/events/list?category=motorsports" },
      ],
    },
    {
      title: "Policies",
      items: [
        { label: "Privacy Policy", href: FOOTER_LINKS.PRIVACY_POLICY },
        { label: "Terms of Service", href: FOOTER_LINKS.TERMS_OF_SERVICE },
        // { label: "Sales and Refunds", href: FOOTER_LINKS.SALES_AND_REFUNDS },
      ],
    },
  ];
  return (
    <footer className=" w-full z-50 bg-white">
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-6 gap-8 pt-8 px-6">
        <div className="space-y-6 col-span-2 flex flex-col items-center md:block">
          <Image
            src={IMAGE_LINKS.LOGO_TRANSPARENT}
            alt="AutoLNK"
            width={120}
            height={40}
          />
          <p className="text-sm text-gray-600 mt-8 mb-4 text-nowrap">
            Download the app by clicking the link below :
          </p>
          <div className="flex flex-row gap-2">
            <Link
              href="https://apps.apple.com/in/app/autolnk/id6478376890"
              target="_blank"
              className="w-36"
            >
              <Image
                src={IMAGE_LINKS.DOWNLOAD_ON_APPSTORE}
                alt="AutoLNK Download on App Store"
                width={135}
                height={40}
                className="h-10 w-auto object-contain"
                style={{
                  imageRendering: "auto",
                  shapeRendering: "geometricPrecision",
                }}
                priority
              />
            </Link>
            <Link
              href="https://play.google.com/store/apps/details?id=com.xcelerate.xcelerate"
              target="_blank"
              className="w-36"
            >
              <Image
                src={IMAGE_LINKS.DOWNLOAD_ON_GOOGLE}
                alt="Autolnk Download on Google Play"
                width={135}
                height={40}
                className="h-10 w-auto object-contain"
                style={{
                  imageRendering: "auto",
                  shapeRendering: "geometricPrecision",
                }}
                priority
              />
            </Link>
          </div>
        </div>

        {navLinks.map((link, index) => (
          <div key={index}>
            <h3 className=" block text-[#0E0F1D] font-bold text-lg leading-[21px] hover:text-gray-900">
              {link.title}
            </h3>
            <div className="space-y-2 flex flex-col gap-y-2 mt-8">
              {link.items.map((item, idx) => (
                <Link
                  href={item.href}
                  key={idx}
                  target="_blank"
                  className="block text-sm font-normal leading-[16.4px] text-[#0E0F1D] hover:text-gray-800"
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
        ))}

        <div className="col-span-1 flex flex-col items-start text-left ml-2">
          <div>
            <h3 className="text-lg font-semibold">Social media</h3>
            <div className="flex gap-x-5 mt-6">
              <Link
                href={SOCIAL_LINKS.FACEBOOK}
                target="_blank"
                className="text-[#0E0F1D] hover:text-gray-900"
              >
                <FacebookIcon size={20} />
              </Link>
              <Link
                href={SOCIAL_LINKS.TWITTER}
                target="_blank"
                className="text-[#0E0F1D] hover:text-gray-900"
              >
                <TwitterIcon size={20} />
              </Link>
              <Link
                href={SOCIAL_LINKS.LINKEDIN}
                target="_blank"
                className="text-[#0E0F1D] hover:text-gray-900"
              >
                <LinkedInIcon size={20} />
              </Link>
              <Link
                href={SOCIAL_LINKS.INSTAGRAM}
                target="_blank"
                className="text-[#0E0F1D] hover:text-gray-900"
              >
                <InstagramIcon size={20} />
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className=" w-full flex items-center justify-center mx-auto mt-8 py-4 border-t border-gray-200">
        <div className="max-w-7xl w-full flex flex-col md:flex-row justify-between items-center px-6">
          <p className="text-sm text-gray-600">
            © 2025 AutoLNK | All rights reserved
          </p>
          <p className="text-sm text-gray-600">Designed by AutoLNK</p>
        </div>
      </div>
    </footer>
  );
};

export default FooterNavBar;
