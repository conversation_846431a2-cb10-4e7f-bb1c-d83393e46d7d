import { Controller } from "react-hook-form";
import BaseLabel from "../BaseLabel";

const FormField = ({ name, control, errors, render, label }) => (
  <div className="w-full flex flex-col  max-w-xs">
    <BaseLabel className="mb-1 capitalize">{label}</BaseLabel>
    <Controller
      name={name}
      control={control}
      render={({ field }) => render(field)}
    />
    {errors[name] && (
      <p className="text-red-500 text-xs mx-auto mt-3">
        {errors[name].message}
      </p>
    )}
  </div>
);

export default FormField;
