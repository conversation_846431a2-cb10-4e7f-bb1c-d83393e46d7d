import React, { useEffect } from "react";
import { FaExclamationTriangle } from "react-icons/fa";
import { getErrorMessage } from "./error-message";
import { reportError } from "@/lib/utils/sentryErrorLogs";

interface FormErrorProps {
  message: string;
}

export const FormError = ({
  message = "Invalid credentials",
}: FormErrorProps) => {
  const errorMessage = getErrorMessage(message);

  return (
    <div className="bg-red-200 p-3 rounded-md flex items-center gap-x-2 text-sm font-semibold text-red-500 border-2 border-red-300">
      <FaExclamationTriangle height={4} width={4} />
      <p>{errorMessage}</p>
    </div>
  );
};
