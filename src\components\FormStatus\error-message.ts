const errorMessages: Record<string, string> = {
  USER_DOES_NOT_EXIST: "This email or username does not exist.",
  AUTHENTICATION_FAILED_SIGN_IN: "The password you entered is incorrect.",
  INVALID_OTP_CODE_SIGN_IN: "The OTP code you entered is incorrect.",
  INVALID_OTP_CODE_SIGN_UP: "The OTP code you entered is incorrect.",
  USER_ALREADY_EXIST: "This user already exists.",
  // Global
  INSTANCE_NOT_CONFIGURED:
    "The system instance is not properly configured. Please contact support.",
  INVALID_EMAIL: "The email address provided is not valid.",
  EMAIL_REQUIRED: "An email address is required.",
  SIGNUP_DISABLED: "User registration is currently disabled.",
  OTP_LINK_LOGIN_DISABLED: "Login using OTP links is currently disabled.",
  PASSWORD_LOGIN_DISABLED: "Login using password is currently disabled.",
  USER_ACCOUNT_DEACTIVATED: "The user account has been deactivated.",
  USERNAME_ALREADY_EXIST: "This username is already taken.",
  EXPIRED_PASSWORD_TOKEN: "The password reset token has expired.",
  INVALID_PASSWORD_TOKEN: "The password reset token is invalid.",
  // Password strength
  INVALID_PASSWORD: "The password provided is invalid.",
  PASSWORD_NOT_STRONG:
    "The password is not strong enough. Please choose a stronger password.",
  SMTP_NOT_CONFIGURED: "SMTP is not configured for sending emails.",

  // Sign Up
  AUTHENTICATION_FAILED_SIGN_UP: "Authentication failed during sign-up.",
  REQUIRED_EMAIL_PASSWORD_SIGN_UP:
    "Email and password are required for sign-up.",
  INVALID_EMAIL_SIGN_UP: "The email address provided for sign-up is invalid.",
  INVALID_EMAIL_OTP_SIGN_UP: "The OTP for sign-up is invalid.",
  OTP_SIGN_UP_EMAIL_CODE_REQUIRED:
    "An OTP code is required to complete the sign-up process.",
  EMAIL_PASSWORD_AUTHENTICATION_DISABLED:
    "Email and password authentication is disabled for sign-up.",

  // Sign In
  REQUIRED_EMAIL_PASSWORD_SIGN_IN:
    "Email and password are required for sign-in.",
  VALUE_OR_PASSWORD_OR_FIELD_REQUIRED:
    "A required field is missing or incorrect.",
  INVALID_EMAIL_SIGN_IN: "The email address provided for sign-in is invalid.",
  INVALID_FIELD_TYPE_SIGN_IN: "The field type is invalid for sign-in.",
  INVALID_EMAIL_OTP_SIGN_IN: "The OTP for sign-in is invalid.",
  OTP_SIGN_IN_EMAIL_CODE_REQUIRED:
    "An OTP code is required to complete the sign-in process.",

  // Both Sign in and Sign up for OTP
  EXPIRED_OTP_CODE_SIGN_IN: "The OTP code for sign-in has expired.",
  EXPIRED_OTP_CODE_SIGN_UP: "The OTP code for sign-up has expired.",
  EMAIL_CODE_ATTEMPT_EXHAUSTED_SIGN_IN:
    "The maximum number of OTP attempts for sign-in has been reached.",
  EMAIL_CODE_ATTEMPT_EXHAUSTED_SIGN_UP:
    "The maximum number of OTP attempts for sign-up has been reached.",

  // Reset Password
  PASSWORD_REQUIRED: "A password is required for resetting.",
  EMAIL_OR_PHONE_OR_USERNAME_REQUIRED:
    "Email, phone, or username is required for password reset.",
  METHOD_REQUIRED: "A method for resetting the password is required.",
  CODE_REQUIRED: "A reset code is required.",

  // Change Password
  INCORRECT_OLD_PASSWORD: "The old password provided is incorrect.",
  MISSING_PASSWORD: "A new password is required.",
  INVALID_NEW_PASSWORD: "The new password provided is invalid.",

  // Set Password
  PASSWORD_ALREADY_SET: "A password has already been set for this account.",

  // Rate Limit
  RATE_LIMIT_EXCEEDED:
    "You have exceeded the rate limit. Please try again later.",

  // Unknown
  AUTHENTICATION_FAILED: "Authentication failed. Please try again.",
  DEFAULT: "An unknown error occurred. Please try again.", // Default error message
};

// Function to map error codes to messages
export function getErrorMessage(errorCode: string): string {
  // If errorCode exists, return its corresponding message, otherwise return the default message
  return errorMessages[errorCode] || errorCode;
}
