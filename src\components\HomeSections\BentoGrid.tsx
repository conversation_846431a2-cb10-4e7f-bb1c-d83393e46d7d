"use client";

import React, { useState } from "react";
import { VideoModal } from "../VideoModal/VideoModal";
import Image from "next/image";
import clsx from "clsx";
import { useMediaQuery } from "@/lib/hooks/useMediaQuery";
import { BASE_URL } from "./utils/constants";

const BentoGrid = () => {
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const handleWatchVideoClick = () => {
    setIsVideoModalOpen(true);
  };

  const handleCloseVideoModal = () => {
    setIsVideoModalOpen(false);
  };

  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <section className="w-full bg-white pt-[75px] pb-10">
      <div className="max-w-[1050px] mx-auto px-4">
        {/* Grid Container */}
        <div className="space-y-4 md:space-y-8">
          <div className="flex gap-6 md:gap-[30px] flex-col md:flex-row w-full ">
            {/* Vehicle Registration Card */}
            <div className="bg-gradient-to-br from-[#F6F8F8]  to-[#FBFCFC] rounded-[14px] p-8 py-[26px] relative  overflow-hidden w-full md:w-[41%] min-h-[390px] md:min-h-[405px]">
              <div className="relative z-10 mb-10">
                <h3 className="text-[23px]  font-semibold text-[#101010] mb-4 tracking-[0.4px]">
                  Vehicle registration
                </h3>
                <p className="text-[#6F7275] text-[20px] tracking-[1px] md:tracking-[0.5px] font-normal leading-[24px] w-[95%] md:w-[80%]">
                  Effortlessly bill tickets upon approval, eliminating payment
                  follow-ups.
                </p>
              </div>
              <div className="absolute md:bottom-1 bottom-0 right-0 ">
                <Image
                  width={1000}
                  height={1000}
                  quality={100}
                  priority={true}
                  src={`${BASE_URL}/landing-page/registration.png`}
                  alt="Automated Flyers"
                  className="h-full w-full object-cover object-left"
                />
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#F6F8F8] to-[#FBFCFC] rounded-[14px] p-8 py-[26px] relative overflow-hidden w-full md:w-[60%] min-h-[365px] md:min-h-[405px]">
              <div className="relative z-10 mb-10">
                <h3 className="text-[23px]  font-semibold text-[#101010] mb-4 tracking-[0.4px]">
                  Automated flyers
                </h3>
                <p className="text-[#6F7275] text-[20px] tracking-[1px] md:tracking-[0.5px] font-normal leading-[24px] w-full md:w-[80%]">
                  Stop making each vehicle flyer, we automated it{" "}
                  <br className="hidden md:block" />
                  to <strong>SAVE YOU TIME.</strong>
                  <br className="block md:hidden" />
                  <span
                    className="text-blue-500 text-[14px] font-medium md:ml-2 cursor-pointer hover:underline"
                    onClick={handleWatchVideoClick}
                  >
                    Watch video
                  </span>
                </p>
              </div>
              <div className="absolute md:bottom-0 bottom-[5px] right-0 ">
                <Image
                  width={1000}
                  height={1000}
                  quality={100}
                  priority={true}
                  src={`${BASE_URL}/landing-page/flyers.png`}
                  alt="Automated Flyers"
                  className="h-full w-full object-cover object-left"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-8 w-full ">
            <div className="bg-gradient-to-br from-[#F6F8F8] to-[#FBFCFC] rounded-[14px] p-8 py-[26px] relative  overflow-hidden w-full md:w-[58%] min-h-[385px] md:min-h-[430px]">
              <div className="relative z-10 mb-10">
                <h3 className="text-[23px]  font-semibold text-[#101010] mb-4 tracking-[0.4px]">
                  Mobile ticket delivery
                </h3>
                <p className="text-[#6F7275] text-[20px] tracking-[1px] md:tracking-[0.5px] font-normal leading-[24px] w-full md:w-[95%]">
                  Get tickets instantly, straight to your phone. No more digging
                  through your email.
                </p>
              </div>
              <div className={clsx("absolute bottom-0", isMobile && "left-0")}>
                <Image
                  width={1000}
                  height={1000}
                  quality={100}
                  priority={true}
                  src={`${BASE_URL}/landing-page/ticket.png`}
                  alt="Automated Flyers"
                  className="h-full w-full object-cover object-left"
                />
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#F6F8F8] to-[#FBFCFC] rounded-[14px] p-8 py-[26px] relative overflow-hidden w-full md:w-[42%] md:min-h-[430px] min-h-[390px]">
              <div className="relative z-10 mb-10">
                <h3 className="text-[23px]  font-semibold text-[#101010] mb-4 tracking-[0.4px]">
                  In-person payments
                </h3>
                <p className="text-[#6F7275] text-[20px] tracking-[1px] md:tracking-[0.5px] font-normal leading-[24px] w-full md:w-[80%]">
                  Tap to pay: Seamless in-person payments, anytime, anywhere.
                </p>
              </div>
              <div className="absolute md:bottom-0 bottom-[-20px] right-0">
                <Image
                  width={1000}
                  height={1000}
                  quality={100}
                  priority={true}
                  src={`${BASE_URL}/landing-page/payments.png`}
                  alt="Automated Flyers"
                  className="h-full w-full object-cover object-left"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-8 w-full ">
            <div className="bg-gradient-to-br from-[#F6F8F8] to-[#FBFCFC] rounded-[14px] p-8 py-[26px] relative  overflow-hidden w-full md:w-[50%] min-h-[380px] md:min-h-[425px]">
              <div className="relative z-10 mb-10">
                <h3 className="text-[23px]  font-semibold text-[#101010] mb-[18px] tracking-[0.4px] ">
                  Waivers
                </h3>
                <p className="text-[#6F7275] text-[20px] tracking-[1px] md:tracking-[0.5px] font-normal leading-[24px] w-full md:w-[80%]">
                  Sign waivers digitally with ease, fully backed by secure
                  standards, and ditch the paperwork.
                </p>
              </div>
              <div className="absolute bottom-0">
                <Image
                  width={1000}
                  height={1000}
                  quality={100}
                  priority={true}
                  src={`${BASE_URL}/landing-page/waivers.png`}
                  alt="Automated Flyers"
                  className="h-full w-full object-cover object-left"
                />
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#F6F8F8] to-[#FBFCFC] rounded-[14px] p-8 py-[26px] relative overflow-hidden w-full md:w-[50%] min-h-[385px] md:min-h-[432px]">
              <div className="relative z-10 mb-10">
                <div className="flex items-center gap-2.5 mb-[11px] md:mb-[18px]">
                  <span className="bg-[#F6001FF0] text-white text-[16.25px] md:text-[19.17px] font-bold px-[14px] py-[1.5px] rounded-[10.25px]">
                    LIVE
                  </span>
                  <h3 className="text-[23px]  font-semibold text-[#101010] m-0">
                    Awards
                  </h3>
                </div>
                <p className="text-[#6F7275] text-[20px] tracking-[1px] md:tracking-[0.5px] font-normal leading-[24px] w-full md:w-[85%]">
                  Announce winners in real-time, boosting attendee engagement by
                  showcasing which vehicle won.
                </p>
              </div>
              <div className="absolute bottom-0">
                <Image
                  width={1000}
                  height={1000}
                  quality={100}
                  priority={true}
                  src={`${BASE_URL}/landing-page/awards.png`}
                  alt="Automated Flyers"
                  className="h-full w-full object-cover object-left"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      <VideoModal
        isOpen={isVideoModalOpen}
        onClose={handleCloseVideoModal}
        videoSrc={`${BASE_URL}/landing-page/flyers-demo.mov`}
      />
    </section>
  );
};

export default BentoGrid;
