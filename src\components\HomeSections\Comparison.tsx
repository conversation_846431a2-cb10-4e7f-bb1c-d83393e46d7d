import { Check<PERSON><PERSON>, XIcon } from "lucide-react";
import React from "react";
import Image from "next/image";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { AUTO_LNK_ITEMS, EXISTING_PLATFORM_ITEMS } from "./utils/constants";

const Comparison = () => {
  return (
    <section className="w-full bg-white py-8 md:py-20 mb-20 md:mb-40">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12 md:mb-[72px]">
          <h2 className="text-[38px] leading-[43px] md:text-[45px] font-[600] text-black mb-6 tracking-[0.4px] md:tracking-[-0.1px] ">
            Don't let your current ticketing platform{" "}
            <span className="text-[#22C55E] whitespace-nowrap font-[700]">
              hold you back
            </span>
          </h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-11 max-w-[945px] mx-auto">
          {/* Left Column - Existing Platform */}
          <div className="space-y-5 rounded-[30px]  bg-[#FF01000A] px-[34px] py-[30px]">
            <h3 className="text-[23px] ml-1 md:ml-0 md:text-left md:text-[25px] font-[600] text-black mb-6 md:mb-8 tracking-[-0.2px]">
              Your Existing Ticketing platform
            </h3>
            {EXISTING_PLATFORM_ITEMS.map((item, index) => (
              <div key={index} className="flex items-start gap-2.5 mb-6">
                <div className="flex-shrink-0 mt-[3px]">
                  <XIcon className="text-[#FF0100]" strokeWidth={3.5}  />
                </div>
                <div className="flex flex-col gap-[2px]">
                  <h4 className="text-lg md:text-[21px] font-semibold text-black">
                    {item.title}
                  </h4>
                  <p className="text-[#77787b] font-inter text-[17px] md:text-[20px] font-[400] tracking-[-0.3px] leading-[23px] md:leading-[26px]">
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Right Column - AutoLNK */}
          <div className="space-y-5 rounded-[30px] bg-[#53D4000D] px-[34px] py-[30px]">
            <h3 className="text-[23px] ml-1 md:ml-0 md:text-left md:text-[25px] font-[600] text-black mb-6 md:mb-8 tracking-[-0.2px]">
              <span className="hidden md:inline">
                Your experience with AutoLNK
              </span>
              <span className="md:hidden flex items-center gap-1">
                Your experience with{" "}
                <Image
                  src={IMAGE_LINKS.LOGO_TRANSPARENT}
                  alt="AutoLNK Logo"
                  width={80}
                  height={24}
                  className="h-7 w-auto"
                />
              </span>
            </h3>
            {AUTO_LNK_ITEMS.map((item, index) => (
              <div key={index} className="flex items-start gap-2.5 mb-6">
                <div className="flex-shrink-0 mt-[3px]">
                  <CheckIcon className="text-[#53D400]" strokeWidth={4} />
                </div>
                <div className="flex flex-col gap-[2px]">
                  <h4 className="text-lg md:text-[21px] font-semibold text-black">
                    {item.title}
                  </h4>
                  <p className="text-[#77787b] font-inter text-[17px] md:text-[20px] font-[400] tracking-[-0.3px] leading-[23px] md:leading-[26px] pr-2.5">
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Comparison;
