import React from 'react';
import { FEATURES_DATA, FEATURES_DATA_MOBILE } from './utils/constants';

const Features = () => {
 
  return (
    <section className="w-full max-w-7xl mx-auto px-3 md:px-4 py-8 md:py-20">
      <div className="text-center mb-10 md:mb-[60px]">
        <h2 className="text-[38px] md:text-[55px] font-[600] text-black mb-5 tracking-[0.4px] md:tracking-[1px] leading-[43px] md:leading-[55px]">
          Everything you need, <span 
            className="bg-gradient-to-r from-[#2E80FA] from-0% via-[#2E80FA] via-[90%] to-white to-100% bg-clip-text text-transparent"
            style={{
              backgroundSize: '100% 100%',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            one single platform
          </span>
        </h2>
        <p className="block md:hidden text-[16px] text-[#6F7275] font-[400] w-[85%] leading-[23px] mx-auto tracking-[1.1px]">
        Don&apos;t let your business get left behind, it&apos;s time to say goodbye to outdated platforms
        </p>
        <p className="hidden md:block text-lg md:text-[20px] text-[#6F7275] font-[400] max-w-3xl mx-auto tracking-[0.8px]">
          AutoLNK works directly with event organizers to understand what features you need
        </p>
      </div>

      <div className="md:hidden grid grid-cols-2 gap-8 lg:gap-12 w-[85%] mx-auto">
        {FEATURES_DATA_MOBILE.map((column, columnIndex) => (
          <div key={columnIndex} className="space-y-4 mx-auto ">
            {column.map((feature, featureIndex) => (
              <div key={featureIndex} className="">
                <p className="text-[14px] font-medium text-black tracking-[0.7px]">
                  {feature}
                </p>
              </div>
            ))}
          </div>
        ))}
      </div>
      
      <div className="hidden md:grid grid-cols-3 gap-8 lg:gap-12 max-w-[1130px] mx-auto">
        {FEATURES_DATA.map((column, columnIndex) => (
          <div key={columnIndex} className="space-y-8 mx-auto ">
            {column.map((feature, featureIndex) => (
              <div key={featureIndex} className="">
                <p className="text-sm md:text-[21px] font-medium text-black tracking-[0.1px]">
                  {feature}
                </p>
              </div>
            ))}
          </div>
        ))}
      </div>
    </section>
  );
};

export default Features;

