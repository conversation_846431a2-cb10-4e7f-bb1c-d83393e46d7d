"use client";

import Link from "next/link";
import { useSessionData } from "@/lib/hooks/useSession";
import { HERO_TEXT_DESKTOP, HERO_TEXT_MOBILE } from "./utils/constants";
import { Button } from "../ui/button";
import TestimonialCarousel from "./TestimonialCarousel/TestimonialCarousel";
import { useMediaQuery } from "@/lib/hooks/useMediaQuery";
import { AUTH_STATUS } from "@/lib/utils/constants";
import { getButtonConfig } from "./utils/helpers";
import { Session } from "next-auth";

const getButtonStyle = (isMobile: boolean) => ({
  background: "#007FFF",
  backgroundImage: isMobile
    ? "none"
    : "radial-gradient(circle at bottom right, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 30%, transparent 70%)",
});

const BUTTON_BASE_CLASS = "rounded-[15px] text-[14px] md:text-[16px] font-[400] py-[20px] md:py-[23px] px-4 md:px-5 text-white";

export default function Hero() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const heroText = isMobile ? HERO_TEXT_MOBILE : HERO_TEXT_DESKTOP;
  const { data: session, status } = useSessionData();

  const renderButton = () => {
    const isLoading = status === AUTH_STATUS.LOADING;
    
    // Reserve space during loading to prevent UI shift
    if (isLoading) {
      return (
        <div style={{ opacity: 0, pointerEvents: 'none' }}>
          <Button
            className={BUTTON_BASE_CLASS}
            style={getButtonStyle(isMobile)}
          >
            Get Started - free
          </Button>
        </div>
      );
    }

    const { href, text } = getButtonConfig(session as Session);
    
    return (
      <div className="hero-button-entrance">
        <Link href={href}>
          <Button
            className={BUTTON_BASE_CLASS}
            style={getButtonStyle(isMobile)}
          >
            {text}
          </Button>
        </Link>
      </div>
    );
  };

  return (
    <div className="px-2 lg:px-8 max-w-full mx-auto">
      <h1 className="hero-title text-[46px] font-inter leading-[50px] md:leading-[60px] font-[700] tracking-[-1.8px] md:text-[70px] text-center md:font-[800] text-[#313131] mt-8 sm:mt-12 lg:mt-16">
        {heroText
          ?.split(" ")
          ?.slice(0, isMobile ? 2 : 3)
          .map((word, index) => (
            <span
              key={index}
              className="hero-word inline-block md:overflow-hidden lg:pb-2.5"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <span className="text-[#000000]">{word}&nbsp;</span>
            </span>
          ))}
        <span
          className="hero-word inline-block overflow-hidden lg:pb-2.5 bg-transparent mr-2 md:mr-0"
          style={{ animationDelay: `${(isMobile ? 2 : 3) * 0.2}s` }}
        >
          <span
            className="bg-[#4ACA63] md:bg-gradient-to-r md:from-[#4ACA63] md:from-0% md:via-[#4ACA63] md:via-[94%] md:to-[#d8ecdb] md:to-100% bg-clip-text text-transparent"
            style={{
              backgroundSize: "100% 100%",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            {heroText
              ?.split(" ")
              ?.slice(isMobile ? 2 : 3)
              .join(" ")}
          </span>
        </span>
      </h1>
      <p className="hero-subtitle w-[95%] md:w-[74%] font-inter text-[#6F7275] tracking-normal !font-[400] text-[18px] md:text-[25px] leading-[23px] md:leading-[30px] text-center mt-3.5 md:mt-6 mx-auto">
        Don&apos;t let your business get left behind, its{" "}
        <br className="block lg:hidden" /> time to say goodbye to outdated{" "}
        <br className="hidden lg:block" /> {isMobile ? "" : "automotive "}{" "}
        platforms
      </p>
      <div className="mt-9 flex items-center gap-4 justify-center">
        {renderButton()}
      </div>
      <div className="mt-9 sm:mt-10 md:mt-[52px] ">
        <TestimonialCarousel />
      </div>
    </div>
  );
}
