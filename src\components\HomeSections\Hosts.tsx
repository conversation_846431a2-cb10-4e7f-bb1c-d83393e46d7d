"use client"
import React from 'react';
import Image from 'next/image';
import { HOSTS_DETAILS } from './utils/constants';
import { useMediaQuery } from "@/lib/hooks/useMediaQuery";

const Hosts = () => {
const isMobile = useMediaQuery("(max-width: 768px)");
  return (
    <section className="w-full bg-[#1D1F26] py-8 md:py-20 my-7">
      <div className="max-w-[97%] mx-auto px-4">
        {/* Title */}
        <div className="text-center mb-10 md:mb-16">
        <h2 className="block md:hidden text-[29px] font-[600] text-white mb-4 leading-[40px] items-center justify-center gap-2 md:gap-3">
            These folks{' '}
            <span className="text-[#F7A403]">host events attendees love</span>
            {" "} with {" "}
            <Image 
              src="/logo-white.svg" 
              alt="AutoLNK" 
              width={48}
              height={48}
              loading="lazy"
              className="h-7 md:h-11 inline-block w-auto"
            />
          </h2>


          <h2 className="hidden text-[29px] font-inter md:text-[46px] font-bold text-white mb-4 tracking-[-1.3px] leading-[38px] md:flex flex-wrap items-center justify-center gap-2 md:gap-3">
            <span>These folks{' '}</span> <br className="block md:hidden" />
            <span className="text-[#F7A403]">host events attendees love</span>
            <span>with</span>
            <Image 
              src="/logo-white.svg" 
              alt="AutoLNK" 
              width={48}
              height={48}
              loading="lazy"
              className="h-6 md:h-11 inline-block w-auto"
            />
          </h2>
        </div>
        
        {/* Logo Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mx-auto">
          {HOSTS_DETAILS.map((host, index) => (
            <div 
              key={index} 
              className="flex items-center justify-center p-3 md:px-8 md:py-5 bg-[#232429] transition-colors duration-300 rounded-[5px] group"
            >
              <div className="w-full h-[68px] md:h-24 lg:h-28 flex items-center justify-center">
                <Image
                  src={host.logo}
                  alt={`${host.name} logo`}
                  width={isMobile ? host.mobileWidth : host.width}
                  height={isMobile ? host.mobileHeight : host.height}
                  className="object-contain"
                  loading="lazy"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Hosts;

