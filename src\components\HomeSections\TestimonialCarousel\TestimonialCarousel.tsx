import Image from "next/image";
import { MESSAGE_IMAGES } from "../utils/constants";

const TestimonialCarousel = () => {
  return (
    <div className="mx-auto py-2 md:max-w-7xl">
      <div
        className="flex overflow-hidden relative gap-6 p-2 group"
        style={{
          maskImage:
            "linear-gradient(to left, transparent 0%, black 20%, black 80%, transparent 95%)",
        }}
      >
        {Array(6)
          .fill(null)
          .map((_, index) => (
            <div
              key={`animated-testimonial-carousel-${index}`}
              className="flex flex-row gap-10 justify-around items-center animate-testimonial-carousel shrink-0"
            >
              {MESSAGE_IMAGES.map((message, key) => (
                <div key={key} className="flex-shrink-0 mx-5">
                  <Image
                    src={message.url}
                    alt={message.alt}
                    width={message.width}
                    quality={100}
                    priority={true}
                    height={message.height}
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
          ))}
      </div>
    </div>
  );
};

export default TestimonialCarousel;
