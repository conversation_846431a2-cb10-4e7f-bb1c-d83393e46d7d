export const HERO_TEXT_DESKTOP = "How event organizers sell more tickets";
export const HERO_TEXT_MOBILE = "How organizers sell more tickets";
export const BASE_URL = "https://dv2epwsej0xvt.cloudfront.net/static_assets";

// Testimonial message images
export const MESSAGE_IMAGES = [
  {
    id: 1,
    url: `${BASE_URL}/testimonials/message-6.webp`,
    alt: "Message 1",
    width: 210,
    height: 35,
  },
  {
    id: 2,
    url: `${BASE_URL}/testimonials/message-5.webp`,
    alt: "Message 2",
    width: 254,
    height: 92,
  },
  {
    id: 3,
    url: `${BASE_URL}/testimonials/message-4.webp`,
    alt: "Message 3",
    width: 280,
    height: 57,
  },
  {
    id: 4,
    url: `${BASE_URL}/testimonials/message-7.webp`,
    alt: "Message 4",
    width: 294,
    height: 55,
  },
  {
    id: 5,
    url: `${BASE_URL}/testimonials/message-9.webp`,
    alt: "Message 5",
    width: 280,
    height: 39,
  },
  {
    id: 6,
    url: `${BASE_URL}/testimonials/message-1.webp`,
    alt: "Message 6",
    width: 195,
    height: 72,
  },
  {
    id: 7,
    url: `${BASE_URL}/testimonials/message-3.webp`,
    alt: "Message 7",
    width: 260,
    height: 98,
  },
  {
    id: 8,
    url: `${BASE_URL}/testimonials/message-8.webp`,
    alt: "Message 8",
    width: 200,
    height: 77,
  },
  {
    id: 9,
    url: `${BASE_URL}/testimonials/message-10.webp`,
    alt: "Message 9",
    width: 278,
    height: 57,
  }, 
  {
    id: 10,
    url: `${BASE_URL}/testimonials/message-2.webp`,
    alt: "Message 10",
    width: 286,
    height: 42,
  },
];

export const FEATURES_DATA = [
  // Left column
  [
    "Custom branding",
    "Advanced Analytics",
    "Pixel tracking",
    "Waivers",
    "Awards",
    "Custom forms",
    "Featured list",
    "Merch add-ons",
  ],
  // Middle column
  [
    "Ticket scanning",
    "Automated custom flyers",
    "Ticket delivery by text",
    "Mobile app",
    "Fast and easy onboarding",
    "Drive Share",
    "Staff",
    "Weekly new features",
  ],
  // Right column
  [
    "Tap-to-pay",
    "Free Email marketing",
    "Collab events",
    "Customizable tickets",
    "Import customers csv.",
    "Direct messaging",
    "Event calendars",
    "Global chatroom",
  ],
];

export const FEATURES_DATA_MOBILE = [
  [
    "Custom branding",
    "Advanced Analytics",
    "Pixel tracking",
    "Waivers",
    "Awards",
    "Custom forms",
    "Featured list",
    "Merch add-ons",
    "Tap-to-pay",
    "Free Email marketing",
    "Collab events",
    "Global chatroom",
  ],
  [
    "Ticket scanning",
    "Automated flyers",
    "Ticket delivery by text",
    "Mobile app",
    "Easy onboarding",
    "Drive Share",
    "Staff",
    "Weekly new features",
    "Customizable tickets",
    "Import customers csv.",
    "Direct messaging",
    "Event calendars",
  ],
];

export const EXISTING_PLATFORM_ITEMS = [
  {
    title: "Outdated",
    description:
      "Uses old technology that's unsecure, looks ugly, and doesn't match your brand.",
  },
  {
    title: "Attendee complaints",
    description:
      "Attendees can't find tickets, are confused, and unsure if they can trust the website",
  },
  {
    title: "Poor customer service",
    description:
      "Support is slow, unhelpful, and doesn't listen to your suggestions",
  },
  {
    title: "Limited features",
    description:
      "Offers few options, no customization, and never releases new features",
  },
  {
    title: "Unreasonable contracts",
    description:
      "Overly restrictive terms, uncapped fees, that lock you into unfavorable agreements.",
  },
];

export const AUTO_LNK_ITEMS = [
  {
    title: "New and modern",
    description:
      "A secure, appealing, easy to use platform that uses current tech and AI",
  },
  {
    title: "Increased sales",
    description:
      "With a clear and trustworthy experience, your customers will keep coming back",
  },
  {
    title: "Service you deserve",
    description:
      "Fast, helpful support that listens to your suggestions and acts on them.",
  },
  {
    title: "Useful features",
    description:
      "Tools suggested by you, built for you, that's released every day",
  },
  {
    title: "No strings attached",
    description:
      "Our product speaks for itself, free from any contractual constraints or obligations",
  },
];

export const HOSTS_DETAILS = [
  {
    name: "Midnight",
    logo: `${BASE_URL}/landing-page/midnight.png`,
    width: 245,
    height: 78,
    mobileWidth: 139,
    mobileHeight: 44,
  },
  {
    name: "Calicreaming",
    logo: `${BASE_URL}/landing-page/cali.png`,
    width: 259,
    height: 30,
    mobileWidth: 147,
    mobileHeight: 17,
  },
  {
    name: "Fitted",
    logo: `${BASE_URL}/landing-page/fitted.png`,
    width: 151,
    height: 67,
    mobileWidth: 85,
    mobileHeight: 38,
  },
  {
    name: "Reload",
    logo: `${BASE_URL}/landing-page/reload.png`,
    width: 242,
    height: 46,
    mobileWidth: 139,
    mobileHeight: 26,
  },
  {
    name: "Baeuros",
    logo: `${BASE_URL}/landing-page/baeuros.png`,
    width: 179,
    height: 65,
    mobileWidth: 101,
    mobileHeight: 37,
  },
  {
    name: "Pacificautofest",
    logo: `${BASE_URL}/landing-page/pacificautofest.png`,
    width: 253,
    height: 35,
    mobileWidth: 143,
    mobileHeight: 20,
  },
  {
    name: "Downtoearth",
    logo: `${BASE_URL}/landing-page/downtoearth.png`,
    width: 253,
    height: 34,
    mobileWidth: 143,
    mobileHeight: 19,
  },
  {
    name: "Peaked",
    logo: `${BASE_URL}/landing-page/peaked.png`,
    width: 252,
    height: 22,
    mobileWidth: 142,
    mobileHeight: 13,
  },
  {
    name: "Slammin",
    logo: `${BASE_URL}/landing-page/slammin.png`,
    width: 175,
    height: 61,
    mobileWidth: 99,
    mobileHeight: 35,
  },
  {
    name: "Scrapefest",
    logo: `${BASE_URL}/landing-page/scrapefest.png`,
    width: 228,
    height: 34,
    mobileWidth: 128,
    mobileHeight: 19,
  },
  {
    name: "Seven Suns",
    logo: `${BASE_URL}/landing-page/sevensuns.png`,
    width: 251,
    height: 26,
    mobileWidth: 141,
    mobileHeight: 14,
  },
  {
    name: "Sharkfest",
    logo: `${BASE_URL}/landing-page/sharkfest.png`,
    width: 218,
    height: 70,
    mobileWidth: 123,
    mobileHeight: 40,
  },
];
