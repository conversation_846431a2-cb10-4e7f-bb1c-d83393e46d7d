import { Session } from "next-auth";

export const getButtonConfig = (
  session: Session
): {
  href: string;
  text: string;
} => {
  // No session - redirect to signup
  if (!session) {
    return {
      href: "/auth/signup",
      text: "Get Started - free",
    };
  }

  // Session exists with organization details - go to events
  if (session?.user?.orgDetails) {
    return {
      href: "/events",
      text: "Browse Events",
    };
  }

  // Session exists but no organization - complete professional setup
  return {
    href: "/professional-account-creation",
    text: "Get Started - free",
  };
};

