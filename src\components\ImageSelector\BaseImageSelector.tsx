import React, { useCallback, RefObject, useEffect, useRef } from "react";
import { useDropzone, Accept } from "react-dropzone";
import { Button, Image, Spinner } from "@nextui-org/react";
import BaseLabel from "../BaseLabel";

interface BaseImageSelectorProps {
  label: string;
  onImageSelect: (file: File) => void | Promise<void>;
  onRemoveImage: () => void;
  className?: string;
  value?: File | string | null;
  imageUrl?: string;
  fileInputRef: RefObject<HTMLInputElement>;
  error?: string;
  isRequired?: boolean;
  isLoading?: boolean;
  accept?: Accept;
}

const BaseImageSelector: React.FC<BaseImageSelectorProps> = ({
  label,
  onImageSelect,
  onRemoveImage,
  className = "",
  value,
  fileInputRef,
  error,
  isRequired = false,
  isLoading = false,
  accept = { "image/*": [] },
}) => {
  const objectUrlRef = useRef<string | null>(null);

  // Clean up object URL on unmount or value change
  useEffect(() => {
    return () => {
      if (objectUrlRef.current) {
        URL.revokeObjectURL(objectUrlRef.current);
        objectUrlRef.current = null;
      }
    };
  }, []);

  // Clean up previous object URL when value changes
  useEffect(() => {
    if (objectUrlRef.current && !(value instanceof File)) {
      URL.revokeObjectURL(objectUrlRef.current);
      objectUrlRef.current = null;
    }
  }, [value]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles?.length > 0) {
        const file = acceptedFiles[0];
        if (file) {
          await onImageSelect(file);
        }
      }
    },
    [onImageSelect]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: accept || { "image/*": [] },
    multiple: false,
    maxFiles: 1,
    noClick: true,
    noKeyboard: true,
  });

  const handleImageSelect = () => {
    fileInputRef?.current?.click();
  };

  const handleFileInputChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event?.target?.files;
    if (files?.length > 0) {
      const file = files[0];
      if (file) {
        await onImageSelect(file);
      }
    }
  };

  const getImageSrc = () => {
    if (value instanceof File) {
      try {
        // Clean up previous object URL
        if (objectUrlRef.current) {
          URL.revokeObjectURL(objectUrlRef.current);
        }

        const objectUrl = URL.createObjectURL(value);
        objectUrlRef.current = objectUrl;
        return objectUrl;
      } catch (error) {
        console.error("Error creating object URL:", error);
        return null;
      }
    }
    return (typeof value === "string" && value) || null;
  };

  const imageSrc = getImageSrc();

  const handleRemoveImage = (e: React.MouseEvent) => {
    e?.stopPropagation();
    if (!isLoading && onRemoveImage) {
      // Clean up object URL before removing
      if (objectUrlRef.current) {
        URL.revokeObjectURL(objectUrlRef.current);
        objectUrlRef.current = null;
      }
      onRemoveImage();
    }
  };

  // Safe accept keys join with validation
  const acceptString =
    accept && typeof accept === "object"
      ? Object.keys(accept).filter(Boolean).join(",")
      : "image/*";

  return (
    <div className={`w-full relative ${className}`}>
      <BaseLabel>
        {label} {isRequired && "*"}
      </BaseLabel>
      <div
        {...getRootProps()}
        className={`mt-1 h-[120px] flex flex-col justify-center items-center border-1 border-dashed rounded-lg p-4 text-center
          ${isDragActive ? "border-blue-500 bg-blue-50" : ""}
          ${error ? "border-[#F31260]" : "border-[#8A8A8A]"}`}
        role="button"
        aria-label={`Image upload area for ${label || "image"}`}
      >
        <input
          {...getInputProps()}
          ref={fileInputRef}
          accept={acceptString}
          multiple={false}
          aria-label="File input"
          onChange={handleFileInputChange}
        />
        {imageSrc ? (
          <div className="w-full h-full flex justify-center items-center relative">
            <Image
              src={imageSrc}
              alt={`Selected ${label || "image"} preview`}
              className="max-h-[90px] object-cover"
              fallbackSrc="/placeholder-image.png"
            />
            <button
              type="button"
              onClick={handleRemoveImage}
              className="absolute top-[-8px] right-[-8px] w-6 h-6 flex items-center justify-center bg-white rounded-full shadow-sm"
              aria-label="Remove image"
              disabled={isLoading}
            >
              {isLoading ? (
                <Spinner size="sm" />
              ) : (
                <Image
                  src="/delete-icon.svg"
                  alt="remove image"
                  width={16}
                  height={16}
                  fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDRMNCA3TDggOEw0IDEyIiBzdHJva2U9IiM2QzcyODAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo="
                />
              )}
            </button>
          </div>
        ) : (
          <div className="text-center">
            <Button
              onPress={handleImageSelect}
              className="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-3 min-w-16 h-8 text-tiny gap-2 rounded-small [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none text-default-foreground data-[hover=true]:opacity-hover bg-white border-1 border-[#e3e3e3] shadow-sm"
              size="sm"
              variant="flat"
              isDisabled={isLoading}
              isLoading={isLoading}
            >
              Add Image
            </Button>
          </div>
        )}
      </div>
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
};

export default BaseImageSelector;
