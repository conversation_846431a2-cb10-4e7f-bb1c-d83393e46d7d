import React, { useState, useCallback, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@nextui-org/react";
import <PERSON><PERSON><PERSON>, { Point, Area } from "react-easy-crop";

interface CropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (croppedImage: string) => void;
  isUploading?: boolean;
  currentImage: string | null;
  aspectRatio?: number;
  cropShape?: "rect" | "round";
  title?: string;
}

const CropModal: React.FC<CropModalProps> = ({
  isOpen,
  onClose,
  onSave,
  isUploading = false,
  currentImage,
  aspectRatio = 1,
  cropShape = "rect",
  title = "Crop Image",
}) => {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

  useEffect(() => {
    if (!isOpen || !currentImage) {
      setCrop({ x: 0, y: 0 });
      setZoom(1);
      setCroppedAreaPixels(null);
    }
  }, [isOpen, currentImage]);

  const onCropComplete = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const createCroppedImage = async () => {
    if (!currentImage || !croppedAreaPixels) return;

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const imageObj = new Image();

    imageObj.src = currentImage;
    await new Promise((resolve) => {
      imageObj.onload = resolve;
    });

    // Set canvas size to match cropped dimensions
    canvas.width = croppedAreaPixels.width;
    canvas.height = croppedAreaPixels.height;

    // Draw the cropped image
    ctx?.drawImage(
      imageObj,
      croppedAreaPixels.x,
      croppedAreaPixels.y,
      croppedAreaPixels.width,
      croppedAreaPixels.height,
      0,
      0,
      croppedAreaPixels.width,
      croppedAreaPixels.height
    );

    // Convert to base64
    const croppedImage = canvas.toDataURL("image/jpeg", 0.95);
    onSave(croppedImage);
  };

  const handleSave = () => {
    createCroppedImage();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="2xl"
      classNames={{
        base: "max-h-[90vh]",
        backdrop: "z-[1300]",
        wrapper: "z-[1301]",
        body: "max-h-[70vh] overflow-hidden",
      }}
    >
      <ModalContent>
        <ModalHeader>{title}</ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            <div className="relative mx-auto w-full h-[400px]">
              {currentImage && (
                <Cropper
                  image={currentImage}
                  crop={crop}
                  zoom={zoom}
                  aspect={aspectRatio}
                  onCropChange={setCrop}
                  onZoomChange={setZoom}
                  onCropComplete={onCropComplete}
                  cropShape={cropShape}
                  showGrid={cropShape === "rect"}
                  style={{
                    containerStyle: {
                      width: "100%",
                      height: "100%",
                      backgroundColor: "#000",
                    },
                    cropAreaStyle: {
                      border: "2px solid rgba(255, 255, 255, 0.7)",
                    },
                    mediaStyle: {
                      width: "100%",
                      height: "100%",
                      objectFit: "contain",
                    },
                  }}
                />
              )}
            </div>
            <div className="w-full px-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 min-w-[40px]">Zoom:</span>
                <Slider
                  size="sm"
                  step={0.1}
                  maxValue={3}
                  minValue={1}
                  value={zoom}
                  onChange={(value) => setZoom(value as number)}
                  className="flex-1"
                  aria-label="Zoom"
                />
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="danger" variant="light" onPress={handleCancel}>
            Cancel
          </Button>
          <Button
            color="primary"
            onPress={handleSave}
            isLoading={isUploading}
            isDisabled={!croppedAreaPixels}
          >
            Save
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default CropModal;

