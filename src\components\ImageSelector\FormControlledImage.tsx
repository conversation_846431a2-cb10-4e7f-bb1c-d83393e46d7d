import React, { useRef, useState } from "react";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import BaseImageSelector from "./BaseImageSelector";
import { useImageUpload } from "./useImageUpload";
import { Accept } from "react-dropzone";
import CropModal from "./CropModal";
import { processImageFile } from "@/lib/utils/heicConverter";
import toast from "react-hot-toast";

interface FormControlledImageProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>;
  control: Control<TFieldValues>;
  label: string;
  className?: string;
  isRequired?: boolean;
  fileSize?: number;
  imageUrl?: string;
  imageId?: number;
  ticketId?: string;
  setIsFormLoading?: (isFormLoading: boolean) => void;
  accept?: Accept;
  enableCrop?: boolean;
  aspectRatio?: number;
  cropShape?: "rect" | "round";
  cropTitle?: string;
}

function FormControlledImage<TFieldValues extends FieldValues>({
  name,
  control,
  label,
  className,
  isRequired,
  fileSize = 10 * 1024 * 1024,
  setIsFormLoading,
  accept,
  enableCrop = false,
  aspectRatio = 1,
  cropShape = "rect",
  cropTitle = "Crop Image",
}: FormControlledImageProps<TFieldValues>) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showCropModal, setShowCropModal] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [originalFileName, setOriginalFileName] = useState<string | null>(null);

  const cleanupImageUrl = (url: string | null) => {
    if (url && typeof url === "string") {
      try {
        URL.revokeObjectURL(url);
      } catch (error) {
        // Ignore errors when revoking URLs that weren't created by createObjectURL
        console.debug(
          "URL revoke error (this is normal for non-blob URLs):",
          error
        );
      }
    }
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        const {
          handleImageSelect,
          handleRemoveImage,
          isUploading,
          previewFile,
        } = useImageUpload({
          fileSize,
          onSuccess: (url) => {
            if (onChange) {
              onChange(url);
            }
          },
          setIsFormLoading,
        });

        const onImageSelect = async (file: File) => {
          if (!file) {
            toast.error("No file selected");
            return;
          }

          try {
            // Process HEIC files first (convert to JPEG if needed)
            const processedFile = await processImageFile(file);
            if (!processedFile) {
              toast.error(
                "Failed to process image. Please try a different format."
              );
              return;
            }

            if (enableCrop) {
              // Store original filename for use after cropping
              setOriginalFileName(file.name || "image");
              // For crop mode, create preview URL and show crop modal
              try {
                const imageUrl = URL.createObjectURL(processedFile);
                setSelectedImageUrl(imageUrl);
                setShowCropModal(true);
              } catch (error) {
                console.error("Error creating object URL:", error);
                toast.error("Failed to prepare image for cropping.");
              }
            } else {
              // For non-crop mode, upload directly
              handleImageSelect(processedFile);
            }
          } catch (error) {
            console.error("Error processing HEIC file:", error);
            toast.error(
              "Failed to process image. Please try a different format."
            );
          }
        };

        const onCropSave = async (croppedImageDataUrl: string) => {
          if (!croppedImageDataUrl || typeof croppedImageDataUrl !== "string") {
            toast.error("Invalid cropped image data");
            return;
          }

          try {
            // Convert data URL to File
            const response = await fetch(croppedImageDataUrl);
            if (!response.ok) {
              throw new Error("Failed to fetch cropped image data");
            }

            const blob = await response.blob();
            if (!blob) {
              throw new Error("Failed to create blob from cropped image");
            }

            // Create filename from original name, trim spaces and ensure .jpg extension
            let fileName = "cropped-image.jpg"; // fallback
            if (originalFileName?.trim()) {
              // Remove file extension and trim spaces
              const nameWithoutExt = originalFileName
                .replace(/\.[^/.]+$/, "")
                .trim()
                .replace(/\s+/g, "_");
              fileName = nameWithoutExt
                ? `${nameWithoutExt}.jpg`
                : "cropped-image.jpg";
            }

            const file = new File([blob], fileName, {
              type: "image/jpeg",
            });

            // Upload the cropped file
            handleImageSelect(file);
            setShowCropModal(false);

            // Clean up the object URL and reset state
            cleanupImageUrl(selectedImageUrl);
            setSelectedImageUrl(null);
            setOriginalFileName(null);
          } catch (error) {
            console.error("Error processing cropped image:", error);
            toast.error("Failed to process cropped image. Please try again.");
            setShowCropModal(false);
            cleanupImageUrl(selectedImageUrl);
            setSelectedImageUrl(null);
            setOriginalFileName(null);
          }
        };

        const onCropClose = () => {
          setShowCropModal(false);
          cleanupImageUrl(selectedImageUrl);
          setSelectedImageUrl(null);
          // Reset original filename
          setOriginalFileName(null);
          // Reset file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        };

        const onRemoveImage = () => {
          handleRemoveImage();
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        };

        // Handle both File and string URL values with proper type checking
        const getDisplayValue = (): File | string | null => {
          // Check if value is a File object
          if (
            value &&
            typeof value === "object" &&
            "name" in value &&
            "size" in value &&
            "type" in value
          ) {
            return value as File;
          }
          if (typeof value === "string" && value.trim()) {
            return value;
          }
          return null;
        };

        const displayValue = getDisplayValue();

        return (
          <>
            <div className={className}>
              <BaseImageSelector
                label={label || "Image"}
                onImageSelect={onImageSelect}
                onRemoveImage={onRemoveImage}
                value={displayValue}
                fileInputRef={fileInputRef}
                error={error?.message}
                isRequired={isRequired}
                isLoading={isUploading}
                accept={accept}
              />
            </div>

            {enableCrop && (
              <CropModal
                isOpen={showCropModal}
                onClose={onCropClose}
                onSave={onCropSave}
                currentImage={selectedImageUrl}
                aspectRatio={aspectRatio}
                cropShape={cropShape}
                title={cropTitle || "Crop Image"}
                isUploading={isUploading}
              />
            )}
          </>
        );
      }}
    />
  );
}

export default FormControlledImage;
