import React, { useRef, useState } from "react";
import { Controller, Control, FieldValues, FieldPath, UseFormSetValue, useWatch } from "react-hook-form";
import BaseImageSelector from "./BaseImageSelector";
import { useImageUpload } from "./useImageUpload";
import { Accept } from "react-dropzone";
import CropModal from "./CropModal";
import { processImageFile } from "@/lib/utils/heicConverter";
import toast from "react-hot-toast";
import { useVehicleAnalysis } from "@/components/VehicleAnalysis/useVehicleAnalysis";

interface FormControlledImageWithAnalysisProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>;
  control: Control<TFieldValues>;
  label: string;
  className?: string;
  isRequired?: boolean;
  fileSize?: number;
  imageUrl?: string;
  imageId?: number;
  ticketId?: string;
  setIsFormLoading?: (isFormLoading: boolean) => void;
  accept?: Accept;
  enableCrop?: boolean;
  aspectRatio?: number;
  cropShape?: "rect" | "round";
  cropTitle?: string;
  // Vehicle analysis specific props
  enableVehicleAnalysis?: boolean;
  setValue?: UseFormSetValue<any>;
  onVehicleAnalysisComplete?: (data: {
    make?: string;
    model?: string;
    year?: string;
  }) => void;
  // Callback when image is selected (before analysis)
  onImageSelect?: () => void;
  // Vehicle analysis options for matching
  vehicleAnalysisOptions?: {
    vehicleTypes: Array<{ label: string; value: string }>;
    makeOptions: Array<{ label: string; value: string }>;
  };
}

function FormControlledImageWithAnalysis<TFieldValues extends FieldValues>({
  name,
  control,
  label,
  className,
  isRequired,
  fileSize = 10 * 1024 * 1024,
  setIsFormLoading,
  accept,
  enableCrop = false,
  aspectRatio = 1,
  cropShape = "rect",
  cropTitle = "Crop Image",
  enableVehicleAnalysis = false,
  setValue,
  onVehicleAnalysisComplete,
  onImageSelect,
  vehicleAnalysisOptions,
}: FormControlledImageWithAnalysisProps<TFieldValues>) {
  
  const [showCropModal, setShowCropModal] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [originalFileName, setOriginalFileName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Vehicle analysis hook
  const { analyzeVehicleImage, isAnalyzing } = useVehicleAnalysis({
    setValue,
    onAnalysisComplete: onVehicleAnalysisComplete,
    isEnabled: enableVehicleAnalysis,
    vehicleAnalysisOptions,
  });

  // Watch for existing make and model values to skip analysis if already filled
  const existingMake = useWatch({ control, name: 'vehicle.make' as any });
  const existingModel = useWatch({ control, name: 'vehicle.modelName' as any });

  const cleanupImageUrl = (url: string | null) => {
    if (url && typeof url === "string") {
      try {
        URL.revokeObjectURL(url);
      } catch (error) {
        // Ignore errors when revoking URLs that weren't created by createObjectURL
        console.warn(
          "Failed to revoke object URL (this is usually safe to ignore):",
          error
        );
      }
    }
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        const {
          handleImageSelect,
          handleRemoveImage,
          isUploading,
          previewFile,
        } = useImageUpload({
          fileSize,
          onSuccess: (url) => {
            if (onChange) {
              onChange(url);
            }
          },
          setIsFormLoading,
        });

        const onImageSelectHandler = async (file: File) => {
          if (!file) {
            toast.error("No file selected");
            return;
          }

          // Call the callback to notify parent component
          if (onImageSelect) {
            onImageSelect();
          }

          try {
            // Process HEIC files first (convert to JPEG if needed)
            const processedFile = await processImageFile(file);
            if (!processedFile) {
              toast.error(
                "Failed to process image. Please try a different format."
              );
              return;
            }

            // Trigger vehicle analysis when image is selected (before upload/crop)
            // Skip analysis if make or model already have values
            if (enableVehicleAnalysis && !existingMake && !existingModel) {
              console.log('Running vehicle analysis - no existing make/model found');
              // Run analysis in parallel with image processing
              analyzeVehicleImage(processedFile).catch(error => {
                console.error("Vehicle analysis failed:", error);
                // Don't block the image selection process if analysis fails
              });
            } else if (enableVehicleAnalysis && (existingMake || existingModel)) {
              console.log('Skipping vehicle analysis - make or model already exists:', { 
                existingMake, 
                existingModel 
              });
            }

            if (enableCrop) {
              // Store original filename for use after cropping
              setOriginalFileName(file.name || "image");
              // For crop mode, create preview URL and show crop modal
              try {
                const imageUrl = URL.createObjectURL(processedFile);
                setSelectedImageUrl(imageUrl);
                setShowCropModal(true);
              } catch (error) {
                console.error("Error creating object URL:", error);
                toast.error("Failed to prepare image for cropping.");
              }
            } else {
              // For non-crop mode, upload directly
              handleImageSelect(processedFile);
            }
          } catch (error) {
            console.error("Error processing HEIC file:", error);
            toast.error(
              "Failed to process image. Please try a different format."
            );
          }
        };

        const onCropSave = async (croppedImageDataUrl: string) => {
          try {
            // Convert data URL to blob
            const response = await fetch(croppedImageDataUrl);
            const blob = await response.blob();

            // Create a new file from the blob with original filename
            const croppedFile = new File(
              [blob],
              originalFileName || "cropped-image.jpg",
              {
                type: blob.type || "image/jpeg",
                lastModified: Date.now(),
              }
            );

            // Upload the cropped file
            await handleImageSelect(croppedFile);
            setShowCropModal(false);
            
            // Clean up the temporary URL
            cleanupImageUrl(selectedImageUrl);
            setSelectedImageUrl(null);
            setOriginalFileName(null);
          } catch (error) {
            console.error("Error processing cropped image:", error);
            toast.error("Failed to process cropped image.");
          }
        };

        const onCropClose = () => {
          setShowCropModal(false);
          cleanupImageUrl(selectedImageUrl);
          setSelectedImageUrl(null);
          setOriginalFileName(null);
        };

        const onRemoveImage = () => {
          handleRemoveImage();
          if (onChange) {
            onChange("");
          }
        };

        const getDisplayValue = () => {
          if (previewFile) {
            return previewFile;
          }
          if (value && typeof value === "string" && value.trim() !== "") {
            return value;
          }
          return null;
        };

        const displayValue = getDisplayValue();

        // Show analyzing state in the loading indicator
        const isLoadingWithAnalysis = isUploading || isAnalyzing;

        return (
          <>
            <div className={className}>
              <BaseImageSelector
                label={label || "Image"}
                onImageSelect={onImageSelectHandler}
                onRemoveImage={onRemoveImage}
                value={displayValue}
                fileInputRef={fileInputRef}
                error={error?.message}
                isRequired={isRequired}
                isLoading={isLoadingWithAnalysis}
                accept={accept}
              />
              {isAnalyzing && (
                <div className="mt-2 text-sm text-blue-600 flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  Analyzing vehicle details...
                </div>
              )}
            </div>

            {enableCrop && (
              <CropModal
                isOpen={showCropModal}
                onClose={onCropClose}
                onSave={onCropSave}
                currentImage={selectedImageUrl}
                aspectRatio={aspectRatio}
                cropShape={cropShape}
                title={cropTitle || "Crop Image"}
                isUploading={isUploading}
              />
            )}
          </>
        );
      }}
    />
  );
}

export default FormControlledImageWithAnalysis;