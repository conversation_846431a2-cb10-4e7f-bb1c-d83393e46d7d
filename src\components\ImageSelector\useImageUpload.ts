import { useState } from "react";
import { useImageUploadMutation } from "@/lib/redux/slices/cart/cartApi";
import { compressImage } from "@/lib/utils/imageCompression";
import { formatBytes } from "./helper";
import { sanitizeFilename } from "@/lib/utils/fileUtils";
import { processImageFile } from "@/lib/utils/heicConverter";
import toast from "react-hot-toast";

interface S3Fields {
  contentType: string;
  key: string;
  xAmzAlgorithm: string;
  xAmzCredential: string;
  xAmzDate: string;
  policy: string;
  xAmzSignature: string;
}

interface ImageUploadResponse {
  url: string;
  contentType: string;
  path: string;
  cloudfrontUrl: string;
  headers: {
    contentDisposition: any;
    contentType: any;
  };
}

interface UseImageUploadProps {
  fileSize?: number;
  onSuccess?: (url: string | null) => void;
  setIsFormLoading?: (loading: boolean) => void;
  showSuccessToast?: boolean; // Add this parameter
}

export const useImageUpload = ({
  fileSize = 5 * 1024 * 1024, // 5MB default
  onSuccess,
  setIsFormLoading,
  showSuccessToast = true, // Default to true for backward compatibility
}: UseImageUploadProps = {}) => {
  const [imageUpload] = useImageUploadMutation();
  const [isUploading, setIsUploading] = useState(false);
  const [previewFile, setPreviewFile] = useState<File | null>(null);

  const verifyUrlWithRetry = async (
    url: string,
    maxRetries = 3
  ): Promise<boolean> => {
    if (!url || typeof url !== "string") {
      console.error("Invalid URL provided for verification");
      return false;
    }

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const response = await fetch(url, { method: "HEAD" });
        if (response.ok) {
          return true;
        }
        // If not last attempt, wait before retrying
        if (attempt < maxRetries - 1) {
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, attempt) * 1000)
          );
        }
      } catch (error) {
        console.error(
          `URL verification attempt ${attempt + 1} failed:`,
          error
        );
        if (attempt < maxRetries - 1) {
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, attempt) * 1000)
          );
        }
      }
    }
    return false;
  };

  const uploadToAWS = async (file: File, response: ImageUploadResponse) => {
    if (!file) {
      throw new Error("No file provided for upload");
    }

    if (!response?.url || !response?.headers) {
      throw new Error("Invalid upload response");
    }

    try {
      // Read file as binary data
      const fileData = await new Promise<ArrayBuffer>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result;
          if (result instanceof ArrayBuffer) {
            resolve(result);
          } else {
            reject(new Error("Failed to read file as ArrayBuffer"));
          }
        };
        reader.onerror = () => reject(reader.error || new Error("FileReader error"));
        reader.readAsArrayBuffer(file);
      });

      const config = {
        method: 'PUT',
        body: fileData,
        headers: {
          'Content-Type': response.headers?.contentType || file.type || 'application/octet-stream',
          'Content-Disposition': response.headers?.contentDisposition || '',
        },
      };

      const uploadResponse = await fetch(response.url, config);

      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload to AWS: ${uploadResponse.statusText}`);
      }

      return uploadResponse;
    } catch (error) {
      console.error("Error uploading to AWS:", error);
      throw error;
    }
  };

  const attemptImageUpload = async (file: File, retryCount = 0): Promise<string | null> => {
    if (!file) {
      console.error("No file provided for upload");
      return null;
    }

    try {
      const sanitizedFilename = sanitizeFilename(file, 'image');
      if (!sanitizedFilename) {
        throw new Error("Failed to generate valid filename");
      }

      const response = (await imageUpload({
        filename: sanitizedFilename,
        type: file.type || 'image/jpeg',
      }).unwrap()) as ImageUploadResponse;

      if (!response?.url || !response?.cloudfrontUrl) {
        throw new Error("Invalid upload URL response");
      }

      await uploadToAWS(file, response);
      
      // Verify the CloudFront URL is accessible before returning it
      const isUrlValid = await verifyUrlWithRetry(response.cloudfrontUrl);
      if (!isUrlValid) {
        throw new Error("Image URL is not accessible after multiple attempts");
      }
      
      return response.cloudfrontUrl;
    } catch (error) {
      console.error(`Error uploading image (attempt ${retryCount + 1}):`, error);
      
      // If we haven't reached max retries, try again
      if (retryCount < 2) {
        toast.error(`Upload attempt ${retryCount + 1} failed. Retrying...`);
        return attemptImageUpload(file, retryCount + 1);
      } else {
        // Max retries reached, don't set the image
        toast.error("Failed to upload image after multiple attempts. Please try again later.");
        return null;
      }
    }
  };

  const handleImageSelect = async (file: File) => {
    if (!file) {
      toast.error("No file selected");
      return;
    }

    try {
      setIsFormLoading?.(true);
      
      // Process HEIC files first (convert to JPEG if needed)
      let processedFile: File;
      try {
        processedFile = await processImageFile(file);
        if (!processedFile) {
          throw new Error("File processing returned null");
        }
      } catch (error) {
        console.error("Error processing HEIC file:", error);
        toast.error("Failed to process image. Please try a different format.");
        return;
      }
      
      // Validate file size
      const currentFileSize = processedFile.size || 0;
      if (currentFileSize > fileSize) {
        try {
          const compressedFile = await compressImage(processedFile);
          if (!compressedFile) {
            throw new Error("Image compression failed");
          }
          
          if (compressedFile.size > fileSize) {
            toast.error(
              `Unable to compress image below ${formatBytes(fileSize)}. Please select a smaller image.`
            );
            return;
          }
          processedFile = compressedFile;
        } catch (error) {
          console.error("Error compressing image:", error);
          toast.error(
            `File is too large. Please select an image smaller than ${formatBytes(fileSize)}.`
          );
          return;
        }
      }

      setIsUploading(true);
      setPreviewFile(processedFile);

      const cloudfrontUrl = await attemptImageUpload(processedFile);
      
      if (cloudfrontUrl && typeof cloudfrontUrl === "string") {
        onSuccess?.(cloudfrontUrl);
        if (showSuccessToast) {
          toast.success("Image uploaded successfully");
        }
      } else {
        // If all retries failed, remove the preview
        setPreviewFile(null);
        toast.error("Upload failed. Please try again.");
      }
    } catch (error) {
      console.error("Error processing image:", error);
      toast.error("Error processing image. Please try again.");
      setPreviewFile(null);
    } finally {
      setIsUploading(false);
      setIsFormLoading?.(false);
    }
  };

  const handleRemoveImage = () => {
    setPreviewFile(null);
    onSuccess?.(null);
    toast.success("Image removed successfully");
  };

  return {
    handleImageSelect,
    handleRemoveImage,
    isUploading,
    previewFile,
  };
}; 