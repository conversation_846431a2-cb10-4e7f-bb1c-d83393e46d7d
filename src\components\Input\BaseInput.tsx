import React from "react";
import { Input, InputProps } from "@nextui-org/react";
import BaseLabel from "../BaseLabel";

export interface BaseInputProps extends Omit<InputProps, "css"> {
  helperText?: string;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
}

const BaseInput = React.forwardRef<HTMLInputElement, BaseInputProps>(
  (
    {
      label,
      helperText,
      errorMessage,
      startContent,
      endContent,
      className = "",
      inputClassName = "",
      labelClassName = "",
      ...props
    },
    ref
  ) => {
    const combinedClassName = `w-full ${className}`;
    const combinedInputClassName = `${inputClassName} ${
      errorMessage ? "border-red-500" : ""
    }`;

    return (
      <div className={combinedClassName}>
        {label && (
          <BaseLabel className={labelClassName}>
            {label} {props.isRequired && "*"}
          </BaseLabel>
        )}
        <Input
          {...props}
          ref={ref}
          variant="bordered"
          errorMessage={errorMessage}
          startContent={startContent}
          endContent={endContent}
          description={helperText}
          classNames={{
            base: "max-w-full",
            input: `ios-input ${combinedInputClassName}`,
            innerWrapper: "bg-transparent",
            inputWrapper:
              "bg-transparent border-1 border-solid border-[#828282B2] rounded-lg",
            description: "text-xs text-gray-500",
          }}
        />
      </div>
    );
  }
);

BaseInput.displayName = "BaseInput";

export default BaseInput;
