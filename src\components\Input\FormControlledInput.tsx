import React from "react";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import BaseInput, { BaseInputProps } from "./BaseInput";

interface FormControlledInputProps<TFieldValues extends FieldValues>
  extends Omit<BaseInputProps, "name" | "onChange" | "onBlur" | "value"> {
  name: FieldPath<TFieldValues>;
  control: Control<TFieldValues>;
  rules?: any;
  prefix?: string;
  isDisabled?: boolean;
}

const FormControlledInput = <TFieldValues extends FieldValues>({
  name,
  control,
  rules,
  prefix,
  isDisabled,
  ...rest
}: FormControlledInputProps<TFieldValues>) => (
  <Controller
    name={name}
    control={control}
    rules={rules}
    render={({ field, fieldState: { error } }) => (
      <BaseInput
        {...field}
        {...rest}
        startContent={
          prefix && (
            <span className="text-default-400 text-small">{prefix}</span>
          )
        }
        errorMessage={error?.message}
        isInvalid={!!error}
        disabled={isDisabled}
      />
    )}
  />
);

export default FormControlledInput;
