import DOMPurify from "isomorphic-dompurify";
import { Controller, Control, FieldValues, Path } from "react-hook-form";
import dynamic from "next/dynamic";
import "react-quill/dist/quill.snow.css";
import BaseLabel from "../BaseLabel";
import Head from "next/head";

const QuillField = dynamic(() => import("react-quill"), { ssr: false });

interface FormControlledRichTextInputProps<TFieldValues extends FieldValues> {
  name: Path<TFieldValues>;
  label: string;
  control: Control<TFieldValues>;
  placeholder?: string;
  modules?: any;
  formats?: string[];
}

// The main component
export default function FormControlledRichTextInput<
  TFieldValues extends FieldValues
>({
  name,
  label,
  control,
  placeholder = "",
  modules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      ["bold", "italic", "underline"],
      [{ list: "ordered" }, { list: "bullet" }],
      [],
    ],
  },
  formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "list",
    "bullet",
    "link",
    "image",
  ],
}: FormControlledRichTextInputProps<TFieldValues>) {
  return (
    <>
      <Head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0"
        />
      </Head>
      <div className="text-base">
        <BaseLabel className="mb-1">{label}</BaseLabel>
        <Controller
          name={name}
          control={control}
          render={({ field, fieldState: { error } }) => (
            <>
              <div className="quill-rounded-wrapper">
                <QuillField
                  value={field?.value || ""}
                  onChange={(value: string) => {
                    const sanitizedValue = DOMPurify.sanitize(value);
                    field.onChange(sanitizedValue);
                  }}
                  placeholder={placeholder}
                  modules={modules}
                  theme="snow"
                  formats={formats}
                  className="w-full min-h-[10rem] mt-1 mb-4"
                />
              </div>
              {error?.message && (
                <p className="text-sm font-medium text-red-500">
                  {error?.message}
                </p>
              )}
            </>
          )}
        />
      </div>
    </>
  );
}
