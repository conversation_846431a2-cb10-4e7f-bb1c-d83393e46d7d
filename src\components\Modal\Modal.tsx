import React, { ReactNode } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  useDisclosure,
  ModalProps,
} from "@nextui-org/react";

interface ModalComponentProps {
  trigger: React.ReactElement;
  title: string | React.ReactElement;
  children: ReactNode;
  footer?: ReactNode;
  size?: ModalProps["size"];
  isDismissable?: boolean;
  hideCloseButton?: boolean;
  scrollBehavior?: ModalProps["scrollBehavior"];
  className?: string;
  backdrop?: ModalProps["backdrop"];
  onCloseModal?: () => void;
  isKeyboardDismissDisabled?: boolean;
}

const ModalComponent: React.FC<ModalComponentProps> = ({
  trigger,
  title,
  children,
  footer,
  size = "md",
  isDismissable = true,
  hideCloseButton = false,
  scrollBehavior,
  className,
  backdrop = "opaque",
  onCloseModal,
  isKeyboardDismissDisabled = true,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const triggerWithHandler = React.cloneElement(trigger, {
    onClick: (e: React.MouseEvent) => {
      trigger.props.onClick?.(e);
      onOpen();
    },
  });

  return (
    <>
      {triggerWithHandler}
      <Modal
        isOpen={isOpen}
        onClose={() => {
          onClose();
          onCloseModal?.()
        }}
        size={size}
        isDismissable={isDismissable}
        hideCloseButton={hideCloseButton}
        scrollBehavior={scrollBehavior}
        backdrop={backdrop}
        isKeyboardDismissDisabled={isKeyboardDismissDisabled}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">{title}</ModalHeader>
              <ModalBody className={className}>{children}</ModalBody>
              <ModalFooter>{footer}</ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

export default ModalComponent;
