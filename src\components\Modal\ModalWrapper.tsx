"use client";

import { Mo<PERSON>, <PERSON><PERSON><PERSON>ontent, ModalHeader } from "@nextui-org/react";

interface ModalWrapperProps {
  children: React.ReactNode;
  title: string;
  isOpen: boolean;
  handleClose: () => void;
  size:
    | "xs"
    | "sm"
    | "md"
    | "lg"
    | "xl"
    | "2xl"
    | "3xl"
    | "4xl"
    | "5xl"
    | "full";
  className?: string;
  placement: "top" | "auto" | "center";
}

export default function ModalWrapper({
  children,
  title,
  isOpen,
  handleClose,
  size,
  className,
  placement,
}: ModalWrapperProps) {
  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size={size}
      placement={placement}
      scrollBehavior="outside"
      isDismissable={false}
      classNames={{
        wrapper: "items-center",
        base: className,
        body: "p-0",
      }}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: "easeOut",
            },
          },
          exit: {
            y: 20,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: "easeIn",
            },
          },
        },
      }}
    >
      <ModalContent className="sm:!mb-0 !mb-auto overflow-hidden">
        <ModalHeader className="bg-[#F3F3F3] py-2 border-b-1 border-[#E3E3E3]">
          <p className="text-base font-semibold">{title}</p>
        </ModalHeader>
        {children}
      </ModalContent>
    </Modal>
  );
}
