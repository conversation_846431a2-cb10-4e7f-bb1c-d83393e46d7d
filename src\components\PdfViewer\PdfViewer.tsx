"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import { Button, Skeleton } from "@nextui-org/react";
pdfjs.GlobalWorkerOptions.workerSrc =
  "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.4.168/pdf.worker.min.mjs";

interface PdfViewerProps {
  pdfPath: string;
  fullView?: boolean;
}

const MAX_WIDTH = 1200;
const DEFAULT_WIDTH = 500;

const PdfViewer: React.FC<PdfViewerProps> = ({ pdfPath, fullView = false }) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [width, setWidth] = useState<number>(
    typeof window !== "undefined"
      ? Math.min(window.innerWidth, MAX_WIDTH)
      : DEFAULT_WIDTH
  );

  useEffect(() => {
    const handleResize = () => {
      setWidth(Math.min(window.innerWidth, MAX_WIDTH));
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const onDocumentLoadSuccess = useCallback((pdf: any) => {
    setNumPages(pdf.numPages);
    setIsLoading(false);
  }, []);

  const onDocumentLoadError = useCallback((error: Error) => {
    console.error("Error loading PDF:", error);
    setError(`Failed to load PDF: ${error.message}`);
    setIsLoading(false);
  }, []);

  const goToPreviousPage = () => setPageNumber((prev) => Math.max(prev - 1, 1));
  const goToNextPage = () => {
    if (numPages) {
      setPageNumber((prev) => Math.min(prev + 1, numPages));
    }
  };

  return (
    <>
      {isLoading && <Skeleton className="rounded-lg sm:w-[600px] h-[600px]" />}
      {error && <p className="text-red-500 break-words text-xs">{error}</p>}
      {!error && (
        <>
          <Document
            file={pdfPath}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading=""
          >
            {fullView ? (
              Array.from(new Array(numPages), (el, index) => (
                <Page
                  loading=""
                  key={`page_${index + 1}`}
                  pageNumber={index + 1}
                  width={width ? width - 40 : 400}
                />
              ))
            ) : (
              <Page pageNumber={pageNumber} width={width ? width - 40 : 400} />
            )}
          </Document>

          {!fullView && (
            <div className="flex justify-between items-center mt-5">
              <Button
                onPress={goToPreviousPage}
                disabled={pageNumber <= 1}
                size="sm"
              >
                Previous
              </Button>
              <p className="font-medium">
                Page {pageNumber} of {numPages}
              </p>
              <Button
                onPress={goToNextPage}
                size="sm"
                disabled={pageNumber >= (numPages ?? 0)}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default PdfViewer;
