import React from "react";
import { Controller } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import BaseLabel from "../BaseLabel";

interface FormControlledRadioProps {
  control: any;
  name: string;
  label: string;
  options: Array<{ label: string; value: string }>;
  isRequired?: boolean;
  classNames?: {
    base?: string;
    wrapper?: string;
    label?: string;
  };
  labelClassName?: string;
}

export default function FormControlledRadio({
  control,
  name,
  label,
  options,
  isRequired,
  classNames,
  labelClassName = "",
}: FormControlledRadioProps) {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={isRequired && options?.[0]?.value ? options[0].value : ""}
      render={({ field: { onChange, value }, fieldState }) => {
        const handleValueChange = (newValue: string) => {
          if (newValue) {
            onChange(newValue);
          }
        };

        return (
          <div className={cn("space-y-2", classNames?.base)}>
            {label && (
              <BaseLabel className={labelClassName}>
                {label} {isRequired && "*"}
              </BaseLabel>
            )}
            <RadioGroup
              value={value || ""}
              onValueChange={handleValueChange}
              className={cn("grid gap-2", classNames?.wrapper)}
            >
              {options.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.value}
                    id={option.value}
                    className="border-gray-400 text-gray-400 transition-colors duration-200 data-[state=checked]:border-primary data-[state=checked]:text-primary"
                  />
                  <Label
                    htmlFor={option.value}
                    className={cn("text-base font-normal", classNames?.label)}
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
            {fieldState.error?.message && (
              <p className="text-sm text-destructive">
                {fieldState.error.message}
              </p>
            )}
          </div>
        );
      }}
    />
  );
}
