import Carousel from "@/features/Carousel/Carousel";
import CategoryCard from "../CategoryCard/CategoryCard";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
const CategoriesCarousels = () => {
  const breakpoints = Object.fromEntries(
    [
      [360, 1.5, 25],
      [1024, 4.2, 42],
      [2560, 5.5, 45],
    ].map(([width, slidesPerView, offset]) => [
      width,
      {
        slidesPerView: slidesPerView,
        slidesOffsetBefore: offset,
        slidesOffsetAfter: offset,
      },
    ])
  );

  return (
    <Carousel id="categories-carousels" breakpoints={breakpoints}>
      <CategoryCard title="Meet" imgSrc={IMAGE_LINKS.CATEGORY_MEET} />
      <CategoryCard title="Show" imgSrc={IMAGE_LINKS.CATEGORY_SHOW} />
      <CategoryCard title="Drives" imgSrc={IMAGE_LINKS.CATEGORY_DRIVES} />
      <CategoryCard title="Motorsports" imgSrc={IMAGE_LINKS.CATEGORY_MOTORSPORTS} />
    </Carousel>
  );
};

export default CategoriesCarousels;
