import { Suspense } from "react";
import Carousel from "@/features/Carousel/Carousel";
import EventCard from "../EventCard/EventCard";
import EventCardSkeleton from "../EventCard/EventCardSkeleton";
import "./custom-carousel.css";

interface Event {
  organization: any;
  pixels:
    | {
        meta?: string | undefined;
        ga4?: string | undefined;
        snap?: string | undefined;
        tiktok?: string | undefined;
      }
    | undefined;
  timezone: string;
  id: string;
  name: string;
  startDate: string;
  city: string;
  state: string;
  images: Array<{ photo: string; type?: number }>;
  pixel_id: string;
  slug: string;
  metadata?: {
    tag?: string;
  };
}

type Props = {
  events: Event[];
};

const BREAKPOINT_CONFIG = [
  [360, 2.5, 25],
  [576, 2.5, 25],
  [768, 3.5, 33],
  [1024, 5, 42],
  [1280, 5, 42],
  [1440, 4.5, 42],
  [1920, 5, 45],
  [2560, 6, 45],
] as const;

const EventsCarousels = ({ events }: Props) => {
  const breakpoints = Object.fromEntries(
    BREAKPOINT_CONFIG.map(([width, slidesPerView, offset]) => [
      width,
      {
        slidesPerView,
        slidesOffsetBefore: offset,
        slidesOffsetAfter: offset,
      },
    ])
  );

  // Only prioritize the first 6 images (visible on most screen sizes)
  const PRIORITY_THRESHOLD = 6;

  return (
    <Carousel id="events-carousels" breakpoints={breakpoints} slideByPage={true}>
      {events?.map((event, index) => {
        const mainEventImg = event?.images?.find(
          (img: any) => Number(img?.type) === 1
        );
        const shouldLazyLoad = index >= PRIORITY_THRESHOLD;
        
        return (
          <Suspense key={event.id} fallback={<EventCardSkeleton />}>
            <div className="flex w-full justify-center items-center pt-1 relative">
              <EventCard
                title={event.name}
                date={event.startDate}
                city={event.city}
                state={event.state}
                imgSrc={mainEventImg?.photo}
                href={`/events/${event.slug}`}
                eventId={event.id}
                timezone={event?.timezone}
                tag={event?.metadata?.tag}
                lazy={shouldLazyLoad}
                pixels={event?.organization?.pixels || {}}
              />
            </div>
          </Suspense>
        );
      })}
    </Carousel>
  );
};

export default EventsCarousels;
