/* Main container configuration */
#events-carousels {
  position: relative;
  overflow: hidden !important;
}

/* Make slides properly positioned */
.swiper-slide {
  position: relative;
  overflow: visible !important;
  padding-top: 10px; /* Space for tag */
  transition: transform 0.25s ease;
}

/* Position context for the event card container */
.swiper-slide > div {
  position: relative;
  transition: opacity 0.2s ease-in-out;
}

/* Better targeting method for the TagBadge component */
.swiper-slide [class*="absolute"][class*="top-[-10px]"] {
  position: absolute !important;
  z-index: 999 !important;
  top: -9px !important;
}

/* Container styling */
.swiper {
  overflow-x: hidden !important;
  padding-top: 15px; 
}

.swiper-slide [class*="absolute"] {
  z-index: 100 !important;
  position: absolute !important;
}

/* Smooth transitions for swiper */
.swiper-wrapper {
  transition-timing-function: ease-out !important;
}

/* Make active slide transitions smoother */
.swiper-slide-active {
  transition: transform 0.4s ease;
}

/* Improve touch gesture fluidity */
.swiper-container {
  touch-action: pan-y;
} 