import React, { ReactNode } from "react";
import { Select, SelectItem, SelectProps } from "@nextui-org/react";
import BaseLabel from "../BaseLabel";

export interface BaseSelectProps extends Omit<SelectProps, "children"> {
  options?: Array<{ value: string; label: string }>;
  helperText?: string;
  className?: string;
  selectClassName?: string;
  labelClassName?: string;
  children?: ReactNode;
}

const BaseSelect: React.FC<BaseSelectProps> = ({
  label,
  helperText,
  errorMessage,
  className = "",
  selectClassName = "",
  labelClassName = "",
  options,
  children,
  ...props
}) => {
  const combinedClassName = `w-full ${className}`;
  const combinedSelectClassName = `
    bg-transparent 
    border border-[#828282B2]
    rounded-md 
    transition-all duration-200
    hover:border-gray-400
    focus:border-blue-500
    ${selectClassName} 
    ${errorMessage ? "border-red-500 hover:border-red-600" : ""}
  `;

  return (
    <div className={combinedClassName}>
      {label && (
        <BaseLabel className={labelClassName}>
          {label} {props.isRequired && "*"}
        </BaseLabel>
      )}
      <Select
        {...props}
        aria-label={label}
        variant="bordered"
        errorMessage={errorMessage}
        description={helperText}
        classNames={{
          base: "max-w-full",
          trigger: combinedSelectClassName,
          value: "text-black ",
          listbox: "bg-white",
          innerWrapper: "bg-transparent",
          description: "text-xs",
          popoverContent:
            "[&_li:hover]:bg-[#e7e7e7] [&_li[aria-selected=true]]:bg-[#e7e7e7]",
        }}
      >
        {children ||
          (options &&
            options.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            )))}
      </Select>
    </div>
  );
};

export default BaseSelect;
