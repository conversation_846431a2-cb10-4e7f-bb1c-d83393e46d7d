import React from "react";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import BaseSelect, { BaseSelectProps } from "./BaseSelect";

interface FormControlledSelectProps<TFieldValues extends FieldValues>
  extends Omit<BaseSelectProps, "name" | "onChange" | "onBlur" | "value"> {
  name: FieldPath<TFieldValues>;
  control: Control<TFieldValues>;
  rules?: any;
  options?:
    | {
        label: string;
        value: string;
      }[]
    | [];
}

function FormControlledSelect<TFieldValues extends FieldValues>({
  name,
  control,
  rules,
  children,
  options = [],
  ...rest
}: FormControlledSelectProps<TFieldValues>) {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) => {
        const selectedOption = options.find((opt) => opt.value === field.value);

        return (
          <BaseSelect
            {...field}
            {...rest}
            options={options}
            errorMessage={error?.message}
            isInvalid={!!error}
            value={selectedOption?.value || ""}
            defaultSelectedKeys={field.value ? [field.value] : []}
            selectedKeys={field.value ? [field.value] : []}
          >
            {children}
          </BaseSelect>
        );
      }}
    />
  );
}

export default FormControlledSelect;
