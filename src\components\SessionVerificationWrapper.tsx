"use client";

import { useSessionVerification } from "@/lib/hooks/useSessionVerification";
import { ReactNode, useEffect } from "react";

export function SessionVerificationWrapper({
  children,
}: {
  children: ReactNode;
}) {
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error("Unhandled rejection:", event.reason);
    };

    window.addEventListener("unhandledrejection", handleUnhandledRejection);
    return () => {
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection
      );
    };
  }, []);

  useSessionVerification();

  return <>{children}</>;
}
