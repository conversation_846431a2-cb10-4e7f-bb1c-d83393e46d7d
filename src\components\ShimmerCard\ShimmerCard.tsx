import { Card, Skeleton } from "@nextui-org/react";

const ShimmerCard = () => {
  return (
    <Card
      className="h-max pb-3 shadow-none border-1.5 w-[40vw] md:w-[25vw] lg:w-full"
      radius="lg"
    >
      <Skeleton className="rounded-lg rounded-b-none aspect-square">
        <div className="rounded-lg bg-secondary"></div>
      </Skeleton>
      <div className="p-2">
        <Skeleton className="w-3/5 rounded-lg mb-8">
          <div className="h-4 w-3/5 rounded-lg bg-default-200"></div>
        </Skeleton>
        <div className="w-full grid gap-3 md:flex md:justify-between">
          <div className="w-full flex flex-col gap-2">
            <Skeleton className="h-3 w-3/5 rounded-lg" />
            <Skeleton className="h-3 w-4/5 rounded-lg" />
          </div>
          <div>
            <Skeleton className="flex rounded-md w-full h-6" />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ShimmerCard;
