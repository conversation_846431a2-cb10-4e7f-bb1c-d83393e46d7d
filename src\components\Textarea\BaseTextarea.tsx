import React from "react";
import { Textarea, TextAreaProps } from "@nextui-org/react";
import BaseLabel from "../BaseLabel";

export interface BaseTextareaProps extends Omit<TextAreaProps, "css"> {
  helperText?: string;
  className?: string;
  textareaClassName?: string;
  labelClassName?: string;
}

const BaseTextarea: React.FC<BaseTextareaProps> = ({
  label,
  helperText,
  errorMessage,
  className = "",
  textareaClassName = "",
  labelClassName = "",
  ...props
}) => {
  const combinedClassName = `w-full ${className}`;

  return (
    <div className={combinedClassName}>
      {label && <BaseLabel className={labelClassName}>{label}</BaseLabel>}
      <Textarea
        {...props}
        variant="bordered"
        errorMessage={errorMessage}
        description={helperText}
        classNames={{
          base: "max-w-full",
          input: `
            ${errorMessage ? "border-red-500 dark:border-red-400" : ""}
            ${textareaClassName}
          `,
          innerWrapper: "",
          inputWrapper: "bg-transparent border-1 border-[#828282B2] rounded-lg",
          description: "text-xs text-gray-500 mt-1",
          errorMessage: "text-xs text-red-500 mt-1",
        }}
      />
    </div>
  );
};

export default BaseTextarea;
