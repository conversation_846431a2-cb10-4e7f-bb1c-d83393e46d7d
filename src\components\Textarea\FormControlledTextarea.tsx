import React from "react";
import { Controller, Control, FieldValues, FieldPath } from "react-hook-form";
import BaseTextarea, { BaseTextareaProps } from "./BaseTextarea";

interface FormControlledTextareaProps<TFieldValues extends FieldValues>
  extends Omit<BaseTextareaProps, "name" | "onChange" | "onBlur" | "value"> {
  name: FieldPath<TFieldValues>;
  control: Control<TFieldValues>;
  rules?: any;
}

function FormControlledTextarea<TFieldValues extends FieldValues>({
  name,
  control,
  rules,
  ...rest
}: FormControlledTextareaProps<TFieldValues>) {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) => (
        <BaseTextarea
          {...field}
          {...rest}
          errorMessage={error?.message}
          isInvalid={!!error}
        />
      )}
    />
  );
}

export default FormControlledTextarea;
