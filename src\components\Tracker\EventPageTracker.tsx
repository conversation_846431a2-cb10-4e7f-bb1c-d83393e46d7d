"use client";

import { useEffect } from "react";
import { useMetaPixel } from "@/lib/hooks/useMetaPixel";
import { usePixel } from "@/lib/Providers/FacebookPixelProvider";
import { useThirdPartyPixels } from "@/lib/Providers/ThirdPartyPixelsProvider";
import { useThirdPartyPixelTracking } from "@/lib/hooks/useThirdPartyPixelTracking";

interface EventPageTrackerProps {
  event: {
    id: string;
    title: string;
    pixelId?: string;
    date: string;
    pixels?: {
      meta?: string;
      ga4?: string;
      snap?: string;
      tiktok?: string;
    };
  };
}

export default function EventPageTracker({ event }: EventPageTrackerProps) {
  const { trackEventView: trackMetaEventView } = useMetaPixel();
  const { registerPixel } = usePixel();
  const { registerGA4Pixel, registerSnapPixel, registerTikTokPixel } =
    useThirdPartyPixels();
  const { trackEventView: trackThirdPartyEventView } =
    useThirdPartyPixelTracking();

  // Register all available pixels as early as possible
  useEffect(() => {
    if (process.env.NODE_ENV !== "production") return;

    const metaId = (event as any)?.pixels?.meta || event.pixelId;
    const ga4Id = (event as any)?.pixels?.ga4;
    const snapId = (event as any)?.pixels?.snap;
    const tiktokId = (event as any)?.pixels?.tiktok;

    // Register Meta pixel
    if (metaId) {
      registerPixel(metaId);
    }

    // Register third-party pixels
    if (ga4Id) {
      registerGA4Pixel(ga4Id);
    }

    if (snapId) {
      registerSnapPixel(snapId);
    }

    if (tiktokId) {
      registerTikTokPixel(tiktokId);
    }
  }, [
    event?.pixelId,
    (event as any)?.pixels?.meta,
    (event as any)?.pixels?.ga4,
    (event as any)?.pixels?.snap,
    (event as any)?.pixels?.tiktok,
    registerPixel,
    registerGA4Pixel,
    registerSnapPixel,
    registerTikTokPixel,
  ]);

  // Track the view events
  useEffect(() => {
    // Only trigger if we have all required data
    if (!event?.id || !event?.title || !event?.date) {
      return;
    }

    // Track events with a small delay to ensure pixels are ready
    const timer = setTimeout(() => {
      // Track Meta pixel event
      const metaIdView = (event as any)?.pixels?.meta || event.pixelId;
      if (metaIdView) {
        trackMetaEventView({
          ...(event as any),
          organizerPixelId: metaIdView,
          pixels: (event as any)?.pixels,
        } as any);
      }

      // Track third-party pixel events
      const hasThirdPartyPixels =
        (event as any)?.pixels?.ga4 ||
        (event as any)?.pixels?.snap ||
        (event as any)?.pixels?.tiktok;

      if (hasThirdPartyPixels) {
        trackThirdPartyEventView({
          id: event.id,
          title: event.title,
          date: event.date,
          pixels: (event as any)?.pixels,
        });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [event, trackMetaEventView, trackThirdPartyEventView]);

  return null;
}
