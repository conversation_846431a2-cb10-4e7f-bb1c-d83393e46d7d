"use client";

import { useEffect } from "react";
import { useMetaPixel } from "@/lib/hooks/useMetaPixel";
import { usePixel } from "@/lib/Providers/FacebookPixelProvider";
import { useThirdPartyPixels } from "@/lib/Providers/ThirdPartyPixelsProvider";
import { useThirdPartyPixelTracking } from "@/lib/hooks/useThirdPartyPixelTracking";

interface EventPageTrackerProps {
  event: {
    id: string;
    title: string;
    pixelId?: string;
    date: string;
    pixels?: {
      meta?: string;
      ga4?: string;
      snap?: string;
      tiktok?: string;
    };
    // Add support for multiple organizers
    mainOrganizer?: {
      pixels?: {
        meta?: string;
        ga4?: string;
        snap?: string;
        tiktok?: string;
      };
    };
    collaborators?: Array<{
      id: string;
      pixels?: {
        meta?: string;
        ga4?: string;
        snap?: string;
        tiktok?: string;
      };
    }>;
  };
}

export default function EventPageTracker({ event }: EventPageTrackerProps) {
  const { trackEventView: trackMetaEventView } = useMetaPixel();
  const { registerPixel } = usePixel();
  const { registerGA4Pixel, registerSnapPixel, registerTikTokPixel } =
    useThirdPartyPixels();
  const { trackEventView: trackThirdPartyEventView } =
    useThirdPartyPixelTracking();

  // Helper function to collect and deduplicate all pixels
  const collectAllUniquePixels = () => {
    const allPixels = {
      meta: new Set<string>(),
      ga4: new Set<string>(),
      snap: new Set<string>(),
      tiktok: new Set<string>(),
    };

    // Add event-level pixels
    if (event.pixels?.meta) {
      allPixels.meta.add(event.pixels.meta);
    }
    if (event.pixels?.ga4) {
      allPixels.ga4.add(event.pixels.ga4);
    }
    if (event.pixels?.snap) {
      allPixels.snap.add(event.pixels.snap);
    }
    if (event.pixels?.tiktok) {
      allPixels.tiktok.add(event.pixels.tiktok);
    }

    // Add main organizer pixels
    if (event.mainOrganizer?.pixels?.meta) {
      allPixels.meta.add(event.mainOrganizer.pixels.meta);
    }
    if (event.mainOrganizer?.pixels?.ga4) {
      allPixels.ga4.add(event.mainOrganizer.pixels.ga4);
    }
    if (event.mainOrganizer?.pixels?.snap) {
      allPixels.snap.add(event.mainOrganizer.pixels.snap);
    }
    if (event.mainOrganizer?.pixels?.tiktok) {
      allPixels.tiktok.add(event.mainOrganizer.pixels.tiktok);
    }

    // Add collaborator pixels
    if (event.collaborators) {
      event.collaborators.forEach((collaborator) => {
        if (collaborator.pixels?.meta) {
          allPixels.meta.add(collaborator.pixels.meta);
        }
        if (collaborator.pixels?.ga4) {
          allPixels.ga4.add(collaborator.pixels.ga4);
        }
        if (collaborator.pixels?.snap) {
          allPixels.snap.add(collaborator.pixels.snap);
        }
        if (collaborator.pixels?.tiktok) {
          allPixels.tiktok.add(collaborator.pixels.tiktok);
        }
      });
    }

    // Convert Sets to arrays
    const uniquePixels = {
      meta: Array.from(allPixels.meta),
      ga4: Array.from(allPixels.ga4),
      snap: Array.from(allPixels.snap),
      tiktok: Array.from(allPixels.tiktok),
    };

    return uniquePixels;
  };

  // Register all available pixels as early as possible
  useEffect(() => {
    if (process.env.NODE_ENV !== "production") {
      return;
    }

    const uniquePixels = collectAllUniquePixels();

    // Register Meta pixels
    uniquePixels.meta.forEach((pixelId) => {
      registerPixel(pixelId);
    });

    // Register GA4 pixels
    uniquePixels.ga4.forEach((pixelId) => {
      registerGA4Pixel(pixelId);
    });

    // Register Snap pixels
    uniquePixels.snap.forEach((pixelId) => {
      registerSnapPixel(pixelId);
    });

    // Register TikTok pixels
    uniquePixels.tiktok.forEach((pixelId) => {
      registerTikTokPixel(pixelId);
    });
  }, [
    event?.id,
    // Stabilize object references to prevent infinite re-renders
    event?.pixels?.meta,
    event?.pixels?.ga4,
    event?.pixels?.snap,
    event?.pixels?.tiktok,
    event?.mainOrganizer?.pixels?.meta,
    event?.mainOrganizer?.pixels?.ga4,
    event?.mainOrganizer?.pixels?.snap,
    event?.mainOrganizer?.pixels?.tiktok,
    // Use JSON.stringify for collaborators to create stable reference
    event?.collaborators
      ? JSON.stringify(
          event.collaborators.map((c) => ({ id: c.id, pixels: c.pixels }))
        )
      : null,
    registerPixel,
    registerGA4Pixel,
    registerSnapPixel,
    registerTikTokPixel,
  ]);

  // Track the view events across all pixels
  useEffect(() => {
    // Only trigger if we have all required data
    if (!event?.id || !event?.title || !event?.date) {
      return;
    }

    // Track events with a small delay to ensure pixels are ready
    const timer = setTimeout(() => {
      const uniquePixels = collectAllUniquePixels();

      // Track Meta pixel events for all unique Meta pixels
      if (uniquePixels.meta.length > 0) {
        uniquePixels.meta.forEach((pixelId) => {
          trackMetaEventView({
            ...(event as any),
            organizerPixelId: pixelId,
            pixels: {
              meta: pixelId,
              ga4: uniquePixels.ga4[0], // Use first GA4 pixel if available
              snap: uniquePixels.snap[0], // Use first Snap pixel if available
              tiktok: uniquePixels.tiktok[0], // Use first TikTok pixel if available
            },
          } as any);
        });
      }

      // Track third-party pixel events
      const hasThirdPartyPixels =
        uniquePixels.ga4.length > 0 ||
        uniquePixels.snap.length > 0 ||
        uniquePixels.tiktok.length > 0;

      if (hasThirdPartyPixels) {
        trackThirdPartyEventView({
          id: event.id,
          title: event.title,
          date: event.date,
          pixels: {
            meta: uniquePixels.meta[0], // Use first Meta pixel if available
            ga4: uniquePixels.ga4[0], // Use first GA4 pixel if available
            snap: uniquePixels.snap[0], // Use first Snap pixel if available
            tiktok: uniquePixels.tiktok[0], // Use first TikTok pixel if available
          },
        });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [
    // Stabilize event object reference to prevent infinite re-renders
    event?.id,
    event?.title,
    event?.date,
    // Stabilize pixel references
    event?.pixels?.meta,
    event?.pixels?.ga4,
    event?.pixels?.snap,
    event?.pixels?.tiktok,
    event?.mainOrganizer?.pixels?.meta,
    event?.mainOrganizer?.pixels?.ga4,
    event?.mainOrganizer?.pixels?.snap,
    event?.mainOrganizer?.pixels?.tiktok,
    // Use JSON.stringify for collaborators to create stable reference
    event?.collaborators
      ? JSON.stringify(
          event.collaborators.map((c) => ({ id: c.id, pixels: c.pixels }))
        )
      : null,
    trackMetaEventView,
    trackThirdPartyEventView,
  ]);

  return null;
}
