import React from "react";
import { useQRCode } from "next-qrcode";

interface IQRCodeProps {
  url: string;
}

export function QRCode({
  url = "https://github.com/bunlong/next-qrcode",
}: IQRCodeProps) {
  const { Canvas } = useQRCode();

  return (
    <Canvas
      text={url}
      options={{
        type: "image/jpeg",
        quality: 0.3,
        errorCorrectionLevel: "M",
        margin: 2,
        scale: 1.2,
        width: 5,
        color: {
          dark: "#010599FF",
          light: "#e3dcd2",
        },
      }}
    />
  );
}
