import { useSessionData } from "@/lib/hooks/useSession";
import { useMeMutation } from "@/lib/redux/slices/user/userApi";
import { handleApiError } from "@/lib/utils/errorUtils";
import { TOASTS } from "@/lib/utils/constants";
import { getInitials, getName } from "@/lib/utils/string";
import { cn } from "@nextui-org/react";
import Image from "next/image";
import React, { useRef } from "react";
import toast from "react-hot-toast";
import { MdEdit } from "react-icons/md";
import { useImageUpload } from "@/components/ImageSelector/useImageUpload";
export * from "./QRCode";

interface UserProfileProps {
  className?: string;
}

export function UserProfile({ className }: UserProfileProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data: session, status, update } = useSessionData();
  const [updateUserMutation] = useMeMutation();

  const { handleImageSelect, isUploading } = useImageUpload({
    fileSize: 9.5 * 1024 * 1024,
    onSuccess: async (url) => {
      if (url) {
        try {
          await updateUserMutation({ avatar: url }).unwrap();
          update();
          toast.success(TOASTS.PROFILE_PIC_UPDATED);
        } catch (error) {
          handleApiError(error, "Failed to update profile picture. Please try again.");
        }
      }
    },
  });

  const handleEditClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await handleImageSelect(file);
      } catch (error) {
        console.error(error);
      }
    }
  };

  return (
    <div className={cn("relative group w-36 h-36", className)}>
      {session?.user?.avatar ? (
        <Image
          src={session?.user?.avatar}
          alt="User avatar"
          layout="fill"
          className="object-cover rounded-full"
          sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 144px"
        />
      ) : (
        <div className="w-full h-full bg-blue-500 rounded-full flex items-center justify-center">
          <p className="text-[50px] text-white">
            {getInitials(getName(session?.user))}
          </p>
        </div>
      )}
      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 rounded-full transition-opacity">
        <button
          onClick={() => handleEditClick()}
          className="text-white text-lg"
          disabled={isUploading}
        >
          <MdEdit />
        </button>
      </div>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
        onChange={handleFileChange}
      />
    </div>
  );
}
