'use client';

import { useState, useCallback } from 'react';
import { UseFormSetValue } from 'react-hook-form';
import toast from 'react-hot-toast';

export interface VehicleAnalysisResult {
  make?: string | null;
  model?: string | null;
  year?: number | null;
  vehicle_type?: string | null;
  confidence?: number | null;
  error?: string | null;
}

export interface UseVehicleAnalysisProps {
  setValue?: UseFormSetValue<any>;
  onAnalysisComplete?: (data: {
    make?: string;
    model?: string;
    year?: string;
    vehicle_type?: string;
  }) => void;
  isEnabled?: boolean;
  vehicleAnalysisOptions?: {
    vehicleTypes: Array<{ label: string; value: string }>;
    makeOptions: Array<{ label: string; value: string }>;
  };
}

export const useVehicleAnalysis = ({
  setValue,
  onAnalysisComplete,
  isEnabled = true,
  vehicleAnalysisOptions,
}: UseVehicleAnalysisProps = {}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<VehicleAnalysisResult | null>(null);

  const analyzeVehicleImage = useCallback(async (file: File) => {
    if (!isEnabled || !file) return;

    setIsAnalyzing(true);
    
    try {
      // Dynamic import for browser-image-compression (lazy loading)
      const imageCompression = (await import('browser-image-compression')).default;

      // Compression options optimized for vehicle analysis
      const compressionOptions = {
        maxSizeMB: 5,
        maxWidthOrHeight: 1920 * 2,
        useWebWorker: true,
        onProgress: (progress: number) => {
          console.log(`Compression progress: ${progress}%`);
        },
      };

      // Compress the image
      const compressedFile = await imageCompression(file, compressionOptions);
      
      // Ensure the compressed file has the correct name and extension
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const compressedFileWithName = new File(
        [compressedFile], 
        file.name || `compressed_image.${fileExtension}`, 
        { 
          type: compressedFile.type || file.type || 'image/jpeg',
          lastModified: Date.now()
        }
      );
      
      // Log compression results
      console.log(`Original file size: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
      console.log(`Compressed file size: ${(compressedFileWithName.size / 1024 / 1024).toFixed(2)} MB`);
      console.log(`Compression ratio: ${((1 - compressedFileWithName.size / file.size) * 100).toFixed(1)}%`);
      console.log(`File name: ${compressedFileWithName.name}`);
      console.log(`File type: ${compressedFileWithName.type}`);
      
      // Create form data for the API call
      const formData = new FormData();
      formData.append('image_file', compressedFileWithName);

      // Call the vehicle analysis API
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}api/vehicleanalysis/`, {
        method: 'POST',
        body: formData,
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Analysis failed: ${response.status} ${response.statusText}`);
      }

      const result: VehicleAnalysisResult = await response.json();
      
      setAnalysisResult(result);

      if (result.error) {
        console.error('Vehicle analysis API error:', result.error);
        return;
      }

      const shouldAutoPopulate = result.confidence && result.confidence > 0.5;

      if (shouldAutoPopulate && setValue) {
        if (result.model) {
          setValue('vehicle.modelName', result.model, { shouldValidate: true });
        }
        
        if (result.year) {
          setValue('vehicle.year', result.year.toString(), { shouldValidate: true });
        }

        if (result.vehicle_type && vehicleAnalysisOptions?.vehicleTypes) {
          console.log('Setting vehicle type:', result.vehicle_type);
          
          const matchedVehicleType = vehicleAnalysisOptions.vehicleTypes.find(
            type => type.label.toLowerCase() === result.vehicle_type!.toLowerCase()
          );
          
          if (matchedVehicleType) {
            console.log('Matched vehicle type:', matchedVehicleType);
            setValue('vehicle.vehicleType', matchedVehicleType.value, { shouldValidate: true });
          } 
        }

        // Auto-populate make
        if (result.make && vehicleAnalysisOptions?.makeOptions) {
          
          // Find matching make by label (case insensitive)
          const matchedMake = vehicleAnalysisOptions.makeOptions.find(
            make => make.label.toLowerCase() === result.make!.toLowerCase()
          );
          
          if (matchedMake) {
            setValue('vehicle.make', matchedMake.value, { shouldValidate: true });
            setValue('vehicle.customMakeName', matchedMake.label, { shouldValidate: true });
          }
        }
      }

      // Call the callback if provided
      if (onAnalysisComplete) {
        onAnalysisComplete({
          make: result.make || undefined,
          model: result.model || undefined,
          year: result.year?.toString() || undefined,
          vehicle_type: result.vehicle_type || undefined,
        });
      }

      // Log results for debugging
      console.log('Vehicle analysis result:', result);
      if (shouldAutoPopulate) {
        console.log('Auto-populated fields based on analysis');
      } else {
        console.log('Confidence too low for auto-population:', result.confidence);
      }

    } catch (error) {
      console.error('Vehicle analysis error:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [isEnabled, setValue, onAnalysisComplete, vehicleAnalysisOptions]);

  return {
    analyzeVehicleImage,
    isAnalyzing,
    analysisResult,
  };
};

export default useVehicleAnalysis;