import React, { useState } from "react";
import Image from "next/image";

interface VehicleImageDisplayProps {
  imageUrl?: string;
  fallbackSrc?: string;
  width?: number;
  height?: number;
  className?: string;
  alt?: string;
}

const VehicleImageDisplay: React.FC<VehicleImageDisplayProps> = ({
  imageUrl = "",
  fallbackSrc = "/images/placeholder.png",
  width = 100,
  height = 100,
  className = "object-cover rounded-md",
  alt = "Vehicle",
}) => {
  const [imageError, setImageError] = useState(false);
  const [nextImageError, setNextImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleNextImageError = () => {
    setNextImageError(true);
  };

  if (!imageUrl || imageError) {
    return (
      <div
        className={`w-full h-full bg-gray-200 rounded-md flex items-center justify-center`}
      >
        <span className="text-xs text-gray-500">No image</span>
      </div>
    );
  }

  // If Next.js Image fails, fallback to regular img tag
  if (nextImageError) {
    return (
      <img
        src={imageUrl}
        alt={alt}
        width={width}
        height={height}
        className={`${className} w-full h-full`}
        onError={handleImageError}
      />
    );
  }

  return (
    <Image
      src={imageUrl}
      alt={alt}
      width={width}
      height={height}
      className={`${className} w-full h-full`}
      onError={handleNextImageError}
      loading="lazy"
      unoptimized={true}
    />
  );
};

export default React.memo(VehicleImageDisplay);
