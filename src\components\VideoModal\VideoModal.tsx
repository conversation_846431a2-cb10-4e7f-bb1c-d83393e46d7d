"use client";

import React, { useRef, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { XIcon } from "lucide-react";
import { Button } from "../ui/button";

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoSrc: string;
}

export const VideoModal: React.FC<VideoModalProps> = ({
  isOpen,
  onClose,
  videoSrc,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isVideoLoading, setIsVideoLoading] = useState(true);

  useEffect(() => {
    if (isOpen && videoRef.current) {
      // Reset loading state when modal opens
      setIsVideoLoading(true);
      
      // Auto-play when modal opens
      videoRef.current.play().catch((error) => {
        console.log("Auto-play was prevented:", error);
      });
    }
  }, [isOpen]);

  // Reset video when modal closes
  const handleClose = () => {
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
    }
    setIsVideoLoading(true);
    onClose();
  };

  // Handle video loading events
  const handleVideoLoadedData = () => {
    setIsVideoLoading(false);
  };

  const handleVideoLoadStart = () => {
    setIsVideoLoading(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-4xl w-[95vw] p-0 overflow-hidden bg-black border-none">
        <div className="py-1 px-3 flex justify-end items-center gap-2">
            <Button  variant="ghost" onClick={handleClose} className="text-white hover:bg-black hover:text-white">
                <XIcon className="w-6 h-6" />
            </Button>
        </div>
        
        <div className="relative w-full aspect-video bg-black rounded-b-lg overflow-hidden">
          {/* Only render video when modal is open to prevent preloading */}
          {isOpen && (
            <video
              ref={videoRef}
              className="w-full h-full object-contain"
              autoPlay
              playsInline
              preload="none"
              muted
              onLoadedData={handleVideoLoadedData}
              onLoadStart={handleVideoLoadStart}
              onError={() => setIsVideoLoading(false)}
            >
              <source src={videoSrc} type="video/mp4" />
              <source src={videoSrc} type="video/quicktime" />
              Your browser does not support the video tag.
            </video>
          )}
          {/* Loading placeholder - only show when video is loading */}
          {isOpen && isVideoLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};


