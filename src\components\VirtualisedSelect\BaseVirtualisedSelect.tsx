import React from "react";
import AsyncSelect from "react-select/async";
import BaseLabel from "../BaseLabel";
import { StylesConfig, ActionMeta, SingleValue } from "react-select";

export interface Option {
  label: string;
  value: string;
}

interface BaseVirtualisedSelectProps {
  label?: string;
  loadOptions: (
    inputValue: string,
    callback: (options: Option[]) => void
  ) => void;
  isDisabled?: boolean;
  isLoading?: boolean;
  onChange: (
    newValue: SingleValue<Option>,
    actionMeta: ActionMeta<Option>
  ) => void;
  value: Option | null;
  placeholder?: string;
  className?: string;
  [key: string]: any;
}

const BaseVirtualisedSelect: React.FC<BaseVirtualisedSelectProps> = ({
  label,
  loadOptions,
  isDisabled,
  isLoading,
  onChange,
  value,
  placeholder,
  className,
  variant,
  ...props
}) => {
  const customStyles: StylesConfig<Option, false> = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "40px",
      height: "40px",
      boxShadow: "none",
      fontSize: "14px",
      borderColor: state.isFocused
        ? "#a8a8a8"
        : variant === "filled"
        ? "white"
        : "#a8a8a8",
      "&:hover": {
        borderColor: "#a8a8a8",
        backgroundColor: variant === "filled" ? "#E4E4E7" : "white",
      },
      cursor: "pointer",
      borderRadius: "8px",
      padding: "0 8px",
      backgroundColor: isDisabled
        ? "#F4F4F5"
        : variant === "filled"
        ? "#F4F4F5"
        : "white",
    }),
    valueContainer: (provided) => ({
      ...provided,
      padding: "0",
    }),
    input: (provided) => ({
      ...provided,
      margin: "0",
      padding: "0",
    }),
    indicatorSeparator: () => ({
      display: "none",
    }),
    dropdownIndicator: (provided, state) => ({
      ...provided,
      color: state.isFocused ? "#a8a8a8" : "#71717A",
      transform: state.isFocused ? "rotate(180deg)" : "rotate(0deg)",
      transition: "transform 0.2s ease-in-out",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      "& svg": {
        width: "15px",
        height: "15px",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      color: "#18181B",
      padding: "6px 12px",
      fontSize: "13px",
    }),
    menu: (provided) => ({
      ...provided,
      borderRadius: "16px",
      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
      overflow: "hidden",
      border: "1px solid #E4E4E7",
      marginTop: props.menuPlacement === "top" ? "-4px" : "0px",
      marginBottom: props.menuPlacement === "top" ? "0px" : "0px",
    }),
    menuList: (provided) => ({
      ...provided,
      padding: "10px",
    }),
    singleValue: (provided) => ({
      ...provided,
      color: "#18181B",
    }),
    placeholder: (provided) => ({
      ...provided,
      color: "#A1A1AA",
    }),
  };

  return (
    <div className={className}>
      {label && (
        <BaseLabel className="mb-1">
          {label} {props.isRequired && "*"}
        </BaseLabel>
      )}
      <AsyncSelect<Option>
        cacheOptions
        loadOptions={loadOptions}
        defaultOptions
        isDisabled={isDisabled}
        isLoading={isLoading}
        onChange={onChange}
        value={value}
        placeholder={placeholder || "Select"}
        styles={customStyles}
        className="react-select-container"
        classNamePrefix="react-select"
        classNames={{
          menuList: () => "custom-scrollbar",
        }}
        {...props}
      />
    </div>
  );
};

export default BaseVirtualisedSelect;
