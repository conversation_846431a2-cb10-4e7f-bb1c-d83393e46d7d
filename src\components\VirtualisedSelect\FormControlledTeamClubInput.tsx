import React, { useState, useEffect } from "react";
import { Controller, Control } from "react-hook-form";
import TeamClubInput, { TeamClubOption } from "./TeamClubInput";

interface FormControlledTeamClubInputProps {
  name: string;
  control: Control<any>;
  label?: string;
  loadOptions: (
    inputValue: string,
    callback: (options: TeamClubOption[]) => void
  ) => void;
  isDisabled?: boolean;
  isLoading?: boolean;
  placeholder?: string;
  onChange?: (value: string) => void;
  className?: string;
  defaultOptions?: TeamClubOption[];
  [key: string]: any;
  helperText?: string;
}

const FormControlledTeamClubInput: React.FC<
  FormControlledTeamClubInputProps
> = ({
  name,
  control,
  label,
  loadOptions,
  isDisabled,
  isLoading,
  placeholder,
  onChange,
  className,
  errors,
  defaultOptions = [],
  helperText,
  ...props
}) => {
  const [options, setOptions] = useState<TeamClubOption[]>(defaultOptions);

  useEffect(() => {
    setOptions(defaultOptions);
  }, [defaultOptions]);

  useEffect(() => {
    loadOptions("", (loadedOptions) => {
      setOptions((prevOptions) => {
        const existingValues = new Set(prevOptions.map((opt) => opt.value));
        const newUniqueOptions = loadedOptions.filter(
          (opt) => !existingValues.has(opt.value)
        );
        return [...prevOptions, ...newUniqueOptions];
      });
    });
  }, [loadOptions]);

  return (
    <div>
      <Controller
        name={name}
        control={control}
        render={({ field }) => {
          return (
            <TeamClubInput
              {...field}
              label={label}
              loadOptions={loadOptions}
              isDisabled={isDisabled}
              isLoading={isLoading}
              placeholder={placeholder}
              className={className}
              onChange={(value) => {
                if (!isDisabled) {
                  field.onChange(value);
                  if (onChange) onChange(value);
                }
              }}
              value={field.value || ""}
              {...props}
            />
          );
        }}
      />
      {errors && errors[name] && (
        <p className="text-red-500 text-xs mt-1">{errors[name]?.message}</p>
      )}
      {helperText && <p className="mt-1 text-gray-500 text-xs">{helperText}</p>}
    </div>
  );
};

export default FormControlledTeamClubInput;
