import React, { useState, useEffect } from "react";
import { Controller, Control } from "react-hook-form";
import BaseVirtualisedSelect, { Option } from "./BaseVirtualisedSelect";

interface FormControlledBaseVirtualisedSelectProps {
  name: string;
  control: Control<any>;
  label?: string;
  loadOptions: (
    inputValue: string,
    callback: (options: Option[]) => void
  ) => void;
  isDisabled?: boolean;
  isLoading?: boolean;
  placeholder?: string;
  onChange?: (selectedOption: Option | null) => void;
  className?: string;
  defaultOptions?: Option[];
  [key: string]: any;
  helperText?: string;
}

const FormControlledBaseVirtualisedSelect: React.FC<
  FormControlledBaseVirtualisedSelectProps
> = ({
  name,
  control,
  label,
  loadOptions,
  isDisabled,
  isLoading,
  placeholder,
  onChange,
  className,
  errors,
  defaultOptions = [],
  helperText,
  ...props
}) => {
  const [options, setOptions] = useState<Option[]>(defaultOptions);

  useEffect(() => {
    setOptions(defaultOptions);
  }, [defaultOptions]);

  useEffect(() => {
    loadOptions("", (loadedOptions) => {
      setOptions((prevOptions) => {
        // Merge new options with existing ones, avoiding duplicates
        const existingValues = new Set(prevOptions.map((opt) => opt.value));
        const newUniqueOptions = loadedOptions.filter(
          (opt) => !existingValues.has(opt.value)
        );
        return [...prevOptions, ...newUniqueOptions];
      });
    });
  }, [loadOptions]);

  return (
    <div>
      <Controller
        name={name}
        control={control}
        render={({ field }) => {
          const selectedOption =
            options?.find((option) => option?.value === field?.value) ||
            (field?.value ? { label: field.value, value: field.value } : null);

          return (
            <BaseVirtualisedSelect
              {...field}
              label={label}
              loadOptions={loadOptions}
              isDisabled={isDisabled}
              isLoading={isLoading}
              placeholder={placeholder}
              className={className}
              onChange={(newValue, actionMeta) => {
                // Update the form field with just the value
                field.onChange(newValue ? newValue.value : "");

                // If the selected option is a new custom value, ensure it's added to local options state
                if (newValue) {
                  setOptions((prev) => {
                    if (prev.some((opt) => opt.value === newValue.value)) {
                      return prev;
                    }
                    return [
                      ...prev,
                      { label: newValue.value, value: newValue.value },
                    ];
                  });
                }

                if (onChange) {
                  onChange(newValue);
                }
              }}
              value={selectedOption || null}
              defaultOptions={options}
              {...props}
            />
          );
        }}
      />
      {errors && errors[name] && (
        <p className="text-red-500 text-xs">{errors[name]?.message}</p>
      )}
      {helperText && <p className="mt-1 text-gray-500 text-xs">{helperText}</p>}
    </div>
  );
};

export default FormControlledBaseVirtualisedSelect;
