import React, { useState, useEffect, useRef, useCallback } from "react";
import { ChevronDown, Check } from "lucide-react";
import BaseLabel from "../BaseLabel";

export interface TeamClubOption {
  label: string;
  value: string;
  isVerified?: boolean;
}

interface TeamClubInputProps {
  label?: string;
  loadOptions: (
    inputValue: string,
    callback: (options: TeamClubOption[]) => void
  ) => void;
  isDisabled?: boolean;
  isLoading?: boolean;
  onChange: (value: string) => void;
  value: string;
  placeholder?: string;
  className?: string;
  onInputChange?: (value: string) => void;
  isRequired?: boolean;
  variant?: string;
}

const TeamClubInput: React.FC<TeamClubInputProps> = ({
  label,
  loadOptions,
  isDisabled,
  isLoading,
  onChange,
  value,
  placeholder,
  className,
  onInputChange,
  isRequired,
  variant,
}) => {
  const [inputValue, setInputValue] = useState(value || "");
  const [options, setOptions] = useState<TeamClubOption[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const optionRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Load options when input changes
  const handleLoadOptions = useCallback(
    (searchValue: string) => {
      loadOptions(searchValue, (loadedOptions) => {
        setOptions(loadedOptions);
        // Only show dropdown if there are actual matches and input is focused
        const hasMatches =
          loadedOptions.length > 0 &&
          inputRef.current === document.activeElement;
        setIsOpen(hasMatches);
        setHighlightedIndex(-1);
      });
    },
    [loadOptions]
  );

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(newValue);
    if (onInputChange) {
      onInputChange(newValue);
    }
    handleLoadOptions(newValue);
  };

  // Handle option selection
  const handleOptionSelect = (option: TeamClubOption) => {
    setInputValue(option.value);
    onChange(option.value);
    setIsOpen(false);
    setHighlightedIndex(-1);
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === "ArrowDown" && options.length > 0) {
        e.preventDefault();
        setIsOpen(true);
        setHighlightedIndex(0);
      }
      return;
    }

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setHighlightedIndex((prev) =>
          prev < options.length - 1 ? prev + 1 : prev
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : -1));
        break;
      case "Enter":
        e.preventDefault();
        if (highlightedIndex >= 0 && options[highlightedIndex]) {
          handleOptionSelect(options[highlightedIndex]);
        }
        break;
      case "Escape":
        setIsOpen(false);
        setHighlightedIndex(-1);
        inputRef.current?.blur();
        break;
      case "Tab":
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  // Handle input focus
  const handleFocus = () => {
    if (options.length > 0) {
      setIsOpen(true);
    }
  };

  // Handle input blur
  const handleBlur = (e: React.FocusEvent) => {
    // Delay closing to allow option clicks
    setTimeout(() => {
      if (!dropdownRef.current?.contains(document.activeElement)) {
        setIsOpen(false);
        setHighlightedIndex(-1);
      }
    }, 150);
  };

  // Update input value when prop value changes
  useEffect(() => {
    setInputValue(value || "");
  }, [value]);

  // Load initial options
  useEffect(() => {
    handleLoadOptions("");
  }, [handleLoadOptions]);

  // Scroll highlighted option into view
  useEffect(() => {
    if (highlightedIndex >= 0 && optionRefs.current[highlightedIndex]) {
      optionRefs.current[highlightedIndex]?.scrollIntoView({
        block: "nearest",
      });
    }
  }, [highlightedIndex]);

  return (
    <div className={className}>
      {label && (
        <BaseLabel className="mb-1">
          {label} {isRequired && "*"}
        </BaseLabel>
      )}
      <div className="relative">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={isDisabled}
            placeholder={placeholder || ""}
            className={`
              w-full h-10 px-3 pr-10 text-sm border rounded-lg transition-colors
              ${
                isDisabled
                  ? "bg-gray-100 cursor-not-allowed"
                  : variant === "filled"
                  ? "bg-gray-100 hover:bg-gray-200"
                  : "bg-white hover:border-gray-400"
              }
              ${isOpen ? "border-gray-400" : "border-gray-300"}
              focus:outline-none focus:border-gray-400 focus:ring-0
            `}
          />
          {(isLoading || (options.length > 0 && inputValue)) && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
              ) : (
                <ChevronDown
                  className={`w-4 h-4 text-gray-500 transition-transform ${
                    isOpen ? "rotate-180" : ""
                  }`}
                />
              )}
            </div>
          )}
        </div>

        {isOpen && options.length > 0 && (
          <div
            ref={dropdownRef}
            className="absolute z-50 w-full bottom-full mb-0 bg-white border border-gray-200 rounded-[16px] shadow-lg max-h-60 overflow-auto"
          >
            <div className="py-2">
              {options.map((option, index) => (
                <div
                  key={`${option.value}-${index}`}
                  ref={(el) => {
                    optionRefs.current[index] = el;
                  }}
                  className={`
                    mx-2 my-1 rounded-lg px-3 py-2 cursor-pointer text-sm flex items-center justify-between
                    ${
                      highlightedIndex === index
                        ? "bg-[#E4E4E7] text-[#303030]"
                        : "text-[#303030] hover:bg-[#E4E4E7]"
                    }
                  `}
                  onClick={() => handleOptionSelect(option)}
                  onMouseEnter={() => setHighlightedIndex(index)}
                >
                  <span className="flex-1">{option.label}</span>
                  {option.value === inputValue && (
                    <Check className="w-4 h-4 text-blue-500 ml-2" />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TeamClubInput;
