import * as React from "react";

import { cn } from "@/lib/utils";

const Input = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<"input"> & { autoFocus?: boolean; tabIndex?: number }
>(({ className, type, autoFocus = false, tabIndex, ...props }, ref) => {
  return (
    <input
      type={type}
      autoFocus={autoFocus}
      tabIndex={tabIndex}
      className={cn(
        "flex h-9 w-full rounded-[7.83px] border border-input bg-transparent px-3 py-1 text-base transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        className
      )}
      ref={ref}
      {...props}
    />
  );
});
Input.displayName = "Input";

export { Input };
