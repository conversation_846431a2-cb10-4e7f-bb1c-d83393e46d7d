"use client";
import {
  useCreateStoreSessionMutation,
  useUpdateStoreSessionMutation,
} from "@/lib/redux/slices/analytics/api";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useVisibilityChange } from "@/lib/hooks/useVisibilityChange";
import { usePostHog } from 'posthog-js/react';

const INACTIVITY_TIMEOUT = 2 * 60 * 1000;
const DEBOUNCE_TIMEOUT = 5000;

export function StoreSessions({
  orgSlug,
  eventId = null,
}: {
  orgSlug: string;
  eventId?: string | null;
}) {
  const [createStoreSessions] = useCreateStoreSessionMutation();
  const [updateStoreSessions] = useUpdateStoreSessionMutation();
  const [sessionActive, setSessionActive] = useState(false);
  const posthog = usePostHog();
  const inactivityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const isTerminatingRef = useRef<boolean>(false);
  const isInitializingRef = useRef<boolean>(false);
  const lastSessionOperationTimeRef = useRef<number>(0);
  const sessionInitializedRef = useRef<boolean>(false);
  const componentMountedRef = useRef<boolean>(true);
  const lastSessionIdRef = useRef<string | null>(null);

  const getReferrerUrl = useCallback((): string | null => {
    // If we've already consumed the initial referrer in this session, don't report it again.
    if (sessionStorage.getItem('initialReferrerConsumed')) {
      return null;
    }

    try {
      let referrerUrl = posthog?.get_property('$referrer') || posthog?.get_property('$initial_referrer')
      if (!referrerUrl || typeof referrerUrl !== 'string') {
        // Fallback to browser referrer
        referrerUrl = document.referrer || null;
      }


      // If no referrer, return null
      if (!referrerUrl) {
        return null;
      }

      // Check if referrer is from same domain (internal navigation)
      const currentDomain = window.location.hostname;
      const referrerDomain = new URL(referrerUrl).hostname;

      
      // Only return referrer if it's from external domain
      if (referrerDomain !== currentDomain) {
        return referrerUrl;
      }
      
      return null; // Internal referrer, don't track
    } catch (error) {
      console.warn("Error getting referrer:", error);
      // Fallback with domain check
      try {
        const browserReferrer = document.referrer;
        if (!browserReferrer) return null;
        
        const currentDomain = window.location.hostname;
        const referrerDomain = new URL(browserReferrer).hostname;
        
        return referrerDomain !== currentDomain ? browserReferrer : null;
      } catch {
        return null;
      }
    }
  }, [posthog, document.referrer]);

  // Create unique storage key based on orgSlug and eventId
  const getStorageKey = useCallback(() => {
    if (eventId) {
      return `storeSessionAnalytics_${orgSlug}_event_${eventId}`;
    }
    return `storeSessionAnalytics_${orgSlug}`;
  }, [orgSlug, eventId]);

  // Function to setup inactivity timeout
  const setupInactivityCheck = useCallback(() => {
    // Clear any existing timeout
    if (inactivityTimeoutRef.current) {
      clearTimeout(inactivityTimeoutRef.current);
    }

    // Only set timeout if component is still mounted
    if (!componentMountedRef.current) {
      return;
    }

    // Set a new timeout to check for inactivity
    inactivityTimeoutRef.current = setTimeout(() => {
      // Skip if component unmounted
      if (!componentMountedRef.current) {
        return;
      }

      const now = Date.now();
      const timeSinceLastActivity = now - lastActivityRef.current;
      if (timeSinceLastActivity >= INACTIVITY_TIMEOUT && sessionActive) {
        terminateSession();
      } else if (sessionActive) {
        // If still active but didn't hit threshold, check again
        setupInactivityCheck();
      }
    }, Math.min(INACTIVITY_TIMEOUT, 60000)); // Check at least every minute
  }, [sessionActive, orgSlug, eventId]);

  // Prevent operations if they happened too recently
  const shouldSkipOperation = useCallback(() => {
    const now = Date.now();
    const timeSinceLastOperation = now - lastSessionOperationTimeRef.current;

    if (timeSinceLastOperation < DEBOUNCE_TIMEOUT) {
      return true;
    }

    lastSessionOperationTimeRef.current = now;
    return false;
  }, []);

  // Function to terminate the current session
  const terminateSession = useCallback(async () => {
    // Skip if we're already terminating or if we should debounce
    if (isTerminatingRef.current || shouldSkipOperation()) {
      return;
    }

    // Skip if no active session
    if (!sessionActive) {
      return;
    }

    isTerminatingRef.current = true;

    try {
      const storageKey = getStorageKey();
      const sessionAnalyticsData = localStorage.getItem(storageKey);

      if (!sessionAnalyticsData) {
        isTerminatingRef.current = false;
        return;
      }

      const storageData = JSON.parse(sessionAnalyticsData);

      const body = {
        endTime: new Date().toISOString(),
        isActive: false,
      };

      await updateStoreSessions({
        orgSlug,
        sessionId: storageData?.id,
        body,
      }).unwrap();

      // Store the last session ID before clearing
      lastSessionIdRef.current = storageData?.id;

      // Clear session data after termination
      localStorage.removeItem(storageKey);
      setSessionActive(false);
      sessionInitializedRef.current = false;

      // Clear inactivity timeout
      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
        inactivityTimeoutRef.current = null;
      }
    } catch (error) {
      console.error("Error terminating session:", error);
    } finally {
      isTerminatingRef.current = false;
    }
  }, [
    orgSlug,
    updateStoreSessions,
    sessionActive,
    shouldSkipOperation,
    getStorageKey,
    eventId,
  ]);

  // Function to clean up all sessions stored in localStorage
  const cleanupAllSessions = useCallback(() => {
    // Find all keys that match our pattern
    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith("storeSessionAnalytics_")) {
        keys.push(key);
      }
    }

    // Remove all found session data
    keys.forEach((key) => {
      try {
        const data = JSON.parse(localStorage.getItem(key) || "{}");
        if (data.id) {
          // Extract orgSlug from key - handle both formats
          const keyParts = key.split("_");
          let extractedOrgSlug = "";

          if (keyParts.length >= 2) {
            extractedOrgSlug = keyParts[1];
          }

          if (extractedOrgSlug) {
            // Try to send termination signal
            updateStoreSessions({
              orgSlug: extractedOrgSlug,
              sessionId: data.id,
              body: { endTime: new Date().toISOString(), isActive: false },
            });
          }
        }
        localStorage.removeItem(key);
      } catch (e) {
        localStorage.removeItem(key);
      }
    });
  }, [updateStoreSessions]);

  // Define initializeSession and reactivateSession functions using refs to avoid circular dependencies
  const initializeSessionRef = useRef<() => Promise<any>>();
  const reactivateSessionRef = useRef<() => Promise<any>>();

  // Initialize the function refs
  initializeSessionRef.current = async () => {
    // Skip if component is unmounted
    if (!componentMountedRef.current) {
      return;
    }

    // Skip if we're already initializing or if we should debounce
    if (isInitializingRef.current || shouldSkipOperation()) {
      return;
    }

    // Skip if there's an active session or if we've already initialized
    if (sessionActive || sessionInitializedRef.current) {
      return;
    }

    // Skip if no orgSlug is available
    if (!orgSlug) {
      return;
    }

    isInitializingRef.current = true;

    try {
      let body: any = {};
      if (eventId) {
        body.eventId = eventId;
      }

      // Capture referrer URL only on initial session creation (not on reactivation)
      if (typeof window !== "undefined") {
        const referrerUrl = getReferrerUrl();
        if (referrerUrl) {
          body.referrer = referrerUrl;
          // Mark the referrer as consumed for this browser session.
          sessionStorage.setItem('initialReferrerConsumed', 'true');
        }
      }

      // Double-check component is still mounted before proceeding
      if (!componentMountedRef.current) {
        isInitializingRef.current = false;
        return;
      }

      const response = await createStoreSessions({
        orgSlug,
        body,
      }).unwrap();

      // Check component is still mounted after API call
      if (!componentMountedRef.current) {
        // If component unmounted during API call, terminate the session
        updateStoreSessions({
          orgSlug,
          sessionId: response?.id,
          body: { endTime: new Date().toISOString(), isActive: false },
        });
        isInitializingRef.current = false;
        return;
      }

      if (response?.id) {
        const storageKey = getStorageKey();
        localStorage.setItem(storageKey, JSON.stringify(response));
        setSessionActive(true);
        sessionInitializedRef.current = true;

        lastActivityRef.current = Date.now(); // Reset activity timestamp
        setupInactivityCheck(); // Start inactivity monitoring
        return response;
      }
    } catch (error) {
      console.error("Error initiating session:", error);
    } finally {
      isInitializingRef.current = false;
    }
  };

  reactivateSessionRef.current = async () => {
    // Skip if component is unmounted
    if (!componentMountedRef.current) {
      return;
    }

    // Skip if we're already initializing or terminating or if we should debounce
    if (
      isInitializingRef.current ||
      isTerminatingRef.current ||
      shouldSkipOperation()
    ) {
      return;
    }

    // Skip if there's already an active session
    if (sessionActive) {
      return;
    }

    // Skip if no orgSlug or lastSessionId is available
    if (!orgSlug || !lastSessionIdRef.current) {
      // If no last session ID, fall back to creating a new session
      initializeSessionRef.current && initializeSessionRef.current();
      return;
    }

    isInitializingRef.current = true;

    try {
      const sessionId = lastSessionIdRef.current;

      // Note: No referrer info on reactivation - only on initial creation
      const response = await updateStoreSessions({
        orgSlug,
        sessionId: sessionId,
        body: {
          isActive: true,
        },
      }).unwrap();

      // Check component is still mounted after API call
      if (!componentMountedRef.current) {
        isInitializingRef.current = false;
        return;
      }

      if (response?.id) {
        const storageKey = getStorageKey();
        localStorage.setItem(storageKey, JSON.stringify(response));
        setSessionActive(true);
        sessionInitializedRef.current = true;
        lastActivityRef.current = Date.now(); // Reset activity timestamp
        setupInactivityCheck(); // Start inactivity monitoring
      }
    } catch (error) {
      console.error("Error reactivating session:", error);
      // If reactivation fails, create a new session instead
      lastSessionIdRef.current = null;
      initializeSessionRef.current && initializeSessionRef.current();
    } finally {
      isInitializingRef.current = false;
    }
  };

  // Create convenient wrapper functions
  const initializeSession = useCallback(async () => {
    return initializeSessionRef.current && initializeSessionRef.current();
  }, []);

  const reactivateSession = useCallback(async () => {
    return reactivateSessionRef.current && reactivateSessionRef.current();
  }, []);

  // Reset inactivity timer on user activity
  const resetInactivityTimer = useCallback(() => {
    // Skip if component is unmounted
    if (!componentMountedRef.current) {
      return;
    }

    // Update last activity timestamp
    lastActivityRef.current = Date.now();

    // Only create a new session if there's no active session and we're not already initializing
    // and we haven't initialized a session yet for this component instance
    if (
      !sessionActive &&
      !isInitializingRef.current &&
      !sessionInitializedRef.current
    ) {
      // Check if we have a last session to reactivate, otherwise create new one
      if (lastSessionIdRef.current) {
        reactivateSession();
      } else {
        initializeSession();
      }
      return;
    }

    // If we have an active session, reset the inactivity timeout
    if (sessionActive) {
      setupInactivityCheck();
    }
  }, [
    initializeSession,
    reactivateSession,
    sessionActive,
    setupInactivityCheck,
  ]);

  // Handle tab visibility changes
  useVisibilityChange({
    onHidden: async () => {
      // Only terminate if there is an active session and we're not already terminating
      if (
        sessionActive &&
        !isTerminatingRef.current &&
        componentMountedRef.current
      ) {
        await terminateSession();
      }
    },
    onVisible: async () => {
      // Only initialize if there is no active session and we're not already initializing
      // and we haven't initialized a session yet for this component instance
      if (
        !sessionActive &&
        !isInitializingRef.current &&
        !sessionInitializedRef.current &&
        componentMountedRef.current
      ) {
        // Check if we have a last session to reactivate, otherwise create new one
        if (lastSessionIdRef.current) {
          await reactivateSession();
        } else {
          await initializeSession();
        }
      }
    },
  });

  // Listen for user activity events and handle component lifecycle
  useEffect(() => {
    // Mark component as mounted
    componentMountedRef.current = true;

    // Skip setup if no org Slug is available
    if (!orgSlug) return;

    // Check localStorage on mount to restore session state
    const storageKey = getStorageKey();
    const sessionData = localStorage.getItem(storageKey);

    if (sessionData) {
      try {
        const parsed = JSON.parse(sessionData);
        if (parsed?.id) {
          setSessionActive(true);
          sessionInitializedRef.current = true;
          lastActivityRef.current = Date.now(); // Reset activity timestamp
          setupInactivityCheck(); // Start inactivity monitoring
        }
      } catch (e) {
        localStorage.removeItem(storageKey);
      }
    }

    const activityEvents = [
      "mousedown",
      "keydown",
      "scroll",
      "mousemove",
      "click",
      "touchstart",
    ];

    const handleUserActivity = () => {
      resetInactivityTimer();
    };

    // Add activity event listeners
    activityEvents.forEach((event) => {
      window.addEventListener(event, handleUserActivity);
    });

    // Initialize session when component mounts, but only if no session exists
    let initTimeout: NodeJS.Timeout | null = null;
    if (
      !sessionActive &&
      !isInitializingRef.current &&
      !sessionInitializedRef.current
    ) {
      // Delay initial session creation to avoid race conditions
      initTimeout = setTimeout(() => {
        if (componentMountedRef.current) {
          initializeSession();
        }
      }, 500);
    }

    // Cleanup function runs when component unmounts
    return () => {
      // Mark component as unmounted to prevent further operations
      componentMountedRef.current = false;

      // Remove event listeners
      activityEvents.forEach((event) => {
        window.removeEventListener(event, handleUserActivity);
      });

      // Clear any pending timeouts
      if (initTimeout) {
        clearTimeout(initTimeout);
      }

      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
        inactivityTimeoutRef.current = null;
      }

      // Terminate the session if it's active
      if (sessionActive && !isTerminatingRef.current) {
        terminateSession();
      }
    };
  }, [
    orgSlug,
    eventId,
    initializeSession,
    resetInactivityTimer,
    terminateSession,
    sessionActive,
    setupInactivityCheck,
    getStorageKey,
  ]);

  // Start inactivity check when session becomes active
  useEffect(() => {
    if (sessionActive && componentMountedRef.current) {
      setupInactivityCheck();
    }
  }, [sessionActive, setupInactivityCheck]);

  // Clean up any stale sessions when component is mounted
  useEffect(() => {
    // We run this cleanup once when the component is mounted
    // to ensure we don't have stale sessions from previous visits
    cleanupAllSessions();
    // This effect should only run once on component mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle browser/tab close with beforeunload event
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (sessionActive && componentMountedRef.current) {
        // Use synchronous approach for beforeunload
        const storageKey = getStorageKey();
        const sessionAnalyticsData = localStorage.getItem(storageKey);
        if (sessionAnalyticsData) {
          const storageData = JSON.parse(sessionAnalyticsData);

          // This won't wait for the API call to complete, but at least logs the event
          updateStoreSessions({
            orgSlug,
            sessionId: storageData?.id,
            body: { endTime: new Date().toISOString(), isActive: false },
          });

          localStorage.removeItem(storageKey);
        }
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [orgSlug, updateStoreSessions, sessionActive, getStorageKey, eventId]);

  return <div className="hidden" />;
}
