"use client";

import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Scrollbar, A11y, Mousewheel } from "swiper/modules";
import { SwiperOptions } from "swiper/types";
import Image from "next/image";
import type { Swiper as SwiperType } from "swiper";
import { useMediaQuery } from "@/lib/hooks/useMediaQuery";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/mousewheel";
import "swiper/css/scrollbar";

interface CarouselProps {
  children: React.ReactNode;
  breakpoints: SwiperOptions["breakpoints"];
  id: string;
  slideByPage?: boolean; // If true, slides by full visible row; if false, slides by one slide
}

const Carousel: React.FC<CarouselProps> = ({
  children,
  breakpoints,
  id,
  slideByPage = false,
}) => {
  const slides = React.Children.toArray(children);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(true);

  const prevRef = useRef<HTMLButtonElement>(null);
  const nextRef = useRef<HTMLButtonElement>(null);
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const isMobile = useMediaQuery("(max-width: 768px)");

  useEffect(() => {
    if (!swiper) return;

    const updateButtonVisibility = () => {
      setShowLeftButton(!swiper.isBeginning);
      setShowRightButton(
        swiper.activeIndex <
          swiper.slides.length - ((swiper.params.slidesPerView as number) || 1)
      );
    };

    swiper.on("slideChange", updateButtonVisibility);
    swiper.on("resize", updateButtonVisibility);

    updateButtonVisibility();

    return () => {
      swiper.off("slideChange", updateButtonVisibility);
      swiper.off("resize", updateButtonVisibility);
    };
  }, [swiper]);

  const NavigationButton: React.FC<{ direction: "prev" | "next" }> = ({
    direction,
  }) => {
    const handleClick = () => {
      if (!swiper) return;
      if (slideByPage) {
        const currentIndex = swiper.activeIndex;
        const slidesPerView = (swiper.params.slidesPerView as number) || 1;
        const slidesToMove = Math.floor(slidesPerView);

        if (direction === "prev") {
          const targetIndex = Math.max(0, currentIndex - slidesToMove);
          swiper.slideTo(targetIndex);
        } else {
          const maxIndex = swiper.slides.length - slidesToMove;
          const targetIndex = Math.min(maxIndex, currentIndex + slidesToMove);
          swiper.slideTo(targetIndex);
        }
      } else {
        direction === "prev" ? swiper.slidePrev() : swiper.slideNext();
      }
    };

    return (
      <button
        ref={direction === "prev" ? prevRef : nextRef}
        onClick={handleClick}
        className={`hidden md:flex absolute rounded-full justify-center items-center bg-[#BCBCBCE5] top-1/2 z-10 transform -translate-y-1/2 w-[40px] h-[40px] xl:w-[56px] xl:h-[56px] ${
          direction === "prev" ? "left-5 rotate-180" : "right-5"
        }`}
      >
        <Image
          src="/arrow.svg"
          alt={`${direction}-arrow`}
          width={15}
          height={15}
        />
      </button>
    );
  };

  return (
    <div id={id} className="relative ml-4 sm:ml-0">
      {showLeftButton && <NavigationButton direction="prev" />}
      {showRightButton && <NavigationButton direction="next" />}

      <Swiper
        spaceBetween={10}
        onSwiper={setSwiper}
        slidesPerView={2.3}
        slidesOffsetBefore={20}
        slidesOffsetAfter={20}
        modules={[Navigation, Scrollbar, A11y, Mousewheel]}
        navigation={{
          prevEl: prevRef.current,
          nextEl: nextRef.current,
        }}
        mousewheel={{
          forceToAxis: true,
          sensitivity: 1.9,
          thresholdDelta: 20,
          thresholdTime: 120,
          invert: false,
          eventsTarget: "container",
        }}
        className="w-full overflow-x-hidden"
        breakpoints={breakpoints}
        simulateTouch={true}
        touchRatio={1.5}
        threshold={2}
        preventInteractionOnTransition={false}
        allowTouchMove={true}
        speed={isMobile ? 350 : 400}
        freeMode={{
          enabled: true,
          momentumBounce: true,
          momentumRatio: 0.85,
          momentumVelocityRatio: 0.85,
          minimumVelocity: 0.1,
          sticky: false,
        }}
        touchEventsTarget="container"
        touchStartPreventDefault={false}
        touchMoveStopPropagation={false}
        touchReleaseOnEdges={true}
        resistance={true}
        resistanceRatio={0.1}
        cssMode={isMobile}
        effect="slide"
        watchSlidesProgress={false}
        grabCursor={true}
      >
        {slides.map((child, index) => (
          <SwiperSlide key={index}>{child}</SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default Carousel;
