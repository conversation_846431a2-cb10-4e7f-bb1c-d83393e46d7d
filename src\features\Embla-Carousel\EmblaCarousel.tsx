import React, { useCallback, useEffect, useState } from "react";
import { EmblaCarouselType, EmblaOptionsType } from "embla-carousel";
import useEmblaCarousel from "embla-carousel-react";
import Image from "next/image";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

type PropType = {
  slides: number[];
  options?: EmblaOptionsType;
};

const EmblaCarousel: React.FC<PropType> = (props) => {
  const { slides, options } = props;
  const [emblaRef, emblaApi] = useEmblaCarousel(options);
  type UseDotButtonType = {
    selectedIndex: number;
    scrollSnaps: number[];
    onDotButtonClick: (index: number) => void;
  };

  const useDotButton = (
    emblaApi: EmblaCarouselType | undefined
  ): UseDotButtonType => {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

    const onDotButtonClick = useCallback(
      (index: number) => {
        if (!emblaApi) return;
        emblaApi.scrollTo(index);
      },
      [emblaApi]
    );

    const onInit = useCallback((emblaApi: EmblaCarouselType) => {
      setScrollSnaps(emblaApi.scrollSnapList());
    }, []);

    const onSelect = useCallback((emblaApi: EmblaCarouselType) => {
      setSelectedIndex(emblaApi.selectedScrollSnap());
    }, []);

    useEffect(() => {
      if (!emblaApi) return;

      onInit(emblaApi);
      onSelect(emblaApi);
      emblaApi
        .on("reInit", onInit)
        .on("reInit", onSelect)
        .on("select", onSelect);
    }, [emblaApi, onInit, onSelect]);

    return {
      selectedIndex,
      scrollSnaps,
      onDotButtonClick,
    };
  };

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const DotButton: React.FC<any> = (props) => {
    const { children, ...restProps } = props;
    return (
      <button type="button" {...restProps}>
        {children}
      </button>
    );
  };

  if (slides.length === 0)
    return (
      <div className="flex justify-center items-center w-full h-full">
        <Image
          src={IMAGE_LINKS.NO_IMG}
          alt="Autolnk No Image"
          width={350}
          height={350}
          className="rounded-2xl object-cover"
        />
      </div>
    );

  return (
    <section className="embla">
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">
          {slides.map((data: any, index) => (
            <div className="embla__slide" key={index}>
              <div className="embla__slide__number">
                <Image
                  src={data.photo}
                  alt={data.id}
                  width={400}
                  height={400}
                  className="rounded-2xl object-cover"
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="">
        <div className="flex w-full justify-center items-center my-3">
          {scrollSnaps.map((_, index) => (
            <DotButton
              key={index}
              onClick={() => onDotButtonClick(index)}
              className={
                index === selectedIndex
                  ? "rounded-full mx-1 w-3 h-3 flex bg-blue-500"
                  : "rounded-full mx-1 flex w-3 h-3 bg-gray-300"
              }
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default EmblaCarousel;
