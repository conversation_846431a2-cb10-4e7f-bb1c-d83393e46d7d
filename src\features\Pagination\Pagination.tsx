"use client";
import React from "react";
import { Button } from "@nextui-org/react";
import { useRouter } from "next/navigation";

interface PaginationProps {
  totalPages: number;
  nextCursor: string;
  prevCursor: string;
  nextPage: boolean;
  prevPage: boolean;
  perPage: number | string;
}

const PaginationComponent: React.FC<PaginationProps> = ({
  totalPages,
  nextCursor,
  prevCursor,
  nextPage,
  prevPage,
  perPage,
}) => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = React.useState(
    !nextPage ? totalPages : 1
  );
  // const [loading, setLoading] = React.useState(false);

  const handleLoad = (type: "next" | "prev", cursor: string) => {
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set("cursor", cursor);
    searchParams.set("per_page", String(perPage));

    setCurrentPage((prev) => (type === "next" ? prev + 1 : prev - 1));

    router.push(`${window.location.pathname}?${searchParams.toString()}`);
  };

  return (
    <div className="flex gap-3 overflow-hidden justify-center items-center">
      {prevPage && (
        <Button
          size="md"
          variant="flat"
          color="default"
          onPress={() => handleLoad("prev", prevCursor)}
          className="mr-2"
        >
          Previous
        </Button>
      )}
      <Button isDisabled>
        {currentPage}/{totalPages}
      </Button>
      {nextPage && (
        <Button
          size="md"
          variant="flat"
          color="default"
          onPress={() => handleLoad("next", nextCursor)}
          className="ml-2"
        >
          Next
        </Button>
      )}
      {/* <Button color="primary" isLoading={loading} onClick={handleLoad} className="w-2/4">
                {loading ? "Loading" : "Load More"}
            </Button> */}
    </div>
  );
};

export default PaginationComponent;
