import Image from "next/image";
import { useRouter } from "next/navigation";
import React from "react";
import { Button } from "@nextui-org/react";
import { CART_STORAGE_KEYS } from "@/lib/utils/constants";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

// Type for the Ticket object
interface Ticket {
  id: string;
  price: number;
  quantity: number;
  ticketType: string;
  total: number;
}

// Type for the main Event object
interface Event {
  id: string;
  type?: "ticket" | "product";
  eventId?: string;
  img?: string;
  title: string;
  ticket: Ticket;
  eventDetails?: {
    eventPhoto: Array<{ photo: string }>;
    eventName: string;
    ticketName: string;
  };
  variant?: any;
  quantity?: number;
  unitPrice?: string;
}

// Type for the Product object
interface ProductVariant {
  id: number;
  name: string;
  price: number;
}

interface CartProduct {
  id: string;
  type: "product";
  variant: {
    id: number;
    name: string;
    price: string;
    media?: Array<{
      image: string;
    }>;
    attributes?: Array<{
      attribute: {
        name: string;
      };
      values: Array<{
        name: string;
        value: string;
      }>;
    }>;
  };
  quantity: number;
  unitPrice: string;
  totalPrice: string;
  deliveryCharges: number;
}

export function Cart({
  onClick,
  cartData,
}: {
  onClick: () => void;
  cartData?: any;
}) {
  const navigate = useRouter();

  if (!cartData?.length) {
    return (
      <div className="flex flex-col gap-y-2 justify-center items-center text-medium font-medium">
        <Image
          src={IMAGE_LINKS.EMPTY_CART}
          alt="Autolnk Empty Cart"
          width={220}
          height={300}
        />
        <p className="md:p-2">Your cart is empty</p>
      </div>
    );
  }

  return (
    <div className="flex flex-row justify-evenly w-full mx-3 gap-x-4 overflow-y-auto">
      <div className="flex flex-col relative gap-y-3 h-full text-medium font-medium w-fit overflow-y-auto">
        <Button
          color="primary"
          className="w-full absolute top-0 px-5 mt-2 block md:hidden"
          onPress={() => navigate.push(`/cart`)}
        >
          Review Cart
        </Button>

        <div className="block md:hidden mt-12" />

        {cartData.length > 0 && (
          <div className="w-full">
            <h2 className="text-lg font-semibold mb-2">Tickets</h2>
            {cartData.map((item: Event | CartProduct) => {
              if ("type" in item && item.type === "product") {
                return (
                  <CartProductItem
                    key={`${item.id}-${item.variant.id}`}
                    product={item as CartProduct}
                  />
                );
              }

              return (
                <CartTicketItem
                  key={item.id}
                  item={item as Event}
                  onClick={() => {
                    navigate.push(`/e/${item?.eventDetails?.eventId}`);
                    onClick();
                  }}
                />
              );
            })}
          </div>
        )}
      </div>

      <Button
        color="primary"
        className="w-fit px-5 hidden md:block"
        onPress={() => navigate.push(`/cart`)}
      >
        Review Cart
      </Button>
    </div>
  );
}

function CartTicketItem({
  item,
  onClick,
}: {
  item: Event;
  onClick: () => void;
}) {
  return (
    <div
      className="flex flex-row gap-x-5 p-2 pr-14 cursor-pointer w-full hover:bg-gray-200 rounded-md items-center justify-start transition duration-150 ease-in-out hover:transition-all"
      onClick={onClick}
    >
      <Image
        className="w-[120px] h-[120px] rounded-md object-cover"
        width={120}
        height={120}
        src={
          item?.eventDetails?.eventPhoto[0]?.photo ??
          item?.img ??
          IMAGE_LINKS.NO_IMG
        }
        alt="Autolnk ticket image"
      />
      <div className="flex flex-col flex-1">
        <p className="font-medium text-base text-gray-800 dark:text-gray-50">
          {item?.eventDetails?.eventName || item.title}
        </p>
        <p className="text-sm text-gray-500">
          {item?.eventDetails?.ticketName || item?.ticket?.ticketType} -{" "}
          {item?.quantity || item?.ticket?.quantity} × $
          {item?.unitPrice || item?.ticket?.price}
        </p>
      </div>
    </div>
  );
}

function CartProductItem({ product }: { product: CartProduct }) {
  const getLocalVariants = localStorage.getItem(
    CART_STORAGE_KEYS.CART_VARIANTS
  );

  const localVariants = JSON.parse(getLocalVariants || "{}");

  return (
    <div className="flex flex-row gap-x-5 p-2 pr-14 w-full hover:bg-gray-200 rounded-md items-center justify-start transition duration-150 ease-in-out hover:transition-all">
      <Image
        className="w-[120px] h-[120px] rounded-md object-cover"
        width={120}
        height={120}
        src={
          product.variant.media?.[0]?.image ??
          localVariants[product.variant.id]?.image ??
          IMAGE_LINKS.NO_IMG
        }
        alt="Autolnk product image"
      />
      <div className="flex flex-col flex-1">
        <p className="font-medium text-base text-gray-800 dark:text-gray-50">
          {product?.variant?.productDetails?.name || ""} -{" "}
          {product?.variant?.name || ""}
        </p>
        <p className="text-sm text-gray-500">
          {product.quantity} × ${product.unitPrice}
        </p>
        {product.variant.attributes &&
          product.variant.attributes.length > 0 && (
            <div className="text-xs text-gray-400 mt-1">
              {product.variant.attributes.map((attr) => (
                <span key={attr.attribute.name} className="mr-2">
                  {attr.attribute.name}: {attr.values[0].name}
                </span>
              ))}
            </div>
          )}
      </div>
    </div>
  );
}
