import {
  But<PERSON>,
  Dropdown,
  Dropdown<PERSON><PERSON>,
  Dropdown<PERSON><PERSON><PERSON>,
  DropdownTrigger,
} from "@nextui-org/react";
import { Session } from "next-auth";
import React, { useCallback, useState } from "react";
import { usePathname } from "next/navigation";

import { BookOpenIcon, BlogIcon, PageClockIcon } from "@shopify/polaris-icons";
import { useUserDetails } from "@/lib/hooks/useUserDetails";
interface MobileDropdownProps {
  isAuthenticated: boolean;
  session: Session | null;
  isPartOfAnyOrganization: boolean;
  firstOrganizationSlug: string | undefined;
}

const BarsIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 448 512"
    className={className}
    fill="currentColor"
    width={20}
    height={20}
  >
    <path d="M0 96C0 78.3 14.3 64 32 64l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 128C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 288c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32L32 448c-17.7 0-32-14.3-32-32s14.3-32 32-32l384 0c17.7 0 32 14.3 32 32z" />
  </svg>
);

export const MobileDropdown = ({
  isAuthenticated,
  session,
  isPartOfAnyOrganization,
  firstOrganizationSlug,
}: MobileDropdownProps) => {
  const [menuOpen, setMenuOpen] = useState(false);
  const pathname = usePathname();
  const isCommunityPage = pathname.startsWith("/community");

  const { user: localUser } = useUserDetails();
  const user = session?.user ?? localUser;

  const getDashboardUrl = useCallback(() => {
    if (!session?.accessToken || !session?.refreshToken) return "";
    if (isPartOfAnyOrganization) {
      return `${process.env.NEXT_PUBLIC_DASHBOARD_URL}organization/${firstOrganizationSlug}/?accessToken=${session.accessToken}&refreshToken=${session.refreshToken}&csrfToken=${session.csrfToken}`;
    }

    return `${process.env.NEXT_PUBLIC_DASHBOARD_URL}organization/${user?.orgDetails?.org?.slug}/?accessToken=${session.accessToken}&refreshToken=${session.refreshToken}&csrfToken=${session.csrfToken}`;
  }, [
    session?.accessToken,
    session?.refreshToken,
    isPartOfAnyOrganization,
    firstOrganizationSlug,
  ]);

  const menuItems = [
    ...(isAuthenticated
      ? [
          <DropdownItem key="message" href={`/message`}>
            Messages
          </DropdownItem>,
        ]
      : []),
    <DropdownItem key="why-autolnk" href={`/`}>
      Why AutoLNK?
    </DropdownItem>,
    <DropdownItem key="events" href={`/events`}>
      Events
    </DropdownItem>,
    ...(isAuthenticated
      ? [
          <DropdownItem key="settings" href={`/profile/settings`}>
            Settings
          </DropdownItem>,
        ]
      : []),
    ...(!isCommunityPage
      ? [
          <DropdownItem key="community" href={`/community`}>
            Community
          </DropdownItem>,
        ]
      : []),
    <DropdownItem key="about" href={`/about`}>
      Company
    </DropdownItem>,
    ...(isCommunityPage
      ? [
          <DropdownItem
            key="community-all"
            href="/community"
            className="md:hidden"
          >
            <div className="flex items-center gap-2">
              <BookOpenIcon className="h-5 w-5" />
              <span>All posts</span>
            </div>
          </DropdownItem>,
          <DropdownItem
            key="community-blog"
            href="/community?type=blog"
            className="md:hidden"
          >
            <div className="flex items-center gap-2">
              <BlogIcon className="h-5 w-5" />
              <span>Blog</span>
            </div>
          </DropdownItem>,
          <DropdownItem
            key="community-updates"
            href="/community?type=updates"
            className="md:hidden"
          >
            <div className="flex items-center gap-2">
              <PageClockIcon className="h-5 w-5" />
              <span>Updates</span>
            </div>
          </DropdownItem>,
        ]
      : []),
    ...(isAuthenticated
      ? [
          <DropdownItem
            key="tickets"
            onPress={() => {
              const url =
                user?.orgDetails || isPartOfAnyOrganization
                  ? getDashboardUrl()
                  : "/professional-account-creation";
              window.location.href = url;
            }}
          >
            {user?.orgDetails || isPartOfAnyOrganization
              ? "Dashboard"
              : "Sell Tickets"}
          </DropdownItem>,
        ]
      : []),
  ];

  return (
    <div>
      <Dropdown closeOnSelect={false}>
        <DropdownTrigger>
          <Button
            isIconOnly
            color="default"
            aria-label="Humberger menu bar"
            className="text-xl focus:outline-none bg-transparent"
            onPress={() => setMenuOpen(!menuOpen)}
          >
            <BarsIcon className="text-xl" />
          </Button>
        </DropdownTrigger>
        <DropdownMenu aria-label="Profile Actions" variant="flat">
          {menuItems}
        </DropdownMenu>
      </Dropdown>
    </div>
  );
};
