import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Dropdown,
  Dropdown<PERSON><PERSON>,
  <PERSON>down<PERSON>enu,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@nextui-org/react";
import Image from "next/image";
import React from "react";
import { NotificationItem } from "./components/NotificationItem";
import { LnkRequestsSection } from "./components/LnkRequestsSection";
import { DropdownItemType } from "./types";
import DashboardInviteNotification from "./components/NotificationTypes/DashboardInvite";
import { useNotifications } from "@/lib/hooks/useNotifications";

const Notifications = () => {
  const {
    isDropdownOpen,
    setIsDropdownOpen,
    newNotifications,
    olderNotifications,
    pendingLnkRequests,
    invitesData,
    lastElementRef,
    unreadCount,
    getDropdownItemsStructure,
    hasMore,
    isFetching
  } = useNotifications();

  // Render the dropdown items with their appropriate children components
  const renderDropdownItems = (): DropdownItemType[] => {
    const itemsStructure = getDropdownItemsStructure();
    
    return itemsStructure.map(item => {
      // Create a new object with all properties from the structure
      const dropdownItem = { ...item } as DropdownItemType;
      
      // Add the appropriate children based on item key
      if (item.key === "lnk-requests") {
        dropdownItem.children = <LnkRequestsSection requests={pendingLnkRequests} />;
      } else if (item.key === "new-header") {
        dropdownItem.children = <p className="text-xl font-bold">New</p>;
      } else if (item.key === "older-header") {
        dropdownItem.children = (
          <p className="text-xl font-bold border-t border-gray-200 pt-2">
            Older
          </p>
        );
      } else if (item.key === "empty") {
        dropdownItem.children = <p className="text-center text-gray-500">No notifications</p>;
      } else if (item.key === "loading") {
        dropdownItem.children = (
          <div className="flex justify-center items-center py-2">
            <Spinner
              size="sm"
              color="primary"
              classNames={{
                circle1: "h-4 w-4",
                circle2: "h-4 w-4",
              }}
            />
          </div>
        );
      } else if (invitesData?.some(invite => invite.invitationId === item.key)) {
        // For dashboard invites
        const invite = invitesData.find(inv => inv.invitationId === item.key);
        if (invite) {
          dropdownItem.children = <DashboardInviteNotification invite={invite} />;
        }
      } else {
        // For regular notifications (both new and old)
        const notification = [...newNotifications, ...olderNotifications].find(
          n => n.id === item.key
        );
        
        if (notification) {
          // If this is the last element that needs a ref, wrap it
          if (item.isLastElement) {
            dropdownItem.children = (
              <div ref={lastElementRef}>
                <NotificationItem notification={notification} />
              </div>
            );
          } else {
            dropdownItem.children = <NotificationItem notification={notification} />;
          }
        }
      }
      
      return dropdownItem;
    });
  };

  return (
    <Dropdown
      isOpen={isDropdownOpen}
      onOpenChange={setIsDropdownOpen}
      backdrop="blur"
    >
      <DropdownTrigger>
        <Button
          isIconOnly
          color="default"
          aria-label="Notifications"
          className="bg-transparent border-none outline-none"
        >
          <Badge
            color="primary"
            content={unreadCount}
            shape="rectangle"
            isInvisible={unreadCount <= 0}
          >
            <Image
              src="/bell-icon.svg"
              width="16"
              height="16"
              alt="bell icon"
            />
          </Badge>
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        items={renderDropdownItems()}
        aria-label="Notifications"
        className="w-[300px] sm:w-[375px] max-h-[500px] overflow-y-auto p-0 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"
        itemClasses={{
          base: "rounded-none p-0",
        }}
      >
        {(item) => (
          <DropdownItem
            key={item.key}
            className={item.className}
            textValue={item.textValue}
            isReadOnly={!item.textValue}
            href={item?.redirectUrl || "#"}
          >
            {item.children}
          </DropdownItem>
        )}
      </DropdownMenu>
    </Dropdown>
  );
};

export default Notifications;
