import { SHOW_ALLOWED_NAV_DETAILS } from "@/lib/utils/constants";
import React from "react";
import { SearchInput } from "./SearchInput";
import { Cart } from "./Cart";

type RenderNavDetailsProps = {
  currentNav: string | null;
  onClick: () => void;
  cartData?: any;
};

export function RenderNavDetails({
  currentNav,
  onClick,
  cartData,
}: RenderNavDetailsProps) {
  switch (currentNav) {
    case SHOW_ALLOWED_NAV_DETAILS.CART:
      return (
        <div className="bg-gray-100 w-full flex justify-center md:my-5">
          <Cart onClick={onClick} cartData={cartData} />
        </div>
      );

    case SHOW_ALLOWED_NAV_DETAILS.SEARCH:
      return (
        <div
          className="bg-gray-100 w-full h-full flex justify-center px-4 md:px-0"
          onClick={(e) => e.stopPropagation()}
        >
          <SearchInput onClick={onClick} />
        </div>
      );

    default:
      return <div />;
  }
}
