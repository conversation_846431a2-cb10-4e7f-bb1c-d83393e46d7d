import { useDebouncedValue } from "@/lib/hooks/useDebounce";
import {
  eventsApi,
  useGetAllEventsByNameQuery,
} from "@/lib/redux/slices/events/eventsApi";
import { useGetOrganizationsByNameQuery } from "@/lib/redux/slices/organization/api";
import { dateToLongString } from "@/lib/utils/date";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { Chip, Input, Skeleton, Tabs, Tab } from "@nextui-org/react";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";

// Custom Search Icon Component
const SearchIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 512 512"
    className={className}
    width="1em"
    height="1em"
    fill="currentColor"
  >
    <path d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z" />
  </svg>
);

type SearchType = "events" | "organizers";

export function SearchInput({ onClick }: { onClick: () => void }) {
  const [query, setQuery] = useState<string>("");
  const [searchType, setSearchType] = useState<SearchType>("events");
  const debouncedQuery = useDebouncedValue(query, 500);

  const {
    data: events,
    error: eventsError,
    isLoading: eventsLoading,
  } = useGetAllEventsByNameQuery(debouncedQuery, {
    skip: !debouncedQuery || searchType !== "events",
  });

  const {
    data: organizers,
    error: organizersError,
    isLoading: organizersLoading,
  } = useGetOrganizationsByNameQuery(debouncedQuery, {
    skip: !debouncedQuery || searchType !== "organizers",
  });

  const [displayedEvents, setDisplayedEvents] = useState(events);
  const [displayedOrganizers, setDisplayedOrganizers] = useState(organizers);

  useEffect(() => {
    if (!debouncedQuery) {
      setDisplayedEvents(undefined);
      setDisplayedOrganizers(undefined);
    } else if (searchType === "events" && events) {
      setDisplayedEvents(events);
    } else if (searchType === "organizers" && organizers) {
      setDisplayedOrganizers(organizers);
    }
  }, [debouncedQuery, events, organizers, searchType]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    let value = e.target.value;
    setQuery(value);
  };

  const isLoading = searchType === "events" ? eventsLoading : organizersLoading;
  const hasResults =
    searchType === "events"
      ? displayedEvents?.results?.length > 0
      : displayedOrganizers?.results?.length > 0;

  return (
    <div className="w-full md:w-1/2 mt-2 md:mt-5 flex flex-col gap-y-3">
      <Tabs
        selectedKey={searchType}
        onSelectionChange={(key) => setSearchType(key as SearchType)}
        className="mb-4"
      >
        <Tab key="events" title="Events" />
        <Tab key="organizers" title="Organizations" />
      </Tabs>

      <Input
        isClearable
        onClear={() => setQuery("")}
        radius="lg"
        autoFocus
        classNames={{
          input: [
            "bg-transparent hover:bg-transparent",
            "text-black/90 dark:text-white/90 text-xl font-semibold font-sfPro outline-none border-none",
            "placeholder:text-default-700/50 dark:placeholder:text-white/60 placeholder:font-bold placeholder:text-xl",
            "focus:outline-none focus:border-none",
          ],
          innerWrapper: "bg-transparent focus:outline-none",
          inputWrapper: [
            "shadow-sm",
            "bg-transparent",
            "!cursor-text",
            "outline-none",
            "focus:outline-none",
          ],
        }}
        className="relative outline-none border-none py-4 hover:bg-transparent text-lg font-medium h-5 font-sfPro"
        placeholder={`Search ${
          searchType === "events" ? "Events" : "Organizations"
        }`}
        startContent={
          <SearchIcon className="text-black/50 mb-0.5 dark:text-white/90 text-slate-400 pointer-events-none flex-shrink-0" />
        }
        onChange={handleInputChange}
      />

      <div className="mx-4 my-8">
        {isLoading && (
          <div className="mt-3">
            <Loader />
          </div>
        )}

        {debouncedQuery && !isLoading && !hasResults && (
          <div className="mt-3 flex justify-center items-center">
            <Image
              src="/items-not-found.svg"
              alt="items-not-found"
              width={220}
              height={300}
            />
          </div>
        )}

        {searchType === "events" && displayedEvents?.results?.length > 0 && (
          <ul className="custom-scrollbar flex flex-col gap-y-2 h-[50vh] overflow-y-scroll">
            {displayedEvents?.results?.map((event) => (
              <Event key={event?.id} event={event} onClick={onClick} />
            ))}
          </ul>
        )}

        {searchType === "organizers" &&
          displayedOrganizers?.results?.length > 0 && (
            <ul className="custom-scrollbar flex flex-col gap-y-2 h-[50vh] overflow-y-scroll">
              {displayedOrganizers?.results?.map((organizer) => (
                <Organizer
                  key={organizer?.id}
                  organizer={organizer}
                  onClick={onClick}
                />
              ))}
            </ul>
          )}
      </div>
    </div>
  );
}

function Loader() {
  return (
    <div className="w-full flex flex-col gap-2">
      <Skeleton className="h-4 w-full rounded-lg" />
      <Skeleton className="h-4 w-1/2 rounded-lg" />
      <Skeleton className="h-4 w-3/4 rounded-lg" />
    </div>
  );
}

function Event({ event, onClick }: any) {
  const mainEventImg = event?.images?.find(
    (img: any) => Number(img?.type) === 1
  );

  return (
    <Link
      href={`/e/${event?.id}`}
      className="text-md font-semibold capitalize text-blue-600"
    >
      <li
        key={event?.id}
        className="flex gap-x-2 hover:bg-gray-200 rounded-md p-1 items-center text-sm text-gray-500"
        onClick={onClick}
      >
        <Image
          width={120}
          height={120}
          src={mainEventImg?.photo ?? IMAGE_LINKS.NO_IMG}
          className="w-16 h-16 md:w-20 md:h-20 object-cover"
          alt="Autolnk event image"
        />
        <p>{event?.name}</p>
        <span>
          <Chip size="sm">{event?.status}</Chip>
        </span>
        <span className="ml-3">
          {dateToLongString(event?.startDate, event?.timezone)}
        </span>
      </li>
    </Link>
  );
}

function Organizer({ organizer, onClick }: any) {
  return (
    <li
      key={organizer?.id}
      className="flex gap-x-2 hover:bg-gray-200 rounded-md p-1 items-center text-sm text-gray-500 justify-between"
      onClick={onClick}
    >
      <div className="flex gap-x-2 items-center">
        <Image
          width={50}
          height={50}
          src={organizer?.owner?.avatar ?? IMAGE_LINKS.IMAGE_PLACEHOLDER}
          alt="Autolnk Organizer Avatar"
          className="rounded-full w-10 h-10 object-cover"
        />
        <Link
          href={`/profile/${organizer?.owner?.id}`}
          className="text-md font-semibold capitalize underline text-blue-600"
        >
          {organizer?.owner?.name}
        </Link>
      </div>
      <span>
        <Chip size="sm">{organizer?.eventsLocation}</Chip>
      </span>
    </li>
  );
}
