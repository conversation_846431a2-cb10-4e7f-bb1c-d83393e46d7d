"use client";

import {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
  PropsWithChildren,
} from "react";
import { Logout } from "@/lib/actions/auth/Logout";
import { useSessionData } from "@/lib/hooks/useSession";
import { useAppDispatch } from "@/lib/redux/hooks";
import { clearUser } from "@/lib/redux/slices/auth/authSlice";
import {
  Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  Link,
  Button,
  Image,
  Spinner,
  Skeleton,
} from "@nextui-org/react";

import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { AUTH_STATUS, SHOW_ALLOWED_NAV_DETAILS } from "@/lib/utils/constants";
import { STORAGE_KEYS } from "@/lib/constants/storage";
import { AnimatePresence, motion } from "framer-motion";
import { MobileDropdown } from "./MobileDropdown";
import { RenderNavDetails } from "./RenderNavDetails";
import { SearchAndCart } from "./searchAndCart";
import { UserMenu } from "./UserMenu";
import { clearCart } from "@/lib/redux/slices/cart/cartSlice";
import { usePostHog } from 'posthog-js/react';
import { useSelector } from "react-redux";
import { RootState } from "@/lib/redux/store";
import { startLogout, endLogout } from "@/lib/redux/slices/auth/authSlice";
import { useGetUserProfileDetailsQuery } from "@/lib/redux/slices/user/userApi";
import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";
import Notifications from "./Notifications";
import { useOrganization } from "@/lib/hooks/useOrganization";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { useUserDetails } from "@/lib/hooks/useUserDetails";

// Custom User Icon Component
const UserIcon = ({ size = 20, color = "#7D7E7B" }: { size?: number; color?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 512 512" 
    width={size} 
    height={size}
    fill={color}
  >
    <path d="M399 384.2C376.9 345.8 335.4 320 288 320l-64 0c-47.4 0-88.9 25.8-111 64.2c35.2 39.2 86.2 63.8 143 63.8s107.8-24.7 143-63.8zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zm256 16a72 72 0 1 0 0-144 72 72 0 1 0 0 144z"/>
  </svg>
);

const FooterNavBar = dynamic(
  () => import("@/components/FooterNavBar/FooterNavBar"),
  {
    ssr: false,
  }
);

type ShowAllowedNavDetail =
  (typeof SHOW_ALLOWED_NAV_DETAILS)[keyof typeof SHOW_ALLOWED_NAV_DETAILS];

const POLLING_INTERVAL = 600000;

interface TopNavBarProps {
  hideFooter?: boolean;
}

const TopNavBar = ({
  children,
  hideFooter = false,
}: PropsWithChildren<TopNavBarProps>) => {
  const [onHover, setOnHover] = useState<ShowAllowedNavDetail | null>(null);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const { data: session, status, update } = useSessionData();
  const { user: localUser } = useUserDetails();
  const user = session?.user ?? localUser;
  const posthog = usePostHog();

  const { data: getUserProfile } = useGetUserProfileDetailsQuery(
    {},
    { skip: !session?.user }
  );

  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);

  const { data: cartData, refetch: refetchCart } = useGetCartQuery(undefined, {
    refetchOnFocus: true,
    refetchOnReconnect: true,
    refetchOnMountOrArgChange: true,
    pollingInterval: POLLING_INTERVAL,
  });

  const {
    isPartOfAnyOrganization,
    hasOrganization,
    firstOrganizationSlug,
    getDashboardUrl,
    organizationsVersion,
    refetchOrganizations,
  } = useOrganization();

  const router = useRouter();

  const pathName = usePathname();
  const authRoutes = ["/signin", "/signup", "/forgotpassword"];
  const dispatch = useAppDispatch();
  const onBoardButtonRef = useRef<HTMLButtonElement>(null);

  const isAuthRoute = authRoutes.includes(pathName);
  const isAuthenticated = useMemo(() => {
    return (
      (status === AUTH_STATUS.AUTHENTICATED &&
        status !== AUTH_STATUS.LOADING) ||
      user
    );
  }, [status, user]);

  const isInOrganization = useMemo(() => {
    return hasOrganization || !!user?.orgDetails;
  }, [hasOrganization, user?.orgDetails, organizationsVersion]);

  useEffect(() => {
    if (user || cartToken) {
      refetchCart();
    }
  }, [session, cartToken]);

  const isLoggingOut = useSelector(
    (state: RootState) => state.auth.isLoggingOut
  );

  const isLoading = useMemo(() => {
    return status === AUTH_STATUS.LOADING || isSigningOut || isLoggingOut;
  }, [status, isSigningOut, isLoggingOut]);

  useEffect(() => {
    if (getUserProfile && !getUserProfile.isOnboarded && !isLoading) {
      onBoardButtonRef?.current?.click();
    }
  }, [getUserProfile, onBoardButtonRef?.current, isLoading]);

  useEffect(() => {
    if (isAuthenticated && session?.user) {
      refetchOrganizations();
    }
  }, [isAuthenticated, session?.user]);

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      dispatch(startLogout());
      await Logout();
      dispatch(clearUser());
      posthog?.reset();
      setTimeout(() => {
        dispatch(clearCart());
        localStorage.removeItem(STORAGE_KEYS.CART_TOKEN);
      }, 2000);
      router.replace("/");
      await update();
    } finally {
      setIsSigningOut(false);
      dispatch(endLogout());
    }
  };

  const resetNav = useCallback(() => {
    if (onHover) {
      setOnHover(null);
    }
  }, [onHover]);

  function handleOnSearch() {
    if (onHover && onHover === SHOW_ALLOWED_NAV_DETAILS.SEARCH) {
      setOnHover(null);
    } else {
      setOnHover(SHOW_ALLOWED_NAV_DETAILS.SEARCH);
    }
  }

  function handleOnCart() {
    if (onHover && onHover === SHOW_ALLOWED_NAV_DETAILS.CART) {
      setOnHover(null);
    } else {
      setOnHover(SHOW_ALLOWED_NAV_DETAILS.CART);
    }
  }

  const renderUserMenu = useCallback(() => {
    if (!session?.user && isLoading) {
      return <Spinner size="sm" />;
    }

    if (session?.user) {
      return (
        <div className="flex gap-x-2 items-center">
          <div className="flex md:hidden">
            <SearchAndCart onSearch={handleOnSearch} onCart={handleOnCart} />
            {session?.user && <Notifications />}
          </div>

          <div className="md:flex hidden items-center">
            {isInOrganization && (
              <>
                {session?.user ? (
                  <button
                    className="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-3 min-w-16 h-8 text-tiny gap-2 [&>svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none bg-primary text-primary-foreground data-[hover=true]:opacity-hover rounded-md mr-2"
                    onClick={() => {
                      const url = getDashboardUrl({
                        orgSlug: user?.orgDetails?.org?.slug,
                      });
                      if (url) window.location.href = url;
                    }}
                  >
                    Dashboard
                  </button>
                ) : (
                  <Skeleton className="w-24 h-8 rounded-md" />
                )}
              </>
            )}

            <NavbarItem as={"li"}>
              <SearchAndCart onCart={handleOnCart} />
            </NavbarItem>
            <NavbarItem as={"li"}>
              <SearchAndCart onSearch={handleOnSearch} />
            </NavbarItem>
            <NavbarItem as={"li"}>
              {session?.user && <Notifications />}
            </NavbarItem>
          </div>
          <UserMenu
            user={session?.user}
            onLogout={handleSignOut}
            isLoading={isSigningOut}
          />
        </div>
      );
    }

    return (
      <div className="hidden md:flex items-center">
        <NavbarItem as={"li"}>
          <SearchAndCart onCart={handleOnCart} />
        </NavbarItem>
        <NavbarItem as={"li"}>
          <SearchAndCart onSearch={handleOnSearch} />
        </NavbarItem>

        <NavbarItem as={"li"}>
          <Button
            isIconOnly
            variant="flat"
            className="bg-transparent"
            startContent={<UserIcon size={21} color="#7D7E7B" />}
            onPress={() => router.push("/auth/signin")}
          />
        </NavbarItem>
      </div>
    );
  }, [
    status,
    session,
    user,
    isSigningOut,
    isInOrganization,
    organizationsVersion,
  ]);

  if (isAuthRoute) return children;

  return (
    <div className="relative h-full">
      <div className="fixed top-0 left-0 w-full flex justify-start items-start flex-col bg-[#F5F5F7] z-50 border-[#D7D7D7] border-b-1 md:px-2">
        <Button ref={onBoardButtonRef} className="hidden" />

        <Navbar
          maxWidth="full"
          className="bg-white md:bg-[#F5F5F7] flex w-full justify-between md:h-12"
        >
          <NavbarContent
            className="hidden md:flex items-center gap-2 mt-1"
            justify="start"
          >
            <Link href="/">
              <Image
                src={IMAGE_LINKS.LOGO_TRANSPARENT}
                alt="AutoLNK"
                className="h-7 w-auto "
              />
            </Link>
          </NavbarContent>
          <NavbarBrand className="md:hidden">
            <div className="flex md:hidden items-center gap-2">
              <div className={`flex md:hidden `}>
                <MobileDropdown
                  isAuthenticated={isAuthenticated}
                  session={session}
                  isPartOfAnyOrganization={isPartOfAnyOrganization()}
                  firstOrganizationSlug={firstOrganizationSlug()}
                />
              </div>
              <Link href="/">
                <Image
                  src={IMAGE_LINKS.LOGO_TRANSPARENT}
                  alt="AutoLNK"
                  width="32"
                  height="32"
                  className="h-7 w-auto"
                />
              </Link>
            </div>
          </NavbarBrand>

          {/* Hamburger Icon for Mobile */}
          <div
            className={`flex items-center md:hidden ${
              status === AUTH_STATUS.AUTHENTICATED ||
              status === AUTH_STATUS.LOADING
                ? "hidden"
                : ""
            }`}
          >
            <SearchAndCart onSearch={handleOnSearch} onCart={handleOnCart} />
            <Button
              isIconOnly
              variant="flat"
              className="bg-transparent"
              startContent={<UserIcon size={20} />}
              onPress={() => router.push("/auth/signin")}
            />
          </div>

          <div
            className={` md:flex md:flex-row justify-center flex-col items-center hidden `}
          >
            <NavbarContent
              className="md:flex gap-4 md:gap-x-6 lg:gap-x-12 justify-center"
              justify="center"
            >
              <NavbarItem as={"li"}>
                <Link
                  href="/"
                  className="text-gray-800 hover:text-gray-700 dark:text-gray-50 font-medium text-sm font-sfPro"
                >
                  Why AutoLNK?
                </Link>
              </NavbarItem>
              <NavbarItem as={"li"}>
                <Link
                  href="/events"
                  className="text-gray-800 hover:text-gray-700 dark:text-gray-50 font-medium text-sm font-sfPro"
                >
                  Events
                </Link>
              </NavbarItem>
              <NavbarItem as={"li"}>
                <Link
                  href="/community"
                  className="text-gray-800 hover:text-gray-700 dark:text-gray-50 font-medium text-sm font-sfPro"
                >
                  Community
                </Link>
              </NavbarItem>
              <NavbarItem as={"li"}>
                <Link
                  href="/about"
                  className="text-gray-800 hover:text-gray-700 dark:text-gray-50 font-medium text-sm font-sfPro"
                >
                  Company
                </Link>
              </NavbarItem>
              {!user?.orgDetails && !user?.teamDetails && (
                <NavbarItem as={"li"}>
                  <Link
                    href="/professional-account-creation"
                    className="text-gray-800 hover:text-gray-700 dark:text-gray-50 font-medium text-sm font-sfPro"
                  >
                    Sell Tickets
                  </Link>
                </NavbarItem>
              )}
              {!isAuthenticated && (
                <NavbarItem as={"li"} className="flex md:hidden">
                  <Button
                    isIconOnly
                    variant="flat"
                    onPress={() => router.push("/auth/signin")}
                    className="bg-transparent"
                    startContent={<UserIcon size={20} />}
                  />
                </NavbarItem>
              )}
            </NavbarContent>
          </div>

          <NavbarContent
            justify="end"
            className={`${isAuthenticated ? "flex" : "hidden md:flex"}`}
          >
            {renderUserMenu()}
          </NavbarContent>
        </Navbar>
        <AnimatePresence>
          {onHover && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{
                opacity: 1,
                height: "auto",
                minHeight: "20rem",
                transition: {
                  height: { type: "spring", stiffness: 100, damping: 20 },
                },
              }}
              exit={{ opacity: 0, height: 0 }}
              transition={{
                opacity: { duration: 0.3, ease: "easeInOut" },
              }}
              className="absolute top-12 left-0 w-full bg-[#F5F5F7] z-40 overflow-hidden"
            >
              <RenderNavDetails
                currentNav={onHover}
                cartData={cartData?.checkout?.lines}
                onClick={() => setOnHover(null)}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <motion.div
        animate={{
          filter: onHover ? "blur(4px)" : "blur(0px)",
          opacity: onHover ? 0.8 : 1,
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className="absolute top-0 bottom-0 left-0 right-0 z-30 min-h-screen"
        onClick={resetNav}
        onHoverStart={() => setOnHover(null)}
      >
        {children}
        {!hideFooter && <FooterNavBar />}
      </motion.div>
    </div>
  );
};

export default TopNavBar;
