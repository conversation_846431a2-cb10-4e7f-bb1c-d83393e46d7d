import { APP_ROLES } from "@/app/profile/[id]/organization/components/constants";
import { getFirstName, getInitials, getName } from "@/lib/utils/string";
import {
  Avatar,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Spinner,
} from "@nextui-org/react";
import Image from "next/image";
import React from "react";

interface TUserMenuProps {
  user: any;
  onLogout: () => void;
  isLoading?: boolean;
}
export function UserMenu({ user, onLogout, isLoading }: TUserMenuProps) {
  const avatarSrc = user?.avatar;

  return (
    <Dropdown backdrop="blur">
      <DropdownTrigger>
        <div className="flex gap-2 items-center cursor-pointer">
          <Avatar
            ImgComponent={Image}
            imgProps={{ width: 32, height: 32 }}
            isBordered={false}
            className="transition-transform"
            color="primary"
            name={getInitials(getName(user))}
            size="sm"
            src={avatarSrc}
            aria-label="User Avatar"
          />
        </div>
      </DropdownTrigger>
      <DropdownMenu
        aria-label="Profile Actions"
        variant="flat"
        hideEmptyContent={true}
      >
        <DropdownItem key="profile" className="h-14 gap-2">
          <p className="font-semibold">Signed in as</p>
          <p className="font-semibold">{user?.email}</p>
        </DropdownItem>
        <DropdownItem key="profile" href={`/profile/${user?.id}`}>
          Profile
        </DropdownItem>
        <DropdownItem key="messages" href={`/message`}>
          Messages
        </DropdownItem>
        <DropdownItem key="settings" href={`/profile/settings`}>
          Settings
        </DropdownItem>
        <DropdownItem
          key="logout"
          color="danger"
          onPress={onLogout}
          className="text-danger"
          disableAnimation={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <Spinner size="sm" />
              <span>Signing out...</span>
            </div>
          ) : (
            "Log Out"
          )}
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  );
}
