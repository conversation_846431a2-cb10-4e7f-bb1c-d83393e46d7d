interface FormattedTextProps {
  text: string | undefined;
}

export const FormattedText: React.FC<FormattedTextProps> = ({ text }) => {
  if (!text) return null;

  const parts = text.split(/(@\w+)/g);
  
  return (
    <>
      {parts.map((part, index) => {
        if (part.startsWith("@")) {
          return (
            <span key={index} className="font-bold">
              {part.substring(1)}
            </span>
          );
        }
        return part;
      })}
    </>
  );
}; 