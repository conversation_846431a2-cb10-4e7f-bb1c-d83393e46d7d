import { Avatar } from "@nextui-org/react";
import { motion } from "framer-motion";
import Link from "next/link";
import { LnkRequest } from "../types";

interface LnkRequestsSectionProps {
  requests: LnkRequest[];
}

export const LnkRequestsSection: React.FC<LnkRequestsSectionProps> = ({ requests }) => {
  if (!requests || requests.length === 0) return null;

  return (
    <Link href="#" className="block">
      <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="relative">
              {/* Main avatar */}
              <Avatar
                src={requests[0]?.requester?.avatar}
                alt={requests[0]?.requester?.username}
                className="w-[40px] h-[40px]"
                size="md"
              />
              {/* Stacked avatar for additional requests */}
              {requests.length > 1 && (
                <motion.div
                  initial={{ x: 0 }}
                  animate={{ x: -8 }}
                  className="absolute -right-4 -bottom-1"
                >
                  <Avatar
                    src={requests[1]?.requester?.avatar}
                    alt={requests[1]?.requester?.username}
                    className="w-[40px] h-[40px] border-2 border-white"
                    size="sm"
                  />
                </motion.div>
              )}
            </div>
            <div className="ml-4">
              <h3 className="text-sm font-semibold">LNK requests</h3>
              <p className="text-sm text-gray-600">
                {requests[0]?.requester?.username}
                {requests.length > 1 ? ` + ${requests.length - 1} other` : ""}
              </p>
            </div>
          </div>
          <div className="text-blue-500">
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      </div>
    </Link>
  );
}; 