import { NotificationItemProps } from "../types";
import { ChatNotification } from "./NotificationTypes/ChatNotification";
import { ConnectionNotification } from "./NotificationTypes/ConnectionNotification";
import { DriveShareNotification } from "./NotificationTypes/DriveShareNotification";
import { DriveShareAlertNotification } from "./NotificationTypes/DriveShareAlertNotification";
import { AnnouncementNotification } from "./NotificationTypes/AnnouncementNotification";
import { ChatPollNotification } from "./NotificationTypes/ChatPollNotification";
import { DriveShareInviteNotification } from "./NotificationTypes/DriveShareInvite";
import { useSessionData } from "@/lib/hooks/useSession";
import { useEffect, useState } from "react";
import { VehicleSubmission } from "./NotificationTypes/VehicleSubmission";
import { ProfileVisit } from "./NotificationTypes/ProfileVisit";
import { NOTIFICATION_TYPES } from "../constants";

export const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
}) => {
  const { data: session } = useSessionData();
  const [userTimezone, setUserTimezone] = useState<string | null>(null);

  useEffect(() => {
    const currentTimeZone = session?.user?.userTimezone;
    if (currentTimeZone) {
      setUserTimezone(currentTimeZone);
    }
  }, [session]);

  const renderNotificationContent = () => {
    if (!notification) return null;

    const { type } = notification;

    switch (type) {
      case NOTIFICATION_TYPES.CHAT_REPLY:
      case NOTIFICATION_TYPES.CHAT_LIKE:
      case NOTIFICATION_TYPES.GLOBALCHAT_MENTION:
      case NOTIFICATION_TYPES.CHAT_MENTION:
        return (
          <ChatNotification
            notification={notification}
            userTimezone={userTimezone}
          />
        );

      case NOTIFICATION_TYPES.CONNECTION_REQUEST:
        return (
          <ConnectionNotification
            notification={notification}
            userTimezone={userTimezone}
          />
        );

      case NOTIFICATION_TYPES.DRIVESHARE_START:
      case NOTIFICATION_TYPES.DRIVESHARE_END:
      case NOTIFICATION_TYPES.DRIVESHARE_ROUTE_UPDATE:
        return (
          <DriveShareNotification
            notification={notification}
            userTimezone={userTimezone}
          />
        );

      case NOTIFICATION_TYPES.DRIVESHARE_USER_ALERT:
        return (
          <DriveShareAlertNotification
            notification={notification}
            userTimezone={userTimezone}
          />
        );

      case NOTIFICATION_TYPES.DRIVESHARE_ANNOUNCEMENT:
        return (
          <AnnouncementNotification
            notification={notification}
            userTimezone={userTimezone}
          />
        );

      case NOTIFICATION_TYPES.DRIVESHARE_INVITATION:
        return (
          <DriveShareInviteNotification
            notification={notification}
            userTimezone={userTimezone}
          />
        );

      case NOTIFICATION_TYPES.CHAT_POLL_VOTE:
        return (
          <ChatPollNotification
            notification={notification}
            userTimezone={userTimezone}
          />
        );

      case NOTIFICATION_TYPES.VEHICLE_SUBMISSION:
      case NOTIFICATION_TYPES.NEW_PAID_EVENT:
        return (
          <VehicleSubmission
            notification={notification}
            userTimezone={userTimezone}
          />
        );

      case NOTIFICATION_TYPES.PROFILE_VISIT:
        return (
          <ProfileVisit
            notification={notification}
          />
        );

      default:
        return (
          <ChatNotification
            notification={notification}
            userTimezone={userTimezone}
          />
        );
    }
  };

  return <div className="py-2.5 px-3">{renderNotificationContent()}</div>;
};
