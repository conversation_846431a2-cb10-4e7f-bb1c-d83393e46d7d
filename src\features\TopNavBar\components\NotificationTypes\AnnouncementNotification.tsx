import Image from "next/image";
import { NotificationItemProps } from "../../types";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";

export const AnnouncementNotification: React.FC<NotificationItemProps> = ({ notification, userTimezone }) => {
  const { data, title, body, createdAt } = notification;

  return (
    <div className="w-full flex items-start">
      <div className="w-[50px]">
        <Image
          src="/announcement.svg"
          width={40}
          height={40}
          alt="Announcement"
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex flex-col items-baseline gap-1">
          <p className="text-sm font-bold">
            {data?.title}{" "}
            <span className="font-normal">
              drive share made an announcement:{" "}
            </span>
            <span className="font-normal text-xs text-gray-500 ml-1">
              {createdAt ? getTimeDifferenceFromISOString(createdAt, userTimezone) : ""}
            </span>
          </p>
          <p className="underline font-bold mt-1">"{title}"</p>
          <p className="text-xs mt-1 whitespace-pre-line break-words">"{body}"</p>
        </div>
      </div>
    </div>
  );
}; 