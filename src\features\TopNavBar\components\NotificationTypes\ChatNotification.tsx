import { Avatar } from "@nextui-org/react";
import { NotificationItemProps } from "../../types";
import { FormattedText } from "../FormattedText";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";

export const ChatNotification: React.FC<NotificationItemProps> = ({ notification, userTimezone }) => {
  const { data, title, body, createdAt } = notification;
  const sender = data?.senderInformation;

  return (
    <div className="w-full flex items-start">
      <div className="w-[50px]">
        <Avatar
          src={sender?.userPhoto}
          alt={sender?.username}
          className="w-[40px] h-[40px]"
          size="md"
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-baseline gap-1">
          <p className="text-sm">
            <FormattedText text={title} />: {data?.replyMessage || body}{" "}
            <span className="font-normal text-xs text-gray-500 ml-1">
              {createdAt ? getTimeDifferenceFromISOString(createdAt, userTimezone) : ""}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
}; 