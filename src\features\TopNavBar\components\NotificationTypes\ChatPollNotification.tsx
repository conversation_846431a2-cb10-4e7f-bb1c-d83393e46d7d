import { Avatar } from "@nextui-org/react";
import { NotificationItemProps } from "../../types";
import { FormattedText } from "../FormattedText";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";
import Image from "next/image";

export const ChatPollNotification: React.FC<NotificationItemProps> = ({ notification, userTimezone }) => {
  const { data, title, body, createdAt } = notification;
  const sender = data?.senderInformation;

  return (
    <div className="w-full flex items-start">
      <div className="w-[50px]">
      <Image
          src={'/globe.svg'}
          width={40}
          height={40}
          alt="Drive share"
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-baseline gap-1">
          <p className="text-sm">
            <FormattedText text={title} />
            <span className="font-normal text-xs text-gray-500 ml-1">
              {createdAt ? getTimeDifferenceFromISOString(createdAt, userTimezone) : ""}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
}; 