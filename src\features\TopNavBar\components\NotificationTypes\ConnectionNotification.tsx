import { Avatar } from "@nextui-org/react";
import { NotificationItemProps } from "../../types";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";

export const ConnectionNotification: React.FC<NotificationItemProps> = ({ notification, userTimezone }) => {
  const { data, body, createdAt } = notification;
  const sender = data?.senderInformation;

  return (
    <div className="w-full flex items-center">
      <div className="w-[50px]">
        <Avatar
          src={sender?.userPhoto}
          alt={sender?.username}
          className="w-[40px] h-[40px]"
          size="md"
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-baseline gap-1">
          <p className="text-sm font-bold">
            {sender?.username}
            <span className="text-sm flex-1 min-w-0 font-normal ml-1">
              {body === "Connection request accepted"
                ? "has LNKed with you"
                : "requested to LNK with you"}
            </span>
            <span className="font-normal text-xs text-gray-500 ml-1">
              {createdAt ? getTimeDifferenceFromISOString(createdAt, userTimezone) : ""}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
}; 