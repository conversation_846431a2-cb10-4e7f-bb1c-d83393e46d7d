import React from "react";
import { Avatar } from "@nextui-org/react";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";
import { DashboardInvite } from "../../types";

const DashboardInviteNotification = ({
  invite,
  userTimezone,
}: {
  invite: DashboardInvite;
  userTimezone: string;
}) => {
  return (
    <div className="flex items-center gap-3">
      <Avatar
        src={invite?.organization?.owner?.avatar || invite?.organization?.avatar}
        alt={invite?.organization?.owner?.name}
        className="flex-shrink-0"
        size="md"
      />
      <div className="flex flex-col flex-grow gap-1">
        <div className="flex items-center ">
          <p className="text-sm">
            <span className="font-semibold">
              {invite?.organization?.owner?.name}
            </span>{" "}
            invited you to join the organization.
            <span className="text-gray-400 ml-1">
              {getTimeDifferenceFromISOString(invite?.createdAt, userTimezone)}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default DashboardInviteNotification;
