import Image from "next/image";
import { NotificationItemProps } from "../../types";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";
import { alertImageMap } from "../../constants";

export const DriveShareAlertNotification: React.FC<NotificationItemProps> = ({
  notification,
  userTimezone,
}) => {
  const { data, createdAt } = notification;

  return (
    <div className="w-full flex items-start">
      <div className="w-[50px]">
        <Image
          src={alertImageMap?.[data?.alertName]}
          width={40}
          height={40}
          alt="Drive share"
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-1">
          <p className="text-sm font-bold">
            {data?.senderInformation?.username}
            <span className="font-normal mx-1">alerted</span>
            {data?.alertName}
            <span className="font-normal text-sm flex-1 min-w-0 mx-1">
              near by in
            </span>
            {data?.driveShareName}
            <span className="font-normal text-xs text-gray-500 ml-1">
              {createdAt
                ? getTimeDifferenceFromISOString(createdAt, userTimezone)
                : ""}
            </span>
          </p>
          {data?.alertCount && (
            <div className="text-[#4F4701] inline-flex items-center justify-center min-w-[24px] h-[24px] rounded-lg bg-[#FFEF9D]  font-medium text-xs px-2">
              {data?.alertCount}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
