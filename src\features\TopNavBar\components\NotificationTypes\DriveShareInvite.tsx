import Image from "next/image";
import { NotificationItemProps } from "../../types";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";
import { NOTIFICATION_TYPES } from "../../constants";
import { FormattedText } from "../FormattedText";
import { Avatar, Button } from "@nextui-org/react";

export const DriveShareInviteNotification: React.FC<NotificationItemProps> = ({
  notification,
  userTimezone,
}) => {
  const { type, data, createdAt, body } = notification;

  return (
    <div className="w-full flex items-start">
      <div className="w-[50px]">
        <Avatar
          src={data?.senderInformation?.userPhoto}
          alt={data?.senderInformation?.username}
          className="w-[40px] h-[40px]"
          size="md"
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-4">
          <p className="text-sm  max-w-[90%]">
            <FormattedText text={body} />
            <span className="font-normal text-xs text-gray-500 ml-1">
              {createdAt ? getTimeDifferenceFromISOString(createdAt, userTimezone) : ""}
            </span>
          </p>
          <Button className="py-1 px-2 w-[35%] rounded-md" size="sm" color="primary">
            Join
          </Button>
        </div>
      </div>
    </div>
  );
};
