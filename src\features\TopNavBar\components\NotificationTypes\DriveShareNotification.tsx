import Image from "next/image";
import { NotificationItemProps } from "../../types";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";
import { NOTIFICATION_TYPES } from "../../constants";

export const DriveShareNotification: React.FC<NotificationItemProps> = ({ notification, userTimezone }) => {
  const { type, data, createdAt } = notification;

  const getDriveShareStatus = (type: string) => {
    switch (type) {
      case NOTIFICATION_TYPES.DRIVESHARE_START:
        return "drive has started";
      case NOTIFICATION_TYPES.DRIVESHARE_END:
        return "drive has ended";
      case NOTIFICATION_TYPES.DRIVESHARE_ROUTE_UPDATE:
        return "drive share route was updated";
      default:
        return "";
    }
  };

  return (
    <div className="w-full flex items-start">
      <div className="w-[50px]">
        <Image
          src="/driveshare.svg"
          width={40}
          height={40}
          alt="Drive share"
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-baseline gap-1">
          <p className="text-sm font-bold">
            {data?.title}
            <span className="font-normal text-sm flex-1 min-w-0 ml-2">
              {getDriveShareStatus(type)}
            </span>
            <span className="font-normal text-xs text-gray-500 ml-1">
              {createdAt ? getTimeDifferenceFromISOString(createdAt, userTimezone) : ""}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
}; 