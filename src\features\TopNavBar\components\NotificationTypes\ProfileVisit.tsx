import { Avatar } from "@nextui-org/react";
import { motion } from "framer-motion";
import Link from "next/link";
import { NotificationData } from "../../types";
import { IMAGE_LINKS } from "@/lib/utils/image-links";
import { cn } from "@/lib/utils";

interface ProfileVisitProps {
  notification: NotificationData;
}

export const ProfileVisit: React.FC<ProfileVisitProps> = ({ notification }) => {
  const visitCount = notification?.data?.visitCount;
  return (
    <Link href="#" className="block">
      <div className=" cursor-pointer">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={cn("relative", visitCount > 1 && "ml-[-4px]")}>
              {/* Main avatar */}
              <Avatar
                src={visitCount >  1 ? IMAGE_LINKS.NO_IMG : notification?.data?.userPhoto}
                alt={notification?.data?.username}
                className="w-[40px] h-[40px] blur-[2.5px]"
                size="md"
              />
              {visitCount > 1 && (
                <motion.div
                  initial={{ x: 0 }}
                  animate={{ x: -8 }}
                  className="absolute -right-4 -bottom-1"
                >
                  <Avatar
                    src={notification?.data?.userPhoto}
                    alt={notification?.data?.username}
                    className="w-[40px] h-[40px] border-2 border-white blur-[2.5px]"
                    size="sm"
                  />
                </motion.div>
              )}
            </div>
            <div className={cn("ml-[10px]", visitCount > 1 && "ml-4")}>
              <p className="text-sm">{notification?.title}</p>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};
