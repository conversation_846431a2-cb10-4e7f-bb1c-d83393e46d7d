import { Avatar } from "@nextui-org/react";
import { NotificationItemProps } from "../../types";
import { getTimeDifferenceFromISOString } from "@/lib/utils/date";
import Image from "next/image";
import { IMAGE_LINKS } from "@/lib/utils/image-links";

export const VehicleSubmission: React.FC<NotificationItemProps> = ({
  notification,
  userTimezone,
}) => {
  const { data, title, body, createdAt } = notification;

  return (
    <div className="w-full flex items-start">
      <div className="w-[50px]">
        <Image
          src={data?.eventImage ?? IMAGE_LINKS.NO_IMG}
          width={40}
          height={40}
          className="aspect-square object-cover"
          alt={data?.eventName + " Autolnk Event Image"}
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-1">
          <p className="text-sm">
            {body}{" "}
            <span className="font-normal text-xs text-gray-500 ml-1">
              {createdAt
                ? getTimeDifferenceFromISOString(createdAt, userTimezone)
                : ""}
            </span>
          </p>
          {data?.pendingCount && (
            <div className="text-[#4F4701] inline-flex items-center justify-center min-w-[24px] h-[24px] rounded-lg bg-[#FFEF9D]  font-medium text-xs px-2">
              {data.pendingCount}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
