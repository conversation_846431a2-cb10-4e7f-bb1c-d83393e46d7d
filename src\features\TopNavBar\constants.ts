export const NOTIFICATION_TYPES = {
  CHAT_REPLY: "chat_reply",
  CHAT_LIKE: "chat_like",
  GLOBALCHAT_MENTION: "globalchat_mention",
  CHAT_MENTION: "chat_mention",
  CONNECTION_REQUEST: "connection_request",
  DRIVESHARE_START: "driveshare_start",
  DRIVESHARE_END: "driveshare_end",
  DRIVESHARE_ROUTE_UPDATE: "driveshare_route_update",
  DRIVESHARE_USER_ALERT: "driveshare_user_alert",
  DRIVESHARE_ANNOUNCEMENT: "driveshare_announcement",
  CHAT_POLL_VOTE: "chat_poll_vote",
  DRIVESHARE_INVITATION: "driveshare_invitation",
  VEHICLE_SUBMISSION: "vehicle_submission",
  PROFILE_VISIT: "profile_visit",
  NEW_PAID_EVENT: "new_paid_event",
} as const;

export type NotificationType = typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];

export const NOTIFICATION_STATUS = {
  NEW: 0,
  READ: 1,
} as const; 

export const alertImageMap = {
  'Police': '/police.svg',
  'Crash': '/crash.svg',
  'Traffic': '/traffic.svg',
  'Road closure': '/closure.svg',
} as const;

export const NOTIFICATIONS_PER_PAGE = 10;
