import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, But<PERSON> } from "@nextui-org/react";
import Image from "next/image";
import { useGetCartQuery } from "@/lib/redux/slices/cart/cartApi";
import { STORAGE_KEYS } from "@/lib/constants/storage";
import { useSessionData } from "@/lib/hooks/useSession";

interface ISearchProps {
  onSearch?: () => void;
  onCart?: () => void;
}

export function SearchAndCart({ onSearch, onCart }: ISearchProps) {
  const { data: session } = useSessionData();
  const cartToken = localStorage.getItem(STORAGE_KEYS.CART_TOKEN);

  const { data: cartData } = useGetCartQuery(undefined, {
    refetchOnFocus: true,
    refetchOnReconnect: true,
    refetchOnMountOrArgChange: true,
    skip: !cartToken && !session?.user,
  });

  const totalItemsCount = cartData?.checkout?.lines?.length || 0;

  return (
    <>
      {onSearch && (
        <motion.div className="nav-item">
          <Button
            isIconOnly
            color="default"
            aria-label="Search"
            className="bg-transparent"
            onPress={onSearch}
          >
            <Image
              src="/search-icon.svg"
              width="16"
              height="16"
              alt="search icon"
            />
          </Button>
        </motion.div>
      )}
      {onCart && (
        <motion.div className="nav-item">
          <Button
            isIconOnly
            color="default"
            aria-label="Cart"
            className="bg-transparent border-none outline-none"
            onPress={onCart}
          >
            <Badge
              color="primary"
              content={totalItemsCount}
              shape="rectangle"
              isInvisible={totalItemsCount <= 0}
            >
              <Image
                src="/cart-icon.svg"
                width="16"
                height="16"
                alt="cart icon"
              />
            </Badge>
          </Button>
        </motion.div>
      )}
    </>
  );
}
