import { NotificationType } from './constants';

export interface SenderInformation {
  userId: string;
  username: string;
  userPhoto: string;
}

export interface NotificationData {
  id: string;
  title: string;
  type: NotificationType;
  body: string;
  status: number;
  createdAt: string;
  batchId: string;
  data: {
    id?: string;
    title?: string;
    driveShareImage?: string;
    alertName?: string;
    driveShareName?: string;
    eventName?: string;
    messageId?: string;
    replyMessage?: string;
    connectionId?: string;
    eventImage?: string;
    senderInformation: SenderInformation;
  };
}

export interface UserProfile {
  id: string;
  name: string;
  avatar: string;
  username: string;
  bio: string;
  email: string;
  phone: string | null;
  isGuest: boolean;
  coverPhoto: string;
  firstName: string;
  lastName: string;
}

export interface LnkRequest {
  id: string;
  requester: UserProfile;
  receiver: UserProfile;
  status: string;
  createdAt: string;
}

export interface LnkRequestsResponse {
  nextCursor: string;
  prevCursor: string;
  nextPageResults: boolean;
  prevPageResults: boolean;
  count: number;
  totalPages: number;
  totalResults: number;
  extraStats: any;
  results: LnkRequest[];
}

export interface NotificationItemProps {
  notification: NotificationData;
  userTimezone?: string | null;
}

export interface DropdownItemType {
  key: string;
  textValue?: string;
  className?: string;
  children: React.ReactNode;
  redirectUrl?: string;
} 


export interface UserDetails {
  id: string;
  name: string;
  avatar: string;
  username: string;
  bio: string;
  email: string;
  phone: string | null;
  isGuest: boolean;
  coverPhoto: string;
}

export interface OrganizationMember {
  username: string;
  name: string;
  role: string;
  avatar: string;
}

export interface Organization {
  id: string;
  companyName: string;
  avatar: string;
  owner: UserDetails;
  coverPhoto: string;
  email: string;
}

export interface DashboardInvite {
  invitationId: string;
  organization: Organization;
  email: string;
  role: string;
  createdAt: string;
  createdByDetail: UserDetails | null;
  permissions: string[];
  status: string;
  members: OrganizationMember[];
  expiresAt: string;
}