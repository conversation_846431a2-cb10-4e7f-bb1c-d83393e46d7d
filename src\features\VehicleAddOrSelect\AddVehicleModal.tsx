"use client";

import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
} from "@mui/material";
import { useSelector } from "react-redux";
import { Button } from "@nextui-org/react";
import { PiWarningCircle } from "react-icons/pi";
import toast from "react-hot-toast";

import FormControlledImage from "../../components/ImageSelector/FormControlledImage";
import FormControlledInput from "../../components/Input/FormControlledInput";
import FormControlledRichTextInput from "@/components/Input/FormControlledRichTextInput";

import { useVehicleForm } from "./hooks/useVehicleForm";
import { useRemoveFromCartMutation } from "@/lib/redux/slices/cart/cartApi";

import { ALLOWED_IMAGE_FILE_SIZE } from "./constants";
import FormControlledSelect from "@/components/Select/FormControlledSelect";
import FormControlledBaseVirtualisedSelect from "@/components/VirtualisedSelect/FormControlledVirtualisedSelect";
import { Option } from "@/components/VirtualisedSelect/BaseVirtualisedSelect";

interface AddVehicleModalProps {
  handleClose: () => void;
  isOpen: boolean;
  isUpdate?: boolean;
  ticketId?: string | null;
  teamName?: string;
  requireLocation?: boolean;
  requirePhoneNumber?: boolean;
}

export default function AddVehicleModal({
  handleClose,
  isOpen,
  isUpdate = false,
  ticketId = null,
  teamName = "",
  requireLocation = false,
  requirePhoneNumber = false,
}: AddVehicleModalProps) {
  const [removeFromCart] = useRemoveFromCartMutation();
  const [makeOptions, setMakeOptions] = useState<Option[]>([]);

  const currentVehicleAdditionTicketId = useSelector(
    (state: any) => state.cart.vehicleAdditionTicketId
  );

  const handleModalClose = async () => {
    if (!isUpdate) {
      await removeFromCart({
        id: currentVehicleAdditionTicketId,
        type: "event_ticket",
      });
    }
    if (isUpdate && Object.keys(errors).length > 0) {
      onError(errors);
      return;
    }
    handleClose();
  };

  const handleDialogClose = async (event: any, reason: string) => {
    if (reason === "backdropClick") {
      return;
    }
    handleModalClose();
  };

  const {
    control,
    handleSubmit,
    onSubmit,
    watch,
    errors,
    isFormSubmitting,
    formState,
    loadMakeOptions,
  } = useVehicleForm({ isUpdate, ticketId, handleClose, teamName });
  const { vehicleOptions, makesLoading } = formState;
  const filteredMakes = vehicleOptions?.makes;

  useEffect(() => {
    if (filteredMakes) {
      const newMakeOptions = filteredMakes.map((make) => ({
        label: make.name,
        value: make.id,
      }));
      setMakeOptions(newMakeOptions);
    }
  }, [filteredMakes]);
  const isVehicleRequiredError =
    errors?.vehicle?.vehicle_images?.root?.message || "";

  const onSubmitWrapper = async (data: any) => {
    try {
      await onSubmit(data);
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Something went wrong. Please try again."
      );
    }
  };

  const onError = (errors: any) => {
    // Helper function to recursively extract error messages
    const extractErrors = (obj: any): string[] => {
      if (!obj) return [];
      if (typeof obj === "string") return [obj];
      if (obj.message) return [obj.message];

      return Object.values(obj).flatMap((value: any) => {
        // Handle array of errors (like vehicle_images)
        if (Array.isArray(value)) {
          return value.flatMap((item) => extractErrors(item));
        }
        // Handle nested objects
        if (value && typeof value === "object") {
          return extractErrors(value);
        }
        return [];
      });
    };

    // Extract all error messages
    const errorMessages = extractErrors(errors);

    // Show all error messages
    errorMessages.forEach((message) => {
      if (message) {
        toast.error(message, {
          id: message,
        });
      }
    });
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleDialogClose}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{ sx: { borderRadius: "12px" } }}
    >
      <form onSubmit={handleSubmit(onSubmitWrapper, onError)}>
        <DialogTitle>Add Vehicle</DialogTitle>
        <DialogContent
          dividers
          sx={{
            maxHeight: "calc(100vh - 16rem)",
            overflowY: "auto",
          }}
        >
          {/* Form fields */}
          <div className="grid md:grid-cols-2 gap-4">
            <FormControlledInput
              control={control}
              name="social_media.instagram"
              label="Instagram"
              prefix="@"
              isRequired={true}
            />
          </div>

          <Divider sx={{ margin: "1rem 0" }} />

          {/* Vehicle details */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="flex flex-col gap-3">
              <FormControlledSelect
                control={control}
                name="vehicle.vehicleType"
                label="Vehicle type"
                placeholder="Vehicle type"
                isRequired={true}
                options={vehicleOptions.types}
              />
              <FormControlledInput
                control={control}
                name="vehicle.year"
                label="Year"
                placeholder="Vehicle Year"
                isRequired={true}
              />
              <FormControlledBaseVirtualisedSelect
                name="vehicle.make"
                control={control}
                label="Make"
                loadOptions={loadMakeOptions}
                defaultOptions={makeOptions}
                className="max-w-full bg-transparent rounded-md text-black "
                isLoading={makesLoading}
                isRequired={true}
                placeholder="Select Make"
                variant="bordered"
                errors={errors}
              />
              <FormControlledInput
                control={control}
                name="vehicle.model_name"
                label="Model"
                placeholder="Model Name"
                isRequired={true}
              />
            </div>
          </div>
          <div className="mt-4 mb-16">
            <FormControlledRichTextInput
              control={control}
              name="vehicle.modification_text"
              label="Modifications"
            />
          </div>

          {/* Vehicle images */}
          <div
            className={`${
              isVehicleRequiredError
                ? "border-1 rounded-md border-dashed border-red-400 p-2"
                : ""
            }`}
          >
            {isVehicleRequiredError && (
              <div className="flex gap-x-2 items-center">
                <div className="text-red-500">
                  <PiWarningCircle />
                </div>

                <p className="font-medium text-sm text-red-500">
                  {isVehicleRequiredError}
                </p>
              </div>
            )}
            <div className="grid md:grid-cols-2 gap-4 mt-4">
              <FormControlledImage
                name="vehicle.vehicle_images.0.image"
                control={control}
                label="Image 1"
                className="mb-4"
                fileSize={ALLOWED_IMAGE_FILE_SIZE}
                isRequired={true}
                imageUrl={
                  watch("vehicle.vehicle_images.0.imageUrl") || undefined
                }
                imageId={
                  typeof watch("vehicle.vehicle_images.0.id") === "number"
                    ? (watch("vehicle.vehicle_images.0.id") as number)
                    : undefined
                }
                ticketId={currentVehicleAdditionTicketId}
                accept={{
                  "image/*": [
                    ".jpeg",
                    ".jpg",
                    ".png",
                    ".webp",
                    ".heic",
                    ".heif",
                  ],
                }}
              />
              <FormControlledImage
                name="vehicle.vehicle_images.1.image"
                control={control}
                label="Image 2"
                className="mb-4"
                fileSize={ALLOWED_IMAGE_FILE_SIZE}
                isRequired={true}
                imageUrl={
                  watch("vehicle.vehicle_images.1.imageUrl") || undefined
                }
                imageId={
                  typeof watch("vehicle.vehicle_images.1.id") === "number"
                    ? (watch("vehicle.vehicle_images.1.id") as number)
                    : undefined
                }
                ticketId={currentVehicleAdditionTicketId}
                accept={{
                  "image/*": [
                    ".jpeg",
                    ".jpg",
                    ".png",
                    ".webp",
                    ".heic",
                    ".heif",
                  ],
                }}
              />
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <FormControlledInput
              label="Team / Club Name"
              placeholder="Name"
              className="mt-4"
              control={control}
              name="team_name"
              isRequired={true}
            />
          </div>

          {requirePhoneNumber && (
            <div className="grid md:grid-cols-2 gap-4">
              <FormControlledInput
                label="Phone Number"
                placeholder="Phone Number"
                className="mt-4"
                control={control}
                name="phone_number"
                isRequired={true}
              />
            </div>
          )}
          {requireLocation && (
            <div className="grid md:grid-cols-2 gap-4">
              <FormControlledInput
                label="Location Traveling From"
                placeholder="Location"
                className="mt-4"
                control={control}
                name="location"
                isRequired={true}
              />
            </div>
          )}
        </DialogContent>
        <DialogActions>
          <Button onPress={handleModalClose} variant="bordered" color="danger">
            Cancel
          </Button>
          <Button type="submit" color="primary" isLoading={isFormSubmitting}>
            {isUpdate ? "Update" : "Add"}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}
