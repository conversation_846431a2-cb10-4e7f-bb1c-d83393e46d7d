"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalBody,
  Image,
} from "@nextui-org/react";
import ModalWrapper from "../../components/Modal/ModalWrapper";

import FormControlledInput from "../../components/Input/FormControlledInput";
import TextDivider from "@/components/Divider/TextDivider";
import { useForm, useWatch } from "react-hook-form";
import {
  useCreateOrderMutation,
  useGetCartQuery,
  useRemoveFromCartMutation,
  useSelectGarageVehicleMutation,
} from "@/lib/redux/slices/cart/cartApi";
import { useSelector } from "react-redux";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  SelectVehicleFormData,
  selectVehicleSchema,
} from "./schema/vehicleSchema";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { DIRECT_ORDER_CREATION_SOURCE, TOASTS } from "@/lib/utils/constants";
import { getErrorMessage } from "@/components/FormStatus/error-message";
import { useSessionData } from "@/lib/hooks/useSession";
import { isZero } from "@/lib/utils/numberUtil";

interface IVehicle {
  id: string;
  name: string;
  year: number;
  model: {
    name: string;
    make: {
      name: string;
      type: {
        name: string;
      };
    };
  };
  coverPhoto: {
    photo: string;
  } | null;
  vehicleImages: Array<{
    photo: string;
  }>;
  modifications: Array<any>;
}

export default function SelectVehicleModal({
  handleClose,
  isOpen,
  vehicleData,
  isUpdate = false,
  ticketId = null,
  addVehicleModalOpen,
  onUpdateCartVehicleData,
  requirePhoneNumber = false,
  requireLocation = false,
}) {
  const [modalPhase, setModalPhase] = useState(1);
  const [selectedVehicle, setSelectedVehicle] = useState<IVehicle | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [createOrder, { isLoading }] = useCreateOrderMutation();

  const { data: session } = useSessionData();

  const {
    control,
    getValues,
    setValue,
    formState: { errors },
    trigger,
  } = useForm<SelectVehicleFormData>({
    resolver: zodResolver(selectVehicleSchema),
  });

  const { data: cartData, refetch: refetchCart } = useGetCartQuery();

  const currentVehicleAdditionTicketId = useSelector(
    (state: any) => state.cart.vehicleAdditionTicketId
  );

  useEffect(() => {
    if (isUpdate && onUpdateCartVehicleData?.vehicle) {
      const localTeamName = localStorage.getItem("teamName");
      const localSocialMedia = localStorage.getItem("socialMedia");
      const localPhoneNumber = localStorage.getItem("phoneNumber");
      const localLocation = localStorage.getItem("location");
      if (localTeamName) {
        setValue("team_name", localTeamName);
        setValue("social_media", JSON.parse(localSocialMedia || "{}"));
        setValue("phone_number", localPhoneNumber);
        setValue("location", localLocation);
      }
      // Filter vehicle with matching make, model and year
      const matchingVehicle = vehicleData?.find(
        (vehicle) =>
          vehicle.model.make.name.toLowerCase() ===
            onUpdateCartVehicleData?.vehicle?.makeName?.toLowerCase() &&
          vehicle.model.name.toLowerCase() ===
            onUpdateCartVehicleData?.vehicle?.modelName?.toLowerCase() &&
          vehicle.year === onUpdateCartVehicleData?.vehicle?.year
      );

      setSelectedVehicle(matchingVehicle);
      setModalPhase(2);
    }
  }, [vehicleData, isUpdate, onUpdateCartVehicleData]);

  const router = useRouter();

  const [selectGarageVehicle] = useSelectGarageVehicleMutation();
  const [removeFromCart] = useRemoveFromCartMutation();

  const modalSwitchToAddModal = () => {
    addVehicleModalOpen();
    handleClose();
  };

  const handleModalClose = async () => {
    if (!isUpdate) {
      await removeFromCart({
        id: currentVehicleAdditionTicketId,
        type: "event_ticket",
      });
    }
    handleClose();
  };

  const renderModalBody = () => {
    switch (modalPhase) {
      case 1:
        return (
          <Phase1
            isUpdate={isUpdate}
            vehicleData={vehicleData}
            handleVehicleSelect={handleVehicleSelect}
            modalSwitchToAddModal={modalSwitchToAddModal}
          />
        );
      case 2:
        return (
          <Phase2
            selectedVehicle={selectedVehicle}
            control={control}
            requirePhoneNumber={requirePhoneNumber}
            requireLocation={requireLocation}
          />
        );
      default:
        return null;
    }
  };

  const handleVehicleSelect = ({ vehicle }) => {
    setSelectedVehicle(vehicle);
    setModalPhase(2);
  };

  const handleModalNextBtnClick = async () => {
    if (modalPhase == 2) {
      // Trigger validation
      const isValid = await trigger();
      if (!isValid) {
        return;
      }

      // Validate vehicle selection
      if (!selectedVehicle?.id) {
        toast.error("Please select a vehicle");
        return;
      }

      // Additional validation for required fields
      const phoneNumber = getValues("phone_number")?.toString();
      const location = getValues("location")?.toString();

      if (requirePhoneNumber && !phoneNumber) {
        toast.error("Please enter your phone number");
        setValue("phone_number", "", {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        });
        return;
      }

      if (requireLocation && !location) {
        toast.error("Please enter your location");
        setValue("location", "", {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        });
        return;
      }

      setIsSubmitting(true);
      try {
        const teamName = getValues("team_name");
        const socialMedia = getValues("social_media");

        const privateMetadata: Record<string, string> = {};
        if (phoneNumber) privateMetadata.phone_number = phoneNumber;
        if (location) privateMetadata.location = location;

        const ticketId = isUpdate
          ? onUpdateCartVehicleData?.eventTicket
          : currentVehicleAdditionTicketId;

        if (!ticketId) {
          console.error("No ticket ID found");
          return;
        }

        // Create a plain object without any class instances
        const payload = {
          id: ticketId,
          type: "event_ticket",
          vehicle_id: selectedVehicle.id,
          team_name: teamName,
          social_media: JSON.parse(JSON.stringify(socialMedia)), // Convert to plain object
          private_metadata: privateMetadata,
        };

        await selectGarageVehicle(payload);

        if (teamName) localStorage.setItem("teamName", teamName);
        if (socialMedia)
          localStorage.setItem("socialMedia", JSON.stringify(socialMedia));
        if (phoneNumber) localStorage.setItem("phoneNumber", phoneNumber);
        if (location) localStorage.setItem("location", location);
        if (isUpdate) {
          await refetchCart();
        }

        setModalPhase(1);
        handleClose();
        router.push("/cart");
      } catch (error) {
        console.error("Error saving vehicle data:", error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleGoBack = () => {
    setModalPhase(1);
  };

  return (
    <ModalWrapper
      title="Select Vehicle"
      isOpen={isOpen}
      handleClose={handleModalClose}
      size="lg"
      placement="center"
      className="sm:!max-w-[32rem] w-full mx-auto sm:!rounded-lg"
    >
      <ModalBody className="max-h-[calc(100dvh-10rem)] sm:max-h-[70vh] overflow-y-auto custom-scrollbar px-6 py-6">
        {renderModalBody()}
      </ModalBody>
      <ModalFooter className="py-2 border-t-1 border-[#E3E3E3] bg-white">
        {modalPhase === 2 && (
          <Button
            onPress={handleGoBack}
            size="sm"
            className="bg-transparent font-semibold"
          >
            Back
          </Button>
        )}
        <Button
          variant="bordered"
          onPress={handleModalClose}
          size="sm"
          className="border-1 border-[#D7D7D7] rounded-lg font-semibold"
          isDisabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          onPress={handleModalNextBtnClick}
          size="sm"
          className="bg-[#007AFF] text-[#fff] rounded-lg font-semibold"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
        >
          Next
        </Button>
      </ModalFooter>
    </ModalWrapper>
  );
}

const VehicleCard = ({
  isSelected,
  vehicle,
  handleVehicleSelect,
  isDisabled = false,
}) => {
  const vehicleImage =
    vehicle?.coverPhoto?.photo || vehicle?.vehicleImages[0]?.photo;

  return (
    <div>
      <div className="bg-[#EDEDED] p-2 rounded-lg flex justify-between items-center gap-4 pr-4">
        <div className="flex gap-4">
          {vehicleImage ? (
            <Image
              src={vehicleImage}
              width={75}
              height={75}
              className="object-cover"
            />
          ) : (
            <div className="w-[75px] h-[75px] bg-gray-200 rounded-2xl flex items-center justify-center text-gray-400 text-xs">
              No Image
            </div>
          )}
          <div className="mt-1">
            <p className="text-[#0D0D0D] text-[15px] font-bold">
              {vehicle?.name}
            </p>
            <p className="text-[#0D0D0D] text-[12px]">{vehicle?.year}</p>
            <div className="flex gap-1 items-center">
              <Image src="/modification.svg" />
              <p className="text-[#0D0D0D] text-[12px] tracking-wide">
                {vehicle?.modifications?.length} Modifications
              </p>
            </div>
          </div>
        </div>
        {!isDisabled ? (
          isSelected ? (
            <div className="rounded-full w-[35px] h-[35px] flex justify-center items-center bg-[#2FFF0080]">
              <Image src="/check.svg" alt="selected" />
            </div>
          ) : (
            <Button
              onPress={() => handleVehicleSelect({ vehicle })}
              className="bg-[#007AFF] h-[35px] text-[#fff] rounded-xl font-medium"
            >
              Select
            </Button>
          )
        ) : (
          <Button
            className="bg-gray-400 h-[35px] text-[#fff] rounded-xl font-medium cursor-not-allowed"
            disabled
          >
            Select
          </Button>
        )}
      </div>
      {!vehicleImage && (
        <div className="text-xs text-red-500">
          Please upload at least one image for your vehicle to select it
        </div>
      )}
    </div>
  );
};

const Phase1 = ({
  isUpdate,
  vehicleData,
  handleVehicleSelect,
  modalSwitchToAddModal,
}) => {
  return (
    <div className="pb-8">
      <div className="md:w-[70%] mx-auto flex flex-col gap-3">
        {vehicleData?.map((vehicle) => {
          const isDisabled =
            (vehicle?.coverPhoto?.photo ? 1 : 0) +
              (vehicle?.vehicleImages?.length || 0) <
            2;
          return (
            <VehicleCard
              key={vehicle?.id}
              vehicle={vehicle}
              handleVehicleSelect={handleVehicleSelect}
              isDisabled={isDisabled}
              isSelected={false}
            />
          );
        })}
      </div>

      {!isUpdate && (
        <>
          <div className="w-[85%] mx-auto">
            <TextDivider text="OR" className="my-8" />
          </div>

          <div className="w-[85%] md:w-[75%] rounded-lg border-1 border-dotted border-black mx-auto py-8 flex justify-center items-center">
            <Button
              onPress={modalSwitchToAddModal}
              className="bg-[#007AFF] text-[#fff] rounded-lg"
            >
              Add new vehicle
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

const Phase2 = ({
  control,
  selectedVehicle,
  requirePhoneNumber,
  requireLocation,
}) => {
  return (
    <div>
      <div className="md:w-[70%] mx-auto flex flex-col gap-3">
        <VehicleCard
          isSelected={true}
          vehicle={selectedVehicle}
          handleVehicleSelect={() => {}}
        />
      </div>

      <div className="w-[92%] mx-auto">
        <TextDivider text="Additional info needed" className="my-2" />
      </div>

      <div className="md:w-[70%]">
        <FormControlledInput
          label="Team / Club Name"
          placeholder="Name"
          className="mt-4"
          control={control}
          name="team_name"
          isRequired={true}
        />
      </div>
      <div className="mt-2 md:w-[70%]">
        <FormControlledInput
          control={control}
          name="social_media.instagram"
          label="Instagram"
          prefix="@"
          isRequired={true}
        />
      </div>
      {requirePhoneNumber && (
        <div className="mt-2 md:w-[70%]">
          <FormControlledInput
            label="Phone Number"
            placeholder="Phone Number"
            className="mt-4"
            control={control}
            name="phone_number"
            isRequired={true}
          />
        </div>
      )}
      {requireLocation && (
        <div className="mt-2 md:w-[70%]">
          <FormControlledInput
            label="Location Traveling From"
            placeholder="Location"
            className="mt-4"
            control={control}
            name="location"
            isRequired={true}
          />
        </div>
      )}
    </div>
  );
};
