import { useCallback, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSelector } from "react-redux";

import { vehicleSchema, VehicleFormData } from "../schema/vehicleSchema";
import {
  useAddVehicleImageMutation,
  useAddVehicleMutation,
  useGetCartQuery,
} from "@/lib/redux/slices/cart/cartApi";
import {
  useGetVehicleMakesListQuery,
  useGetVehicleTypesListQuery,
} from "@/lib/redux/slices/vehicles/vehiclesApi";
import { Option } from "@/components/VirtualisedSelect/BaseVirtualisedSelect";

interface UseVehicleFormProps {
  isUpdate?: boolean;
  ticketId?: string | null;
  handleClose: () => void;
  teamName?: string;
  requireLocation?: boolean;
  requirePhoneNumber?: boolean;
}

export const useVehicleForm = ({
  isUpdate = false,
  ticketId = null,
  handleClose,
  teamName,
  requireLocation = false,
  requirePhoneNumber = false,
}: UseVehicleFormProps) => {
  const router = useRouter();

  const currentVehicleAdditionTicketId = useSelector(
    (state: any) => state.cart.vehicleAdditionTicketId
  );

  const {
    data: cartData,
    isUninitialized,
    refetch: refetchCart,
  } = useGetCartQuery();

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetchCart = useCallback(() => {
    if (!isUninitialized) {
      refetchCart();
    }
  }, [isUninitialized, refetchCart]);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    getValues,
    setError,
    formState: { errors },
    trigger,
  } = useForm<VehicleFormData>({
    resolver: zodResolver(vehicleSchema),
    defaultValues: {
      social_media: { instagram: "" },
      vehicle: {
        vehicleType: "",
        year: "",
        make: "",
        model_name: "",
        modification_text: "",
        vehicle_images: [
          {
            image: null,
            type: "1",
            tag: "image1",
            imageUrl: null,
            id: null,
          },
          {
            image: null,
            type: "1",
            tag: "image2",
            imageUrl: null,
            id: null,
          },
        ],
      },
      team_name: "",
      location: "",
      phone_number: "",
    },
  });
  const selectedVehicleType = watch("vehicle.vehicleType");

  const [addVehicle, { isLoading: isAddingVehicle }] = useAddVehicleMutation();

  const [addVehicleImage, { isLoading: isAddingVehicleImage }] =
    useAddVehicleImageMutation();

  const { data: allTypes } = useGetVehicleTypesListQuery({});

  const { data: allMakes, isLoading: makesLoading } =
    useGetVehicleMakesListQuery({});

  useEffect(() => {
    if (isUpdate && cartData?.checkout?.lines?.[0]) {
      const lineItem = cartData.checkout.lines[0];
      const vehicle = lineItem.vehicle;
      const userDataFromVehicleForm = JSON.parse(
        localStorage.getItem("userDataFromVehicleForm") || "{}"
      );

      const defaultImageStructure = {
        image: null,
        type: "1",
        imageUrl: null,
        id: null,
      };

      reset({
        vehicle: {
          vehicleType:
            vehicle?.makeType || userDataFromVehicleForm?.vehicle_type,
          year: vehicle?.year?.toString(),
          make: vehicle?.makeId || vehicle?.customMakeName,
          model_name: vehicle?.customModelName,
          modification_text: vehicle?.modificationText,
          vehicle_images: [
            {
              ...defaultImageStructure,
              tag: "image1",
              imageUrl:
                vehicle?.images?.find((img) => img.tag === "image1")?.image ||
                null,
              id:
                vehicle?.images?.find((img) => img.tag === "image1")?.id ||
                null,
            },
            {
              ...defaultImageStructure,
              tag: "image2",
              imageUrl:
                vehicle?.images?.find((img) => img.tag === "image2")?.image ||
                null,
              id:
                vehicle?.images?.find((img) => img.tag === "image2")?.id ||
                null,
            },
          ],
        },
        social_media: userDataFromVehicleForm?.social_media,
        team_name: teamName,
        location: userDataFromVehicleForm?.location || "",
        phone_number: userDataFromVehicleForm?.phone_number || "",
      });
    }
  }, [isUpdate, cartData, reset, setValue, getValues]);

  const onSubmit = async (data: VehicleFormData) => {
    try {
      const isValid = await trigger();
      if (!isValid) {
        return;
      }

      // Validate required fields based on props
      if (requireLocation && !data.location?.trim()) {
        setError("location", {
          type: "manual",
          message: "Location is required",
        });
        return;
      }

      if (requirePhoneNumber && !data.phone_number?.trim()) {
        setError("phone_number", {
          type: "manual",
          message: "Phone number is required",
        });
        return;
      }

      // Add all the non-file data
      const vehicleDataPayload = {
        id: currentVehicleAdditionTicketId,
        type: "event_ticket",
        year: data.vehicle.year,
        make: data.vehicle.make,
        model_name: data.vehicle.model_name,
        modification_text: data.vehicle.modification_text,
        name: `${data.vehicle.make} ${data.vehicle.model_name}`,
        social_media: data.social_media,
        team_name: data.team_name,
        vehicle_type: data.vehicle.vehicleType,
        private_metadata: {
          location: data.location,
          phone_number: data.phone_number,
        },
      };

      // First, create/update the vehicle
      await addVehicle(vehicleDataPayload).unwrap();

      // Handle image uploads differently for update vs create
      if (isUpdate) {
        // Only upload images that are File objects (newly selected images)
        const imageUploadPromises = data.vehicle.vehicle_images.map(
          async (imageData, index) => {
            // Skip upload if there's no new image file
            if (!imageData.image) return;

            const formData = new FormData();
            formData.append("id", currentVehicleAdditionTicketId);
            formData.append("image", imageData.image);
            formData.append("type", "event_ticket");
            formData.append("tag", imageData.tag);
            formData.append("vehicle_image_type", "1");

            try {
              await addVehicleImage(formData).unwrap();
            } catch (error) {
              console.error(
                `Error uploading image with tag ${imageData.tag}:`,
                error
              );
              throw error;
            }
          }
        );

        // Wait for all new image uploads to complete
        await Promise.all(imageUploadPromises);
      } else {
        // Original image upload logic for new vehicles
        const imageUploadPromises = data.vehicle.vehicle_images.map(
          async (imageData) => {
            if (imageData.image) {
              const formData = new FormData();
              formData.append("id", currentVehicleAdditionTicketId);
              formData.append("image", imageData.image);
              formData.append("type", "event_ticket");
              formData.append("tag", imageData.tag);
              formData.append("vehicle_image_type", "1");

              try {
                await addVehicleImage(formData).unwrap();
              } catch (error) {
                console.error(
                  `Error uploading image with tag ${imageData.tag}:`,
                  error
                );
                throw error;
              }
            }
          }
        );

        // Wait for all images to be uploaded
        await Promise.all(imageUploadPromises);
      }

      router.push("/cart");
    } catch (error) {
      console.error("Error saving vehicle data:", error);
      throw error; // Re-throw to be handled by the wrapper
    } finally {
      const userData = {
        social_media: getValues("social_media"),
        location: getValues("location"),
        phone_number: getValues("phone_number"),
      };
      localStorage.setItem("userDataFromVehicleForm", JSON.stringify(userData));
      handleClose();
      reset();
      if (isUpdate) {
        safeRefetchCart();
      }
    }
  };

  const filteredMakes = useMemo(() => {
    if (!allMakes) return [];
    return allMakes.filter(
      (make) => make?.type?.id?.toLowerCase() === selectedVehicleType
    );
  }, [allMakes, selectedVehicleType]);

  const formState = useMemo(
    () => ({
      makesLoading,
      vehicleOptions: {
        makes: filteredMakes,
        types: allTypes?.map((type) => ({
          label: type?.name,
          value: type?.id,
        })),
      },
    }),
    [makesLoading, filteredMakes, allTypes]
  );

  const loadMakeOptions = useCallback(
    (inputValue: string, callback: (options: Option[]) => void) => {
      const sanitizedInput = inputValue?.toLowerCase()?.trim() ?? "";

      const filteredOptions = inputValue
        ? filteredMakes
            ?.filter((option: { name?: string; id?: string }) =>
              option?.name?.toLowerCase()?.includes(sanitizedInput)
            )
            ?.map((make) => ({
              label: make?.name ?? "",
              value: make?.id ?? "",
            }))
        : filteredMakes?.map((make) => ({
            label: make?.name ?? "",
            value: make?.id ?? "",
          }));
      callback(filteredOptions);
    },
    [filteredMakes]
  );

  return {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    getValues,
    onSubmit,
    errors,
    isFormSubmitting: isAddingVehicle || isAddingVehicleImage,
    formState,
    loadMakeOptions,
  };
};
