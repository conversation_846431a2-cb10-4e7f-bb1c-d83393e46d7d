import { z } from "zod";

// Add this type for TypeScript
export interface VehicleImage {
  image: File | null;
  imageUrl?: string | null;
  type: string;
  tag: string;
  id?: string | number | null;
}

export const vehicleSchema = z.object({
  social_media: z.object({
    instagram: z.string().min(1, "Instagram is required"),
  }),
  vehicle: z.object({
    vehicleType: z.string().min(1, "Vehicle type is required"),
    year: z.string().min(1, "Vehicle year is required"),
    make: z.string().min(2, "Vehicle make is required"),
    model_name: z.string().min(2, "Vehicle model is required"),
    modification_text: z.string(),
    vehicle_id: z.string().optional(),
    vehicle_images: z.array(
      z.object({
        image: z.instanceof(File).nullable(),
        imageUrl: z.string().nullable().optional(),
        type: z.string(),
        tag: z.string(),
        id: z.union([z.string(), z.number(), z.null()]).optional()
      })
    ),
  }),
  team_name: z.string().min(1, "Team/Club name is required"),
  location: z.string().optional(),
  phone_number: z.string()
    .regex(/^\+?[1-9]\d{9,14}$/, "Please enter a valid phone number")
    .optional()
    .or(z.literal('')),
}).superRefine((data, ctx) => {
  // Check for empty spaces in string fields
  if (data.social_media.instagram.trim() === "") {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Instagram cannot be empty spaces",
      path: ["social_media", "instagram"],
    });
  }

  if (data.vehicle.year.trim() === "") {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Vehicle year cannot be empty spaces",
      path: ["vehicle", "year"],
    });
  }

  if (data.vehicle.make.trim() === "") {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Vehicle make cannot be empty spaces",
      path: ["vehicle", "make"],
    });
  }

  if (data.vehicle.model_name.trim() === "") {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Vehicle model cannot be empty spaces",
      path: ["vehicle", "model_name"],
    });
  }

  if (data.team_name.trim() === "") {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Team/Club name cannot be empty spaces",
      path: ["team_name"],
    });
  }

  // Check vehicle images
  data.vehicle.vehicle_images.forEach((img, index) => {
    const hasImage = img.image !== null;
    const hasImageUrl = typeof img.imageUrl === 'string' && img.imageUrl !== '';

    if (!hasImage && !hasImageUrl) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please add your vehicle image",
        path: [`vehicle`, `vehicle_images`, index],
      });
    }
  });
});

export const selectVehicleSchema = z.object({
  team_name: z.string(),
  social_media: z.object({
    instagram: z.string().min(1, "Instagram is required"),
  }),
  phone_number: z.string()
    .regex(/^\+?[1-9]\d{9,14}$/, "Please enter a valid phone number")
    .optional()
    .or(z.literal('')),
  location: z.string().optional().or(z.literal('')),
}).superRefine((data, ctx) => {
  if (!data.team_name || data.team_name.trim().length === 0) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Team/Club name is required and cannot be empty or just spaces",
      path: ["team_name"],
      fatal: true
    });
  }

  if (data.social_media.instagram.trim() === "") {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Instagram cannot be empty spaces",
      path: ["social_media", "instagram"],
    });
  }
});

export type VehicleFormData = z.infer<typeof vehicleSchema>;
export type SelectVehicleFormData = z.infer<typeof selectVehicleSchema>;
