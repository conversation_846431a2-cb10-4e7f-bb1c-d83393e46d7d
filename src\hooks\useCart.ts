import { useState, useEffect } from 'react';

interface CartData {
  id: string;
  items: any[];
  total: number;
}

interface UseCartReturn {
  data: CartData | null;
  isLoading: boolean;
  error: Error | null;
}

const CART_STORAGE_KEYS = {
  CART_TOKEN: 'cartToken',
  USER: 'user'
};

const CART_HEADERS = {
  X_CART_TOKEN: 'X-Cart-Token'
};

const getCartTokenHeaders = () => {
  if (typeof window !== "undefined") {
    const cartToken = localStorage.getItem(CART_STORAGE_KEYS.CART_TOKEN);
    const user = localStorage.getItem(CART_STORAGE_KEYS.USER);
    
    const hasUser = user && user !== "null" && user !== "{}" && user !== "";

    return cartToken && !hasUser
      ? { [CART_HEADERS.X_CART_TOKEN]: cartToken }
      : {};
  }
  return {};
};

export const useCart = (): UseCartReturn => {
  const [data, setData] = useState<CartData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchCart = async () => {
      try {
        const headers = getCartTokenHeaders();
        
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}api/cart`, {
          headers: {
            'Content-Type': 'application/json',
            ...headers
          },
          credentials: "include",
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch cart data');
        }

        const cartData = await response.json();
        setData(cartData);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An error occurred'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchCart();
  }, []);

  return { data, isLoading, error };
}; 