"use client";

import { ReactNode, useState, useEffect } from "react";
import { UserDetailsContext } from "../hooks/useUserDetails";
import { useAppDispatch, useAppSelector } from "../redux/hooks";
import { setUser, userSelector } from "../redux/slices/auth/authSlice";

export function UserDetailsProvider({ children }: { children: ReactNode }) {
  const user = useAppSelector(userSelector);
  const dispatch = useAppDispatch();
  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      const _user = JSON.parse(storedUser);
      dispatch(setUser(_user));
    }
  }, []);

  return (
    <UserDetailsContext.Provider value={{ user }}>
      {children}
    </UserDetailsContext.Provider>
  );
}
