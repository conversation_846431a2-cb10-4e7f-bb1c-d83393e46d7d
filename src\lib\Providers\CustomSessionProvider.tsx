"use client";

import React, {
  Context,
  createContext,
  type PropsWithChildren,
  useEffect,
  useMemo,
  useState,
} from "react";
import { usePathname } from "next/navigation";
import type { Session } from "next-auth";
import { getCsrfToken } from "next-auth/react";
import { AUTH_STATUS } from "../utils/constants";
import { useDispatch } from "react-redux";
import { setUser, clearUser } from "@/lib/redux/slices/auth/authSlice";
import { keysToCamel } from "../utils/snakeCaseConvertor";

/**
 * Provider props
 */
type TSessionProviderProps = PropsWithChildren<{
  session?: Session | null;
}>;

/**
 * Type of the returned Provider elements with data which contains session data, status that shows the state of the Provider, and update which is the function to update session data
 */
type TUpdateSession = (data?: any) => Promise<Session | null | undefined>;
export type TSessionContextValue = {
  data: Session | null;
  status: string;
  update: TUpdateSession;
  setLoading: (loading: boolean) => void;
};

/**
 * React context to keep session through renders
 */
export const SessionContext: Context<TSessionContextValue | undefined> =
  createContext?.<TSessionContextValue | undefined>(undefined);

export function SessionDataProvider({
  session: initialSession = null,
  children,
}: TSessionProviderProps) {
  const [session, setSession] = useState<Session | null>(initialSession);
  const [loading, setLoading] = useState<boolean>(!initialSession);
  const pathname: string = usePathname();
  const dispatch = useDispatch();


  const fetchSession = async () => {
    if (!initialSession) {
      const fetchedSession: Session | null = await fetchSessionData();
      if (fetchedSession) {
        const updatedSession=keysToCamel(fetchedSession)
        setSession(updatedSession);

        dispatch(
          setUser({
            ...updatedSession.user,
            accessToken: fetchedSession.accessToken,
            accessTokenExpiration: fetchedSession.accessTokenExpiration,
            refreshToken: fetchedSession.refreshToken,
          })
        );
      }
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSession().finally();
  }, [initialSession, pathname]);

  function getSessionStatus() {
    if (loading) return AUTH_STATUS.LOADING;
    return session?.user
      ? AUTH_STATUS.AUTHENTICATED
      : AUTH_STATUS.UNAUTHENTICATED;
  }

  async function onUpdate(data?: any) {
    if (loading || !session?.user) return;
    setLoading(true);
    try {
      const fetchOptions = await createFetchOptions(data);
      const fetchedSession = await fetchSessionData(fetchOptions);
      const updatedSession = keysToCamel(fetchedSession);
      setSession(updatedSession);
      if (updatedSession) {
        dispatch(setUser(updatedSession));
      } else {
        dispatch(clearUser());
      }
      return updatedSession;
    } catch (error) {
      console.error("Error updating session:", error);
    } finally {
      setLoading(false);
    }
  }

  const sessionData = useMemo(
    () => ({
      data: session,
      status: getSessionStatus(),
      update: onUpdate,
      setLoading,
    }),
    [loading, session]
  );

  const createFetchOptions = async (data) => {
    const options: RequestInit = {
      headers: {
        "Content-Type": "application/json",
      },
    };

    if (data) {
      options.method = "POST";
      options.body = JSON.stringify({
        csrfToken: await getCsrfToken(),
        data,
      });
    }

    return options;
  };

  const fetchSessionData = async (fetchOptions?: any) => {
    const response = await fetch("/api/auth/session", fetchOptions);

    if (!response.ok) {
      throw new Error("Failed to fetch session");
    }

    return response.json();
  };

  return (
    <SessionContext.Provider value={sessionData}>
      {children}
    </SessionContext.Provider>
  );
}
