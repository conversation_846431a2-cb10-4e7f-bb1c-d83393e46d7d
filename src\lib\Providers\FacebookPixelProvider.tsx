// lib/Providers/FacebookPixelProvider.tsx
"use client";

import { useEffect, useRef, createContext, useContext } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import Script from "next/script";

declare global {
  interface Window {
    fbq: any;
    _fbq: any;
  }
}

interface FacebookPixelProviderProps {
  children: React.ReactNode;
}

interface PixelContextType {
  registerPixel: (pixelId: string) => void;
  isPixelInitialized: (pixelId: string) => boolean;
}

export const PixelContext = createContext<PixelContextType | null>(null);

// Hook to use pixel context
export function usePixel() {
  const context = useContext(PixelContext);
  if (!context) {
    throw new Error("usePixel must be used within a FacebookPixelProvider");
  }
  return context;
}

export function FacebookPixelProvider({
  children,
}: FacebookPixelProviderProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const initialized = useRef(false);
  const pixelsInitialized = useRef<Set<string>>(new Set());
  const scriptLoaded = useRef(false);

  // In development mode, provide a no-op context
  if (process.env.NODE_ENV !== "production") {
    const devContextValue: PixelContextType = {
      registerPixel: () => {}, // No-op in development
      isPixelInitialized: () => false, // Always false in development
    };

    return (
      <PixelContext.Provider value={devContextValue}>
        {children}
      </PixelContext.Provider>
    );
  }

  const initializeFacebookPixel = () => {
    if (!window.fbq) {
      window.fbq = function (...args: any[]) {
        window.fbq.callMethod
          ? window.fbq.callMethod.apply(window.fbq, args)
          : window.fbq.queue.push(args);
      };
      window._fbq = window.fbq;
      window.fbq.push = window.fbq;
      window.fbq.version = "2.0";
      window.fbq.queue = [];
    }
  };

  const registerPixel = (pixelId: string) => {
    if (!pixelId) return;

    try {
      // Only proceed if the script is loaded
      if (!scriptLoaded.current) {
        return;
      }

      // Only initialize pixels once
      if (pixelsInitialized.current.has(pixelId)) {
        return;
      }

      // Initialize the pixel
      window.fbq("init", pixelId);
      pixelsInitialized.current.add(pixelId);

      // Send a PageView event
      window.fbq("trackSingle", pixelId, "PageView");
    } catch (error) {
      console.error(`Failed to register pixel ${pixelId}:`, error);
    }
  };

  const isPixelInitialized = (pixelId: string) => {
    return pixelsInitialized.current.has(pixelId);
  };

  // Initialize base fbq object
  useEffect(() => {
    initializeFacebookPixel();
  }, []);

  // Handle pixel initialization after script loads
  const handleScriptLoad = () => {
    try {
      scriptLoaded.current = true;
      initializeFacebookPixel();

      initialized.current = true;
    } catch (error) {
      console.error("Failed to initialize Facebook Pixel:", error);
    }
  };

  // Track route changes
  useEffect(() => {
    if (initialized.current) {
      try {
        pixelsInitialized.current.forEach((pixelId) => {
          window.fbq("trackSingle", pixelId, "PageView");
        });
      } catch (error) {
        console.error("Failed to track PageView:", error);
      }
    }
  }, [pathname, searchParams]);

  // Context value
  const contextValue: PixelContextType = {
    registerPixel,
    isPixelInitialized,
  };

  return (
    <PixelContext.Provider value={contextValue}>
      {children}

      <Script
        id="fb-pixel"
        strategy="afterInteractive"
        rel="preconnect"
        onLoad={handleScriptLoad}
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
          `,
        }}
      />

      {Array.from(pixelsInitialized.current).map((pixelId) => (
        <noscript key={pixelId}>
          <img
            height="1"
            width="1"
            style={{ display: "none" }}
            src={`https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1`}
            alt=""
          />
        </noscript>
      ))}
    </PixelContext.Provider>
  );
}
