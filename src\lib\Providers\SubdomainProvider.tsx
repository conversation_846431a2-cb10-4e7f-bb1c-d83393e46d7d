"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { getSubdomainInfo, SubdomainInfo } from "@/lib/utils/subdomainUtils";

interface SubdomainContextType {
  subdomainInfo: SubdomainInfo;
  isWhitelabel: boolean;
  organizerSlug: string | null;
}

const SubdomainContext = createContext<SubdomainContextType | undefined>(
  undefined
);

interface SubdomainProviderProps {
  children: React.ReactNode;
  initialSubdomainInfo?: SubdomainInfo;
}

export function SubdomainProvider({
  children,
  initialSubdomainInfo,
}: SubdomainProviderProps) {
  const [subdomainInfo, setSubdomainInfo] = useState<SubdomainInfo>(
    initialSubdomainInfo || {
      isSubdomain: false,
      organizerSlug: null,
      hostname: "",
    }
  );

  useEffect(() => {
    // Only run on client side
    if (typeof window !== "undefined") {
      const hostname = window.location.hostname;
      const info = getSubdomainInfo(hostname);
      setSubdomainInfo(info);
    }
  }, []);

  const contextValue: SubdomainContextType = {
    subdomainInfo,
    isWhitelabel: subdomainInfo.isSubdomain,
    organizerSlug: subdomainInfo.organizerSlug,
  };

  return (
    <SubdomainContext.Provider value={contextValue}>
      {children}
    </SubdomainContext.Provider>
  );
}

export function useSubdomain(): SubdomainContextType {
  const context = useContext(SubdomainContext);
  if (context === undefined) {
    throw new Error("useSubdomain must be used within a SubdomainProvider");
  }
  return context;
}

// Hook to get organizer slug for API calls
export function useOrganizerSlug(): string | null {
  const { organizerSlug } = useSubdomain();
  return organizerSlug;
}
