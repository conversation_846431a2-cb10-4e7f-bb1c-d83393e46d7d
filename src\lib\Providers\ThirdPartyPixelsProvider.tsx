"use client";

import React, { useEffect, useRef, createContext, useContext } from "react";

declare global {
  interface Window {
    gtag: any;
    snaptr: any;
    ttq: any;
  }
}

interface ThirdPartyPixelsProviderProps {
  children: React.ReactNode;
  ga4Ids?: string[];
  snapIds?: string[];
  tiktokIds?: string[];
}

interface ThirdPartyPixelContextType {
  // GA4 functions
  registerGA4Pixel: (pixelId: string) => void;
  isGA4PixelInitialized: (pixelId: string) => boolean;

  // Snap Pixel functions
  registerSnapPixel: (pixelId: string) => void;
  isSnapPixelInitialized: (pixelId: string) => boolean;

  // TikTok Pixel functions
  registerTikTokPixel: (pixelId: string) => void;
  isTikTokPixelInitialized: (pixelId: string) => boolean;
}

const ThirdPartyPixelContext = createContext<ThirdPartyPixelContextType | null>(
  null
);

export const useThirdPartyPixels = () => {
  const context = useContext(ThirdPartyPixelContext);
  if (!context) {
    throw new Error(
      "useThirdPartyPixels must be used within ThirdPartyPixelsProvider"
    );
  }
  return context;
};

export function ThirdPartyPixelsProvider({
  children,
  ga4Ids = [],
  snapIds = [],
  tiktokIds = [],
}: ThirdPartyPixelsProviderProps) {
  // In development mode, provide a no-op context
  if (process.env.NODE_ENV !== "production") {
    const devContextValue: ThirdPartyPixelContextType = {
      registerGA4Pixel: () => {}, // No-op in development
      isGA4PixelInitialized: () => false, // Always false in development
      registerSnapPixel: () => {}, // No-op in development
      isSnapPixelInitialized: () => false, // Always false in development
      registerTikTokPixel: () => {}, // No-op in development
      isTikTokPixelInitialized: () => false, // Always false in development
    };

    return (
      <ThirdPartyPixelContext.Provider value={devContextValue}>
        {children}
      </ThirdPartyPixelContext.Provider>
    );
  }

  const ga4Loaded = useRef(false);
  const snapLoaded = useRef(false);
  const tiktokLoaded = useRef(false);

  // Track initialized pixels for each platform
  const ga4PixelsInitialized = useRef<Set<string>>(new Set());
  const snapPixelsInitialized = useRef<Set<string>>(new Set());
  const tiktokPixelsInitialized = useRef<Set<string>>(new Set());

  /******************** GA4 *************************/
  const initializeGA4Base = () => {
    if (typeof window === "undefined" || ga4Loaded.current) return;
    ga4Loaded.current = true;

    // Initialize dataLayer
    // @ts-ignore – dataLayer is global
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      // @ts-ignore – gtag pushed to dataLayer
      window.dataLayer.push(arguments);
    }
    // @ts-ignore – expose gtag globally
    window.gtag = gtag;
    gtag("js", new Date());
  };

  const registerGA4Pixel = (pixelId: string) => {
    if (!pixelId || ga4PixelsInitialized.current.has(pixelId)) return;

    try {
      // Initialize base GA4 if not done
      initializeGA4Base();

      // Load the GA script for this ID
      const script = document.createElement("script");
      script.src = `https://www.googletagmanager.com/gtag/js?id=${pixelId}`;
      script.async = true;
      document.head.appendChild(script);

      // @ts-ignore – gtag is global
      window.gtag("config", pixelId, { send_page_view: true });

      ga4PixelsInitialized.current.add(pixelId);
    } catch (error) {
      console.error(`Failed to register GA4 pixel ${pixelId}:`, error);
    }
  };

  const isGA4PixelInitialized = (pixelId: string) => {
    return ga4PixelsInitialized.current.has(pixelId);
  };

  /******************** Snap Pixel *************************/
  const initializeSnapBase = () => {
    if (typeof window === "undefined" || snapLoaded.current) return;
    snapLoaded.current = true;

    (function (e: any, t: any, n: any) {
      if (e.snaptr) return;
      const a: any = (e.snaptr = function () {
        // eslint-disable-next-line prefer-rest-params
        a.handleRequest
          ? a.handleRequest.apply(a, arguments)
          : a.queue.push(arguments);
      });
      a.queue = [];
      const s = "script";
      const r = t.createElement(s);
      r.async = !0;
      r.src = n;
      const u = t.getElementsByTagName(s)[0];
      u.parentNode!.insertBefore(r, u);
    })(window, document, "https://sc-static.net/scevent.min.js");
  };

  const registerSnapPixel = (pixelId: string) => {
    if (!pixelId || snapPixelsInitialized.current.has(pixelId)) return;

    try {
      // Initialize base Snap if not done
      initializeSnapBase();

      // @ts-ignore – snaptr is global
      window.snaptr("init", pixelId);
      // Track initial page view
      // @ts-ignore
      window.snaptr("track", "PAGE_VIEW");

      snapPixelsInitialized.current.add(pixelId);
    } catch (error) {
      console.error(`Failed to register Snap pixel ${pixelId}:`, error);
    }
  };

  const isSnapPixelInitialized = (pixelId: string) => {
    return snapPixelsInitialized.current.has(pixelId);
  };

  /******************** TikTok Pixel *************************/
  const initializeTikTokBase = () => {
    if (typeof window === "undefined" || tiktokLoaded.current) return;
    tiktokLoaded.current = true;

    (function (w: any, d: any, t: any) {
      if (w[t]) return;
      const ttq: any = (w[t] = []);
      ttq.methods =
        "page track identify instances debug enableCookie disableCookie setUserProperties alias clearUserProperties on off once ready reset setConfig".split(
          " "
        );
      ttq.setAndDefer = function (t: any, e: any) {
        t[e] = function () {
          t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
        };
      };
      for (let i = 0; i < ttq.methods.length; i++) {
        ttq.setAndDefer(ttq, ttq.methods[i]);
      }
      ttq.instance = function (t: any) {
        const e = ttq._i[t] || [];
        for (let n = 0; n < ttq.methods.length; n++) {
          ttq.setAndDefer(e, ttq.methods[n]);
        }
        return e;
      };
      ttq.load = function (e: any, n: any) {
        const i = "https://analytics.tiktok.com/i18n/pixel/events.js";
        ttq._i = ttq._i || {};
        ttq._i[e] = [];
        ttq._i[e]._u = i;
        ttq._t = ttq._t || {};
        ttq._t[e] = +new Date();
        ttq._o = ttq._o || {};
        ttq._o[e] = n || {};
        const o = document.createElement("script");
        o.type = "text/javascript";
        o.async = true;
        o.src = i + "?sdkid=" + e + "&lib=" + t;
        const s = document.getElementsByTagName("script")[0];
        s.parentNode!.insertBefore(o, s);
      };
    })(window, document, "ttq");
  };

  const registerTikTokPixel = (pixelId: string) => {
    if (!pixelId || tiktokPixelsInitialized.current.has(pixelId)) return;

    try {
      // Initialize base TikTok if not done
      initializeTikTokBase();

      // @ts-ignore – ttq global
      const ttq = window.ttq;
      ttq.load(pixelId);
      ttq.page();

      tiktokPixelsInitialized.current.add(pixelId);
    } catch (error) {
      console.error(`Failed to register TikTok pixel ${pixelId}:`, error);
    }
  };

  const isTikTokPixelInitialized = (pixelId: string) => {
    return tiktokPixelsInitialized.current.has(pixelId);
  };

  // Initialize with provided IDs on mount
  useEffect(() => {
    if (process.env.NODE_ENV !== "production") return;

    ga4Ids.forEach(registerGA4Pixel);
    snapIds.forEach(registerSnapPixel);
    tiktokIds.forEach(registerTikTokPixel);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const contextValue: ThirdPartyPixelContextType = {
    registerGA4Pixel,
    isGA4PixelInitialized,
    registerSnapPixel,
    isSnapPixelInitialized,
    registerTikTokPixel,
    isTikTokPixelInitialized,
  };

  return (
    <ThirdPartyPixelContext.Provider value={contextValue}>
      {children}
    </ThirdPartyPixelContext.Provider>
  );
}
