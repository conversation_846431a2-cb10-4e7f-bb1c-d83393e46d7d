"use server";
import { signOut, auth } from "@/auth.config";
import { getCsrfToken } from "@/lib/utils/csrfToken";
import { serverSideLogout } from "@/lib/utils/serverLogout";
import { removeCookie } from "@/lib/utils/cookieUtils";

export async function Logout() {
  try {
    const session = await auth();

    if (session?.accessToken && session?.refreshToken) {
      try {
        const csrfToken = await getCsrfToken();
        await serverSideLogout(
          session.accessToken,
          session.refreshToken,
          csrfToken
        );
      } catch (error) {
        // Log the error but continue with client-side logout
        console.error("Server-side logout error:", error instanceof Error ? error.message : "Unknown error");
      }
    }

    // Always perform client-side logout with redirection
    return signOut({ 
      callbackUrl: "/",
      redirect: true 
    });
  } catch (error) {
    console.error("Logout error:", error instanceof Error ? error.message : "Unknown error");
    // Ensure cleanup and redirection even if session check fails
    remove<PERSON><PERSON><PERSON>("csrfToken");
    remove<PERSON><PERSON><PERSON>("refreshToken");
    return signOut({ 
      callbackUrl: "/",
      redirect: true 
    });
  }
}
