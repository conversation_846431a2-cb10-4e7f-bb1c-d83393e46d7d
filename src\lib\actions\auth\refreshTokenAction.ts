'use server';

import { keysToCamel } from '@/lib/utils/snakeCaseConvertor';
import { cookies } from 'next/headers';
import { 
  TOKEN_REFRESH_ERRORS, 
  TOKEN_ERROR_CODES, 
  AUTH_ERROR_MESSAGES,
  HTTP_STATUS,
  COOKIE_NAMES
} from '@/lib/utils/constants';

interface RefreshTokenResponse {
  accessToken: string;
  refreshToken?: string;
  csrfToken?: string;
  accessTokenExpiration?: string; 
}

interface RefreshTokenError {
  error: string;
  message?: string;
  details?: any;
  status?: number;
}

export async function refreshTokenAction(
  currentRefreshToken: string,
  currentCsrfToken: string
): Promise<RefreshTokenResponse | RefreshTokenError> {
  if (!currentRefreshToken || !currentCsrfToken) {
    return { 
      error: TOKEN_REFRESH_ERRORS.MISSING_TOKENS, 
      message: AUTH_ERROR_MESSAGES.MISSING_TOKENS 
    };
  }

  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    if (!apiUrl) {
      return { 
        error: TOKEN_REFRESH_ERRORS.CONFIGURATION_ERROR, 
        message: AUTH_ERROR_MESSAGES.CONFIG_ERROR
      };
    }

    const response = await fetch(
      `${apiUrl}auth/token/refresh/`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `${COOKIE_NAMES.REFRESH_TOKEN}=${currentRefreshToken}`,
        },
        body: JSON.stringify({
          csrf_token: currentCsrfToken,
        }),
      }
    );

    if (response.ok) {
      const refreshedApiData = await response.json();
      const refreshedTokens = keysToCamel(refreshedApiData) as RefreshTokenResponse;

      if (refreshedTokens.refreshToken) {
        const cookieStore = cookies();
        cookieStore.set(COOKIE_NAMES.REFRESH_TOKEN, refreshedTokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', 
          path: '/',
          expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        });
      }
      
      return refreshedTokens;
    } else {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { detail: await response.text() || AUTH_ERROR_MESSAGES.TOKEN_REFRESH_FAILED };
      }
      
      // Determine if this is a token validity issue (401) or other error
      const isTokenInvalid = response.status === HTTP_STATUS.UNAUTHORIZED || 
                            errorData?.code === TOKEN_ERROR_CODES.TOKEN_NOT_VALID ||
                            errorData?.detail?.includes(TOKEN_ERROR_CODES.TOKEN_NOT_VALID) ||
                            errorData?.detail?.includes(AUTH_ERROR_MESSAGES.INVALID_TOKEN) ||
                            errorData?.detail?.includes(TOKEN_ERROR_CODES.SIGNATURE_HAS_EXPIRED);
      
      return {
        error: isTokenInvalid ? TOKEN_REFRESH_ERRORS.INVALID_REFRESH_TOKEN : TOKEN_REFRESH_ERRORS.REFRESH_API_CALL_FAILED,
        message: errorData.detail || errorData.error_message || `Token refresh API call failed with status: ${response.status}`,
        details: errorData,
        status: response.status
      };
    }
  } catch (error: any) {
    return {
      error: TOKEN_REFRESH_ERRORS.REFRESH_EXCEPTION,
      message: error.message || 'An unexpected error occurred during server-side token refresh.',
      details: error
    };
  }
} 