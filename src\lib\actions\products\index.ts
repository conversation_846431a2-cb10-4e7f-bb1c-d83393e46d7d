'use server';

import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";

export const getProductDetails = async (slug: string, orgSlug: string) => {
  if (!slug || !orgSlug) {
    return { error: "Missing required parameters" };
  }
  
  try {
    const url = `${process.env.NEXT_PUBLIC_API_URL}api/channels/${orgSlug}/products-by-slug/${slug}/`;
    const response = await fetch(
      url,
      {
        credentials: "include",
        cache: "no-store", // or use Next.js revalidation
      }
    );

    if (!response.ok) {
      return { 
        error: `Failed to fetch product data: ${response.status}`,
        status: response.status 
      };
    }
    
    const rawData = await response.json();
    const data = keysToCamel(rawData);
    return { data };
  } catch (error) {
    console.error("Error fetching product data:", error);
    return { 
      error: error instanceof Error ? error.message : "Unknown error occurred" 
    };
  }
};