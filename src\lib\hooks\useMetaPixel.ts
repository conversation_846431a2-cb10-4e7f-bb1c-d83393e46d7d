"use client";

import { useEffect, useRef } from 'react';
import { useSessionData } from './useSession';
import { getName } from '@/lib/utils/string';
import * as pixelTracking from '@/lib/utils/pixelTracking';
import { usePixel } from '@/lib/Providers/FacebookPixelProvider';

// This matches the type in pixelTracking.ts
interface PixelEventData {
  id: string;
  title: string;
  date: string;
  organizerPixelId?: string; // meta
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

interface EventData {
  id: string;
  title: string;
  date: string;
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
  pixelId?: string; // backward compatibility
}

interface TicketData {
  id: string;
  eventId: string;
  type: "free" | "paid";
  name: string;
  price: number;
  quantity?: number;
}

/**
 * Hook to provide consistent Meta Pixel tracking functionality
 * with proper session data and error handling
 */
export function useMetaPixel() {
  const { data: session } = useSessionData();
  const { registerPixel } = usePixel();
  const initialized = useRef(false);

  // Ensure we only initialize tracking once
  useEffect(() => {
    initialized.current = true;
    return () => {
      initialized.current = false;
    };
  }, []);

  const getUserData = () => ({
    id: session?.user?.id,
    email: session?.user?.email,
    name: getName(session?.user),
    isAnonymous: !session?.user,
  });

  // Safely format event data with type checking
  const formatEventData = (event: EventData): PixelEventData | null => {
    const metaId = event?.pixels?.meta || event?.pixelId;
    if (!metaId) return null;

    return {
      id: event.id,
      title: event.title,
      date: event.date,
      organizerPixelId: metaId,
      pixels: event?.pixels,
    };
  };

  /**
   * Ensure pixel is registered with Facebook before tracking
   */
  const ensurePixelRegistered = (pixelId?: string) => {
    if (!pixelId) return;
    
    try {
      // Register the pixel with our provider
      registerPixel(pixelId);
    } catch (error) {
      console.error('[useMetaPixel] Failed to register pixel:', error);
    }
  };

  /**
   * Track an event page view
   */
  const trackEventView = (event: EventData) => {
    const metaId = event?.pixels?.meta || event?.pixelId;
    if (!metaId) return;
    
    try {
      // Register the pixel first
      ensurePixelRegistered(metaId);
      
      const formattedEvent = formatEventData(event);
      if (!formattedEvent) return;
      
      pixelTracking.trackEventPageView(
        formattedEvent,
        getUserData()
      );
    } catch (error) {
      console.error('[useMetaPixel] Failed to track event view:', error);
    }
  };

  /**
   * Track ticket add to cart
   */
  const trackAddToCart = (event: EventData, tickets: TicketData[]) => {
    const metaId = event?.pixels?.meta || event?.pixelId;
    if (!metaId) return;
    
    try {
      // Register the pixel first
      ensurePixelRegistered(metaId);
      
      const formattedEvent = formatEventData(event);
      if (!formattedEvent) return;
      
      pixelTracking.trackAddToCart(
        formattedEvent,
        tickets,
        getUserData()
      );

    } catch (error) {
      console.error('[useMetaPixel] Failed to track add to cart:', error);
    }
  };

  /**
   * Track checkout initiation
   */
  const trackInitiateCheckout = (event: EventData, tickets: TicketData[]) => {
    const metaId = event?.pixels?.meta || event?.pixelId;
    if (!metaId) return;
    
    try {
      // Register the pixel first
      ensurePixelRegistered(metaId);
      
      const formattedEvent = formatEventData(event);
      if (!formattedEvent) return;
      
      pixelTracking.trackInitiateCheckout(
        formattedEvent,
        tickets,
        getUserData()
      );
    } catch (error) {
      console.error('[useMetaPixel] Failed to track checkout:', error);
    }
  };

  /**
   * Track purchase completion
   */
  const trackPurchase = (event: EventData, tickets: TicketData[], transactionId: string, pixels: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  }) => {
    const metaId = event?.pixels?.meta || event?.pixelId;
    if (!metaId) return;
    
    try {
      // Register the pixel first
      ensurePixelRegistered(metaId);
      
      const formattedEvent = formatEventData(event);
      if (!formattedEvent) return;
      
      pixelTracking.trackPurchaseComplete(
        formattedEvent,
        tickets,
        transactionId,
        getUserData(),
        pixels
      );
    } catch (error) {
      console.error('[useMetaPixel] Failed to track purchase:', error);
    }
  };

  /**
   * Track free event ticket selection
   */
  const trackFreeEventTicketSelect = (event: EventData, ticket: TicketData) => {
    const metaId = event?.pixels?.meta || event?.pixelId;
    if (!metaId) return;
    
    try {
      // Register the pixel first
      ensurePixelRegistered(metaId);
      
      const formattedEvent = formatEventData(event);
      if (!formattedEvent) return;
      
      pixelTracking.trackFreeEventTicketSelect(
        formattedEvent,
        ticket,
        getUserData()
      );
    } catch (error) {
      console.error('[useMetaPixel] Failed to track free ticket select:', error);
    }
  };

  /**
   * Track free event ticket deselection
   */
  const trackFreeEventTicketDeselect = (event: EventData, ticket: TicketData) => {
    const metaId = event?.pixels?.meta || event?.pixelId;
    if (!metaId) return;
    
    try {
      // Register the pixel first
      ensurePixelRegistered(metaId);
      
      const formattedEvent = formatEventData(event);
      if (!formattedEvent) return;
      
      pixelTracking.trackFreeEventTicketDeselect(
        formattedEvent,
        ticket,
        getUserData()
      );
    } catch (error) {
      console.error('[useMetaPixel] Failed to track free ticket deselect:', error);
    }
  };

  return {
    trackEventView,
    trackAddToCart,
    trackInitiateCheckout,
    trackPurchase,
    trackFreeEventTicketSelect,
    trackFreeEventTicketDeselect,
  };
} 