import { useState, useEffect, useMemo, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  useAddVehicleModificationMutation,
  useUpdateVehicleModificationMutation,
  useGetModificationCategoriesQuery,
  useGetModificationPartsQuery,
  useGetModificationTypesQuery,
  useGetVehicleDetailsQuery,
} from "@/lib/redux/slices/vehicles/vehiclesApi";
import { useSessionData } from "@/lib/hooks/useSession";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { TOASTS } from "../utils/constants";

const modificationSchema = z.object({
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().min(1, "Subcategory is required"),
  part: z.string().min(1, "Part is required"),
  brand: z.string().min(1, "Brand is required"),
});

type ModificationFormData = z.infer<typeof modificationSchema>;

export const useModificationForm = (
  vehicleId: string,
  modificationId: string | null = null
) => {
  const isUpdateMode = !!modificationId;
  const { data: sessionData } = useSessionData();
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>("");
  const [addUpdateModificationError, setAddUpdateModificationError] =
    useState<any>(null);

  const router = useRouter();

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ModificationFormData>({
    resolver: zodResolver(modificationSchema),
    defaultValues: {
      category: "",
      subcategory: "",
      part: "",
      brand: "",
    },
  });

  const {
    data: vehicleDetails,
    isLoading: isVehicleDetailsLoading,
    error: vehicleDetailError,
  } = useGetVehicleDetailsQuery(
    { id: vehicleId },
    { skip: !sessionData?.user }
  );

  const { data: categories, isLoading: isCategoriesLoading } =
    useGetModificationTypesQuery({}, { skip: !sessionData?.user });

  const { data: subCategories, isLoading: isSubCategoriesLoading } =
    useGetModificationCategoriesQuery({}, { skip: !selectedCategory });

  const { data: parts, isLoading: isPartsLoading } =
    useGetModificationPartsQuery({}, { skip: !selectedSubCategory });

  const [addVehicleModification, { isLoading: isAddLoading }] =
    useAddVehicleModificationMutation();
  const [updateVehicleModification, { isLoading: isUpdateLoading }] =
    useUpdateVehicleModificationMutation();

  const modificationDetails = useMemo(() => {
    if (!isUpdateMode) return null;
    return vehicleDetails?.modifications?.find(
      (mod) => mod.id === modificationId
    );
  }, [isUpdateMode, modificationId, vehicleDetails]);

  const filteredCategories = useMemo(() => {
    if (!categories || !vehicleDetails?.model?.make?.type?.id) return [];
    return categories.filter(
      (category) => category.vehicleType === vehicleDetails.model.make.type.id
    );
  }, [categories, vehicleDetails]);

  const filteredSubCategories = useMemo(() => {
    if (!subCategories || !vehicleDetails?.model?.make?.type?.id) return [];
    return subCategories.filter(
      (subCategory) =>
        subCategory?.type?.vehicleType === vehicleDetails.model.make.type.id &&
        subCategory?.type?.id === selectedCategory
    );
  }, [subCategories, vehicleDetails, selectedCategory]);

  const filteredParts = useMemo(() => {
    if (!parts || !vehicleDetails?.model?.make?.type?.id) return [];
    const selectedVehicleTypeCategories = parts.vehicleTypes?.find(
      (part) => part?.vehicleId === vehicleDetails.model.make.type.id
    );
    const selectedCategoryRelatedSubCategories =
      selectedVehicleTypeCategories?.types?.find(
        (part) => part?.id === selectedCategory
      );
    const selectedSubCategoryRelatedParts =
      selectedCategoryRelatedSubCategories?.categories?.find(
        (part) => part?.id === selectedSubCategory
      );
    return selectedSubCategoryRelatedParts?.parts || [];
  }, [parts, vehicleDetails, selectedCategory, selectedSubCategory]);

  useEffect(() => {
    if (isUpdateMode && modificationDetails) {
      setValue("category", modificationDetails.part.category.type.id);
      setValue("subcategory", modificationDetails.part.category.id);
      setValue("part", modificationDetails.part.id);
      setValue("brand", modificationDetails.brand);
    }
  }, [isUpdateMode, modificationDetails, setValue]);

  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === "category") setSelectedCategory(value.category || "");
      if (name === "subcategory")
        setSelectedSubCategory(value.subcategory || "");
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    if (!isUpdateMode) {
      setValue("subcategory", "");
      setValue("part", "");
    }
  }, [selectedCategory, setValue, isUpdateMode]);

  const onSubmit = useCallback(
    async (data: ModificationFormData) => {
      const { part, brand } = data;
      const modificationData = {
        vehicle_id: vehicleDetails?.id,
        part_id: part,
        brand: brand,
        is_active: true,
      };

      try {
        const response = isUpdateMode
          ? await updateVehicleModification({
              id: modificationId!,
              data: modificationData,
            })
          : await addVehicleModification({ data: modificationData });

        if ("error" in response) {
          console.error("API error:", response.error);
          setAddUpdateModificationError(response.error || "An error occurred");
          toast.error(TOASTS.ERROR);
        } else {
          toast.success(
            isUpdateMode
              ? TOASTS.MODIFICATION_UPDATED
              : TOASTS.MODIFICATION_ADDED
          );
          router.push(`/profile/vehicles/${vehicleId}`);
        }
      } catch (error: any) {
        toast.error(error?.message || TOASTS.ERROR);
        console.error("Error submitting form:", error);
      }
    },
    [
      isUpdateMode,
      vehicleDetails,
      modificationId,
      updateVehicleModification,
      addVehicleModification,
      router,
      vehicleId,
    ]
  );

  const handleDeleteModification = useCallback(async () => {
    try {
      const response = await updateVehicleModification({
        id: modificationId!,
        data: { is_active: false },
      });
      if ("error" in response) {
        console.error("API error:", response.error);
        toast.error(TOASTS.ERROR);
      } else {
        toast.success(TOASTS.MODIFICATION_DELETED);
        router.push(`/profile/vehicles/${vehicleId}`);
      }
    } catch (error: any) {
      toast.error(error?.message || TOASTS.ERROR);
      console.error("Unexpected error:", error);
    }
  }, [modificationId]);

  const formState = useMemo(
    () => ({
      isUpdateMode,
      isVehicleDetailsLoading,
      isCategoriesLoading,
      isSubCategoriesLoading,
      isPartsLoading,
      isSubmitting: isAddLoading || isUpdateLoading,
      selectedCategory,
      selectedSubCategory,
      addUpdateModificationError,
      vehicleDetailError,
    }),
    [
      isUpdateMode,
      isVehicleDetailsLoading,
      isCategoriesLoading,
      isSubCategoriesLoading,
      isPartsLoading,
      isAddLoading,
      isUpdateLoading,
      selectedCategory,
      selectedSubCategory,
      addUpdateModificationError,
      vehicleDetailError,
    ]
  );

  return {
    formState,
    formData: {
      categories: filteredCategories,
      subCategories: filteredSubCategories,
      parts: filteredParts,
    },
    formMethods: {
      control,
      errors,
      handleSubmit,
      onSubmit,
      handleDeleteModification,
    },
  };
};
