import { useCallback, useEffect, useRef, useState } from "react";
import {
  useGetUserDashboardInvitesQuery,
  useGetUserLnkRequestsQuery,
  useGetUserNotificationsQuery,
  useMarkViewNotificationsMutation,
} from "@/lib/redux/slices/user/userApi";
import { 
  NotificationData, 
  LnkRequestsResponse, 
  DashboardInvite, 
  DropdownItemType 
} from "@/features/TopNavBar/types";
import { NOTIFICATION_STATUS, NOTIFICATIONS_PER_PAGE } from "@/features/TopNavBar/constants";
import { useOrganization } from "./useOrganization";

// Define a type for dropdown item structure without children
export type DropdownItemStructure = Omit<DropdownItemType, 'children'> & {
  isLastElement?: boolean;
};

export const useNotifications = () => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [cursor, setCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [accumulatedResults, setAccumulatedResults] = useState<NotificationData[]>([]);
  const [viewedNotifications, setViewedNotifications] = useState<Set<string>>(new Set());

  const { getDashboardUrl } = useOrganization();

  const [markNotificationsAsRead] = useMarkViewNotificationsMutation();

  // Fetch dashboard invites
  const { data: invitesData } = useGetUserDashboardInvitesQuery(
    {},
    {
      refetchOnFocus: true,
      refetchOnReconnect: true,
      refetchOnMountOrArgChange: true,
    }
  );

  // Fetch notifications
  const {
    data: notificationsData,
    isFetching,
    isUninitialized,
    refetch,
  } = useGetUserNotificationsQuery(
    {
      cursor: cursor || undefined,
      perPage: NOTIFICATIONS_PER_PAGE,
    },
    {
      refetchOnFocus: true,
      refetchOnReconnect: true,
      refetchOnMountOrArgChange: true,
    }
  );

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetch = useCallback(() => {
    if (!isUninitialized) {
      refetch();
    }
  }, [isUninitialized, refetch]);

  // Fetch LNK requests
  const { data: lnkRequestsData } = useGetUserLnkRequestsQuery(
    {},
    {
      refetchOnFocus: true,
      refetchOnReconnect: true,
      refetchOnMountOrArgChange: true,
    }
  ) as { data: LnkRequestsResponse | undefined };

  // Update accumulated results and hasMore based on API response
  useEffect(() => {
    if (notificationsData) {
      if (!cursor) {
        // Initial load or reset
        setAccumulatedResults(notificationsData.results);
      } else if (notificationsData.results.length > 0) {
        // Append new unique results to existing ones
        setAccumulatedResults((prev) => {
          const newResults = notificationsData.results.filter(
            (newItem) =>
              !prev.some((existingItem) => existingItem.id === newItem.id)
          );
          return [...prev, ...newResults];
        });
      }
      setHasMore(notificationsData.nextPageResults);
    }
  }, [notificationsData, cursor]);

  // Split notifications into new and older
  const newNotifications =
    accumulatedResults?.filter(
      (n: NotificationData) => n.status === NOTIFICATION_STATUS.NEW
    ) || [];
  const olderNotifications =
    accumulatedResults?.filter(
      (n: NotificationData) => n.status === NOTIFICATION_STATUS.READ
    ) || [];

  // Track which notifications have been viewed when dropdown opens
  useEffect(() => {
    if (isDropdownOpen) {
      setCursor(null);
      setHasMore(true);

      if (newNotifications.length > 0) {
        // Add currently viewed notifications to the viewedNotifications set
        const currentNewIds = newNotifications.map(
          (notification) => notification.batchId
        );
        setViewedNotifications((prev) => {
          const newSet = new Set(prev);
          currentNewIds.forEach((id) => newSet.add(id));
          return newSet;
        });
      }
    } else if (!isDropdownOpen && newNotifications.length > 0) {
      // When closing, only mark notifications as read if they were viewed
      const notificationsToMark = newNotifications
        .filter((notification) => viewedNotifications.has(notification.batchId))
        .map((notification) => notification.batchId);

      if (notificationsToMark.length > 0) {
        markNotificationsAsRead({
          batch_ids: notificationsToMark,
          status: NOTIFICATION_STATUS.READ,
        }).then(() => {
          // Refetch notifications after marking as read
          setCursor(null);
          setHasMore(true);
          safeRefetch();
        });
      }
    }
  }, [isDropdownOpen]);

  // Intersection Observer for infinite scroll
  const observer = useRef<IntersectionObserver | null>(null);
  const lastElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (isFetching || !hasMore) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver((entries) => {
        if (
          entries[0].isIntersecting &&
          hasMore &&
          !isFetching &&
          notificationsData?.nextCursor
        ) {
          setCursor(notificationsData.nextCursor);
        }
      });

      if (node) observer.current.observe(node);
    },
    [isFetching, hasMore, notificationsData?.nextCursor]
  );

  const pendingLnkRequests =
    lnkRequestsData?.results?.filter(
      (request) => request.status === "pending"
    ) || [];

  // Calculate total unread count
  const unreadCount =
    (newNotifications?.length || 0) +
    (pendingLnkRequests?.length || 0) +
    (invitesData?.length || 0);

  // Function to generate dropdown items structure
  const getDropdownItemsStructure = (): DropdownItemStructure[] => {
    const items: DropdownItemStructure[] = [];

    // Add LNK requests section if there are any pending requests
    if (pendingLnkRequests.length > 0) {
      items.push({
        key: "lnk-requests",
        className: "p-0 border-b border-gray-200",
      });
    }

    // Add Dashboard Invites section if there are any invites
    if (invitesData?.length > 0) {
      invitesData?.forEach((invite: DashboardInvite) => {
        items.push({
          key: invite?.invitationId,
          className: "py-2.5 px-3 cursor-pointer",
          textValue: invite?.organization?.companyName || "",
          redirectUrl: `/profile/settings/dashboard-invites/`,
        });
      });
    }

    // Add notifications sections
    if (newNotifications.length > 0) {
      // Add "New" section header
      items.push({
        key: "new-header",
        className: "px-4 py-2 cursor-default",
      });

      // Add new notifications
      newNotifications.forEach((notification: NotificationData) => {
        items.push({
          key: notification.id,
          className: "p-0",
          textValue: notification?.title || "",
          redirectUrl: notification?.type === 'vehicle_submission' ? getDashboardUrl({ orgSlug: notification?.data?.orgSlug }) : undefined
        });
      });

      // Add "Older" section only if there are new notifications
      if (olderNotifications.length > 0) {
        items.push({
          key: "older-header",
          className: "px-4 py-2 cursor-default",
        });

        // Add older notifications
        olderNotifications.forEach((notification: NotificationData, index) => {
          const isLastElement = index === olderNotifications.length - 1;
          items.push({
            key: notification.id,
            className: "p-0",
            textValue: notification?.title || "",
            isLastElement: isLastElement && hasMore,
            redirectUrl: notification?.type === 'vehicle_submission' ? getDashboardUrl({ orgSlug: notification?.data?.orgSlug }) : undefined
          });
        });
      }
    } else if (olderNotifications.length > 0) {
      // If there are only older notifications, show them without any header
      olderNotifications.forEach((notification: NotificationData, index) => {
        const isLastElement = index === olderNotifications.length - 1;
        items.push({
          key: notification.id,
          className: "p-0",
          textValue: notification?.title || "",
          isLastElement: isLastElement && hasMore,
          redirectUrl: notification?.type === 'vehicle_submission' ? getDashboardUrl({ orgSlug: notification?.data?.orgSlug }) : undefined
        });
      });
    }

    // Show empty state if no notifications and no LNK requests
    if (items.length === 0) {
      items.push({
        key: "empty",
        className: "h-14 cursor-default",
      });
    }

    // Add loading spinner item if fetching more notifications
    if (isFetching) {
      items.push({
        key: "loading",
        className: "w-full border-t border-gray-200",
      });
    }

    return items;
  };

  return {
    isDropdownOpen,
    setIsDropdownOpen,
    newNotifications,
    olderNotifications,
    pendingLnkRequests,
    invitesData,
    lastElementRef,
    unreadCount,
    getDropdownItemsStructure,
    hasMore,
    isFetching
  };
}; 