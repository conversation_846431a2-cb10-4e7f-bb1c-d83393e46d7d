import { useGetUserOrganizationsQuery } from "@/lib/redux/slices/user/userApi";
import { useSessionData } from "./useSession";
import { useMemo, useEffect, useState, useCallback } from "react";

export const useOrganization = () => {
  const { data: session, status } = useSessionData();
  const [shouldFetch, setShouldFetch] = useState(false);
  const [organizationsVersion, setOrganizationsVersion] = useState(0);
  
  useEffect(() => {
    if (session?.user) {
      setShouldFetch(true);
    } else {
      setShouldFetch(false);
    }
  }, [session?.user]);
  
  const { 
    data: getUserOrganizations, 
    isSuccess,
    isLoading,
    refetch 
  } = useGetUserOrganizationsQuery(
    undefined,
    { 
      skip: !shouldFetch,
      refetchOnMountOrArgChange: true
    }
  );
  
  // Immediately refetch when session is available
  useEffect(() => {
    if (shouldFetch) {
      refetch();
    }
  }, [shouldFetch, refetch]);
  
  // Increment version when organization data changes
  useEffect(() => {
    if (isSuccess || session?.user?.orgDetails) {
      setOrganizationsVersion(prev => prev + 1);
    }
  }, [getUserOrganizations, isSuccess, session?.user?.orgDetails]);

  const hasOrganization = useMemo(() => {
    return Boolean(
      (getUserOrganizations && getUserOrganizations.length > 0) || 
      session?.user?.orgDetails?.org?.id
    );
  }, [getUserOrganizations, session?.user?.orgDetails?.org?.id, organizationsVersion]);

  const isPartOfAnyOrganization = () => hasOrganization;

  const getFirstOrganizationSlug = () => {
    // First try to get the current org ID from session
    if (session?.user?.orgDetails?.org?.slug) {
      return session.user.orgDetails.org.slug;
    }
    // Fallback to first organization from the list
    return getUserOrganizations?.[0]?.slug;
  };

  const getDashboardUrl = ({ orgSlug }: { orgSlug?: string}) => {
    if (!session?.user || !session?.accessToken || !session?.refreshToken) {
      return "";
    }

    const baseUrl = process.env.NEXT_PUBLIC_DASHBOARD_URL;
    const authParams = `accessToken=${session.accessToken}&refreshToken=${session.refreshToken}&csrfToken=${session.csrfToken}`;
    const organizationSlug = orgSlug || getFirstOrganizationSlug();

    if (organizationSlug) {
      return `${baseUrl}organization/${organizationSlug}/?${authParams}`;
    }

    return "";
  };

  const safeRefetch = useCallback(() => {
    try {
      if (shouldFetch) {
        refetch();
      }
    } catch (error) {
      console.log("Organization refetch error:", error);
    }
  }, [refetch, shouldFetch]);

  return {
    organizations: getUserOrganizations,
    isPartOfAnyOrganization,
    hasOrganization,
    firstOrganizationSlug: getFirstOrganizationSlug,
    getDashboardUrl,
    organizationsVersion,
    refetchOrganizations: safeRefetch,
    isLoadingOrganizations: isLoading
  };
}; 