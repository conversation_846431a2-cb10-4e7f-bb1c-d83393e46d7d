import { useMemo, useState, useCallback } from "react";
import { useSessionData } from "@/lib/hooks/useSession";
import {
  useGetUserDetailsQuery,
  useGetUserProfileDetailsQuery,
  useMeMutation,
  useUpdateUserProfileMutation,
} from "@/lib/redux/slices/user/userApi";
import toast from "react-hot-toast";
import { TOASTS } from "@/lib/utils/constants";
import { handleApiError } from "@/lib/utils/errorUtils";
import * as z from "zod";
import { useChangeUserPasswordMutation } from "../redux/slices/auth/authApi";
import { SETTINGS_FIELDS } from "@/app/profile/settings/constants";
import { reportError } from "@/lib/utils/sentryErrorLogs";
import { PROFILE_ERRORS } from "@/app/profile/constants/errorMessages";

const fieldSchemas = {
  name: z.object({
    name: z.string().min(2, "Name must be at least 2 characters").refine((value) => /^[a-zA-Z\s]+$/.test(value), {
      message: "Name can only contain letters, and spaces",
    }),
  }),
  firstName: z.string().min(2, "First name must be at least 2 characters").refine((value) => /^[a-zA-Z\s]+$/.test(value), {
    message: "First name can only contain letters.",
  }),
  lastName: z.string().min(2, "Last name must be at least 2 characters").refine((value) => /^[a-zA-Z\s]+$/.test(value), {
    message: "Last name can only contain letters.",
  }),

  username: z.object({
    username: z.string().min(3, "Username must be at least 3 characters"),
  }),
  email: z.object({ email: z.string().email("Invalid email address") }),
  phone: z.object({
    phone: z.string().regex(/^\d{10}$/, "Invalid phone number"),
  }),
  bio: z.object({
    bio: z.string(),
  }),
  password: z
    .object({
      currentPassword: z.string().min(1, "Current password is required"),
      newPassword: z
        .string()
        .min(8, "New password must be at least 8 characters"),
      confirmPassword: z
        .string()
        .min(8, "Confirm password must be at least 8 characters"),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    }),
  image: z.object({}),
  address: z.object({
    addressLine1: z.string().min(1, "Address is required"),
    addressLine2: z.string().optional(),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    postalCode: z.string().regex(/^\d{4,10}$/, "Invalid zip code"),
    country: z.string().min(1, "Country is required"),
  }),
  socialMedia: z.object({}).optional(),
  userTimezone: z.object({}).optional(),
};

type FieldName = keyof typeof fieldSchemas | "hideModifications" | "hidePoints";

export const useProfileSettings = () => {
  const { data: session, status, update } = useSessionData();
  const {
    data: profileDetails,
    isLoading: isProfileDetailsLoading,
    isUninitialized,
    refetch,
  } = useGetUserProfileDetailsQuery(
    {},
    {
      skip: !session?.user,
    }
  );

  // Create a safe refetch function that checks if the query is initialized
  const safeRefetch = useCallback(() => {
    if (!isUninitialized) {
      refetch();
    }
  }, [isUninitialized, refetch]);
  const { data: userDetails } = useGetUserDetailsQuery(
    {},
    {
      skip: !session?.user,
    }
  );
  const [editingField, setEditingField] = useState<FieldName | null>(null);
  const [updateUserMutation] = useMeMutation();
  const [updatePassword] = useChangeUserPasswordMutation();
  const [updateProfile] = useUpdateUserProfileMutation();
  const user = useMemo(() => {
    return { ...session?.user, ...profileDetails, ...userDetails };
  }, [profileDetails, session, userDetails]);
  const handleEdit = (field: FieldName) => setEditingField(field);

  const handleCloseModal = () => setEditingField(null);

  // Function to report API errors
  const reportApiError = (error: any, field: string) => {
    // Report to Sentry first
    const errorForSentry = error?.data?.detail || error?.data?.error || error?.message || PROFILE_ERRORS.PROFILE_UPDATE_ERROR;
    reportError(errorForSentry, {
      page: "profile-settings",
      field,
      type: "api_error"
    });
    
    // Use handleApiError for consistent toast display with proper error.detail handling
    handleApiError(error, PROFILE_ERRORS.PROFILE_UPDATE_ERROR);
  };

  const handleSave = async (field: FieldName, data: any) => {
    try {
      if (field === SETTINGS_FIELDS.NAME) {
        await updateUserMutation({
          [SETTINGS_FIELDS.FIRST_NAME]: data.name.firstName,
          [SETTINGS_FIELDS.LAST_NAME]: data.name.lastName,
        });
        toast.success(TOASTS.USER_PROFILE_UPDATED);
      } else if (field === SETTINGS_FIELDS.PASSWORD) {
        await updatePassword({
          token: session?.accessToken,
          old_password: data.currentPassword,
          new_password: data.newPassword,
        }).unwrap();
        toast.success(TOASTS.PASSWORD_UPDATED);
      } else if (field === SETTINGS_FIELDS.ADDRESS) {
        await updateProfile({
          address_line1: data.addressLine1,
          address_line2: data.addressLine2,
          city: data.city,
          state: data.state,
          postal_code: data.postalCode,
          country: data.country,
        }).unwrap();
        toast.success(TOASTS.USER_PROFILE_UPDATED);
      } else if (["hideModifications", "hidePoints"].includes(field)) {
        await updateProfile({
          ...(field === "hideModifications"
            ? { hide_modifications: data }
            : { hide_points: data }),
        }).unwrap();
        toast.success(TOASTS.USER_PROFILE_UPDATED);
      } else if (field === SETTINGS_FIELDS.SOCIAL_MEDIA) {
        await updateProfile({ [field]: data[field] }).unwrap();
        toast.success(TOASTS.USER_PROFILE_UPDATED);
      } else {
        await updateUserMutation({ [field]: data[field] });
        toast.success(TOASTS.USER_PROFILE_UPDATED);
      }
      update();
      safeRefetch();
      handleCloseModal();
    } catch (error: any) {
      reportApiError(error, field);
    }
  };
  const getInitialValue = (field: FieldName, user: any) => {
    switch (field) {
      case "address":
        return {
          addressLine1: user.addressLine1,
          addressLine2: user.addressLine2,
          city: user.city,
          state: user.state,
          postalCode: user.postalCode,
          country: user.country ? user.country : "United States",
        };
      default:
        return { [field]: user[field] };
    }
  };

  return {
    user,
    status,
    editingField,
    handleEdit,
    handleCloseModal,
    handleSave,
    fieldSchemas,
    getInitialValue,
  };
};
