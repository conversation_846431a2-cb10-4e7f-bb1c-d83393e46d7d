'use client';

import { useCallback, useEffect } from 'react';
import { useVisibilityChange } from './useVisibilityChange';
import { useSessionData } from './useSession';
import { useDispatch } from 'react-redux';
import { clearUser, startLogout, endLogout } from '@/lib/redux/slices/auth/authSlice';
import { Logout } from '@/lib/actions/auth/Logout';
import { AUTH_STATUS } from '@/lib/utils/constants';
import { removeCookie } from '@/lib/utils/cookieUtils';

export function useSessionVerification() {
  const { data: session, status, update, setLoading } = useSessionData();
  const dispatch = useDispatch();

  const handleLogout = useCallback(async () => {
    try {
      dispatch(startLogout());
      setLoading(true);
      removeCookie('csrfToken');
      removeCookie('refreshToken');
      await Logout();
      dispatch(clearUser());
    } finally {
      setLoading(false);
      dispatch(endLogout());
    }
  }, [dispatch, setLoading]);

  useEffect(() => {
    if (status === AUTH_STATUS.AUTHENTICATED && session?.error === 'RefreshAccessTokenError') {
      handleLogout();
    }
  }, [session, status, handleLogout]);
} 