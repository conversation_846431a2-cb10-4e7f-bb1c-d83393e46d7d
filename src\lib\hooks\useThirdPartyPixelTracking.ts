"use client";

import { useEffect, useRef } from 'react';
import { useSessionData } from './useSession';
import { getName } from '@/lib/utils/string';
import { useThirdPartyPixels } from '@/lib/Providers/ThirdPartyPixelsProvider';
import * as pixelTracking from '@/lib/utils/pixelTracking';

interface EventData {
  id: string;
  title: string;
  date: string;
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

interface TicketData {
  id: string;
  eventId: string;
  type: "free" | "paid";
  name: string;
  price: number;
  quantity?: number;
}

/**
 * Hook to provide consistent third-party pixel tracking functionality
 * for GA4, Snap, and TikTok pixels
 */
export function useThirdPartyPixelTracking() {
  const { data: session } = useSessionData();
  const { 
    registerGA4Pixel, 
    registerSnapPixel, 
    registerTikTokPixel,
    isGA4PixelInitialized,
    isSnapPixelInitialized,
    isTikTokPixelInitialized
  } = useThirdPartyPixels();
  const initialized = useRef(false);

  // Ensure we only initialize tracking once
  useEffect(() => {
    initialized.current = true;
    return () => {
      initialized.current = false;
    };
  }, []);

  const getUserData = () => ({
    id: session?.user?.id,
    email: session?.user?.email,
    name: getName(session?.user),
    isAnonymous: !session?.user,
  });

  /**
   * Ensure pixels are registered before tracking
   */
  const ensurePixelsRegistered = (event: EventData) => {
    const { ga4, snap, tiktok } = event.pixels || {};
    
    if (ga4) registerGA4Pixel(ga4);
    if (snap) registerSnapPixel(snap);
    if (tiktok) registerTikTokPixel(tiktok);
  };

  /**
   * Track GA4 events
   */
  const trackGA4Event = (eventName: string, parameters: Record<string, any> = {}) => {
    try {
      // @ts-ignore - gtag is global
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', eventName, parameters);
      }
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track GA4 event:', error);
    }
  };

  /**
   * Track Snap events
   */
  const trackSnapEvent = (eventName: string, parameters: Record<string, any> = {}) => {
    try {
      window?.snaptr?.('track', eventName, parameters);
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track Snap event:', error);
    }
  };

  /**
   * Track TikTok events
   */
  const trackTikTokEvent = (eventName: string, parameters: Record<string, any> = {}) => {
    try {
      
      window?.ttq?.track?.(eventName, parameters);
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track TikTok event:', error);
    }
  };

  /**
   * Track an event page view across all platforms
   */
  const trackEventView = (event: EventData) => {
    const { ga4, snap, tiktok } = event.pixels || {};
    
    // Only proceed if we have third-party pixels (excluding meta)
    if (!ga4 || !snap || !tiktok) return;
    
    try {
      // Register pixels first
      ensurePixelsRegistered(event);
      
      pixelTracking.trackEventPageView(
        event,
        getUserData()
      );
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track event view:', error);
    }
  };

  /**
   * Track add to cart across platforms
   */
  const trackAddToCart = (event: EventData, tickets: TicketData[]) => {
    const { ga4, snap, tiktok } = event.pixels || {};
    
    // Only proceed if we have third-party pixels (excluding meta)
    if (!ga4 || !snap || !tiktok) return;
    
    try {
      ensurePixelsRegistered(event);
      
      pixelTracking.trackAddToCart(
        event,
        tickets,
        getUserData()
      );
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track add to cart:', error);
    }
  };

  /**
   * Track checkout initiation
   */
  const trackInitiateCheckout = (event: EventData, tickets: TicketData[]) => {
    const { ga4, snap, tiktok } = event.pixels || {};
    
    // Only proceed if we have third-party pixels (excluding meta)
    if (!ga4 || !snap || !tiktok) return;
    
    try {
      ensurePixelsRegistered(event);
      
      pixelTracking.trackInitiateCheckout(
        event,
        tickets,
        getUserData()
      );
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track checkout initiation:', error);
    }
  };

  /**
   * Track purchase completion
   */
  const trackPurchase = (event: EventData, tickets: TicketData[], transactionId: string) => {
    const { ga4, snap, tiktok } = event.pixels || {};
    
    // Only proceed if we have third-party pixels (excluding meta)
    if (!ga4 || !snap || !tiktok) return;
    
    try {
      ensurePixelsRegistered(event);
      
      pixelTracking.trackPurchaseComplete(
        event,
        tickets,
        transactionId,
        getUserData(),
        event.pixels
      );
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track purchase:', error);
    }
  };

  /**
   * Track free event ticket selection
   */
  const trackFreeEventTicketSelect = (event: EventData, ticket: TicketData) => {
    const { ga4, snap, tiktok } = event.pixels || {};
    
    // Only proceed if we have third-party pixels (excluding meta)
    if (!ga4 || !snap || !tiktok) return;
    
    try {
      ensurePixelsRegistered(event);
      
      pixelTracking.trackFreeEventTicketSelect(
        event,
        ticket,
        getUserData()
      );
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track free ticket select:', error);
    }
  };

  /**
   * Track free event ticket deselection
   */
  const trackFreeEventTicketDeselect = (event: EventData, ticket: TicketData) => {
    const { ga4, snap, tiktok } = event.pixels || {};
    
    // Only proceed if we have third-party pixels (excluding meta)
    if (!ga4 || !snap || !tiktok) return;
    
    try {
      ensurePixelsRegistered(event);
      
      pixelTracking.trackFreeEventTicketDeselect(
        event,
        ticket,
        getUserData()
      );
    } catch (error) {
      console.error('[useThirdPartyPixelTracking] Failed to track free ticket deselect:', error);
    }
  };

  return {
    trackEventView,
    trackAddToCart,
    trackInitiateCheckout,
    trackPurchase,
    trackFreeEventTicketSelect,
    trackFreeEventTicketDeselect,
    // Direct platform tracking functions
    trackGA4Event,
    trackSnapEvent,
    trackTikTokEvent,
    // Pixel status functions
    isGA4PixelInitialized,
    isSnapPixelInitialized,
    isTikTokPixelInitialized,
  };
} 