import { useState, useEffect, useMemo, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useRouter } from "next/navigation";
import { useSessionData } from "@/lib/hooks/useSession";
import {
  useAddVehicleMutation,
  useUpdateVehicleMutation,
  useGetVehicleMakesListQuery,
  useGetVehicleModelsListQuery,
  useGetVehicleTypesListQuery,
  useGetVehicleDetailsQuery,
} from "@/lib/redux/slices/vehicles/vehiclesApi";
import toast from "react-hot-toast";
import { TOASTS } from "../utils/constants";
import { handleApiError } from "../utils/errorUtils";
import { Option } from "@/components/VirtualisedSelect/BaseVirtualisedSelect";

interface Photo {
  url: string;
  file: File | null;
  id: string;
}

interface VehicleFormData {
  name: string;
  year: string;
  makeId: string;
  modelId: string;
  isActive: boolean;
  coverImage?: string;
  images?: string[];
}

const schema = z.object({
  name: z.string().min(1, "Vehicle Name is required"),
  vehicleType: z.string().min(1, "Vehicle Type is required"),
  year: z.string().min(1, "Vehicle Year is required"),
  make: z.string().min(1, "Vehicle Make is required"),
  model: z.string().min(1, "Vehicle Model is required"),
});

type FormFields = z.infer<typeof schema>;

export const useVehicleForm = (vehicleId = null) => {
  const isEditMode = !!vehicleId;
  const [photos, setPhotos] = useState<{
    cover: Photo | null;
    secondary: Photo[];
  }>({
    cover: null,
    secondary: [],
  });
  const [addUpdateVehicleError, setAddUpdateVehicleError] = useState<any>(null);
  const router = useRouter();
  const { data: sessionData } = useSessionData();

  const {
    control,
    watch,
    setValue,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm<FormFields>({
    resolver: zodResolver(schema),
    defaultValues: {
      vehicleType: "car",
      year: "",
      make: "",
      model: "",
      name: "",
    },
  });

  const selectedVehicleType = watch("vehicleType");
  const selectedVehicleMake = watch("make");

  const [addVehicle, { isLoading: isAddLoading }] = useAddVehicleMutation();
  const [updateVehicle, { isLoading: isUpdateLoading }] =
    useUpdateVehicleMutation();

  const { data: vehicleData, isLoading: isVehicleLoading } =
    useGetVehicleDetailsQuery(
      { token: sessionData?.accessToken, id: vehicleId },
      { skip: !isEditMode || !sessionData?.accessToken }
    );

  const { data: allMakes, isLoading: makesLoading } =
    useGetVehicleMakesListQuery(
      { token: sessionData?.accessToken },
      { skip: !sessionData?.accessToken }
    );

  const { data: allTypes, isLoading: typesLoading } =
    useGetVehicleTypesListQuery(
      { token: sessionData?.accessToken },
      { skip: !sessionData?.accessToken }
    );

  const filteredMakes = useMemo(() => {
    if (!allMakes) return [];
    return allMakes.filter(
      (make) => make?.type?.name?.toLowerCase() === selectedVehicleType
    );
  }, [allMakes, selectedVehicleType]);

  const { data: models, isLoading: modelsLoading } =
    useGetVehicleModelsListQuery(
      {
        make: filteredMakes?.find((make) => make.id === selectedVehicleMake)
          ?.name,
        type: selectedVehicleType,
      },
      { skip: !sessionData?.accessToken || !selectedVehicleMake }
    );

  useEffect(() => {
    const isEditAndDetailsLoaded =
      isEditMode && vehicleData && !modelsLoading && !makesLoading;
    if (isEditAndDetailsLoaded) {
      const coverPhotoExists =
        vehicleData?.coverPhoto &&
        Object.keys(vehicleData.coverPhoto)?.length > 0;
      const coverPhoto = coverPhotoExists
        ? {
            url: vehicleData.coverPhoto.photo,
            file: null,
            id: vehicleData.coverPhoto.id,
          }
        : null;

      const secondaryImages = vehicleData?.vehicleImages
        ? vehicleData.vehicleImages.map((img) => ({
            url: img?.photo,
            file: null,
            id: img?.id,
          }))
        : [];

      reset({
        name: vehicleData?.name,
        vehicleType: vehicleData?.model?.make?.type?.name?.toLowerCase(),
        year: vehicleData?.year?.toString(),
        make: vehicleData?.model?.make?.id,
        model: vehicleData?.model?.id,
      });
      setPhotos({
        cover: coverPhoto,
        secondary: secondaryImages,
      });
    }
  }, [isEditMode, vehicleData, reset, modelsLoading, makesLoading]);

  useEffect(() => {
    reset({ vehicleType: selectedVehicleType, year: "", make: "", model: "" });
  }, [selectedVehicleType, reset]);

  useEffect(() => {
    if (!isEditMode) {
      setValue("model", "");
    }
  }, [selectedVehicleMake, setValue]);

  useEffect(() => {
    if (!isEditMode) {
      setValue("make", "");
    }
  }, [selectedVehicleType, setValue]);

  const prepareFormData = (data: FormFields): VehicleFormData => {
    const { name, year, make, model } = data;
    return {
      name,
      year,
      makeId: make,
      modelId: model,
      isActive: true,
      coverImage: photos?.cover?.url,
      images: photos?.secondary?.map(photo => photo.url),
    };
  };

  const onSubmit = async (data: FormFields) => {
    try {
      const totalImageCount = (photos?.cover ? 1 : 0) + photos?.secondary?.length;
      if (totalImageCount < 2) {
        toast.error(TOASTS.VEHICLE_IMAGE_ERROR);
        return;
      }

      const formData = prepareFormData(data);
      let response;
      if (isEditMode) {
        response = await updateVehicle({
          token: sessionData?.accessToken,
          id: vehicleId,
          data: formData,
        });
      } else {
        response = await addVehicle(formData);
      }

      if ("error" in response) {
        console.error("API error:", response.error);
        setAddUpdateVehicleError(response.error || "An error occurred");
      } else {
        toast.success(
          isEditMode ? TOASTS.VEHICLE_UPDATED : TOASTS.VEHICLE_ADDED
        );
        router.push(`/profile/${sessionData?.user?.id}`);
      }
    } catch (error: any) {
      handleApiError(error, TOASTS.ERROR);
      console.error("Unexpected error:", error);
    }
  };

  const handleDeleteVehicle = async () => {
    if (!vehicleId) {
      toast.error("Vehicle ID is missing");
      return;
    }
    try {
      const response = await updateVehicle({
        token: sessionData?.accessToken,
        id: vehicleId,
        data: {
          name: vehicleData?.name,
          year: vehicleData?.year?.toString(),
          makeId: vehicleData?.model?.make?.id,
          modelId: vehicleData?.model?.id,
          isActive: false,
        },
      });
      if ("error" in response) {
        console.error("API error:", response.error);
        toast.error(TOASTS.ERROR);
      } else {
        toast.success(TOASTS.VEHICLE_DELETED);
        router.push(`/profile/${sessionData?.user?.id}`);
      }
    } catch (error: any) {
      handleApiError(error, TOASTS.ERROR);
      console.error("Unexpected error:", error);
    }
  };

  const loadMakeOptions = useCallback(
    (inputValue: string, callback: (options: Option[]) => void) => {
      const sanitizedInput = inputValue?.toLowerCase()?.trim() ?? "";

      const filteredOptions = inputValue
        ? filteredMakes
            ?.filter((option: { name?: string; id?: string }) =>
              option?.name?.toLowerCase()?.includes(sanitizedInput)
            )
            ?.map((make) => ({
              label: make?.name ?? "",
              value: make?.id ?? "",
            }))
        : filteredMakes?.map((make) => ({
            label: make?.name ?? "",
            value: make?.id ?? "",
          }));
      callback(filteredOptions);
    },
    [filteredMakes]
  );

  const loadModelOptions = useCallback(
    (inputValue: string, callback: (options: Option[]) => void) => {
      const sanitizedInput = inputValue?.toLowerCase()?.trim() ?? "";
      const modelsList = models?.types?.[0]?.makes?.[0]?.models;

      if (!modelsList?.length) {
        callback([]);
        return;
      }

      const filteredOptions = inputValue
        ? modelsList
            .filter((option: { name?: string; id?: string }) =>
              option?.name?.toLowerCase()?.includes(sanitizedInput)
            )
            .map((model) => ({
              label: model?.name ?? "",
              value: model?.id ?? "",
            }))
        : modelsList.map((model) => ({
            label: model?.name ?? "",
            value: model?.id ?? "",
          }));

      callback(filteredOptions);
    },
    [models]
  );

  const formActions = useMemo(
    () => ({
      setCoverPhoto: (photo: Photo) =>
        setPhotos((prev) => ({ ...prev, cover: photo })),
      setSecondaryPhotos: (newPhotos: Photo | Photo[]) => {
        setPhotos((prev) => {
          if (Array.isArray(newPhotos)) {
            return { ...prev, secondary: newPhotos };
          } else {
            return { ...prev, secondary: [...prev.secondary, newPhotos] };
          }
        });
      },
      removeSecondaryPhoto: (index: number) =>
        setPhotos((prev) => ({
          ...prev,
          secondary: prev.secondary.filter((_, i) => i !== index),
        })),
    }),
    []
  );

  const formState = useMemo(
    () => ({
      isEditMode,
      isAddLoading,
      isUpdateLoading,
      isVehicleLoading,
      typesLoading,
      makesLoading,
      modelsLoading,
      photos: photos,
      vehicleOptions: {
        types: allTypes,
        makes: filteredMakes,
        models,
      },
      selectedVehicleMake,
      addUpdateVehicleError,
    }),
    [
      isEditMode,
      isAddLoading,
      isUpdateLoading,
      isVehicleLoading,
      typesLoading,
      makesLoading,
      modelsLoading,
      photos,
      allTypes,
      filteredMakes,
      models,
      selectedVehicleMake,
      addUpdateVehicleError,
    ]
  );

  return {
    formState,
    formActions,
    formMethods: {
      control,
      errors,
      handleSubmit,
      onSubmit,
      handleDeleteVehicle,
      setValue,
      loadMakeOptions,
      loadModelOptions,
    },
  };
};
