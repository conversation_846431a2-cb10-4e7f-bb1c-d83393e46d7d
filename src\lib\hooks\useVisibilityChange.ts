import { useEffect, useCallback } from 'react';

interface VisibilityChangeProps {
  onVisible?: () => void;
  onHidden?: () => void;
}

export function useVisibilityChange({ onVisible, onHidden }: VisibilityChangeProps) {
  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState === 'visible') {
      onVisible?.();
    } else {
      onHidden?.();
    }
  }, [onVisible, onHidden]);

  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [handleVisibilityChange]);
} 