import { createBaseQuery<PERSON>ithReauth } from "@/lib/utils/baseQuery";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import { createApi } from "@reduxjs/toolkit/query/react";
import { ANALYTICS_ENDPOINTS } from "./apiEndpoints";

export const analyticsApi = createApi({
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "api/channels/"
  ),
  reducerPath: "analyticsApi",
  tagTypes: ["Analytics"],
  endpoints: (build) => ({
    createStoreSession: build.mutation<any, any>({
      query: ({ orgSlug, body }) => ({
        url: `${orgSlug}/${ANALYTICS_ENDPOINTS.storeSessions}`,
        method: "POST",
        body: keysToSnake(body),
      }),
    }),
    updateStoreSession: build.mutation<any, any>({
      query: ({ orgSlug, body, sessionId }) => ({
        url: `${orgSlug}/${ANALYTICS_ENDPOINTS.storeSessions}${sessionId}/`,
        method: "PATCH",
        body: keysToSnake(body),
      }),
    }),
  }),
});

export const { useCreateStoreSessionMutation,useUpdateStoreSessionMutation } = analyticsApi;
