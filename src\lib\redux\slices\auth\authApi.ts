import { createApi } from "@reduxjs/toolkit/query/react";
import Cookies from 'js-cookie';

import { AUTH_ENDPOINTS } from "@/lib/redux/slices/auth/authEndpoints";
import { createBaseQueryWithoutAuth } from "@/lib/utils/baseQuery";
import {
  IRefreshTokenResponse,
  ISigninPayload,
  ISigninResponse,
  ISignupResponse,
} from "@/lib/types";

export const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: createBaseQueryWithoutAuth(
    process.env.NEXT_PUBLIC_API_URL! + "auth/"
  ),

  endpoints: (builder) => ({
    signIn: builder.mutation<ISigninResponse, FormData>({
      query: (credentials) => ({
        url: AUTH_ENDPOINTS.login,
        method: "POST",
        body: credentials,
        formData: true,
      }),
    }),
    signUp: builder.mutation<ISignupResponse, FormData>({
      query: (credentials) => ({
        url: AUTH_ENDPOINTS.signup,
        method: "POST",
        body: credentials,
        formData: true,
      }),
    }),
    signUpWithOtp: builder.mutation<ISignupResponse, FormData>({
      query: (credentials) => ({
        url: AUTH_ENDPOINTS.signUpWithOtp,
        method: "POST",
        body: credentials,
        formData: true,
      }),
    }),
    refreshToken: builder.mutation<IRefreshTokenResponse, any>({
      query: ({ token }) => {
        const refreshToken = Cookies.get('refreshToken');
        let cookieString = '';
        if (refreshToken) {
          cookieString = `refreshToken=${refreshToken}`;
        }
        
        return {
          url: AUTH_ENDPOINTS.tokenRefresh,
          method: "POST",
          headers: {
             'Content-Type': 'application/json',
             'accept': '*/*',
             'Origin': typeof window !== 'undefined' ? window.location.origin : '',
             ...(cookieString && { 'Cookie': cookieString })
          },
          body: { csrf_token: token },
        };
      },
    }),
    signOut: builder.mutation<any, any>({
      query: () => ({
        url: AUTH_ENDPOINTS.logout,
        method: "POST",
      }),
    }),
    forgotPassword: builder.mutation<any, { email: string; method: string }>({
      query: ({ email, method }) => ({
        url: AUTH_ENDPOINTS.forgotPassword,
        method: "POST",
        body: { email, method },
      }),
    }),
    resetPassword: builder.mutation<any, { password: string; uidb64: string; token: string }>({
      query: ({ password, uidb64, token }) => ({
        url: `${AUTH_ENDPOINTS.resetPassword}${uidb64}/${token}/`,
        method: "POST",
        body: { password },
      }),
    }),
    verifyOtp: builder.mutation<any, { code: string; email: string }>({
      query: ({ code, email }) => ({
        url: AUTH_ENDPOINTS.codeVerify,
        method: "POST",
        body: { code, email },
      }),
    }),
    emailCheck: builder.mutation<any, { email: string }>({
      query: ({ email }) => ({
        url: AUTH_ENDPOINTS.emailCheck,
        method: "POST",
        body: { email },
      }),
    }),
    changeUserPassword: builder.mutation<any, any>({
      query: ({token, new_password, old_password}) => ({
        url: AUTH_ENDPOINTS.changePassword,
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: { new_password, old_password },
      }),
    }),
    verifyToken: builder.mutation<any, string>({
      query: (token) => ({
        url: AUTH_ENDPOINTS.tokenVerify,
        method: "POST",
        body: { token },
      }),
    }),
    generateOtp: builder.mutation<any, { email: string }>({
      query: ({ email }) => ({
        url: AUTH_ENDPOINTS.generateOtp,
        method: "POST",
        body: { email },
      }),
    }),
  }),
});

export const {
  useSignInMutation,
  useSignUpMutation,
  useSignUpWithOtpMutation,
  useRefreshTokenMutation,
  useSignOutMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useVerifyOtpMutation,
  useEmailCheckMutation,
  useChangeUserPasswordMutation,
  useVerifyTokenMutation,
  useGenerateOtpMutation,
} = authApi;
