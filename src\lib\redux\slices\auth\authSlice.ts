import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ISignupResponse } from "@/lib/types";

export interface IUser {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  isActive: boolean;
  isBot: boolean;
  isPhoneVerified: boolean;
  userTimezone: string;
  accessToken: string;
  refreshToken: string;
  csrfToken: string;
  profile?: any;
}

export interface AuthState {
  user: IUser | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  isLoggingOut: boolean;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  isLoggingOut: false,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<ISignupResponse>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
      if (action?.payload?.accessToken && action?.payload?.refreshToken) {
        localStorage.setItem("user", JSON.stringify(action.payload));
      } else {
        localStorage.removeItem("user");
      }
    },
    clearUser: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      localStorage.removeItem("user");
      localStorage.removeItem("orgId");
      localStorage.removeItem("orgSlug");
    },
    updateUser: (state, action: PayloadAction<Partial<ISignupResponse>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
    loadUser: (state) => {
      const savedUser = localStorage.getItem("user");
      return savedUser ? JSON.parse(savedUser) : null;
    },
    startLogout: (state) => {
      state.isLoggingOut = true;
    },
    endLogout: (state) => {
      state.isLoggingOut = false;
    },
  },
});

export const userSelector = (state: { auth: AuthState }) => state.auth.user;

export const { setUser, clearUser, updateUser, loadUser, startLogout, endLogout } = authSlice.actions;

export default authSlice.reducer;
