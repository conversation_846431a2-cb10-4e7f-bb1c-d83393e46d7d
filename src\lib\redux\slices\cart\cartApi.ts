import { createBaseQueryWithReauth } from "@/lib/utils/baseQuery";
import { createApi } from "@reduxjs/toolkit/query/react";
import { CART_ENDPOINTS } from "./cartEndpoints";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import { CART_STORAGE_KEYS, CART_HEADERS, CART_ERRORS } from "@/lib/utils/constants";
import { 
  CartResponse, 
  AddToCartRequest, 
  UpdateQuantityRequest, 
  RemoveItemRequest,
  InventoryCheckRequest,
  InventoryResponse,
  PaymentGatewayResponse
} from "../../types";
import { handleInvalidCartToken } from "@/app/cart/utils/cartUtils";

const getCartTokenHeaders = () => {
  if (typeof window !== "undefined") {
    const cartToken = localStorage.getItem(CART_STORAGE_KEYS.CART_TOKEN);
    const user = localStorage.getItem(CART_STORAGE_KEYS.USER);
    
    // Check if user exists and is not empty/null/undefined
    const hasUser = user && user !== "null" && user !== "{}" && user !== "";
    
    // Only return cart token if it exists and user is not logged in
    return (cartToken && !hasUser) ? { [CART_HEADERS.X_CART_TOKEN]: cartToken } : {};
  }
  return {};
};

export interface ClaimCartPayload {
  email: string;
  firstName?: string;
  lastName?: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
}

export interface CreatePaymentPayload {
  token: string;
  metadata: {
    checkoutToken: string;
  };
}

export interface CheckoutCompletePayload {
  checkoutToken: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
}

export const cartApi = createApi({
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "api/"
  ),
  reducerPath: "cartApi",
  tagTypes: ["Carts", "ProductCart"],
  endpoints: (build) => ({
    bookFreeEvent: build.mutation<any, any>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.bookFreeEvents,
        method: "POST",
        body: keysToSnake(data),
      }),
    }),
    bookFreeToAttendEvent: build.mutation<any, any>({
      query: ({ eventId }: any) => ({
        url: `events/${eventId}/${CART_ENDPOINTS.bookFreeToAttendEvents}`,
        method: "POST",
        body: keysToSnake({}),
      }),
    }),
    cancelFreeToAttendEvent: build.mutation<any, any>({
      query: ({ eventId }: any) => ({
        url: `events/${eventId}/${CART_ENDPOINTS.bookFreeToAttendEvents}`,
        method: "DELETE",
      }),
    }),
    getBookFreeToAttendEvent: build.query<any, any>({
      query: ({ eventId }: any) => ({
        url: `events/${eventId}/${CART_ENDPOINTS.bookFreeToAttendEvents}`,
        method: "GET",
      }),
    }),
    bookPaidEvent: build.mutation<any, { data: any }>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.bookPaidEvents,
        method: "POST",
        body: keysToSnake(data),
      }),
    }),
    bookPaidEventGuest: build.mutation<any, { data: any }>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.bookPaidEventsGuest,
        method: "POST",
        body: keysToSnake(data),
      }),
    }),
    getBookingDeatils: build.query<any, { id: any }>({
      query: ({ id }) => ({
        url: `events/bookings/${id}/`,
      }),
    }),
    getFreeEventBookingStatus: build.query<any, { id: any }>({
      query: ({ id }) => ({
        url: `api/events/bookings/${id}`,
        method: "GET",
      }),
    }),
    cancelFreeEvent: build.mutation<any, { id: any }>({
      query: ({ id }) => ({
        url: `events/bookings/cancel/${id}/`,
        method: "PATCH",
      }),
    }),
    checkInventory: build.query<InventoryResponse, InventoryCheckRequest>({
      query: ({ orgSlug, variantId }) => ({
        url: `channels/${orgSlug}/variants/${variantId}/check-inventory/`,
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
      keepUnusedDataFor: 0,
    }),
    getCart: build.query<CartResponse, void>({
      query: () => ({
        url: CART_ENDPOINTS.getCart,
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
      keepUnusedDataFor: 0,
      providesTags: ["ProductCart"],
      onQueryStarted: async (_, { queryFulfilled, dispatch }) => {
        try {
          await queryFulfilled;
        } catch (error: any) {
          if (error?.error?.data?.error === CART_ERRORS.INVALID_CART_TOKEN) {
            handleInvalidCartToken();
            dispatch(cartApi.endpoints.getCart.initiate());
          }
        }
      },
    }),

    addToCart: build.mutation<CartResponse, any>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.addToCart,
        method: "POST",
        body: data,
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
      invalidatesTags: ["ProductCart"],
    }),

    updateQuantity: build.mutation<CartResponse, UpdateQuantityRequest>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.updateQuantity,
        method: "POST",
        body: data,
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
      invalidatesTags: ["ProductCart"],
    }),

    removeFromCart: build.mutation<CartResponse, { id: string; type: string }>({
      query: (data) => ({
        url: CART_ENDPOINTS.removeItem,
        method: "POST",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
      invalidatesTags: ["ProductCart"],
    }),

    checkoutCart: build.mutation<any, void>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.checkoutCart,
        method: "POST",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    createPayment: build.mutation<any, CreatePaymentPayload>({
      query: (data) => ({
        url: CART_ENDPOINTS.createPayment,
        method: "POST",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    createTransaction: build.mutation<any, void>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.createTransaction,
        method: "POST",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    checkoutComplete: build.mutation<any, CheckoutCompletePayload>({
      query: (data) => ({
        url: CART_ENDPOINTS.checkoutComplete,
        method: "POST",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    addVehicle: build.mutation<any, void>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.addVehicle,
        method: "POST",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    removeVehicle: build.mutation<any, void>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.removeVehicle,
        method: "DELETE",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    addVehicleImage: build.mutation<any, FormData>({
      query: (formData) => ({
        url: CART_ENDPOINTS.addVehicleImage,
        method: "POST",
        body: formData,
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    removeVehicleImage: build.mutation<any, { imageId: number; id: string; type: string }>({
      query: (data) => ({
        url: CART_ENDPOINTS.removeVehicleImage,
        method: "DELETE",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    claimCart: build.mutation<any, ClaimCartPayload>({
      query: (data) => ({
        url: CART_ENDPOINTS.claimCart,
        method: "POST",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    clearCart: build.mutation<any, void>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.clearCart,
        method: "POST",
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    selectGarageVehicle: build.mutation<CartResponse, { id: string; type: string; vehicle_id: string; team_name?: string; social_media?: any; private_metadata?: any }>({
      query: (data) => ({
        url: CART_ENDPOINTS.selectGarageVehicle,
        method: "POST",
        body: keysToSnake(data),
      }),
    }),
    applyVoucher: build.mutation<any, { code: string }>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.applyVoucher,
        method: "PATCH",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        }
      }),
    }),
    removeVoucher: build.mutation<any, void>({
      query: () => ({
        url: CART_ENDPOINTS.removeVoucher,
        method: "PATCH",
        headers: {
          ...getCartTokenHeaders(),
        }
      }),
    }),
    initializePaymentGateway: build.query<PaymentGatewayResponse, void>({
      query: () => ({
        url: CART_ENDPOINTS.initializePaymentGateway,
        method: "GET",
        headers: {
          ...getCartTokenHeaders(),
        },
      }),
    }),
    createOrder: build.mutation<any, any>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.createOrder,
        method: "POST",
        body: keysToSnake(data),
      }),
    }),
    updatePlatformFee: build.mutation<any, any>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.updatePlatformFee,
        method: "PATCH",
        body: keysToSnake(data),
        headers: {
          ...getCartTokenHeaders(),
        },
        keepUnusedDataFor: 0,
      }),
    }),
    imageUpload: build.mutation<any, any>({
      query: (data: any) => ({
        url: `${CART_ENDPOINTS.imageUpload}?filename=${data.filename}`,
        method: "GET",
      }),
    }),
    billingAddress: build.mutation<any, any>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.billingAddress,
        method: "PATCH",
        body: keysToSnake(data),
      }),
    }),
    shippingAddress: build.mutation<any, any>({
      query: (data: any) => ({
        url: CART_ENDPOINTS.shippingAddress,
        method: "PATCH",
        body: keysToSnake(data),
      }),
    }),
    shippingMethods: build.query<any, any>({
      query: ({cartToken}) => ({
        url: `${CART_ENDPOINTS.shippingMethods}${cartToken}/`,
        method: "GET",
      }),
    }),
    setShippingMethod: build.mutation<any, any>({
      query: ({token, shippingMethodId, type}) => ({
        url: `${CART_ENDPOINTS.setShippingMethod}${token}/`,
        method: "PATCH",
        body: keysToSnake({shippingMethodId, type}),
      }),
    }),
  }),
});

export const {
  useBookFreeEventMutation,
  useBookFreeToAttendEventMutation,
  useCancelFreeToAttendEventMutation,
  useGetBookFreeToAttendEventQuery,
  useBookPaidEventMutation,
  useGetBookingDeatilsQuery,
  useGetFreeEventBookingStatusQuery,
  useCancelFreeEventMutation,
  useBookPaidEventGuestMutation,
  useGetCartQuery,
  useAddToCartMutation,
  useUpdateQuantityMutation,
  useRemoveFromCartMutation,
  useCheckoutCartMutation,
  useCheckInventoryQuery,
  useCreatePaymentMutation,
  useCreateTransactionMutation,
  useCheckoutCompleteMutation,
  useAddVehicleMutation,
  useRemoveVehicleMutation,
  useAddVehicleImageMutation,
  useRemoveVehicleImageMutation,
  useClaimCartMutation,
  useClearCartMutation,
  useSelectGarageVehicleMutation,
  useApplyVoucherMutation,
  useRemoveVoucherMutation,
  useInitializePaymentGatewayQuery,
  useCreateOrderMutation,
  useUpdatePlatformFeeMutation,
  useImageUploadMutation,
  useBillingAddressMutation,
  useShippingAddressMutation,
  useShippingMethodsQuery,
  useSetShippingMethodMutation,
} = cartApi;
