import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface Ticket {
  ticketId: string;
  quantity: number;
  price: number;
  ticketType: string;
}

interface CartItem {
  eventId: string;
  eventTitle: string;
  eventImg?: string;
  eventTotal: number;
  ticket: {
    ticketId: string;
    quantity: number;
    price: number;
    ticketType: string;
  };
  vehicleFormData?: Record<string, any>;
}

interface ProductVariant {
  id: number;
  name: string;
  price: number;
  attributes?: Array<{
    attribute: { name: string };
    values: Array<{ name: string; value: string }>;
  }>;
}

interface CartProduct {
  productId: number;
  productName: string;
  productImage?: string;
  quantity: number;
  price: number;
  total: number;
  variant: ProductVariant;
  selectedAttributes?: Record<string, string>;
}

interface CartState {
  total: number;
  productsTotal: number;
  tickets: CartItem[];
  cart: CartItem[];
  products: CartProduct[];
  vehicleAdditionTicketId: string | null;
}

const initialState: CartState = {
  total: 0,
  productsTotal: 0,
  tickets: [],
  cart: [],
  products: [],
  vehicleAdditionTicketId: null,
};

const calculateTotal = (items: CartItem[]): number =>
  items.reduce((sum, item) => sum + item.eventTotal, 0);

const calculateProductsTotal = (items: CartProduct[]): number =>
  items.reduce((sum, item) => sum + item.total, 0);

export const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    addTicket: (state, action: PayloadAction<CartItem>) => {
      const existingTicketIndex = state?.tickets?.findIndex(
        (item) =>
          item.eventId === action.payload.eventId &&
          item.ticket.ticketId === action.payload.ticket.ticketId
      );

      if (existingTicketIndex !== -1) {
        state.tickets[existingTicketIndex] = action.payload;
      } else {
        state.tickets.push(action.payload);
      }

      state.total = calculateTotal(state.tickets);
    },
    setCartFromTickets: (state) => {
      state.cart = state.tickets;
      state.tickets = [];
    },
    clearTickets: (state) => {
      state.tickets = [];
    },
    clearCart: (state) => {
      state.cart = [];
      state.total = 0;
    },
    clearProducts: (state) => {
      state.products = [];
      state.productsTotal = 0;
    },
    quantityIncrease: (state, action: PayloadAction<{ ticketId: string }>) => {
      const cartItem = state.cart?.find(
        (item) => item.ticket.ticketId === action.payload.ticketId
      );

      if (cartItem) {
        cartItem.ticket.quantity += 1;
        cartItem.eventTotal = cartItem.ticket.quantity * cartItem.ticket.price;
        state.total = calculateTotal(state.cart);
      }
    },
    quantityDecrease: (state, action: PayloadAction<{ ticketId: string }>) => {
      const cartItemIndex = state?.cart?.findIndex(
        (item) => item.ticket.ticketId === action.payload.ticketId
      );

      if (cartItemIndex !== -1) {
        if (state.cart[cartItemIndex].ticket.quantity > 1) {
          state.cart[cartItemIndex].ticket.quantity -= 1;
          state.cart[cartItemIndex].eventTotal =
            state.cart[cartItemIndex].ticket.quantity *
            state.cart[cartItemIndex].ticket.price;
        } else {
          state.cart.splice(cartItemIndex, 1);
        }
        state.total = calculateTotal(state.cart);
      }
    },
    removeTicket: (state, action: PayloadAction<{ ticketId: string }>) => {
      state.cart = state.cart.filter(
        (item) => item.ticket.ticketId !== action.payload.ticketId
      );
      state.total = calculateTotal(state.cart);
    },
    removeTempTicket: (state, action: PayloadAction<{ ticketId: string }>) => {
      state.tickets = state.tickets.filter(
        (item) => item.ticket.ticketId !== action.payload.ticketId
      );
      state.total = calculateTotal(state.cart);
    },
    updateVehicleAdditionTicketId: (
      state,
      action: PayloadAction<{ ticketId: string | null }>
    ) => {
      state.vehicleAdditionTicketId = action.payload.ticketId;
    },
    updateVehicleData: (
      state,
      action: PayloadAction<{
        ticketId: string;
        vehicleFormData: Record<string, any>;
      }>
    ) => {
      const { ticketId, vehicleFormData } = action.payload;
      const ticketToUpdate = state?.cart?.find(
        (item) => item.ticket.ticketId === ticketId
      );

      if (ticketToUpdate) {
        ticketToUpdate.vehicleFormData = vehicleFormData;
      }
    },
    addProduct: (state, action: PayloadAction<CartProduct>) => {
      if (!Array.isArray(state.products)) {
        state.products = [];
      }

      const existingProductIndex = state.products.findIndex(
        (item) =>
          item.productId === action.payload.productId &&
          item.variant.id === action.payload.variant.id &&
          JSON.stringify(item.selectedAttributes) ===
            JSON.stringify(action.payload.selectedAttributes)
      );

      if (existingProductIndex !== -1) {
        const product = state.products[existingProductIndex];
        product.quantity += action.payload.quantity;
        product.total = product.quantity * product.price;
      } else {
        state.products.push({
          ...action.payload,
          total: action.payload.quantity * action.payload.price,
        });
      }

      state.productsTotal = calculateProductsTotal(state.products);
    },

    updateProductQuantity: (
      state,
      action: PayloadAction<{
        productId: number;
        variantId: number;
        quantity: number;
      }>
    ) => {
      const { productId, variantId, quantity } = action.payload;
      const productIndex = state.products?.findIndex(
        (item) => item.productId === productId && item.variant.id === variantId
      );

      if (productIndex !== -1) {
        if (quantity > 0) {
          state.products[productIndex].quantity = quantity;
          state.products[productIndex].total =
            quantity * state.products[productIndex].price;
        } else {
          state.products.splice(productIndex, 1);
        }
        state.productsTotal = calculateProductsTotal(state.products);
      }
    },

    removeProduct: (
      state,
      action: PayloadAction<{ productId: number; variantId: number }>
    ) => {
      state.products = state.products.filter(
        (item) =>
          !(
            item.productId === action.payload.productId &&
            item.variant.id === action.payload.variantId
          )
      );
      state.productsTotal = calculateProductsTotal(state.products);
    },

    syncProductCart: (state, action: PayloadAction<CartProduct[]>) => {
      state.products = action.payload;
      state.productsTotal = calculateProductsTotal(action.payload);
    },
  },
});

export const {
  addTicket,
  clearCart,
  setCartFromTickets,
  quantityIncrease,
  quantityDecrease,
  removeTicket,
  removeTempTicket,
  updateVehicleAdditionTicketId,
  updateVehicleData,
  addProduct,
  updateProductQuantity,
  removeProduct,
  syncProductCart,
  clearProducts,
  clearTickets,
} = cartSlice.actions;

export default cartSlice.reducer;
