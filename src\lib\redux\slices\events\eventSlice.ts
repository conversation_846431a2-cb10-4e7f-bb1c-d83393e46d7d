import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface EventTicket {
  ticket: {
    ticketId: string;
  };
}

interface FormData {
  ticketId: string;
}

interface SavedFormDataState {
  [key: string]: FormData;
}

interface EventState {
  eventTickets: EventTicket[];
  activeFormTicket: EventTicket | null;
  savedFormData: SavedFormDataState;
  selectedVariantIds: string[];
  waivers: any;
}

const initialState: EventState = {
  eventTickets: [],
  activeFormTicket: null,
  savedFormData: {},
  selectedVariantIds: [],
  waivers: [],
};

export const eventSlice = createSlice({
  name: "events",
  initialState,
  reducers: {
    setEventTickets: (state, action: PayloadAction<EventTicket[]>) => {
      state.eventTickets = action.payload;
    },
    addEventTicket: (state, action: PayloadAction<EventTicket>) => {
      const existingTicketIndex = state.eventTickets.findIndex(
        (ticket) => ticket.ticket.ticketId === action.payload.ticket.ticketId
      );

      if (existingTicketIndex !== -1) {
        state.eventTickets[existingTicketIndex] = action.payload;
      } else {
        state.eventTickets.push(action.payload);
      }
    },
    deleteEventTicket: (state, action: PayloadAction<string>) => {
      const ticketId = action.payload;
      state.eventTickets = state.eventTickets.filter(
        (ticket) => ticket.ticket.ticketId !== ticketId
      );

      if (state.activeFormTicket?.ticket.ticketId === ticketId) {
        state.activeFormTicket = null;
      }
    },
    clearEventTickets: (state) => {
      state.eventTickets = [];
    },
    setActiveFormTicket: (state, action: PayloadAction<EventTicket | null>) => {
      state.activeFormTicket = action.payload;
    },
    clearActiveFormTicket: (state) => {
      state.activeFormTicket = null;
    },
    setSavedFormData: (state, action: PayloadAction<SavedFormDataState>) => {
      state.savedFormData = action.payload;
    },
    clearSavedFormData: (state) => {
      state.savedFormData = {};
    },
    deleteSavedFormData: (state, action: PayloadAction<string>) => {
      const newSavedFormData = { ...state.savedFormData };
      delete newSavedFormData[`ticket_${action.payload}`];
      state.savedFormData = newSavedFormData;
    },
    setSelectedVariantIds: (state, action: PayloadAction<string[]>) => {
      state.selectedVariantIds = action.payload;
    },
    clearSelectedVariantIds: (state) => {
      state.selectedVariantIds = [];
    },
    setWaivers: (state, action: PayloadAction<any>) => {
      state.waivers = action.payload;
    },
    clearWaivers: (state) => {
      state.waivers = [];
    },
  },
});

export const {
  setEventTickets,
  clearEventTickets,
  addEventTicket,
  deleteEventTicket,
  setActiveFormTicket,
  clearActiveFormTicket,
  setSavedFormData,
  clearSavedFormData,
  deleteSavedFormData,
  setSelectedVariantIds,
  clearSelectedVariantIds,
  setWaivers,
  clearWaivers,
} = eventSlice.actions;

export default eventSlice.reducer;
