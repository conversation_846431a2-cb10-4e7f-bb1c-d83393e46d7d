export interface ApiImage {
  id: string;
  created_at: string;
  updated_at: string;
  photo: string;
  type: number;
  created_by: string;
  updated_by: string;
  event: string;
}

export interface ApiTicketTemplate {
  id: string;
  name: string;
  description: string;
  price: string;
  currency: string;
  vehicle_approval_required: boolean;
}

export interface ApiTicket {
  id: string;
  name: string;
  description: string;
  ticket_template: ApiTicketTemplate;
  ticket_quantity: number;
  ticket_price: string;
  currency: string;
  vehicle_approval_required: boolean;
}

export interface ApiTicketStatistics {
  tickets_sold: number;
  vip_tickets_sold: number;
  tickets_cancelled: number;
  bookings_cancelled: number;
}

export interface ApiEvent {
  id: string;
  images: ApiImage[];
  tickets: ApiTicket[];
  ticket_statistics: ApiTicketStatistics;
  status: string;
  pixel_id: string;
  created_at: string;
  updated_at: string;
  name: string;
  category: string;
  description: string;
  geo_location: null | string;
  venue_name: string;
  country: string;
  state: string;
  city: string;
  zip_code: string;
  address: string;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  total_attendance_count: number;
  show_attendance_count: boolean;
  show_vip_list: boolean;
  hide_address: boolean;
  show_address_after: string | null;
  ticket_purchase_limit: number;
  meta: Record<string, unknown>;
  created_by: string;
  updated_by: string;
  organization: string;
}

export interface ApiEventResponse {
  next_cursor: string;
  prev_cursor: string;
  next_page_results: boolean;
  prev_page_results: boolean;
  count: number;
  total_pages: number;
  total_results: number;
  extra_stats: null | unknown;
  results: ApiEvent[];
}

export interface Event {
  id: string;
  images: {
    id: string;
    createdAt: string;
    updatedAt: string;
    photo: string;
    type: number;
    createdBy: string;
    updatedBy: string;
    event: string;
  }[];
  tickets: {
    id: string;
    name: string;
    description: string;
    ticketTemplate: {
      id: string;
      name: string;
      description: string;
      price: string;
      currency: string;
      vehicleApprovalRequired: boolean;
    };
    ticketQuantity: number;
    ticketPrice: string;
    currency: string;
    vehicleApprovalRequired: boolean;
  }[];
  ticketStatistics: {
    ticketsSold: number;
    vipTicketsSold: number;
    ticketsCancelled: number;
    bookingsCancelled: number;
  };
  status: string;
  pixelId: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  category: string;
  description: string;
  geoLocation: null | string;
  venueName: string;
  country: string;
  state: string;
  city: string;
  zipCode: string;
  address: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  totalAttendanceCount: number;
  showAttendanceCount: boolean;
  showVipList: boolean;
  hideAddress: boolean;
  showAddressAfter: string | null;
  ticketPurchaseLimit: number;
  meta: Record<string, unknown>;
  createdBy: string;
  updatedBy: string;
  organization: string;
  slug: string;
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

export interface EventResponse {
  nextCursor: string;
  prevCursor: string;
  nextPageResults: boolean;
  prevPageResults: boolean;
  count: number;
  totalPages: number;
  totalResults: number;
  extraStats: null | unknown;
  results: Event[];
}
