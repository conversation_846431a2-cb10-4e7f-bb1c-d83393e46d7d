import { createApi } from "@reduxjs/toolkit/query/react";
import { createBaseQueryWithReauth } from "@/lib/utils/baseQuery";
import type { Event, EventResponse } from "./eventTypes";

export const eventsApi = createApi({
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "api/"
  ),
  reducerPath: "eventsApi",
  tagTypes: ["Events"],
  endpoints: (build) => ({
    getAllEvents: build.query<EventResponse, string>({
      query: (query) => `/events/list?${query}`,
      providesTags: ["Events"],
    }),
    getAllEventsByName: build.query<EventResponse, string>({
      query: (query) => `/events/list?name=${query}`,
      providesTags: ["Events"],
    }),
    getEventById: build.query<Event, { id: string }>({
      query: ({ id }) => `/events/detail/${id}`,
      providesTags: ["Events"],
    }),
    getEventsByOrgIdOrSlug: build.query<EventResponse, { orgIdOrSlug: string; status?: string; cursor?: string; perPage?: number; orderBy?: string }>({
      query: ({ orgIdOrSlug, status, cursor, perPage, orderBy }) => {
        const params = new URLSearchParams();
        
        // Always include these params
        params.append('include_collab_events', 'true');
        params.append('per_page', String(perPage ?? 10));
        params.append('order_by', orderBy ?? 'start_date');
        
        // Only add cursor if it's provided and not empty
        if (cursor && cursor !== '') {
          params.append('cursor', cursor);
        }
        
        // Only add status if it's provided
        if (status) {
          params.append('status', status);
        }
        
        return `/channels/${orgIdOrSlug}/events/public/?${params.toString()}`;
      },
      providesTags: ["Events"],
      keepUnusedDataFor: 0,
    }),
  }),
});

export const {
  useGetAllEventsQuery,
  useGetAllEventsByNameQuery,
  useGetEventByIdQuery,
  useGetEventsByOrgIdOrSlugQuery,
} = eventsApi;
