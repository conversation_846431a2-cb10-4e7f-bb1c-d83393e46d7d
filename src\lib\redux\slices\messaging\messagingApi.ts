import { createApi } from "@reduxjs/toolkit/query/react";

import { InboxEndpoints } from "./messsagingEndpoints";

import { createBaseQueryWithReauth } from "@/lib/utils/baseQuery";

export const messagingApi = createApi({
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "chat/"
  ),
  reducerPath: "messagingApi",
  tagTypes: ["Messaging"],
  endpoints: (build) => ({
    getDialogs: build.query<any, any>({
      query: (data) => {
        return {
          url: `${InboxEndpoints.getDialogs}/`,
          method: "GET",
        };
      },
    }),
    getMessages: build.query<any, any>({
      query: () => {
        return {
          url: InboxEndpoints.getMessages,
          method: "GET",
        };
      },
    }),
    getMessagesWithUser: build.query<any, any>({
      query: (data) => {
        return {
          url: `${InboxEndpoints.getMessages}/${data?.userId}/`,
          method: "GET",
        };
      },
    }),
  }),
});

export const {
  useGetDialogsQuery,
  useGetMessagesQuery,
  useGetMessagesWithUserQuery,
} = messagingApi;
