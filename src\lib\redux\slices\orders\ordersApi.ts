import { createApi } from "@reduxjs/toolkit/query/react";
import { createBaseQueryWithReauth } from "@/lib/utils/baseQuery";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import type { Order } from "@/app/order/success/types";
import { ORDERS_ENDPOINTS } from "./ordersApiEndpoints";

export const ordersApi = createApi({
  reducerPath: "ordersApi",
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "api/"
  ),
  tagTypes: ["Orders"],
  endpoints: (builder) => ({
    getOrderById: builder.query<Order, { orderId: string }>({
      query: ({ orderId }) => ({
        url: `orders/${orderId}`,
        method: "GET",
      }),
      providesTags: (result, error, { orderId }) => [
        { type: "Orders", id: orderId },
      ],
    }),
    addOrderLines: builder.mutation<
      Order,
      {
        orgIdentifier: string;
        orderId: string;
        lines: Array<{
          eventTicketId: string;
          quantity: number;
        }>;
      }
    >({
      query: ({ orgIdentifier, orderId, lines }) => ({
        url: ORDERS_ENDPOINTS.addOrderLines(orgIdentifier),
        method: "POST",
        body: keysToSnake({
          id: orderId,
          lines: lines.map((line) => ({
            eventTicketId: line.eventTicketId,
            quantity: line.quantity,
            forceNewLine: true,
          })),
        }),
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: "Orders", id: orderId },
      ],
    }),
    updateOrderBillingAddress: builder.mutation<
      any,
      {
        orgIdentifier: string;
        orderId: string;
        billingAddress: {
          firstName?: string;
          lastName?: string;
          company?: string;
          street_address_1?: string;
          street_address_2?: string;
          city?: string;
          country_area?: string;
          country?: string;
          postalCode?: string;
          phone?: string;
        };
      }
    >({
      query: ({ orgIdentifier, orderId, billingAddress }) => ({
        url: ORDERS_ENDPOINTS.updateBillingAddress(orgIdentifier, orderId),
        method: "PATCH",
        body: keysToSnake({
          billing_address: billingAddress,
        }),
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: "Orders", id: orderId },
      ],
    }),
    getOrderPaymentClientSecret: builder.query<
      {
        confirmationNeeded: boolean;
        confirmationData: {
          clientSecret: string;
          id: string;
          connectId: string;
          isCollaboratorCheckout: boolean;
        };
      },
      {
        orgIdentifier: string;
        orderId: string;
      }
    >({
      query: ({ orgIdentifier, orderId }) => ({
        url: ORDERS_ENDPOINTS.getPaymentClientSecret(orgIdentifier, orderId),
        method: "GET",
      }),
    }),
    updateOrderLine: builder.mutation<
      any,
      {
        orgIdentifier: string;
        lineId: string;
        quantity: number;
      }
    >({
      query: ({ orgIdentifier, lineId, quantity }) => ({
        url: ORDERS_ENDPOINTS.updateOrderLine(orgIdentifier, lineId),
        method: "PATCH",
        body: keysToSnake({ quantity }),
      }),
      invalidatesTags: (result, error, { lineId }) => [
        { type: "Orders", id: lineId },
      ],
    }),
    deleteOrderLine: builder.mutation<
      any,
      {
        orgIdentifier: string;
        lineId: string;
      }
    >({
      query: ({ orgIdentifier, lineId }) => ({
        url: ORDERS_ENDPOINTS.deleteOrderLine(orgIdentifier, lineId),
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { lineId }) => [
        { type: "Orders", id: lineId },
      ],
    }),
  }),
});

export const {
  useGetOrderByIdQuery,
  useAddOrderLinesMutation,
  useUpdateOrderBillingAddressMutation,
  useGetOrderPaymentClientSecretQuery,
  useUpdateOrderLineMutation,
  useDeleteOrderLineMutation,
} = ordersApi;
