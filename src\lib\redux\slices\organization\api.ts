import { createBaseQuery<PERSON>ithReauth } from "@/lib/utils/baseQuery";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import { createApi } from "@reduxjs/toolkit/query/react";
import { organizationEndpoints } from "./apiEnpoints";

export const organizationApi = createApi({
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "api"
  ),
  reducerPath: "organizationApi",
  tagTypes: ["Organizations"],
  endpoints: (build) => ({
    organizationCreation: build.mutation<any, any>({
      query: () => ({
        url: `${organizationEndpoints.channels}/`,
        method: "POST",
      }),
    }),
    teamCreation: build.mutation<any, any>({
      query: () => ({
        url: `${organizationEndpoints.teams}/`,
        method: "POST",
      }),
      invalidatesTags: ['Organizations'],
    }),
    teamList: build.query<any, any>({
      query: () => ({
        url: `${organizationEndpoints.teamPublic}/`,
        method: "GET",
      }),
      providesTags: ['Organizations'],
    }),
    updateOrganization: build.mutation<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/`,
        method: "PATCH",
        body: keysToSnake(data.body),
      }),
    }),
    getOrganizationById: build.query<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/`,
        method: "GET"
      }),
    }),
    updateUserDashboardInvite: build.mutation<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/invitations/${data.inviteId}/join/`,
        method: "POST",
        body: keysToSnake(data.body),
      }),
    }),
    followOrganization: build.mutation<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/follow/`,
        method: "POST",
        body: keysToSnake(data),
      }),
    }),
    unFollowOrganization: build.mutation<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/unfollow/`,
        method: "POST",
        body: keysToSnake(data),
      }),
    }),
    getOrganizationFollowingStatus: build.query<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/follow/status/`,
        method: "GET"
      }),
    }),
    getOrganizationsByName: build.query<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/?name=${data}`,
        method: "GET"
      }),
      keepUnusedDataFor: 0,
    }),
    getOrganizationPoliciesAndGuidelines: build.query<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/policy/public/?policy_fields=${data.policyFields}`,
        method: "GET"
      }),
      keepUnusedDataFor: 0,
    }),
    referralRegister: build.mutation<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/referral-program/use-referral/`,
        method: "POST",
        body: keysToSnake(data.body),
      }),
    }),
    resendTicket: build.mutation<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.channels}/${data.orgSlug}/resend-ticket/`,
        method: "POST",
        body: keysToSnake(data.body),
      }),
    }),
    sendOrgMessage: build.mutation<any, any>({
      query: (data) => ({
        url: `${organizationEndpoints.sendOrgMessage(data.orgSlug)}`,
        method: "POST",
        body: keysToSnake(data.body),
      }),
    }),
  }),
});

export const {
  useOrganizationCreationMutation,
  useUpdateOrganizationMutation,
  useGetOrganizationByIdQuery,
  useUpdateUserDashboardInviteMutation,
  useFollowOrganizationMutation,
  useUnFollowOrganizationMutation,
  useGetOrganizationFollowingStatusQuery,
  useGetOrganizationsByNameQuery,
  useGetOrganizationPoliciesAndGuidelinesQuery,
  useTeamCreationMutation,
  useReferralRegisterMutation,
  useTeamListQuery,
  useResendTicketMutation,
  useSendOrgMessageMutation,
} = organizationApi;
