import { createApi } from "@reduxjs/toolkit/query/react";
import { createBaseQueryWithReauth } from "@/lib/utils/baseQuery";
import { PRODUCTS_ENDPOINTS } from "./productsApiEndpoints";

export const productsApi = createApi({
  reducerPath: "productsApi",
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "api/channels/"
  ),
  endpoints: (builder) => ({
    getProductsList: builder.query<any, { orgSlug: string }>({
      query: ({ orgSlug }) => {
        return {
          url: `${orgSlug}/${PRODUCTS_ENDPOINTS.products}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 1,
    }),
    getProductDetails: builder.query<any, { orgSlug: string; productId: any }>({
      query: ({ orgSlug, productId }) => {
        return {
          url: `${orgSlug}/${PRODUCTS_ENDPOINTS.products}${productId}/`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 1,
    }),
  }),
});

export const {
    useGetProductsListQuery,
    useGetProductDetailsQuery,
} = productsApi;
