import { createApi } from "@reduxjs/toolkit/query/react";
import { createBaseQueryWithoutAuth } from "@/lib/utils/baseQuery";
import { TICKETS_ENDPOINTS } from "./ticketsApiEndpoints";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";

export const ticketsApi = createApi({
  reducerPath: "ticketsApi",
  baseQuery: createBaseQueryWithoutAuth(
    process.env.NEXT_PUBLIC_API_URL! + "api/channels/"
  ),
  endpoints: (builder) => ({
    verifyTicketPassword: builder.mutation<any, any>({
      query: ({ orgSlug, ticketId, body }) => ({
        url: `${orgSlug}/ticket/${ticketId}/${TICKETS_ENDPOINTS.verifyPassword}`,
        method: "POST",
        body: keysToSnake(body),
      }),
    }),
  }),
});

export const { useVerifyTicketPasswordMutation } = ticketsApi;
