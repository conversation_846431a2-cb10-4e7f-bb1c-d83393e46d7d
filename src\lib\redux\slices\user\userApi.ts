import { createApi } from "@reduxjs/toolkit/query/react";

import { createBaseQueryWithReauth } from "@/lib/utils/baseQuery";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";
import { USER_ENDPOINTS } from "@/lib/redux/slices/user/userApiEndpoints";
import { IAddress, IBookingResponse } from "../../types";

// Define the tag type
export const USER_API_TAGS = {
  address: "Address",
  user: "User",
} as const;

// Create a type for the tags
type TagTypes = (typeof USER_API_TAGS)[keyof typeof USER_API_TAGS];

export const userApi = createApi({
  reducerPath: "userApi",
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "api/"
  ),
  tagTypes: [USER_API_TAGS.address, USER_API_TAGS.user],
  endpoints: (builder) => ({
    me: builder.mutation<any, any>({
      query: (data) => ({
        url: USER_ENDPOINTS.me,
        method: "PATCH",
        body: keysToSnake(data),
      }),
      invalidatesTags: [{ type: USER_API_TAGS.user }],
    }),
    getUserDetails: builder.query<any, any>({
      query: () => ({
        url: USER_ENDPOINTS.me,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
      providesTags: [USER_API_TAGS.user],
    }),
    getBookedEvents: builder.query<IBookingResponse, { status?: string }>({
      query: (props) => {
        const { status } = props;
        let url = "users/me/orders/";

        if (status) {
          url += `?status=${status}`;
        }

        return {
          url,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
    }),
    getUserProfileDetails: builder.query<any, any>({
      query: () => ({
        url: USER_ENDPOINTS.profile,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
      providesTags: [USER_API_TAGS.user],
    }),
    updateUserProfile: builder.mutation<any, any>({
      query: (data) => ({
        url: USER_ENDPOINTS.profile,
        method: "PATCH",
        body: keysToSnake(data),
      }),
      invalidatesTags: [{ type: USER_API_TAGS.user }],
    }),
    getUserDashboardInvites: builder.query<any, any>({
      query: () => ({
        url: USER_ENDPOINTS.invitations,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    userSearch: builder.query<any, any>({
      query: (props) => ({
        url: `${USER_ENDPOINTS.userSearch}?search=${props.search}`,
        method: "GET",
      }),
    }),
    getUser: builder.query<any, any>({
      query: (props) => ({
        url: `${USER_ENDPOINTS.getUser}${props.userId}/profile`,
        method: "GET",
      }),
    }),
    // Get all addresses
    getUserAddresses: builder.query<IAddress[], void>({
      query: () => ({
        url: USER_ENDPOINTS.addresses,
        method: "GET",
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: USER_API_TAGS.address, id })),
              { type: USER_API_TAGS.address, id: "LIST" },
            ]
          : [{ type: USER_API_TAGS.address, id: "LIST" }],
    }),

    // Get single address
    getUserAddress: builder.query<IAddress, string>({
      query: (id) => ({
        url: `${USER_ENDPOINTS.addresses}${id}/`,
        method: "GET",
      }),
      providesTags: (_result, _error, id) => [
        { type: USER_API_TAGS.address, id },
      ],
    }),

    // Add new address
    addUserAddress: builder.mutation<IAddress, { addressType: string; addressDetails: any }>({
      query: (data) => ({
        url: USER_ENDPOINTS.addresses,
        method: "POST",
        body: keysToSnake(data),
      }),
      invalidatesTags: [{ type: USER_API_TAGS.address, id: "LIST" }],
    }),

    // Update address
    updateUserAddress: builder.mutation<
      IAddress,
      { id: string; data: Partial<Omit<IAddress, "id">> }
    >({
      query: ({ id, data }) => ({
        url: `${USER_ENDPOINTS.addresses}${id}/`,
        method: "PATCH",
        body: keysToSnake(data),
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: USER_API_TAGS.address, id },
        { type: USER_API_TAGS.address, id: "LIST" },
      ],
    }),

    // Delete address
    deleteUserAddress: builder.mutation<void, string>({
      query: (id) => ({
        url: `${USER_ENDPOINTS.addresses}${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: USER_API_TAGS.address, id },
        { type: USER_API_TAGS.address, id: "LIST" },
      ],
    }),
    getUserOrganizations: builder.query<any, void>({
      query: () => ({
        url: `users/me/organizations/`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    getUserNotifications: builder.query<any, any>({
      query: (props) => ({
        url: `users/notifications/?cursor=${props.cursor ?? ""}&per_page=${props.perPage ?? 10}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    getUserLnkRequests: builder.query<any, any>({
      query: () => ({
        url: `users/me/connections/`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    markViewNotifications: builder.mutation<any, any>({
      query: (props) => ({
        url: `users/notifications/`,
        method: "PATCH",
        body: keysToSnake(props),
      }),
    }),
    checkUsername: builder.mutation<any, { username: string }>({
      query: ({ username }) => ({
        url: `${USER_ENDPOINTS.getUser}${username}/`,
        method: "GET",
      }),
    }),
    setDefaultAddresses: builder.mutation<any, { addressType: string; addressId: string }>({
      query: (props) => ({ 
        url: USER_ENDPOINTS.setDefaultAddresses,
        method: "PATCH",
        body: keysToSnake(props),
      }),
    }),
  }),
});

export const {
  useMeMutation,
  useGetUserDetailsQuery,
  useGetBookedEventsQuery,
  useGetUserProfileDetailsQuery,
  useUpdateUserProfileMutation,
  useGetUserDashboardInvitesQuery,
  useUserSearchQuery,
  useGetUserQuery,
  useGetUserAddressesQuery,
  useGetUserAddressQuery,
  useAddUserAddressMutation,
  useUpdateUserAddressMutation,
  useDeleteUserAddressMutation,
  useGetUserOrganizationsQuery,
  useGetUserNotificationsQuery,
  useGetUserLnkRequestsQuery,
  useMarkViewNotificationsMutation,
  useCheckUsernameMutation,
  useSetDefaultAddressesMutation,
} = userApi;
