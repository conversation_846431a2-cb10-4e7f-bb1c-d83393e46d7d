import { createApi } from "@reduxjs/toolkit/query/react";
import { createBaseQueryWithReauth } from "@/lib/utils/baseQuery";
import { VEHICLES_ENDPOINTS } from "./vehiclesApiEndpoint";
import { keysToSnake } from "@/lib/utils/snakeCaseConvertor";

interface VehicleFormData {
  name: string;
  year: string;
  makeId: string;
  modelId: string;
  isActive: boolean;
  coverImage?: string;
  images?: string[];
}

export const vehicleApi = createApi({
  reducerPath: "vehiclesApi",
  baseQuery: createBaseQueryWithReauth(
    process.env.NEXT_PUBLIC_API_URL! + "api/"
  ),
  endpoints: (builder) => ({
    getVehicleTypesList: builder.query<any, any>({
      query: () => {
        return {
          url: `${VEHICLES_ENDPOINTS.vehicleTypes}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 1,
    }),
    getVehicleMakesList: builder.query<any, any>({
      query: () => {
        return {
          url: `${VEHICLES_ENDPOINTS.vehicleMakes}`,
          method: "GET",
        };
      },
    }),
    getVehicleModelsList: builder.query<
      any,
      { type?: string | null; make?: string | null }
    >({
      query: ({ type, make }) => ({
        url: `${VEHICLES_ENDPOINTS.vehicleModels}?type=${type}&make=${make}`,
        method: "GET",
      }),
    }),
    addVehicle: builder.mutation<any, VehicleFormData>({
      query: (data) => ({
        url: "/vehicles/",
        method: "POST",
        body: keysToSnake(data),
        headers: {
          'Content-Type': 'application/json',
        },
      }),
    }),
    getVehicleDetails: builder.query<any, any>({
      query: ({ id }) => {
        return {
          url: `${VEHICLES_ENDPOINTS.vehiclesList}${id}/`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
    }),

    updateVehicle: builder.mutation<any, { token: any; id: string; data: VehicleFormData }>(
      {
        query: ({ token, id, data }) => {
          return {
            url: `${VEHICLES_ENDPOINTS.vehiclesList}${id}/`,
            method: "PUT",
            body: keysToSnake(data),
            headers: {
              'Content-Type': 'application/json',
            },
          };
        },
      }
    ),

    getModificationTypes: builder.query<any, any>({
      query: () => {
        return {
          url: `${VEHICLES_ENDPOINTS.modficationTypes}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 1,
    }),
    getVehiclesList: builder.query<any, any>({
      query: () => {
        return {
          url: `${VEHICLES_ENDPOINTS.vehiclesList}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 0,
    }),
    getModificationCategories: builder.query<any, any>({
      query: () => {
        return {
          url: `${VEHICLES_ENDPOINTS.modificationCategories}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 1,
    }),
    getModificationParts: builder.query<any, any>({
      query: () => {
        return {
          url: `${VEHICLES_ENDPOINTS.modificationParts}`,
          method: "GET",
        };
      },
      keepUnusedDataFor: 1,
    }),
    addVehicleModification: builder.mutation<any, any>({
      query: ({ data }) => ({
        url: VEHICLES_ENDPOINTS.modifications,
        method: "POST",
        body: data,
      }),
    }),
    updateVehicleModification: builder.mutation<any, any>({
      query: ({ id, data }) => ({
        url: `${VEHICLES_ENDPOINTS.modifications}${id}/`,
        method: "PUT",
        body: data,
      }),
    }),
  }),
});

export const {
  useGetVehicleMakesListQuery,
  useGetVehicleModelsListQuery,
  useGetVehicleTypesListQuery,
  useAddVehicleMutation,
  useGetVehicleDetailsQuery,
  useUpdateVehicleMutation,
  useGetModificationTypesQuery,
  useGetVehiclesListQuery,
  useGetModificationCategoriesQuery,
  useGetModificationPartsQuery,
  useAddVehicleModificationMutation,
  useUpdateVehicleModificationMutation,
} = vehicleApi;
