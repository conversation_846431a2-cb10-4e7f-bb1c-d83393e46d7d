import type { Action, ThunkAction } from "@reduxjs/toolkit";
import { combineReducers, configureStore } from "@reduxjs/toolkit";
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import storage from "./storage";
import { authApi } from "./slices/auth/authApi";
import authReducer from "./slices/auth/authSlice";
import { userApi } from "./slices/user/userApi";
import { eventsApi } from "./slices/events/eventsApi";

// `combineSlices` automatically combines the reducers using
// their `reducerPath`s, therefore we no longer need to call `combineReducers`.

import cartReducer from "./slices/cart/cartSlice";
import { cartApi } from "./slices/cart/cartApi";
import { organizationApi } from "./slices/organization/api";
import { vehicleApi } from "./slices/vehicles/vehiclesApi";
import { messagingApi } from "./slices/messaging/messagingApi";
import { productsApi } from "./slices/products/productsApi";
import { ticketsApi } from "./slices/tickets/ticketsApi";
import { analyticsApi } from "./slices/analytics/api";
import { ordersApi } from "./slices/orders/ordersApi";
import eventReducer from "./slices/events/eventSlice";
import { restrictionMiddleware } from "@/lib/utils/restrictionMiddleware";

const rootReducer = combineReducers({
  auth: authReducer,
  [authApi.reducerPath]: authApi.reducer,
  cart: cartReducer,
  events: eventReducer,
  [cartApi.reducerPath]: cartApi.reducer,
  [userApi.reducerPath]: userApi.reducer,
  [eventsApi.reducerPath]: eventsApi.reducer,
  [organizationApi.reducerPath]: organizationApi.reducer,
  [vehicleApi.reducerPath]: vehicleApi.reducer,
  [messagingApi.reducerPath]: messagingApi.reducer,
  [productsApi.reducerPath]: productsApi.reducer,
  [ticketsApi.reducerPath]: ticketsApi.reducer,
  [analyticsApi.reducerPath]: analyticsApi.reducer,
  [ordersApi.reducerPath]: ordersApi.reducer,
});
// Infer the `RootState` type from the root reducer
export type RootState = ReturnType<typeof rootReducer>;

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["events"],
};
const persistedReducer = persistReducer(persistConfig, rootReducer);

export const makeStore = () => {
  return configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        },
      }).concat([
        authApi.middleware,
        cartApi.middleware,
        userApi.middleware,
        organizationApi.middleware,
        eventsApi.middleware,
        vehicleApi.middleware,
        messagingApi.middleware,
        productsApi.middleware,
        ticketsApi.middleware,
        analyticsApi.middleware,
        ordersApi.middleware,
        restrictionMiddleware,
      ]),
  });
};

export type AppStore = ReturnType<typeof makeStore>;
export type AppDispatch = AppStore["dispatch"];
export type AppThunk<ThunkReturnType = void> = ThunkAction<
  ThunkReturnType,
  RootState,
  unknown,
  Action
>;
const store = makeStore();
const persistor = persistStore(store);

export { store, persistor };
