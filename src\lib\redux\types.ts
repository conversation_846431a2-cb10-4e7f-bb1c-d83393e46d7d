export interface IBookingResponse {
  next_cursor: string;
  prev_cursor: string;
  next_page_results: boolean;
  prev_page_results: boolean;
  count: number;
  total_pages: number;
  total_results: number;
  extra_stats: null | any;
  results: Booking[];
}

interface Booking {
  eventDetails: any;
  booking_id: string;
  number: string;
  event: Event;
  status: string;
  created_at: string;
  tickets: Ticket[];
}

interface Event {
  id: string;
  name: string;
  images: Image[];
  address: string;
  start_date: string;
}

interface Image {
  id: string;
  created_at: string;
  updated_at: string;
  photo: string;
  type: number;
  created_by: string;
  updated_by: string;
  event: string;
}

interface Ticket {
  ticket_id: string | null;
  quantity: number;
  ticket_title: string | null;
  title_description: string | null;
  ticket_price: string;
  is_vip: boolean;
  vehicle: Vehicle | null;
}

interface Vehicle {
  name: string;
  year: number;
  type: string;
  make: string;
  model_name: string;
  modification_text: string;
  modifications: any[];
  images: VehicleImage[];
}

interface VehicleImage {
  image: string;
  tag: string;
  type: number;
}

interface Country {
  code: string;
  name: string;
}

export interface IAddress {
  id: string;
  firstName: string;
  lastName: string;
  companyName: string;
  streetAddress_1: string;
  streetAddress_2: string;
  city: string;
  cityArea: string;
  postalCode: string;
  country: Country;
  countryArea: string;
  phone: string;
  isDefault: boolean;
}

export interface CartResponse {
  checkout?: {
    lines?: Array<any>;
  };
  items: Array<{
    id: number;
    variant: {
      id: number;
      name: string;
      price: number;
      product: {
        id: number;
        name: string;
        images: Array<{ photo: string }>;
      };
    };
    quantity: number;
    total: number;
  }>;
  total: number;
}

export interface AddToCartRequest {
  variant_id: number;
  quantity: number;
  attributes?: Record<string, string>;
}

export interface UpdateQuantityRequest {
  variant_id?: number;
  id?: string;
  type?: string;
  quantity: number;
  waiver_response_data?: Array<{
    waiverUrl: string;
    waiverSignature: string;
  }>;
}

export interface RemoveItemRequest {
  variant_id: number;
}

export interface InventoryCheckRequest {
  orgSlug: string;
  variantId: number;
}

export interface InventoryResponse {
  quantity: number;
  quantityAllocated: number;
  availableQuantity: number;
}

export interface PaymentGatewayResponse {
  id: string;
  data: {
    publishableKey: string;
    payment_type: string;
  };
}
