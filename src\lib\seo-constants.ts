// Structured data for AutoLNK website
export const websiteJsonLd = [
  {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    '@id': 'https://www.autolnkusa.com/#website',
    'name': 'AutoLNK',
    'url': 'https://www.autolnkusa.com/events',
    'mainEntity': {
      '@type': 'Organization',
      '@id': 'https://www.autolnkusa.com/#organization',
      'name': 'AutoLNK',
      'url': 'https://www.autolnkusa.com/events',
      'logo': 'https://www.autolnkusa.com/logo.png',
      'sameAs': [
        'https://www.facebook.com/people/Autolnk/61569856860016/',
        'https://x.com/autolnkusa',
        'https://www.instagram.com/autolnkusa',
        'https://www.linkedin.com/company/autolnk/'
      ]
    }
  }
];

// Breadcrumb for events list page
export const eventsListBreadcrumbJsonLd = {
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  'itemListElement': [
    {
      '@type': 'ListItem',
      'position': 1,
      'name': 'Home',
      'item': 'https://www.autolnkusa.com'
    },
    {
      '@type': 'ListItem',
      'position': 2,
      'name': 'Events',
      'item': 'https://www.autolnkusa.com/events'
    },
    {
      '@type': 'ListItem',
      'position': 3,
      'name': 'Event List',
      'item': 'https://www.autolnkusa.com/events/list'
    }
  ]
};

// Function to create JSON-LD for a single event
export const createEventJsonLd = (eventData) => {
  const mainEventImg = eventData?.images?.find(
    (img) => Number(img?.type) === 1
  );

  return {
    '@context': 'https://schema.org',
    '@type': 'Event',
    '@id': `https://www.autolnkusa.com/events/${eventData.slug}#event`,
    'name': eventData.name,
    'description': eventData.description,
    'image': mainEventImg?.photo,
    'startDate': eventData.startDate,
    'endDate': eventData.endDate,
    'eventStatus': 'https://schema.org/EventScheduled',
    'eventAttendanceMode': 'https://schema.org/OfflineEventAttendanceMode',
    'location': {
      '@type': 'Place',
      'name': eventData.venueName,
      'address': {
        '@type': 'PostalAddress',
        'addressLocality': eventData.city,
        'addressRegion': eventData.state,
        'streetAddress': eventData.address,
        'addressCountry': eventData.country,
        'postalCode': eventData.zipCode
      }
    },
    'organizer': {
      '@type': 'Organization',
      'name': eventData.organization?.owner?.name ?? 'AutoLNK',
      'url': `https://www.autolnkusa.com/profile/${eventData.organization?.owner?.id}/organization`
    },
    'offers': eventData.tickets?.map((ticket) => ({
      '@type': 'Offer',
      'name': ticket.name,
      'description': ticket.description ? ticket.description.replace(/<[^>]*>/g, '').replace(/[*#_~`\[\]()>-]/g, '') : undefined,
      'price': ticket.ticketPrice,
      'priceCurrency': ticket.currency ?? 'USD',
      'availability': ticket.quantityLeft > 0 ? 'https://schema.org/InStock' : 'https://schema.org/SoldOut',
      'validFrom': ticket.availableFrom || undefined,
      'validThrough': ticket.availableTill || undefined,
      'url': `https://www.autolnkusa.com/events/${eventData.slug}`
    }))
  };
};

// Featured events list for the events home page
export const createFeaturedEventsJsonLd = (events) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    '@id': 'https://www.autolnkusa.com/events#featuredevents',
    'itemListElement': events.map((event, index) => ({
      '@type': 'ListItem',
      '@id': `https://www.autolnkusa.com/events/${event.slug}#listitem`,
      'position': index + 1,
      'item': {
        '@type': 'Event',
        '@id': `https://www.autolnkusa.com/events/${event.slug}#event`,
        'name': event.name,
        'description': `Event listing by AutoLNK. Find details and tickets for this event organized by a ${event.organization?.owner?.name ?? "partner"}.`,
        'startDate': event.startDate,
        'endDate': event.endDate,
        'eventStatus': 'https://schema.org/EventScheduled',
        'eventAttendanceMode': 'https://schema.org/OfflineEventAttendanceMode',
        'location': {
          '@type': 'Place',
          'name': event.venueName,
          'address': {
            '@type': 'PostalAddress',
            'streetAddress': event.address,
            'addressLocality': event.city,
            'addressRegion': event.state,
            'addressCountry': event.country,
            'postalCode': event?.zipCode
          },
        },
        'organizer': {
            '@type': 'Organization',
            'name': event.organization?.owner?.name ?? 'AutoLNK partner',
            'url': `https://www.autolnkusa.com/profile/${event.organization?.owner?.id}/organization`
         },
        'image': event.images[0].photo,
        'url': `https://www.autolnkusa.com/events/${event.slug}`
      }
    }))
  };
};