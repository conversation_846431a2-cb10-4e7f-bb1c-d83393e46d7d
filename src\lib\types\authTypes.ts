export interface IProfile {
  id: string;
  createdAt: string;
  updatedAt: string;
  theme: Record<string, unknown>;
  isOnboarded: boolean;
  user: string;
}

export interface ITokenPair {
  accessToken: string;
  refreshToken: string;
}

export interface ISignupResponse {
  id: string;
  avatar: string;
  coverPhoto: string;
  dateJoined: string;
  displayName: string;
  phone: string | null;
  name: string;
  isActive: boolean;
  isBot: boolean;
  isPhoneVerified: boolean;
  userTimezone: string;
  username: string;
  isPasswordAutoset: boolean;
  firstName:string;
  lastName:string;
  lastLoginMedium: string;
  email: string;
  profile: IProfile;
  accessToken: string;
  refreshToken: string;
  accessTokenExpiration: string;
  refreshTokenExpiration: string;
}

export interface ISigninResponse {
  token: string;
}

export interface ISigninPayload {
  email: string;
  password: string;
}

export interface ISignupPayload {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export interface IRefreshTokenResponse {
  token: string;
}

export interface IResponseProps {
  message: string;
}
