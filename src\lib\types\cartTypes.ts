import { PROBLEM_TYPES } from "@/app/cart/utils/cartUtils";

/**
 * Cart problem types interfaces
 */
export interface BaseCartProblem {
  type: string;
  lineId: string;
  variantId: number;
  variantName: string;
}

export interface InsufficientStockProblem extends BaseCartProblem {
  type: typeof PROBLEM_TYPES.INSUFFICIENT_STOCK;
  availableQuantity: number;
  requestedQuantity: number;
}

export interface VariantNotAvailableProblem extends BaseCartProblem {
  type: typeof PROBLEM_TYPES.VARIANT_NOT_AVAILABLE;
  reason: "not_published" | "no_price" | "not_available";
}

export interface PriceChangedProblem extends BaseCartProblem {
  type: typeof PROBLEM_TYPES.PRICE_CHANGED;
  oldPrice: number;
  newPrice: number;
}

export interface TicketFormProblem extends BaseCartProblem {
  type: typeof PROBLEM_TYPES.TICKET_FORM;
  reason: string;
  expectedFormsCount: number;
  actualFormsCount: number;
}

export type CartProblem = 
  | InsufficientStockProblem 
  | VariantNotAvailableProblem 
  | PriceChangedProblem 
  | TicketFormProblem;

export interface ExtendedCartResponse {
  checkout?: {
    lines?: any[];
    customerMetadata?: Record<string, any>;
    discounts?: any[];
    initialSubtotal?: number;
    platformFee?: number;
    shippingPrice?: number;
    discountAmount?: number;
    total?: number;
    token?: string;
    voucherCode?: string;
  };
  problems?: CartProblem[];
}

export interface CartFooterProps {
  cartData: ExtendedCartResponse;
  refetchCart: () => void;
  hasProblems?: boolean;
} 