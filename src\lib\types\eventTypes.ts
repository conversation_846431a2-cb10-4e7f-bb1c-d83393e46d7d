interface IImage {
  id: string;
  photo: string;
}

interface ITicket {
  id: string;
  ticket_quanaity: number;
  ticket_template: {
    name: string;
    description: string;
    price: string;
    currency: string;
  };
  eventId?: string;
}

interface IEvent {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  city: string;
  state: string;
  country: string;
  images: IImage[];
  tickets: ITicket[];
  slug: string;
}

interface IEventsResponse {
  results: IEvent[];
  nextCursor: string | null;
  prevCursor: string | null;
  nextPageResults: boolean;
}
