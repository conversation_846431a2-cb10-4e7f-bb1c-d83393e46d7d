declare module "next-auth" {
  interface Session {
    accessToken?: string;
    refreshToken?: string;
    user: {
      teamDetails: any;
      id: string;
      email: string;
      name?: string;
      avatar?: string | null;
      coverPhoto?: string | null;
      bio?: string;
      dateJoined?: string;
      isActive?: boolean;
      isBot?: boolean;
      isPasswordAutoset?: boolean;
      isPhoneVerified?: boolean;
      userTimezone?: string;
      firstName?:string;
      lastName?:string;
      orgDetails?: {
        org: {
          id: string;
          slug: string;
          companyName: string;
          address: string;
          city: string;
          state: string;
          country: string;
          zipCode: string;
          phoneNumber?: string;
          email: string;
          avatar?: string;
          coverPhoto?: string;
          contactFullName: string;
          countryCode?: string;
          description?: string;
          eventsLocation?: string;
          upcomingEventsCount?: number;
          socialMedia?: {
            twitter?: string;
            facebook?: string;
          };
          createdAt?: string;
          updatedAt?: string;
          owner?: {
            id: string;
            name?: string;
            avatar?: string;
            username?: string;
            bio?: string;
          };
        };
        role?: string;
      };
      phone?: string | null;
      signupMedium?: string;
      username?: string;
      role?: string;
    };
  }

  interface User {
    accessToken: string;
    refreshToken: string;
  }
}

// Extend the built-in JWT types
declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
  }
}
