export interface Owner {
  id: string;
  name: string;
  avatar: string;
  username: string;
  bio: string;
  email: string;
  phone: string | null;
  isGuest: boolean;
}

export interface Organization {
  id: string;
  companyName: string;
  avatar: string;
  owner: Owner;
  coverPhoto: string;
  email: string;
}

export interface Member {
  username: string;
  name: string;
  role: string;
  avatar: string;
}

export interface Invite {
  invitationId: string;
  organization: Organization;
  email: string;
  role: string;
  createdAt: string;
  createdByDetail: string | null;
  permissions: string[];
  status: string;
  members: Member[];
  expiresAt: string;
}
