interface IType {
    id: string;
    name: string;
    vehicleType?: string;
  }
  
  interface IVehicleMake {
    id: string;
    name: string;
    type: IType;
  }
  
  interface IVehicleModel {
    id: string;
    name: string;
    make: IVehicleMake;
    msrp: number;
  }
  
  interface IVehiclePart {
    id: string;
    name: string;
    type: IType;
    category: ICategory;
    msrp: number;
  }
  
  interface ICategory {
    id: string;
    name: string;
    type: IType;
  }
  
  interface IVehicleModification {
    id: string;
    brand: string;
    points: number;
    vehicle: Pick<IVehicle, 'id'>;
    part: IVehiclePart;
    meta: string;
  }
  
  interface IUser {
    [key: string]: string;
  }

  interface IVehicleImage {
    id: string,
    photo: string
  }
  
  interface IVehicle {
    id: string;
    name: string;
    model: IVehicleModel;
    coverPhoto: IVehicleImage | null;
    vehicleImages: IVehicleImage[];
    year: number;
    points: number;
    modifications: IVehicleModification[];
    garageId: string;
    user: IUser;
    meta: string;
  }
  