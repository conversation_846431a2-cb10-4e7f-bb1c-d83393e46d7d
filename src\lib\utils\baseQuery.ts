import {
  BaseQueryFn,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FetchBaseQueryError,
  retry,
} from "@reduxjs/toolkit/query";
import { fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { Mutex } from "async-mutex";
import { RootState } from "@/lib/redux/store";
import { keysToCamel } from "@/lib/utils/snakeCaseConvertor";
import {
  setUser,
  clearUser,
} from "@/lib/redux/slices/auth/authSlice";
import { Logout } from "../actions/auth/Logout";
import { removeCookie, getCookie, setCookie } from "./cookieUtils";
import { refreshTokenAction } from "@/lib/actions/auth/refreshTokenAction";
import {
  COOKIE_NAMES,
  TOKEN_ERROR_CODES,
  TOKEN_REFRESH_ERRORS,
  HTTP_STATUS,
} from "@/lib/utils/constants";
import { getSubdomainInfo } from "@/lib/utils/subdomainUtils";

const getTimezone = (state: RootState) => {
  const userTimezone = state?.auth?.user?.userTimezone;
  if (userTimezone) return userTimezone;
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

const getOrganizerSlug = () => {
  // Get organizer slug from subdomain if available (client-side)
  if (typeof window !== "undefined") {
    const subdomainInfo = getSubdomainInfo(window.location.hostname);
    return subdomainInfo.organizerSlug;
  }
  return null;
};

/**
 * Type-guard function to determine if a FetchBaseQueryError represents a token-related authentication error
 * that should trigger a token refresh attempt.
 * 
 * @param error - The error from a fetch request
 * @returns true if the error indicates invalid/expired tokens that warrant a refresh attempt
 */
export const isTokenError = (error: FetchBaseQueryError): boolean => {
  // Must be a 401 Unauthorized error
  if (!error || error.status !== HTTP_STATUS.UNAUTHORIZED) {
    return false;
  }

  // Check for specific token error codes in structured error data
  if (error.data && typeof error.data === "object" && 'code' in error.data) {
    const errorCode = (error.data as any).code;
    return errorCode === TOKEN_ERROR_CODES.TOKEN_NOT_VALID || 
           errorCode === TOKEN_ERROR_CODES.SIGNATURE_HAS_EXPIRED;
  }

  // Handle 401s without specific error codes (generic unauthorized)
  if (!error.data) {
    return true;
  }

  // Check for token-related keywords in string error data
  if (typeof error.data === "string" && error.data.includes("token")) {
    return true;
  }

  return false;
};

export const createBaseQueryWithoutAuth = (
  baseUrl: string,
  maxRetries: number = 0
): BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError> => {
  const baseQuery = async (args, api, extraOptions) => {
    const organizerSlug = getOrganizerSlug();
    
    const rawBaseQuery = fetchBaseQuery({
      baseUrl,
      prepareHeaders: (headers, { getState }) => {
        headers.set("X-Timezone", getTimezone(getState() as RootState));
        
        // Add organizer slug for whitelabel requests
        if (organizerSlug) {
          headers.set("X-Organizer-Slug", organizerSlug);
        }
        
        return headers;
      },
    });

    const result = await rawBaseQuery(args, api, extraOptions);

    return {
      ...result,
      data: result.data ? keysToCamel(result.data) : result.data,
    };
  };

  return retry(baseQuery, {
    maxRetries,
  });
};

const mutex = new Mutex();

const removeRestrictionCookies = () => {
  removeCookie(COOKIE_NAMES.RESTRICTED);
  removeCookie(COOKIE_NAMES.RESTRICTION_CODE);
  removeCookie(COOKIE_NAMES.RESTRICTION_DETAIL);
  removeCookie(COOKIE_NAMES.RESTRICTION_REASON);
};

export const createBaseQueryWithReauth = (
  baseUrl: string,
  maxRetries: number = 0
): BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError> => {
  const rawBaseQuery = fetchBaseQuery({
    baseUrl,
    credentials: "include",
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth?.user?.accessToken;
      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }
      headers.set("X-Timezone", getTimezone(getState() as RootState));
      
      // Add organizer slug for whitelabel requests
      const organizerSlug = getOrganizerSlug();
      if (organizerSlug) {
        headers.set("X-Organizer-Slug", organizerSlug);
      }
      
      return headers;
    },
  });

  const baseQueryWithReauth = async (args, api, extraOptions) => {
    await mutex.waitForUnlock();
    let result = await rawBaseQuery(args, api, extraOptions);

    if (result.data) {
      result.data = keysToCamel(result.data);
    }

    if (result.error?.status !== HTTP_STATUS.FORBIDDEN) {
      removeRestrictionCookies();
    }

    if (result.error && isTokenError(result.error)) {
      if (!mutex.isLocked()) {
        const release = await mutex.acquire();
        try {
          const state = api.getState() as RootState;
          const currentCsrfToken = getCookie(COOKIE_NAMES.CSRF_TOKEN) || state.auth.user?.csrfToken;
          let currentRefreshToken = getCookie(COOKIE_NAMES.REFRESH_TOKEN) || state.auth.user?.refreshToken;

          if (!currentRefreshToken || !currentCsrfToken) {
            console.warn("Missing refresh or CSRF token during token refresh attempt");
            api.dispatch(clearUser());
            removeCookie(COOKIE_NAMES.CSRF_TOKEN);
            removeCookie(COOKIE_NAMES.REFRESH_TOKEN);
            await Logout();
            return result;
          }

          const refreshResult = await refreshTokenAction(currentRefreshToken, currentCsrfToken);

          if ('accessToken' in refreshResult && refreshResult.accessToken) {
            const user = state.auth.user;
            api.dispatch(
              setUser({
                ...user!,
                accessToken: refreshResult.accessToken,
                refreshToken: refreshResult.refreshToken || currentRefreshToken,
                csrfToken: refreshResult.csrfToken || currentCsrfToken,
              })
            );

            if (refreshResult.csrfToken && refreshResult.csrfToken !== currentCsrfToken) {
              setCookie(COOKIE_NAMES.CSRF_TOKEN, refreshResult.csrfToken, { 
                secure: process.env.NODE_ENV === 'production', 
                sameSite: 'lax'
              });
            }

            result = await rawBaseQuery(args, api, extraOptions);
            if (result.data) {
              result.data = keysToCamel(result.data);
            }
            if (result.error?.status !== HTTP_STATUS.FORBIDDEN) {
              removeRestrictionCookies();
            }
          } else {
            const errorDetails = refreshResult as { error: string; message?: string };
            console.warn("Token refresh failed:", errorDetails);
            
            // Clean up auth state
            api.dispatch(clearUser());
            removeCookie(COOKIE_NAMES.CSRF_TOKEN);
            removeCookie(COOKIE_NAMES.REFRESH_TOKEN); 
            await Logout();
            
            return {
              error: {
                status: 'CUSTOM_ERROR' as const,
                error: errorDetails.message || 'Session refresh failed via server action.',
                data: {
                  code: errorDetails.error || TOKEN_REFRESH_ERRORS.REFRESH_FAILED_SERVER_ACTION,
                  originalMessage: errorDetails.message,
                  redirect: true,
                  redirectUrl: '/',
                },
              } as FetchBaseQueryError,
            };
          }
        } catch (error: any) {
          console.error("Unexpected error during token refresh:", error);
          api.dispatch(clearUser());
          removeCookie(COOKIE_NAMES.CSRF_TOKEN);
          removeCookie(COOKIE_NAMES.REFRESH_TOKEN);
          await Logout(); 
          return { 
              error: {
                  status: 'CUSTOM_ERROR' as const,
                  error: error.message || 'An unexpected error occurred during re-authentication.',
                  data: { details: error.message, originalError: error }
              } as FetchBaseQueryError,
          };
        } finally {
          release();
        }
      } else {
        await mutex.waitForUnlock();
        result = await rawBaseQuery(args, api, extraOptions);
        if (result.data) {
          result.data = keysToCamel(result.data);
        }
        if (result.error?.status !== HTTP_STATUS.FORBIDDEN) {
          removeRestrictionCookies();
        }
      }
    }
    
    return result;
  };

  return retry(baseQueryWithReauth, {
    maxRetries,
  });
};
