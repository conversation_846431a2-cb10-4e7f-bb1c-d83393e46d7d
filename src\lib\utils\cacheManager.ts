// lib/utils/cacheManager.ts
import { isClient } from "./environment";
import { clearUser } from "@/lib/redux/slices/auth/authSlice";
import { store } from "@/lib/redux/store";
import { SafeStorage } from "./safeStorage";

interface CacheConfig {
  prefix: string;
  excludePatterns?: RegExp[];
  protectedKeys?: string[];
  clearCookies?: boolean;
  clearServiceWorkers?: boolean;
}

interface BrowserInfo {
  name: string;
  version: string;
  isSafari: boolean;
  isChrome: boolean;
  isFirefox: boolean;
}

interface DiagnosisResult {
  deploymentId: string;
  storedDeploymentId: string | null;
  cacheAvailable: boolean;
  serviceWorkerAvailable: boolean;
  protectedKeys: string[];
  browser?: BrowserInfo;
  localStorage: {
    size: number;
    protectedItemsPresent: Record<string, boolean>;
  };
  indexedDB?: {
    databases: string[];
    status: string;
  };
  cookies?: {
    count: number;
    protected: string[];
  };
  serviceWorkers?: {
    active: number;
    status: string;
  };
  caches?: {
    keys: string[];
    contents: Record<string, string[]>;
  };
  performance?: {
    resourceCount: number;
    navigationTiming: any;
    memory?: {
      jsHeapSizeLimit: number;
      totalJSHeapSize: number;
      usedJSHeapSize: number;
    };
  };
  error?: {
    hasError: boolean;
    message: string;
  };
}

export class CacheManager {
  private static readonly DEPLOY_ID_KEY = "app-deploy-id";
  private static isClearing = false; // Mutex lock for cache clearing
  private static errorLoopCounts = new Map<string, number>(); // Track error loops by type
  private static readonly MAX_ERROR_LOOPS = 3; // Maximum error loops before giving up
  private static lastReloadTime = 0; // Track last reload time globally
  private static readonly MIN_RELOAD_INTERVAL = 10000; // Minimum 10 seconds between reloads
  private static readonly DEFAULT_CONFIG: CacheConfig = {
    prefix: "next-",
    excludePatterns: [/\/api\/auth\//, /\/_next\/static\//],
    protectedKeys: ["user", "app-deploy-id"],
    clearCookies: true,
    clearServiceWorkers: true,
  };

  private static config: CacheConfig;

  private static getBrowserInfo(): BrowserInfo {
    const userAgent = navigator.userAgent;
    const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
    const isChrome = /chrome|chromium|crios/i.test(userAgent);
    const isFirefox = /firefox|fxios/i.test(userAgent);

    return {
      name: isSafari
        ? "Safari"
        : isChrome
        ? "Chrome"
        : isFirefox
        ? "Firefox"
        : "Other",
      version: userAgent.match(/version\/(\d+(\.\d+)?)/i)?.[1] || "0",
      isSafari,
      isChrome,
      isFirefox,
    };
  }

  static initialize(config: Partial<CacheConfig> = {}) {
    this.config = { ...this.DEFAULT_CONFIG, ...config };
    if (isClient()) {
      try {
        const url = new URL(window.location.href);
        if (url.searchParams.has("_cache_buster")) {
          url.searchParams.delete("_cache_buster");
          window.history.replaceState({}, "", url.toString());
        }
      } catch (error) {
        console.error("Failed to clean URL:", error);
      }

      // Only perform deployment checks in a production environment and not on localhost.
      if (
        process.env.NODE_ENV === "production" &&
        window.location.hostname !== "localhost"
      ) {
        this.checkDeployment().catch(console.error);
      }
    }
  }

  private static async getDeploymentId(): Promise<string> {
    try {
      // 1. Prioritize build-time environment variable
      const buildTime = process.env.NEXT_PUBLIC_BUILD_TIME;
      if (buildTime) {
        return buildTime;
      }

      // 2. Fallback to Next.js build ID
      if (isClient() && window.__NEXT_DATA__?.buildId) {
        return window.__NEXT_DATA__.buildId;
      }
      
      // 3. Last resort fallback to timestamp, with a warning
      console.warn(
        "CacheManager: NEXT_PUBLIC_BUILD_TIME and window.__NEXT_DATA__.buildId are not available. " +
        "Falling back to a timestamp for cache busting, which may be unreliable. " +
        "For best results, set NEXT_PUBLIC_BUILD_TIME in your build process."
      );
      return Date.now().toString();
    } catch (error) {
      // In case of any unexpected error, still provide a fallback
      console.error("Error getting deployment ID:", error);
      return Date.now().toString();
    }
  }

  private static async checkDeployment(): Promise<void> {
    try {
      const currentDeployId = await this.getDeploymentId();
      let storedDeployId: string | null = null;
      
      const storedResult = SafeStorage.getItem(this.DEPLOY_ID_KEY);
      if (storedResult.success) {
        storedDeployId = storedResult.data || null;
      } else {
        console.warn("Could not access localStorage for deployment check:", storedResult.error);
        return;
      }

      // Always update the deployment ID in storage first.
      // This prevents infinite reload loops on Safari, where a page reload
      // can interrupt the script before the new ID is saved.
      const setResult = SafeStorage.setItem(this.DEPLOY_ID_KEY, currentDeployId);
      if (!setResult.success) {
        console.warn("Could not store deployment ID:", setResult.error);
      }

      if (storedDeployId && storedDeployId !== currentDeployId) {
        console.log(
          `New deployment detected (${storedDeployId} -> ${currentDeployId}), clearing caches...`
        );
        await this.clearAllCaches();
      }
    } catch (error) {
      console.error("CacheManager: Failed to check deployment status.", error);
    }
  }

  static async clearAllCaches(): Promise<void> {
    if (!isClient() || this.isClearing) {
      if (this.isClearing) console.log("Cache clearing already in progress.");
      return;
    }

    this.isClearing = true;
    console.log("Starting complete site data cleanup...");
    
    const browser = this.getBrowserInfo();
    const isSafari = browser.isSafari;

    try {
      // Clear user data from Redux store first
      store.dispatch(clearUser());

      // 1. Clear Cache Storage with timeout
      if ("caches" in window) {
        try {
          const clearCachePromise = (async () => {
            const keys = await caches.keys();
            await Promise.all(keys.map((key) => caches.delete(key)));
          })();
          
          await Promise.race([
            clearCachePromise,
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error("Cache clear timeout")), 10000)
            )
          ]);
          console.log("Cache Storage cleared");
        } catch (e) {
          console.log("Cache Storage clear attempted but may have failed:", e);
        }
      }

      // 2. Clear IndexedDB with proper async handling
      if (window.indexedDB) {
        try {
          let dbs: string[] = [];
          
          if (isSafari) {
            dbs = ["next-pwa", "workbox", "localforage"]; // Known databases for Safari
          } else {
            try {
              // Check if databases() method is available
              if (typeof window.indexedDB.databases === 'function') {
                const databases = await window.indexedDB.databases();
                dbs = databases.map((db) => db.name).filter(Boolean) as string[];
              }
            } catch (e) {
              console.log("IndexedDB.databases() not supported, using fallback list");
              dbs = ["next-pwa", "workbox", "localforage"];
            }
          }

          // Properly await database deletions
          const deletePromises = dbs.map((dbName) => {
            return new Promise<void>((resolve) => {
              try {
                const deleteReq = indexedDB.deleteDatabase(dbName);
                deleteReq.onsuccess = () => resolve();
                deleteReq.onerror = () => {
                  console.log(`Failed to delete database: ${dbName}`);
                  resolve(); // Still resolve to not block other operations
                };
                deleteReq.onblocked = () => {
                  console.log(`Database deletion blocked: ${dbName}`);
                  setTimeout(() => resolve(), 2000); // Timeout after 2 seconds
                };
              } catch (e) {
                console.log(`Error deleting database ${dbName}:`, e);
                resolve();
              }
            });
          });

          await Promise.all(deletePromises);
          console.log("IndexedDB cleared");
        } catch (e) {
          console.log("IndexedDB clear attempted but may have failed:", e);
        }
      }

      // 3. Clear LocalStorage except protected items with error handling
      try {
        Object.keys(localStorage).forEach((key) => {
          if (!this.isProtectedKey(key)) {
            try {
              localStorage.removeItem(key);
            } catch (e) {
              console.warn(`Failed to remove localStorage key: ${key}`, e);
            }
          }
        });
      } catch (e) {
        console.warn("Failed to enumerate localStorage keys:", e);
      }

      // 4. Clear SessionStorage except protected items with error handling
      try {
        Object.keys(sessionStorage).forEach((key) => {
          if (!this.isProtectedKey(key)) {
            try {
              sessionStorage.removeItem(key);
            } catch (e) {
              console.warn(`Failed to remove sessionStorage key: ${key}`, e);
            }
          }
        });
      } catch (e) {
        console.warn("Failed to enumerate sessionStorage keys:", e);
      }

      // 5. Clear Cookies
      if (this.config.clearCookies) {
        this.clearCookies(isSafari);
      }

      // 6. Clear Service Workers with timeout and error handling
      if (this.config.clearServiceWorkers && "serviceWorker" in navigator) {
        try {
          const swPromise = (async () => {
            const registrations = await navigator.serviceWorker.getRegistrations();
            await Promise.all(
              registrations.map(async (reg) => {
                try {
                  if (isSafari && reg.active) {
                    // Send message with timeout
                    const messagePromise = new Promise((resolve) => {
                      setTimeout(resolve, 1000); // 1 second timeout
                    });
                    reg.active.postMessage({ type: "SKIP_WAITING" });
                    await messagePromise;
                  }
                  return reg.unregister();
                } catch (e) {
                  console.warn("Failed to unregister service worker:", e);
                }
              })
            );
          })();
          
          await Promise.race([
            swPromise,
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error("SW clear timeout")), 5000)
            )
          ]);
          console.log("Service Workers cleared");
        } catch (e) {
          console.log("Service Worker clear attempted but may have failed:", e);
        }
      }

      // 7. Clear Next.js specific caches
      try {
        if (window.__NEXT_DATA__?.dynamicIds) {
          window.__NEXT_DATA__.dynamicIds = [];
        }
      } catch (e) {
        console.warn("Failed to clear Next.js dynamic IDs:", e);
      }

      // 8. Clear Performance data
      if ("performance" in window) {
        try {
          performance.clearResourceTimings();
          if (!isSafari) {
            performance.clearMarks?.();
            performance.clearMeasures?.();
          }
        } catch (e) {
          console.warn("Failed to clear performance data:", e);
        }
      }

      // 9. Safari-specific reload strategy with safety checks
      if (isSafari) {
        try {
          const url = new URL(window.location.href);
          url.searchParams.delete("_cache_buster");
          
          // Check if we've been in a reload loop
          const reloadCount = parseInt(url.searchParams.get("_reload_count") || "0");
          if (reloadCount >= 3) {
            console.warn("Too many cache-related reloads, stopping to prevent infinite loop");
            return;
          }
          
          url.searchParams.set("_cache_buster", Date.now().toString());
          url.searchParams.set("_reload_count", (reloadCount + 1).toString());
          
          window.location.href = url.toString();
        } catch (e) {
          console.error("Failed to perform Safari reload:", e);
          // Fallback to simple reload
          window.location.reload();
        }
      }
    } catch (error) {
      console.error("Error clearing caches:", error);
      // Do not re-throw, to prevent crashing the global error handler
    } finally {
      this.isClearing = false;
      console.log("Cache cleanup finished.");
    }
  }

  private static clearCookies(isSafari: boolean): void {
    try {
      document.cookie.split(";").forEach((cookie) => {
        const cookieName = cookie.split("=")[0].trim();
        if (!this.isProtectedKey(cookieName)) {
          const pastDate = new Date(0).toUTCString();

          const variants = [
            `${cookieName}=; expires=${pastDate}; path=/`,
            `${cookieName}=; expires=${pastDate}; path=/; domain=${window.location.hostname}`,
            `${cookieName}=; expires=${pastDate}; path=/; domain=.${window.location.hostname}`,
          ];

          if (isSafari) {
            variants.push(
              `${cookieName}=; expires=${pastDate}; path=/; secure`,
              `${cookieName}=; expires=${pastDate}; path=/; SameSite=Lax`,
              `${cookieName}=; expires=${pastDate}; path=/; SameSite=Strict`
            );
          }

          variants.forEach((variant) => {
            try {
              document.cookie = variant;
            } catch (e) {
              console.warn(`Failed to clear cookie: ${cookieName}`, e);
            }
          });
        }
      });
    } catch (e) {
      console.warn("Failed to clear cookies:", e);
    }
  }

  private static isProtectedKey(key: string): boolean {
    return (this.config.protectedKeys || []).includes(key);
  }

  /**
   * Check if a reload is safe to prevent reload loops
   */
  private static isReloadSafe(): boolean {
    const now = Date.now();
    const timeSinceLastReload = now - this.lastReloadTime;
    return timeSinceLastReload >= this.MIN_RELOAD_INTERVAL;
  }

  /**
   * Safely trigger a page reload with loop protection
   */
  static safeReload(reason: string = "unknown"): boolean {
    if (!this.isReloadSafe()) {
      console.warn(`Reload prevented: Too soon since last reload. Reason: ${reason}`);
      return false;
    }

    this.lastReloadTime = Date.now();
    console.log(`Safe reload triggered. Reason: ${reason}`);
    
    try {
      window.location.reload();
      return true;
    } catch (error) {
      console.error("Failed to reload page:", error);
      return false;
    }
  }

  /**
   * Get or increment error count for a specific error type
   */
  private static getErrorCount(errorType: string): number {
    const current = this.errorLoopCounts.get(errorType) || 0;
    this.errorLoopCounts.set(errorType, current + 1);
    return current + 1;
  }

  /**
   * Reset error count for a specific error type
   */
  private static resetErrorCount(errorType: string): void {
    this.errorLoopCounts.delete(errorType);
  }

  static watchForErrors(): void {
    if (!isClient()) return;

    window.addEventListener("error", async (event) => {
      if (this.isLikelyCacheError(event.error)) {
        const errorType = "cache-error";
        const errorCount = this.getErrorCount(errorType);
        
        if (errorCount > this.MAX_ERROR_LOOPS) {
          console.warn("Too many cache error reloads, stopping to prevent infinite loop");
          return;
        }
        
        console.log(`Cache-related error detected (attempt ${errorCount}), clearing caches...`);
        await this.clearAllCaches();
        
        // Reset error count after successful cache clear
        setTimeout(() => {
          this.resetErrorCount(errorType);
        }, 30000); // Reset after 30 seconds
        
        this.safeReload("cache-error");
      }
    });

    window.addEventListener("unhandledrejection", async (event) => {
      if (event.reason && this.isLikelyCacheError(event.reason)) {
        const errorType = "chunk-error";
        const errorCount = this.getErrorCount(errorType);
        
        if (errorCount > this.MAX_ERROR_LOOPS) {
          console.warn("Too many chunk load error reloads, stopping to prevent infinite loop");
          return;
        }
        
        console.log(`Chunk load error detected (attempt ${errorCount}), clearing caches...`);
        await this.clearAllCaches();
        
        // Reset error count after successful cache clear
        setTimeout(() => {
          this.resetErrorCount(errorType);
        }, 30000); // Reset after 30 seconds
        
        this.safeReload("chunk-error");
      }
    });
  }

  private static isLikelyCacheError(error: any): boolean {
    if (!error) {
      return false;
    }

    // Ignore specific third-party script errors that are not cache-related
    const thirdPartyErrorPatterns = [
      /Cannot set properties of undefined \(setting '_env'\)/i, // TikTok pixel error
    ];

    const errorMessage =
      typeof error === "string" ? error : error.message || error.toString();

    if (typeof errorMessage !== "string") {
      return false;
    }

    if (thirdPartyErrorPatterns.some((pattern) => pattern.test(errorMessage))) {
      console.warn("Ignoring known third-party script error:", errorMessage);
      return false; // Not a cache error, so we don't clear the cache
    }

    const cacheErrorPatterns = [
      /ChunkLoadError/i,
      /Loading chunk .* failed/i,
      /Loading CSS chunk .* failed/i,
      /Cannot find module/i,
      /Failed to fetch dynamically imported module/i,
      /Network error/i,
    ];

    return cacheErrorPatterns.some((pattern) => pattern.test(errorMessage));
  }

  static async diagnoseCache(): Promise<DiagnosisResult> {
    if (!isClient()) return this.getBasicDiagnosis();

    const browser = this.getBrowserInfo();

    try {
      let storedDeployId: string | null = null;
      const storedResult = SafeStorage.getItem(this.DEPLOY_ID_KEY);
      if (storedResult.success) {
        storedDeployId = storedResult.data || null;
      } else {
        console.warn("Could not access localStorage to get stored deployment ID.");
      }

      const diagnosis: DiagnosisResult = {
        deploymentId: await this.getDeploymentId(),
        storedDeploymentId: storedDeployId,
        cacheAvailable: "caches" in window,
        serviceWorkerAvailable: "serviceWorker" in navigator,
        protectedKeys: this.config.protectedKeys || [],
        browser,
        localStorage: {
          size: this.getLocalStorageSize(),
          protectedItemsPresent: this.getProtectedItemsStatus(),
        },
      };

      if (!browser.isSafari) {
        await this.addFullDiagnosis(diagnosis);
      }

      return diagnosis;
    } catch (error) {
      return {
        ...this.getBasicDiagnosis(),
        error: {
          hasError: true,
          message: error instanceof Error ? error.message : "Unknown error",
        },
      };
    }
  }

  private static getBasicDiagnosis(): DiagnosisResult {
    return {
      deploymentId: Date.now().toString(),
      storedDeploymentId: null,
      cacheAvailable: false,
      serviceWorkerAvailable: false,
      protectedKeys: this.config.protectedKeys || [],
      localStorage: {
        size: 0,
        protectedItemsPresent: {},
      },
    };
  }

  private static getLocalStorageSize(): number {
    try {
      return Object.keys(localStorage).length;
    } catch (e) {
      console.warn("Could not access localStorage to get size");
      return 0;
    }
  }

  private static getProtectedItemsStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {};
    const protectedKeys = this.config.protectedKeys || [];

    protectedKeys.forEach((key) => {
      const result = SafeStorage.getItem(key);
      if (result.success) {
        status[key] = result.data !== null && result.data !== undefined;
      } else {
        status[key] = false;
        console.warn(`Could not access localStorage to check protected key: ${key}`);
      }
    });

    return status;
  }

  private static async addFullDiagnosis(
    diagnosis: DiagnosisResult
  ): Promise<void> {
    // Add detailed diagnosis for non-Safari browsers
    try {
      if (diagnosis.cacheAvailable) {
        const keys = await caches.keys();
        const contents: Record<string, string[]> = {};
        for (const key of keys) {
          try {
            const cache = await caches.open(key);
            const requests = await cache.keys();
            contents[key] = requests.map((req) => req.url);
          } catch (e) {
            console.warn(`Failed to get cache contents for ${key}:`, e);
            contents[key] = [];
          }
        }
        diagnosis.caches = { keys, contents };
      }

      if (window.indexedDB) {
        try {
          // Check if databases() method is available
          if (typeof window.indexedDB.databases === 'function') {
            const databases = await window.indexedDB.databases();
            diagnosis.indexedDB = {
              databases: databases.map((db) => db.name || "unnamed"),
              status: "available",
            };
          } else {
            diagnosis.indexedDB = {
              databases: [],
              status: "databases() method not available",
            };
          }
        } catch (e) {
          diagnosis.indexedDB = {
            databases: [],
            status: `error: ${e instanceof Error ? e.message : 'unknown'}`,
          };
        }
      }

      if ("performance" in window) {
        try {
          diagnosis.performance = {
            resourceCount: performance.getEntriesByType("resource").length,
            navigationTiming: performance.getEntriesByType("navigation")[0],
          };

          // @ts-ignore
          if (performance.memory) {
            diagnosis.performance.memory = {
              // @ts-ignore
              jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
              // @ts-ignore
              totalJSHeapSize: performance.memory.totalJSHeapSize,
              // @ts-ignore
              usedJSHeapSize: performance.memory.usedJSHeapSize,
            };
          }
        } catch (e) {
          console.warn("Failed to get performance data:", e);
        }
      }
    } catch (e) {
      console.warn("Failed to add full diagnosis:", e);
    }
  }
}
