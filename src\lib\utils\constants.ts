export const AUTH_STATUS = {
  LOADING: "loading",
  AUTHENTICATED: "authenticated",
  UNAUTHENTICATED: "unauthenticated",
};

export const SHOW_ALLOWED_NAV_DETAILS = {
  SEARCH: "search",
  CART: "cart",
} as const;

export const modificationTypeIcons = {
  Exterior: "/ic_exterior.svg",
  Power: "/ic_power.svg",
  Interior: "/ic_interior.svg",
  "Wheels/Suspension": "/ic_wheels.svg",
};

export const TOASTS = {
  ERROR: "An error occurred",
  SUCCESS: "Success",
  VEHICLE_ADDED: "Vehicle added successfully",
  MODIFICATION_ADDED: "Modification added successfully",
  VEHICLE_UPDATED: "Vehicle updated successfully",
  VEHICLE_DELETED: "Vehicle deleted successfully",
  MODIFICATION_UPDATED: "Modification updated successfully",
  MODIFICATION_DELETED: "Modification deleted successfully",
  PASSWORD_RESET_SUCCESS: "Password reset successfully",
  IMAGE_NOT_VALID: "Please upload a valid image file (jpg, jpeg, png, webp, heic, heif).",
  HEIC_CONVERSION_SUCCESS: "HEIC/HEIF image converted to JPEG successfully",
  HEIC_CONVERSION_ERROR: "Failed to convert HEIC/HEIF image. Please try a different format.",
  USER_PROFILE_UPDATED: "User profile updated successfully",
  PASSWORD_UPDATED: "Password updated successfully",
  PROFILE_PIC_UPDATED: "Profile picture updated successfully",
  INVITE_ACCEPTED: "You have successfully joined the organization",
  INVITE_DECLINED: "You have declined the invite",
  ORG_CREATED: "Organization created successfully",
  ORG_ERROR: "Failed to create organization. Please try again.",
  ORG_FOLLOWED: "You are now following this organization",
  ORG_UNFOLLOWED: "You have unfollowed this organization",
  ADDRESS_ADDED: "You have added a address",
  ADDRESS_UPDATED: "You have updated a address",
  ADDRESS_DELETED: "You have deleted a address",
  VEHICLE_IMAGE_ERROR: "Please upload at least 2 images (cover photo and at least one secondary image)",
  DISCOUNT_ERROR: "Error applying discount",
  DISCOUNT_APPLIED: "Discount applied",
  DISCOUNT_USAGE_LIMIT_REACHED: "Voucher code usage limit exceeded",
  ORG_NAME_DUPLICATE: "User with the same name already has an organization. Go in the settings to change the name to create a new organization.",
  ADDRESS_SET_DEFAULT: "Address set as default successfully",
  ADDRESS_SET_DEFAULT_ERROR: "Error setting default address",
  NOT_APPROVED: "You are not approved yet.",
};

export const settingsSideBarLinks = [
  { href: "/profile/settings", label: "Account" },
  { href: "/profile/settings/orders", label: "Orders" },
  { href: "/profile/settings/dashboard-invites", label: "Dashboard Invites" },
];

export const TICKET_LIST_VARIANTS = {
  TABLE: "table",
  CARD: "card",
};

export const EDIT_STATUS = {
  ABOUT: "ABOUT",
  PROFILE_PICTURE: "PROFILE_PICTURE",
  COVER_PHOTO: "COVER_PHOTO",
  DEFAULT: "DEFAULT",
};

// home page compare table data
export const features = [
  "Ability to fight chargebacks",
  "Direct messaging",
  "Vendor submission tickets",
  "VIP list",
  "Email marketing",
  "Staff accounts",
  "Host calendar",
  "Awards",
  "Payout scheduling",
  "Mobile app event management",
  "Drive share",
];

export const platforms = [
  { name: "Eventbrite", features: [1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0] },
  { name: "The Foat", features: [0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0] },
  { name: "AutoLNK", features: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1] },
];

export const PAGE_TITLES = {
  blog: "Blog posts",
  updates: "Updates",
  default: "All posts",
} as const;

export const CART_STORAGE_KEYS = {
  CART_TOKEN: "cartToken",
  USER: "user",
  CART_VARIANTS: "cartVariants",
} as const;

export const CART_HEADERS = {
  X_CART_TOKEN: "X-CART-TOKEN",
} as const;

export const SOCIAL_LINKS = {
  FACEBOOK: "https://www.facebook.com/people/Autolnk/**************/",
  TWITTER: "https://x.com/autolnkusa?s=21&t=v4hWcIZyTRp3brLbObj-Cw",
  INSTAGRAM:
    "https://www.instagram.com/autolnkusa?igsh=NTc4MTIwNjQ2YQ%3D%3D&utm_source=qr",
  LINKEDIN: "https://www.linkedin.com/company/autolnk/",
};

export const FOOTER_LINKS = {
  PRIVACY_POLICY: "/privacy-policy",
  TERMS_OF_SERVICE: "/terms-of-service",
  SALES_AND_REFUNDS: "/sales-and-refunds",
};

export const DIRECT_ORDER_CREATION_SOURCE = 'main'

export const LINE_ITEM_TYPES = {
  EVENT_TICKET: "event_ticket",
  PRODUCT: "product",
};

export const ORDER_STATUS = {
  FULFILLED: "fulfilled",
  UNFULFILLED: "unfulfilled",
  VOIDED: "voided",
  CANCELLED: "cancelled",
};

export const CART_ERRORS = {
  INVALID_CART_TOKEN: "['Invalid cart token or cart has been deleted']",
  PARSING_ERROR: "PARSING_ERROR",
};

export const RESTRICTION_GIF = "https://media1.tenor.com/m/1Eow5SSMxo0AAAAd/benjammins-block-button.gif"

export const RESTRICTION_REASON = {
  BLACKLISTED: "Chargeback filed",
  RATE_LIMIT_EXCEEDED: "Exceeded API call limit",
};

// Auth and Token Management Constants
export const COOKIE_NAMES = {
  REFRESH_TOKEN: 'refreshToken',
  CSRF_TOKEN: 'csrfToken',
  RESTRICTED: 'restricted',
  RESTRICTION_CODE: 'restriction_code',
  RESTRICTION_DETAIL: 'restriction_detail',
  RESTRICTION_REASON: 'restriction_reason',
} as const;

export const TOKEN_REFRESH_ERRORS = {
  INVALID_REFRESH_TOKEN: 'InvalidRefreshToken',
  REFRESH_API_CALL_FAILED: 'RefreshApiCallFailed',
  MISSING_TOKENS: 'MissingTokens',
  CONFIGURATION_ERROR: 'ConfigurationError',
  REFRESH_EXCEPTION: 'RefreshException',
  REFRESH_FAILED_SERVER_ACTION: 'REFRESH_FAILED_SERVER_ACTION',
} as const;

export const TOKEN_ERROR_CODES = {
  TOKEN_NOT_VALID: 'token_not_valid',
  SIGNATURE_HAS_EXPIRED: 'signature_has_expired',
  SIGNATURE_EXPIRED: 'signature_has_expired', // WebSocket variant
} as const;

export const AUTH_ERROR_MESSAGES = {
  SEND_MESSAGE_ERROR: 'Error: Could not send message. Please try again.',
  AUTH_ERROR: 'Authentication error. Please log in again.',
  TOKEN_REFRESH_ERROR: 'Failed to refresh token. Please log in again.',
  NO_AUTH_TOKEN: 'No authentication token available',
  SESSION_EXPIRED: 'Session expired. Refreshing token...',
  CONNECTION_ERROR: 'Connection error. Attempting to reconnect...',
  CONNECTION_LOST: 'Connection lost. Attempting to reconnect...',
  MISSING_TOKENS: 'Refresh token or CSRF token is missing for server action.',
  CONFIG_ERROR: 'API URL is not configured.',
  TOKEN_REFRESH_FAILED: 'Token refresh failed with non-JSON response.',
  INVALID_TOKEN: 'Invalid token',
  UNAUTHORIZED: 'unauthorized',
} as const;

export const HTTP_STATUS = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  OK: 200,
} as const;

export const WEBSOCKET_CODES = {
  TOKEN_EXPIRED: 4001,
} as const;

export const WEBSOCKET_REASONS = {
  SIGNATURE_EXPIRED: 'signature_has_expired',
} as const;


export const CALICREAMING_ORGANIZER_ID = "a456995c-5b92-438a-96a1-f2e6ff64f60e";
