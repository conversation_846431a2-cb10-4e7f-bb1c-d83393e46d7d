import Cookies from 'js-cookie';
import { COOKIE_NAMES } from '@/lib/utils/constants';

interface CookieOptions {
  expires?: number;
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  httpOnly?: boolean;
}

type SameSiteType = 'strict' | 'lax' | 'none';

const getCookieDomain = (currentDomain: string) => {
  if (typeof window === 'undefined') return undefined;
  if (currentDomain === 'localhost' || currentDomain === '127.0.0.1' || currentDomain.includes('localhost')) {
    return undefined;
  }
  try {
    const apiUrl = new URL(process.env.NEXT_PUBLIC_API_URL || '');
    // Only return domain if it's different from current domain and not localhost
    const apiDomain = apiUrl.hostname;
    if (apiDomain !== currentDomain && !apiDomain.includes('localhost')) {
      return apiDomain;
    }
    return undefined;
  } catch (e) {
    console.warn('Failed to parse API URL for cookie domain:', e);
    return undefined;
  }
};

const DEFAULT_COOKIE_OPTIONS = {
  path: '/',
  secure: process.env.NODE_ENV === 'production',
  sameSite: (process.env.NODE_ENV === 'production' ? 'none' : 'lax') as SameSiteType,
};

const REFRESH_TOKEN_OPTIONS = {
  path: '/',
  secure: process.env.NODE_ENV === 'production',
  sameSite: (process.env.NODE_ENV === 'production' ? 'none' : 'lax') as SameSiteType,
  expires: 7,
};

export const setCookie = (name: string, value: string | undefined | null, options: CookieOptions = {}) => {
  if (value === null || value === undefined) {
    console.warn(`Attempted to set cookie ${name} with null/undefined value`);
    return;
  }

  const currentDomain = typeof window !== 'undefined' ? window.location.hostname : '';
  const isDevelopment = currentDomain === 'localhost' || currentDomain === '127.0.0.1' || currentDomain.includes('localhost');

  const baseOptions = name === COOKIE_NAMES.REFRESH_TOKEN ? REFRESH_TOKEN_OPTIONS : DEFAULT_COOKIE_OPTIONS;

  const cookieOptions = { 
    ...baseOptions,
    domain: getCookieDomain(currentDomain),
    secure: !isDevelopment && process.env.NODE_ENV === 'production',
    sameSite: (isDevelopment ? 'lax' : 'none') as SameSiteType,
    ...options,
  };

  if (options.expires !== undefined) {
      cookieOptions.expires = options.expires;
  }
  
  try {
    Cookies.set(name, value, cookieOptions);
    if (process.env.NODE_ENV === 'development') {
      console.debug(`Cookie ${name} set with options:`, cookieOptions);
    }
  } catch (error) {
    console.error(`Failed to set cookie ${name}:`, error);
  }
};

export const getCookie = (name: string) => {
  try {
    return Cookies.get(name);
  } catch (error) {
    console.error(`Failed to get cookie ${name}:`, error);
    return undefined;
  }
};

export const removeCookie = (name: string, options: CookieOptions = {}) => {
  const currentDomain = typeof window !== 'undefined' ? window.location.hostname : '';
  const cookieOptions = { 
    ...DEFAULT_COOKIE_OPTIONS, 
    path: '/',
    domain: getCookieDomain(currentDomain),
    ...options,
  };
  
  try {
    Cookies.remove(name, cookieOptions);
    if (process.env.NODE_ENV === 'development') {
      console.debug(`Cookie ${name} removed with options:`, cookieOptions);
    }
  } catch (error) {
    console.error(`Failed to remove cookie ${name}:`, error);
  }
};

export const getCsrfToken = () => {
  return getCookie(COOKIE_NAMES.CSRF_TOKEN);
};

export const setCsrfToken = (token: string) => {
  setCookie(COOKIE_NAMES.CSRF_TOKEN, token, {
    ...DEFAULT_COOKIE_OPTIONS,
    httpOnly: true,
  });
};

export const removeCsrfToken = () => {
  removeCookie(COOKIE_NAMES.CSRF_TOKEN);
}; 