import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import updateLocale from "dayjs/plugin/updateLocale";

dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(updateLocale);

export const dateToLongString = (date: string, tz: string) => {
  return tz ? dayjs(date).tz(tz).format("ddd, MMM D, YYYY") : dayjs(date).format("ddd, MMM D, YYYY");
};
export const dateToFormattedString = (date: string, tz: string) => {
  return tz ? dayjs(date).tz(tz).format("ddd, MMM D") : dayjs(date).format("ddd, MMM D");
};

export const todayDate = () => {
  return dayjs().format("ddd, MMM D, YYYY");
};

export const formatDateTime = (date: string | Date): string => {
  return dayjs(date).format("ddd, MMM D: h:mm A");
};

export const getTimeDifference = (timestamp: number): string => {
  const now = dayjs();
  const givenTime = dayjs.unix(timestamp);
  const diff = now.diff(givenTime, "second");

  if (diff < 60) {
    return `${diff}s`;
  } else if (diff < 3600) {
    return `${Math.floor(diff / 60)}min`;
  } else if (diff < 86400) {
    return `${Math.floor(diff / 3600)}h`;
  } else {
    return `${Math.floor(diff / 86400)}d`;
  }
};

export const getTimeDifferenceFromISOString = (
  isoString: string,
  userTimezone?: string
): string => {

  const now = userTimezone 
    ? dayjs().tz(userTimezone) 
    : dayjs();
  
  const givenTime = userTimezone 
    ? dayjs(isoString).tz(userTimezone) 
    : dayjs(isoString);

  
  const diff = now.diff(givenTime, "second");

  if (diff < 60) {
    return `${diff}s`;
  } else if (diff < 3600) {
    return `${Math.floor(diff / 60)}m`;
  } else if (diff < 86400) {
    return `${Math.floor(diff / 3600)}h`;
  } else {
    return `${Math.floor(diff / 86400)}d`;
  }
};

export const formatIsoDate = (date: string) => {
  try {
    const formattedDate = new Date(date).toISOString().split("T")[0];
    return formattedDate;
  } catch (error) {
    console.error("Failed to Format Date");
    return null;
  }
};

export const getUtcTimeFromIsoDate = (isoString: string): string => {
  if (!isoString) {
    return "";
  }
  const date = new Date(isoString);
  let hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();

  // Determine AM/PM
  const ampm = hours >= 12 ? "pm" : "am";

  // Convert to 12-hour format
  hours = hours % 12;
  hours = hours ? hours : 12; // The hour '0' should be '12'

  // Format minutes to be two digits
  const minutesStr = minutes < 10 ? `0${minutes}` : minutes;

  return `${hours}:${minutesStr}${ampm}`;
};

export const getTimeOnlyFromIsoDate = (isoString: string, tz?: string): string => {
  if (!isoString) {
    return "";
  }
  
  const time = tz ? dayjs(isoString).tz(tz) : dayjs(isoString);
  return time.format("h:mma").toLowerCase();
};

export const isWithinDateRange = (fromDate: string, tillDate: string, tz: string = 'UTC'): boolean => {
  const now = dayjs().tz(tz);
  const availableFrom = dayjs(fromDate).tz(tz);
  const availableTill = dayjs(tillDate).tz(tz);
  
  return now.isAfter(availableFrom) && now.isBefore(availableTill);
};

export const isBeforeDate = (date: string, tz: string = 'UTC'): boolean => {
  const now = dayjs().tz(tz);
  const targetDate = dayjs(date).tz(tz);
  return now.isBefore(targetDate);
};

export const isAfterDate = (date: string, tz: string = 'UTC'): boolean => {
  const now = dayjs().tz(tz);
  const targetDate = dayjs(date).tz(tz);
  return now.isAfter(targetDate);
};

export const formatMonthDay = (date: string, tz: string = 'UTC'): { month: string; day: number; suffix: string } => {
  const targetDate = tz ? dayjs(date).tz(tz) : dayjs(date);
  const day = targetDate.date();
  const month = targetDate.format('MMMM');
  
  // Add ordinal suffix to day
  const ordinalSuffix = (day: number): string => {
    if (day > 3 && day < 21) return 'th';
    switch (day % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  };

  return {
    month,
    day,
    suffix: ordinalSuffix(day)
  };
};

/**
 * Calculate the time remaining until a scheduled time
 * @param scheduledAt - The scheduled time in ISO format
 * @param timezone - The timezone to use for the calculation
 * @returns An object containing the time remaining, or null if the scheduled time has passed
 */

export const calculateTimeRemaining = (scheduledAt: string, timezone: string = 'UTC') => {
  if (!scheduledAt) return null

  try {
    // Get current time in the specified timezone
    const now = dayjs().tz(timezone)
    
    // Parse the scheduled time and convert to the specified timezone
    const scheduledTime = dayjs.utc(scheduledAt).tz(timezone)
    
    // Calculate difference in milliseconds
    const timeDiff = scheduledTime.diff(now)
    
    // If the scheduled time has passed, return null
    if (timeDiff <= 0) {
      return null
    }
    
    // Calculate hours and minutes remaining
    const totalMinutes = Math.floor(timeDiff / (1000 * 60))
    const hours = Math.floor(totalMinutes / 60)
    const minutes = totalMinutes % 60
    
    return {
      hours,
      minutes,
      isWithin24Hours: hours < 24,
      totalMinutes,
      scheduledTime: scheduledTime.toISOString(),
      currentTime: now.toISOString()
    }
  } catch (err) {
    console.error('Error calculating time remaining:', err)
    return null
  }
}

