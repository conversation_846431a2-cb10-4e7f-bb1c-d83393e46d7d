import { TOASTS } from "./constants";
import toast from "react-hot-toast";

interface ErrorResponse {
  data?: {
    [key: string]: string | string[];
  };
}

// New interface for API errors with the structure: { type, code, detail, attr }
interface ApiErrorResponse {
  type?: string;
  code?: string;
  detail?: string;
  attr?: any;
  list?: Array<{
    type?: string;
    code?: string;
    detail?: string;
    attr?: string;
  }>;
}

// Helper function to format error message with attr prefix for address validation errors
function formatErrorMessage(detail: string, attr?: string): string {
  if (detail.includes("This value is not valid for the address.") && attr) {
    return `${attr}: ${detail}`;
  }
  return detail;
}

// this function can be used to get the error message from the error object returned from our APIs
export function getErrorMessage(error: unknown, fallbackErrorMessage?: string): string {
  try {
    if (typeof error === "string") {
      return error;
    }

    const err = error as ErrorResponse;

    if (err?.data && typeof err.data === "object") {
      const messages = Object.values(err.data).flatMap((value) =>
        Array.isArray(value) ? value : [value]
      );

      return messages.filter((msg) => typeof msg === "string").join(", ");
    }

    if (error instanceof Error) {
      return error.message;
    }

    return fallbackErrorMessage || TOASTS?.ERROR;
  } catch (error) {
    console.error("Error details:", error);
    return TOASTS?.ERROR;
  }
}

// New function to handle API errors with the new structure and show appropriate toasts
export function handleApiError(error: unknown, fallbackErrorMessage?: string): void {
  try {
    if (typeof error === "string") {
      toast.error(error);
      return;
    }

    // Handle the new API error structure
    if (error && typeof error === "object" && 'data' in error) {
      const apiError = error.data as ApiErrorResponse;
      
      // Handle multiple errors
      if (apiError?.type === "multiple" && apiError?.list && Array.isArray(apiError.list)) {
        // Show each error in the list
        apiError.list.forEach((individualError, index) => {
          if (individualError.detail) {
            // Add a small delay between toasts to make them readable
            setTimeout(() => {
              const formattedMessage = formatErrorMessage(individualError.detail!, individualError.attr);
              toast.error(formattedMessage);
            }, index * 100);
          }
        });
        
        // Check if any of the individual errors is about order already fulfilled
        const hasOrderFulfilledError = apiError.list.some(
          (individualError) => individualError.detail === "Order is already fulfilled."
        );
        
        if (hasOrderFulfilledError) {
          setTimeout(() => {
            if (typeof window !== "undefined") {
              window.location.href = "/";
            }
          }, 1000);
        }
        
        return;
      }
      
      // Handle single validation error
      if (apiError?.type === "validation_error" && apiError?.detail) {
        // Show the specific error detail from the API with proper formatting
        const formattedMessage = formatErrorMessage(apiError.detail, apiError.attr);
        toast.error(formattedMessage);
        
        // Check if the error is about order already fulfilled and redirect to home
        if (apiError.detail === "Order is already fulfilled.") {
          setTimeout(() => {
            if (typeof window !== "undefined") {
              window.location.href = "/";
            }
          }, 1000);
        }
        
        return;
      }
    }

    // Fallback to existing error handling
    const errorMessage = getErrorMessage(error, fallbackErrorMessage);
    toast.error(errorMessage);
  } catch (err) {
    console.error("Error handling API error:", err);
    toast.error(fallbackErrorMessage || TOASTS?.ERROR);
  }
}

// Enhanced function specifically for handling API errors with optional router for better navigation
export function handleApiErrorWithRouter(
  error: unknown, 
  router?: { push: (path: string) => void }, 
  fallbackErrorMessage?: string
): void {
  try {
    if (typeof error === "string") {
      toast.error(error);
      return;
    }

    // Handle the new API error structure
    if (error && typeof error === "object" && 'data' in error) {
      const apiError = error.data as ApiErrorResponse;
      
      // Handle multiple errors
      if (apiError?.type === "multiple" && apiError?.list && Array.isArray(apiError.list)) {
        // Show each error in the list
        apiError.list.forEach((individualError, index) => {
          if (individualError.detail) {
            // Add a small delay between toasts to make them readable
            setTimeout(() => {
              const formattedMessage = formatErrorMessage(individualError.detail!, individualError.attr);
              toast.error(formattedMessage);
            }, index * 100);
          }
        });
        
        // Check if any of the individual errors is about order already fulfilled
        const hasOrderFulfilledError = apiError.list.some(
          (individualError) => individualError.detail === "Order is already fulfilled."
        );
        
        if (hasOrderFulfilledError) {
          setTimeout(() => {
            if (router) {
              router.push("/");
            } else if (typeof window !== "undefined") {
              window.location.href = "/";
            }
          }, 1000);
        }
        
        return;
      }
      
      // Handle single validation error
      if (apiError?.type === "validation_error" && apiError?.detail) {
        // Show the specific error detail from the API with proper formatting
        const formattedMessage = formatErrorMessage(apiError.detail, apiError.attr);
        toast.error(formattedMessage);
        
        // Check if the error is about order already fulfilled and redirect to home
        if (apiError.detail === "Order is already fulfilled.") {
          setTimeout(() => {
            if (router) {
              router.push("/");
            } else if (typeof window !== "undefined") {
              window.location.href = "/";
            }
          }, 1000);
        }
        
        return;
      }
    }

    // Fallback to existing error handling
    const errorMessage = getErrorMessage(error, fallbackErrorMessage);
    toast.error(errorMessage);
  } catch (err) {
    console.error("Error handling API error:", err);
    toast.error(fallbackErrorMessage || TOASTS?.ERROR);
  }
}

// Alternative function for handling multiple errors as a single consolidated message
export function handleApiErrorConsolidated(
  error: unknown, 
  router?: { push: (path: string) => void }, 
  fallbackErrorMessage?: string
): void {
  try {
    if (typeof error === "string") {
      toast.error(error);
      return;
    }

    // Handle the new API error structure
    if (error && typeof error === "object" && 'data' in error) {
      const apiError = error.data as ApiErrorResponse;
      
      // Handle multiple errors - show as a single consolidated message
      if (apiError?.type === "multiple" && apiError?.list && Array.isArray(apiError.list)) {
        const errorMessages = apiError.list
          .filter((individualError) => individualError.detail)
          .map((individualError) => formatErrorMessage(individualError.detail!, individualError.attr));
        
        if (errorMessages.length > 0) {
          const consolidatedMessage = errorMessages.length === 1 
            ? errorMessages[0]
            : `Multiple issues found: ${errorMessages.join(', ')}`;
          
          toast.error(consolidatedMessage);
        }
        
        // Check if any of the individual errors is about order already fulfilled
        const hasOrderFulfilledError = apiError.list.some(
          (individualError) => individualError.detail === "Order is already fulfilled."
        );
        
        if (hasOrderFulfilledError) {
          setTimeout(() => {
            if (router) {
              router.push("/");
            } else if (typeof window !== "undefined") {
              window.location.href = "/";
            }
          }, 1000);
        }
        
        return;
      }
      
      // Handle single validation error
      if (apiError?.type === "validation_error" && apiError?.detail) {
        const formattedMessage = formatErrorMessage(apiError.detail, apiError.attr);
        toast.error(formattedMessage);
        
        if (apiError.detail === "Order is already fulfilled.") {
          setTimeout(() => {
            if (router) {
              router.push("/");
            } else if (typeof window !== "undefined") {
              window.location.href = "/";
            }
          }, 1000);
        }
        
        return;
      }
    }

    // Fallback to existing error handling
    const errorMessage = getErrorMessage(error, fallbackErrorMessage);
    toast.error(errorMessage);
  } catch (err) {
    console.error("Error handling API error:", err);
    toast.error(fallbackErrorMessage || TOASTS?.ERROR);
  }
}