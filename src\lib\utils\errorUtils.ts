import { TOASTS } from "./constants";

interface ErrorResponse {
  data?: {
    [key: string]: string | string[];
  };
}

// this function can be used to get the error message from the error object returned from our APIs
export function getErrorMessage(error: unknown, fallbackErrorMessage?: string): string {
  try {
    if (typeof error === "string") {
      return error;
    }

    const err = error as ErrorResponse;

    if (err?.data && typeof err.data === "object") {
      const messages = Object.values(err.data).flatMap((value) =>
        Array.isArray(value) ? value : [value]
      );

      return messages.filter((msg) => typeof msg === "string").join(", ");
    }

    if (error instanceof Error) {
      return error.message;
    }

    return fallbackErrorMessage || TOASTS?.ERROR;
  } catch (error) {
    console.error("Error details:", error);
    return TOASTS?.ERROR;
  }
}
