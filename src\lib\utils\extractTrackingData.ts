/**
 * Extracts event and ticket data from cart data for Meta Pixel tracking
 * 
 * This function parses complex cart data and extracts the relevant information
 * needed for Meta Pixel tracking events like checkout and purchase.
 */
export interface TrackingEventData {
  id: string;
  title: string;
  date: string;
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

export interface TrackingTicketData {
  id: string;
  eventId: string;
  type: "free" | "paid";
  name: string;
  price: number;
  quantity?: number;
}

export interface TrackingData {
  event: TrackingEventData;
  tickets: TrackingTicketData[];
}

/**
 * Extract event and ticket information from cart data for tracking
 */
export const extractTrackingData = (cartData: any): TrackingData | null => {
  if (!cartData?.lines) return null;
  
  try {
    // Find first event line to get event details
    const eventLine = cartData.lines.find(
        (line: any) => line.type === "event_ticket"
      );
    
      if (!eventLine) return null;
  
      const event = eventLine?.eventDetails;
    
    // Get all ticket items
    const ticketItems = cartData.lines
    .filter((line: any) => line.type === "event_ticket")
    .map((line: any) => ({
      id: line.id || line.variantId,
      eventId: event?.eventId,
      type: line.totalPrice > 0 ? "paid" : "free",
      name: event?.eventName,
      price: parseFloat(line.totalPrice || 0),
      quantity: line.quantity || 1,
    }));

  if (ticketItems.length === 0) return null;

  return {
    event: {
      id: event?.eventId,
      title: event?.eventName,
      date: new Date().toISOString(),
      pixels: cartData?.organization?.pixels,
    },
    tickets: ticketItems,
  };
  } catch (error) {
    console.error("Error extracting tracking data:", error);
    return null;
  }
}; 
