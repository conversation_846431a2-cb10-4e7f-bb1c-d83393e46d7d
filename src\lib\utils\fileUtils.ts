/**
 * Sanitizes a filename by removing special characters, spaces, and converting to lowercase
 * @param file The file object to sanitize
 * @param defaultName The default name to use if the sanitized name is empty (defaults to 'file')
 * @returns A sanitized filename with the original extension
 */
export const sanitizeFilename = (file: File, defaultName: string = 'file'): string => {
  // Get the file extension from the original file
  const extension = file.name.split('.').pop()?.toLowerCase() || '';
  // Get the base name (everything before the last dot)
  const baseName = file.name.slice(0, file.name.lastIndexOf('.'));
  // Remove all special characters, spaces, and underscores, then convert to lowercase
  const sanitizedBaseName = baseName
    .replace(/[^a-zA-Z0-9]/g, '') // Remove all special characters
    .toLowerCase(); // Convert to lowercase for consistency
  
  // If the base name is empty after sanitization, use the default name
  const finalBaseName = sanitizedBaseName || defaultName;
  
  return `${finalBaseName}.${extension}`;
}; 