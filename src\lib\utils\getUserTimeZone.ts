import { auth } from "@/auth.config";

export const getUserTimeZone = async () => {
    try {
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
        console.error('Error getting user timezone:', error);
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
    }
};

export const getUserTimeZoneWithSession = async (session?: any) => {
    try {
        if (!session) {
            session = await auth();
        }
        const userTimezone = session?.user?.userTimezone;
        if (userTimezone) return userTimezone;
        // Fallback to system timezone
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
        console.error('Error getting user timezone:', error);
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
    }
};
