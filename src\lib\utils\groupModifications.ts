export const groupVehicleModifications = (
  vehicleDetail: IVehicle,
  modificationTypes: any[]
) => {
  try {
    const groupedModifications: {
      typeId: string;
      typeName: string;
      count: number;
      totalPoints: number;
      modifications: { [categoryName: string]: IVehicleModification[] };
    }[] = modificationTypes?.map((type) => ({
      typeId: type?.id,
      typeName: type?.name,
      count: 0,
      totalPoints: 0,
      modifications: {},
    }));

    vehicleDetail?.modifications?.forEach((modification) => {
      const modificationTypeId: string = modification?.part?.category?.type?.id;
      const categoryName: string = modification?.part?.category?.name;

      const group = groupedModifications?.find(
        (g) => g?.typeId === modificationTypeId
      );

      if (group) {
        group.count += 1;
        group.totalPoints += modification?.points;

        // Group modifications by category name
        if (!group?.modifications[categoryName]) {
          group.modifications[categoryName] = [];
        }
        group.modifications[categoryName].push(modification);
      }
    });

    return groupedModifications;
  } catch (error) {
    console.error("Error in groupVehicleModifications: ", error);
    return [];
  }
};
