import { RestrictionError } from "@/lib/types/restriction";
import { RESTRICTION_REASON } from "./constants";

/**
 * Get the reason for the restriction
 * @param code
 * @returns string
 */

export const getRestrictionReason = (code: string): string => {
  switch (code) {
    case 'blacklisted':
      return RESTRICTION_REASON.BLACKLISTED;
    case 'rate_limit_exceeded':
      return RESTRICTION_REASON.RATE_LIMIT_EXCEEDED;
    default:
      return 'Access restricted';
  }
}; 

/**
 * Checks if an error response indicates a restriction
 */
export const isRestrictionError = (error: any): boolean => {
  if (!error) return false;

  const status = error.status || error?.error?.status || error?.response?.status;
  // Extract data from possible error structures
  const errorData = error?.data || error?.error?.data || error;
  
  // Check for specific restriction error format
  return (
    status === 403 &&
    errorData &&
    errorData?.type === 'authentication_error' &&
    (errorData?.code === 'blacklisted' || errorData?.code === 'rate_limit_exceeded')
  );
};

/**
 * Handles restriction by setting cookies and redirecting
 * Can be used directly with try/catch blocks for fetch calls
 */
export const handleRestriction = (error: any): void => {
  // If not a restriction error, do nothing
  if (!isRestrictionError(error)) return;
  
  const errorData = error?.data || error?.error?.data || error as RestrictionError;
  
  // Set cookies with restriction information
  document.cookie = `restricted=true; path=/`;
  document.cookie = `restriction_code=${errorData?.code}; path=/`;
  document.cookie = `restriction_reason=${encodeURIComponent(getRestrictionReason(errorData?.code))}; path=/`;
  document.cookie = `restriction_detail=${encodeURIComponent(errorData?.detail)}; path=/`;
  
  
  // Redirect to restricted page if not already there
  if (!window.location.pathname.startsWith('/restricted')) {
    window.location.href = '/restricted';
  }
};

/**
 * Wraps an API fetch call with restriction handling
 * Usage: const data = await fetchWithRestrictionHandling(() => fetch('/api/endpoint'));
 */
export async function fetchWithRestrictionHandling<T>(
  fetchFn: () => Promise<Response>
): Promise<T> {
  try {
    const response = await fetchFn();
    
    if (!response.ok) {
      const errorData = await response.json();
      
      if (isRestrictionError(errorData)) {
        handleRestriction(errorData);
        throw new Error('Access restricted');
      }
      
      throw errorData;
    }
    
    return await response.json() as T;
  } catch (error) {
    if (isRestrictionError(error)) {
      handleRestriction(error);
    }
    throw error;
  }
} 