import toast from 'react-hot-toast';
import { TOASTS } from './constants';

/**
 * Checks if a file is a HEIC/HEIF format
 * @param file - File to check
 * @returns boolean indicating if the file is HEIC/HEIF
 */
export const isHeicFile = (file: File): boolean => {
  if (!file) return false;
  
  const heicMimeTypes = [
    'image/heic',
    'image/heif',
    'image/heic-sequence',
    'image/heif-sequence'
  ];
  
  // Check MIME type
  if (file.type && heicMimeTypes.includes(file.type.toLowerCase())) {
    return true;
  }
  
  // Check file extension as fallback
  const fileName = file.name?.toLowerCase();
  if (!fileName) return false;
  
  return fileName.endsWith('.heic') || fileName.endsWith('.heif');
};

/**
 * Converts HEIC file to JPEG format
 * @param file - HEIC file to convert
 * @returns Promise<File> - Converted JPEG file
 */
export const convertHeicToJpeg = async (file: File): Promise<File> => {
  if (!file) {
    throw new Error('No file provided for HEIC conversion');
  }

  // Check if we're on the client side
  if (typeof window === 'undefined') {
    throw new Error('HEIC conversion is not supported on the server side');
  }

  try {
    // Dynamically import heic2any to avoid SSR issues
    const heic2any = (await import('heic2any')).default;
    
    // Convert HEIC to JPEG blob
    const convertedBlob = await heic2any({
      blob: file,
      toType: 'image/jpeg',
      quality: 0.95, // High quality for initial conversion
    }) as Blob;
    
    // Create a new File object with proper name and type
    const originalName = file.name?.replace(/\.(heic|heif)$/i, '') || 'converted-image';
    const convertedFile = new File(
      [convertedBlob], 
      `${originalName}.jpg`, 
      { 
        type: 'image/jpeg',
        lastModified: file.lastModified || Date.now()
      }
    );
    
    return convertedFile;
  } catch (error) {
    console.error('Error converting HEIC to JPEG:', error);
    throw new Error('Failed to convert HEIC image. Please try selecting a different image format.');
  }
};

/**
 * Processes an image file, converting HEIC to JPEG if necessary
 * @param file - File to process
 * @returns Promise<File> - Processed file (converted if HEIC, original if not)
 */
export const processImageFile = async (file: File): Promise<File> => {
  if (!file) {
    throw new Error('No file provided for processing');
  }

  // Skip HEIC processing on server side
  if (typeof window === 'undefined') {
    return file;
  }

  if (isHeicFile(file)) {
    console.log('HEIC file detected, converting to JPEG...');
    toast.loading('Converting HEIC image...', { id: 'heic-conversion' });
    
    try {
      const convertedFile = await convertHeicToJpeg(file);
      toast.success(TOASTS.HEIC_CONVERSION_SUCCESS, { id: 'heic-conversion' });
      return convertedFile;
    } catch (error) {
      toast.error(TOASTS.HEIC_CONVERSION_ERROR, { id: 'heic-conversion' });
      throw error;
    }
  }
  
  return file;
}; 