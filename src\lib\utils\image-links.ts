const BASE_URL = "https://dv2epwsej0xvt.cloudfront.net/static_assets";
export const IMAGE_LINKS = Object.freeze({
    APPLE_STORE: `${BASE_URL}/applestore.png`,
    DOWNLOAD_ON_APPSTORE: `${BASE_URL}/download-on-appstore.svg`,
    DOWNLOAD_ON_GOOGLE: `${BASE_URL}/download-on-google.png`,
    ABOUT_MAIN: `${BASE_URL}/about-main.png`,
    ABOUT_BANNER: `${BASE_URL}/about-banner.png`,
    SIGN_UP_SCREENSHOTS: `${BASE_URL}/sign-up.webp`,
    MO<PERSON>LE_APP_EVENT_VIEW: `${BASE_URL}/mobile-app-event-view.png`,
    BANNER: `${BASE_URL}/new_banner.webp`,
    HERO_SECTION: `${BASE_URL}/hero_section.webp`,
    CATEGORY_MEET: `${BASE_URL}/meet.webp`,
    CATEGORY_SHOW: `${BASE_URL}/shows.webp`,
    CATEGORY_DRIVES: `${BASE_URL}/drives.webp`,
    CATEGORY_MOTORSPORTS: `${BASE_URL}/motorsports.webp`,
    APPLE_WALLET: `${BASE_URL}/apple-wallet.svg`,
    LOGO_TRANSPARENT: `${BASE_URL}/logo-transparent.png`,
    VENDOR: `${BASE_URL}/vendor.png`,
    EMPTY_CART: `${BASE_URL}/empty-cart.png`,
    IMAGE_PLACEHOLDER: `${BASE_URL}/image-placeholder.png`,
    PLACEHOLDER_BIG: `${BASE_URL}/placeholder-big.webp`,
    DASHBOARD_BANNER: `${BASE_URL}/dashboard.png`,
    NO_IMG: `${BASE_URL}/no-img.webp`,
});
