export const compressImage = (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;
      
      img.onload = () => {
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;
        
        // Maintain aspect ratio while scaling down if needed
        const MAX_WIDTH = 1600; // Reduced from 2048 to help with size
        const MAX_HEIGHT = 1600;
        
        if (width > height) {
          if (width > MAX_WIDTH) {
            height = Math.round((height * MAX_WIDTH) / width);
            width = MAX_WIDTH;
          }
        } else {
          if (height > MAX_HEIGHT) {
            width = Math.round((width * MAX_HEIGHT) / height);
            height = MAX_HEIGHT;
          }
        }
        
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0, width, height);
        
        const TARGET_SIZE_IN_BYTES = 9.5 * 1024 * 1024; // 9.5MB
        
        // Start with high quality
        let quality = 1.0;
        const minQuality = 0.1;
        const qualityStep = 0.05;
        
        const compressIterative = () => {
          const dataUrl = canvas.toDataURL('image/jpeg', quality);
          const binaryString = atob(dataUrl.split(',')[1]);
          const bytes = new Uint8Array(binaryString.length);
          
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          
          const compressedFile = new File([bytes], file.name, { type: 'image/jpeg' });
          
          // If file is still too large and we can reduce quality further
          if (compressedFile.size > TARGET_SIZE_IN_BYTES && quality > minQuality) {
            quality = Math.max(quality - qualityStep, minQuality);
            compressIterative();
          } else {
            resolve(compressedFile);
          }
        };
        
        compressIterative();
      };
      
      img.onerror = (error) => {
        reject(error);
      };
    };
    
    reader.onerror = (error) => {
      reject(error);
    };
  });
}; 