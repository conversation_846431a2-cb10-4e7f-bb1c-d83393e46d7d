import { ISignupResponse } from "../types";

export const mapToSignupResponse = (response: any): ISignupResponse => {
  return {
    id: response?.user?.id,
    avatar: response?.user?.avatar,
    coverPhoto: response?.user?.cover_photo,
    dateJoined: response?.user?.date_joined,
    displayName: response?.user?.display_name,
    phone: response?.user?.phone,
    name: response?.user?.name || "", // Handle empty name
    isActive: response?.user?.is_active,
    isBot: response?.user?.is_bot,
    isPhoneVerified: response?.user?.is_phone_verified,
    userTimezone: response?.user?.user_timezone,
    username: response?.user?.username,
    isPasswordAutoset: response?.user?.is_password_autoset,
    lastLoginMedium: response?.user?.last_login_medium,
    email: response?.user?.email,
    firstName: response?.user?.first_name,
    lastName: response?.user?.last_name,
    profile: {
      id: "default-id", // Update with actual data if available
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      theme: {}, // Update with actual theme data if available
      isOnboarded: true, // Update based on your logic
      user: response?.user?.id,
    },
    accessToken: response?.accessToken,
    refreshToken: response?.refreshToken,
    accessTokenExpiration: response?.expires,
    refreshTokenExpiration: "", // Update with actual data if available
  };
};
