export const validateUSPhoneNumber = (phone: string): boolean => {
  if (!phone || typeof phone !== 'string') {
    return false;
  }

  const cleanPhone = phone.replace(/[^\d+]/g, '');
  
  const patterns = [
    /^\+1\d{10}$/,
    /^\d{10}$/,
  ];

  return patterns.some(pattern => pattern.test(cleanPhone));
};

export const hasMinimumUSPhoneLength = (phone: string): boolean => {
  if (!phone || typeof phone !== 'string') {
    return false;
  }

  const cleanPhone = phone.replace(/[^\d+]/g, '');
  
  if (cleanPhone.startsWith('+1')) {
    return cleanPhone.length >= 11;
  }
  
  return cleanPhone.length >= 10;
};

// New international phone validation functions
export const validateInternationalPhoneNumber = (phone: string, countryCode: string): boolean => {
  if (!phone || typeof phone !== 'string') {
    return false;
  }

  const cleanPhone = phone.replace(/[^\d+]/g, '');
  
  // Country-specific validation rules
  switch (countryCode) {
    case '1': // US/Canada - exactly 10 digits after country code
      return /^\+1\d{10}$/.test(cleanPhone);
    
    case '44': // UK - 10-11 digits after country code
      return /^\+44\d{10,11}$/.test(cleanPhone);
    
    case '33': // France - 9 digits after country code
      return /^\+33\d{9}$/.test(cleanPhone);
    
    case '49': // Germany - 10-12 digits after country code
      return /^\+49\d{10,12}$/.test(cleanPhone);
    
    case '81': // Japan - 10-11 digits after country code
      return /^\+81\d{10,11}$/.test(cleanPhone);
    
    case '86': // China - 11 digits after country code
      return /^\+86\d{11}$/.test(cleanPhone);
    
    case '91': // India - 10 digits after country code
      return /^\+91\d{10}$/.test(cleanPhone);
    
    case '61': // Australia - 9 digits after country code
      return /^\+61\d{9}$/.test(cleanPhone);
    
    case '7': // Russia - 10 digits after country code
      return /^\+7\d{10}$/.test(cleanPhone);
    
    case '55': // Brazil - 10-11 digits after country code
      return /^\+55\d{10,11}$/.test(cleanPhone);
    
    case '39': // Italy - 9-11 digits after country code
      return /^\+39\d{9,11}$/.test(cleanPhone);
    
    case '34': // Spain - 9 digits after country code
      return /^\+34\d{9}$/.test(cleanPhone);
    
    case '31': // Netherlands - 9 digits after country code
      return /^\+31\d{9}$/.test(cleanPhone);
    
    case '46': // Sweden - 8-9 digits after country code
      return /^\+46\d{8,9}$/.test(cleanPhone);
    
    case '47': // Norway - 8 digits after country code
      return /^\+47\d{8}$/.test(cleanPhone);
    
    case '45': // Denmark - 8 digits after country code
      return /^\+45\d{8}$/.test(cleanPhone);
    
    case '41': // Switzerland - 9 digits after country code
      return /^\+41\d{9}$/.test(cleanPhone);
    
    case '43': // Austria - 10-12 digits after country code
      return /^\+43\d{10,12}$/.test(cleanPhone);
    
    case '32': // Belgium - 9 digits after country code
      return /^\+32\d{9}$/.test(cleanPhone);
    
    case '30': // Greece - 10 digits after country code
      return /^\+30\d{10}$/.test(cleanPhone);
    
    default:
      // For other countries, use a more flexible but reasonable validation
      // Most international numbers are between 8-15 digits after country code
      const regex = new RegExp(`^\\+${countryCode}\\d{8,15}$`);
      return regex.test(cleanPhone);
  }
};

export const hasMinimumInternationalPhoneLength = (phone: string, countryCode: string): boolean => {
  if (!phone || typeof phone !== 'string') {
    return false;
  }

  const cleanPhone = phone.replace(/[^\d+]/g, '');
  const expectedPrefix = `+${countryCode}`;
  
  if (!cleanPhone.startsWith(expectedPrefix)) {
    return false;
  }
  
  // Country-specific minimum length requirements
  // Show "too short" until user is close to completing the number
  switch (countryCode) {
    case '1': // US/Canada - needs exactly 10 digits after +1
      return cleanPhone.length >= 10; // Show "too short" until at least 8 digits entered (+1 + 8 digits)
    
    case '44': // UK - needs at least 10 digits after +44
      return cleanPhone.length >= 11; // +44 + 8 digits minimum before switching to format error
    
    case '33': // France - needs exactly 9 digits after +33
      return cleanPhone.length >= 10; // +33 + 7 digits minimum
    
    case '49': // Germany - needs at least 10 digits after +49
      return cleanPhone.length >= 10; // +49 + 7 digits minimum
    
    case '81': // Japan - needs at least 10 digits after +81
      return cleanPhone.length >= 10; // +81 + 7 digits minimum
    
    case '86': // China - needs exactly 11 digits after +86
      return cleanPhone.length >= 11; // +86 + 8 digits minimum
    
    case '91': // India - needs exactly 10 digits after +91
      return cleanPhone.length >= 10; // +91 + 7 digits minimum
    
    case '61': // Australia - needs exactly 9 digits after +61
      return cleanPhone.length >= 9; // +61 + 6 digits minimum
    
    case '7': // Russia - needs exactly 10 digits after +7
      return cleanPhone.length >= 8; // +7 + 6 digits minimum
    
    case '55': // Brazil - needs at least 10 digits after +55
      return cleanPhone.length >= 10; // +55 + 7 digits minimum
    
    default:
      // For other countries, require at least 6 digits after country code before switching to format validation
      return cleanPhone.length >= expectedPrefix.length + 6;
  }
};

export const getPhoneValidationExample = (countryCode: string): string => {
  // Common examples for major countries
  const examples: Record<string, string> = {
    '1': '******-123-4567', // US/Canada
    '44': '+44-20-7946-0958', // UK
    '33': '+33-1-42-68-53-00', // France
    '49': '+49-30-12345678', // Germany
    '81': '+81-3-1234-5678', // Japan
    '86': '+86-138-0013-8000', // China
    '91': '+91-98765-43210', // India
    '61': '+61-2-9374-4000', // Australia
    '7': '******-123-45-67', // Russia
    '55': '+55-11-99999-9999', // Brazil
  };
  
  return examples[countryCode] || `+${countryCode}-************`;
};

export const formatUSPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  const cleanPhone = phone.replace(/[^\d+]/g, '');
  
  if (cleanPhone.startsWith('+1') && cleanPhone.length === 11) {
    const digits = cleanPhone.slice(2);
    return `+1 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (cleanPhone.length === 10) {
    return `(${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`;
  }
  
  return phone;
};

export const detectCountryCodeMismatch = (phone: string, expectedCountryCode: string): { 
  hasMismatch: boolean; 
  detectedCountryCode?: string;
  detectedCountryName?: string;
} => {
  if (!phone || typeof phone !== 'string') {
    return { hasMismatch: false };
  }

  const cleanPhone = phone.replace(/[^\d+]/g, '');
  
  // Check if phone starts with +
  if (!cleanPhone.startsWith('+')) {
    return { hasMismatch: false };
  }

  // Extract country code from phone number
  // Try common country codes first (1-4 digits)
  const commonCountryCodes: Record<string, string> = {
    '1': 'US/Canada',
    '44': 'United Kingdom',
    '33': 'France',
    '49': 'Germany',
    '81': 'Japan',
    '86': 'China',
    '91': 'India',
    '61': 'Australia',
    '7': 'Russia',
    '55': 'Brazil',
    '39': 'Italy',
    '34': 'Spain',
    '31': 'Netherlands',
    '46': 'Sweden',
    '47': 'Norway',
    '45': 'Denmark',
    '41': 'Switzerland',
    '43': 'Austria',
    '32': 'Belgium',
    '30': 'Greece',
    '351': 'Portugal',
    '353': 'Ireland',
    '358': 'Finland',
    '420': 'Czech Republic',
    '48': 'Poland',
    '36': 'Hungary',
    '90': 'Turkey',
    '82': 'South Korea',
    '65': 'Singapore',
    '60': 'Malaysia',
    '66': 'Thailand',
    '62': 'Indonesia',
    '63': 'Philippines',
    '84': 'Vietnam',
    '52': 'Mexico',
    '54': 'Argentina',
    '56': 'Chile',
    '57': 'Colombia',
    '51': 'Peru',
    '58': 'Venezuela',
    '20': 'Egypt',
    '27': 'South Africa',
    '234': 'Nigeria',
    '254': 'Kenya',
    '233': 'Ghana',
  };

  // Try to match country codes (from longest to shortest to avoid conflicts)
  const sortedCodes = Object.keys(commonCountryCodes).sort((a, b) => b.length - a.length);
  
  for (const code of sortedCodes) {
    if (cleanPhone.startsWith(`+${code}`)) {
      if (code !== expectedCountryCode) {
        return {
          hasMismatch: true,
          detectedCountryCode: code,
          detectedCountryName: commonCountryCodes[code]
        };
      }
      break;
    }
  }

  return { hasMismatch: false };
};
