// lib/tracking/facebook.ts
"use client";

// Early return for development mode
const isDevelopment = process.env.NODE_ENV !== "production";

interface UserData {
  id?: string;
  email?: string;
  name?: string;
  isAnonymous: boolean;
}

interface EventData {
  id: string;
  title: string;
  date: string;
  organizerPixelId?: string; // Meta / Facebook pixel
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  };
}

interface TicketData {
  id: string;
  eventId: string;
  type: "free" | "paid";
  name: string;
  price: number;
  quantity?: number;
}

interface TrackingParams {
  [key: string]: any;
}

// Keep track of initialized pixels to avoid duplicate initialization
const initializedMetaPixels = new Set<string>();
const initializedGA4Pixels = new Set<string>();
const initializedSnapPixels = new Set<string>();
const initializedTikTokPixels = new Set<string>();

/**
 * Ensures a Meta pixel is initialized before tracking
 */
const ensureMetaPixelInitialized = (pixelId: string): boolean => {
  if (isDevelopment) return false;
  if (typeof window === "undefined") return false;
  if (!window.fbq) return false;
  
  // If we've already initialized this pixel in our app, we're good
  if (initializedMetaPixels.has(pixelId)) return true;
  
  try {
    // Initialize the pixel
    window.fbq("init", pixelId);
    initializedMetaPixels.add(pixelId);
    return true;
  } catch (error) {
    console.error(`Failed to initialize Facebook Pixel ${pixelId}:`, error);
    return false;
  }
};

/**
 * Ensures a GA4 pixel is initialized before tracking
 */
const ensureGA4PixelInitialized = (ga4Id: string): boolean => {
  if (isDevelopment) return false;
  if (typeof window === "undefined") return false;
  if (!(window as any).gtag) return false;
  
  // If we've already initialized this pixel in our app, we're good
  if (initializedGA4Pixels.has(ga4Id)) return true;
  
  try {
    // Initialize the GA4 measurement ID
    (window as any).gtag("config", ga4Id);
    initializedGA4Pixels.add(ga4Id);
    return true;
  } catch (error) {
    console.error(`Failed to initialize GA4 Pixel ${ga4Id}:`, error);
    return false;
  }
};

/**
 * Ensures a Snap pixel is initialized before tracking
 */
const ensureSnapPixelInitialized = (snapId: string): boolean => {
  if (isDevelopment) return false;
  if (typeof window === "undefined") return false;
  if (!(window as any).snaptr) return false;
  
  // If we've already initialized this pixel in our app, we're good
  if (initializedSnapPixels.has(snapId)) return true;
  
  try {
    // Initialize the Snap pixel
    (window as any).snaptr("init", snapId);
    initializedSnapPixels.add(snapId);
    return true;
  } catch (error) {
    console.error(`Failed to initialize Snap Pixel ${snapId}:`, error);
    return false;
  }
};

/**
 * Ensures a TikTok pixel is initialized before tracking
 */
const ensureTikTokPixelInitialized = (tiktokId: string): boolean => {
  if (isDevelopment) return false;
  if (typeof window === "undefined") return false;
  if (!(window as any).ttq) return false;
  
  // If we've already initialized this pixel in our app, we're good
  if (initializedTikTokPixels.has(tiktokId)) return true;
  
  try {
    // Initialize the TikTok pixel
    (window as any).ttq.load(tiktokId);
    (window as any).ttq.page();
    initializedTikTokPixels.add(tiktokId);
    return true;
  } catch (error) {
    console.error(`Failed to initialize TikTok Pixel ${tiktokId}:`, error);
    return false;
  }
};

// Helper to send tracking to Meta (Facebook)
const trackMeta = (
  eventName: string,
  params: TrackingParams,
  pixelId: string,
  user?: Partial<UserData>
): void => {
  // Early return for development mode
  if (isDevelopment) return;

  // Retry mechanism for tracking
  const maxRetries = 5;
  let retryCount = 0;

  const attemptTrack = () => {
    if (typeof window === "undefined") {
      console.warn("Window not available - server side rendering");
      return;
    }

    if (!window.fbq) {
      if (retryCount < maxRetries) {
        retryCount++;
        console.info(`Facebook Pixel not ready, retrying (${retryCount}/${maxRetries})...`);
        setTimeout(attemptTrack, 1000); // Retry after 1 second
        return;
      }
      console.warn("Facebook Pixel not initialized after retries");
      return;
    }

    if (!pixelId) {
      console.warn("No pixel ID provided");
      return;
    }

    // Ensure the pixel is initialized before tracking
    if (!ensureMetaPixelInitialized(pixelId)) {
      if (retryCount < maxRetries) {
        retryCount++;
        console.info(`Pixel initialization failed, retrying (${retryCount}/${maxRetries})...`);
        setTimeout(attemptTrack, 1000);
        return;
      }
      console.warn(`Failed to initialize pixel ${pixelId} after ${maxRetries} retries`);
      return;
    }

    const userData: TrackingParams = user
      ? {
          user_type: "registered",
          user_id: user.id,
          user_email: user.email,
          user_name: user.name,
        }
      : { user_type: "anonymous" };

    const enhancedParams = {
      ...params,
      ...userData,
      timestamp: new Date().toISOString(),
    };

    try {
      window.fbq("trackSingleCustom", pixelId, eventName, enhancedParams);
    } catch (error) {
      // Check for specific pixel not found error and handle it more gracefully
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (errorMessage.includes('Pixel') && errorMessage.includes('not found')) {
        // This is the error we're trying to solve
        console.warn(`Pixel ${pixelId} not initialized properly, re-attempting initialization`);
        
        // Try to initialize again explicitly
        try {
          window.fbq("init", pixelId);
          
          // Try tracking again after re-initialization
          setTimeout(() => {
            try {
              window.fbq("trackSingleCustom", pixelId, eventName, enhancedParams);
            } catch (retryError) {
              console.error(`Retry tracking ${eventName} failed:`, retryError);
            }
          }, 100);
        } catch (initError) {
          console.error(`Re-initialization of pixel ${pixelId} failed:`, initError);
        }
      } else {
        // Other errors
        console.error(`Failed to track ${eventName}:`, error);
      }
    }
  };

  attemptTrack();
};

// --------------- GA4 Tracking ---------------
const trackGA4 = (
  eventName: string,
  params: TrackingParams,
  ga4Id?: string
): void => {
  // Early return for development mode
  if (isDevelopment) return;
  
  if (typeof window === "undefined" || !ga4Id) return;

  // Ensure gtag is available
  if (!(window as any).gtag) {
    return; // gtag not yet loaded
  }

  // Ensure the GA4 pixel is initialized before tracking
  if (!ensureGA4PixelInitialized(ga4Id)) {
    console.warn(`Failed to initialize GA4 pixel ${ga4Id}`);
    return;
  }

  try {
    (window as any).gtag("event", eventName, params);
  } catch (err) {
    console.error(`Failed to track GA4 event ${eventName}:`, err);
  }
};

// --------------- Snap Pixel Tracking ---------------
const trackSnap = (
  eventName: string,
  params: TrackingParams,
  snapId?: string
): void => {
  // Early return for development mode
  if (isDevelopment) return;
  
  if (typeof window === "undefined" || !snapId) return;
  // Ensure snaptr is available
  if (!(window as any).snaptr) return;

  // Ensure the Snap pixel is initialized before tracking
  if (!ensureSnapPixelInitialized(snapId)) {
    console.warn(`Failed to initialize Snap pixel ${snapId}`);
    return;
  }

  try {
    // snaptr requires pixel id per call
    (window as any).snaptr("track", eventName, {
      ...params,
      pixel_id: snapId,
    });
  } catch (err) {
    console.error(`Failed to track Snap event ${eventName}:`, err);
  }
};

// --------------- TikTok Pixel Tracking ---------------
const trackTikTok = (
  eventName: string,
  params: TrackingParams,
  tikTokId?: string
): void => {
  // Early return for development mode
  if (isDevelopment) return;
  
  if (typeof window === "undefined" || !tikTokId) return;
  if (!(window as any).ttq) return;

  // Ensure the TikTok pixel is initialized before tracking
  if (!ensureTikTokPixelInitialized(tikTokId)) {
    console.warn(`Failed to initialize TikTok pixel ${tikTokId}`);
    return;
  }

  try {
    const ttq = (window as any).ttq;
    const instance = ttq.instance ? ttq.instance(tikTokId) : ttq;
    instance.track(eventName, params);
  } catch (err) {
    console.error(`Failed to track TikTok event ${eventName}:`, err);
  }
};

// Track across all supported platforms with pixel initialization
const track = (
  eventName: string,
  params: TrackingParams,
  event: EventData,
  user?: Partial<UserData>
): void => {
  // Early return for development mode
  if (isDevelopment) return;
  
  const metaId = event.pixels?.meta || event.organizerPixelId;
  const ga4Id = event.pixels?.ga4;
  const snapId = event.pixels?.snap;
  const tiktokId = event.pixels?.tiktok;

  if (metaId) {
    trackMeta(eventName, params, metaId, user);
  }

  if (ga4Id) {
    trackGA4(eventName, params, ga4Id);
  }

  if (snapId) {
    trackSnap(eventName, params, snapId);
  }

  if (tiktokId) {
    trackTikTok(eventName, params, tiktokId);
  }
};

export const trackEventClick = (
  event: EventData,
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  if (!event.pixels?.meta || !event.pixels?.ga4 || !event.pixels?.snap || !event.pixels?.tiktok)
    return;

  track(
    "event_click",
    {
      content_type: "event",
      content_id: event.id,
      content_name: event.title,
      event_start_date: event.date,
    },
    event,
    user
  );
};

export const trackEventPageView = (
  event: EventData,
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  if (!event) {
    return;
  }

  track(
    "event_view",
    {
      content_type: "event",
      content_ids: [event.id],
      content_name: event.title,
      event_start_date: event.date,
    },
    event,
    user
  );
};

export const trackFreeTicketSelect = (
  event: EventData,
  ticket: TicketData,
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  const hasPixel =
    event.organizerPixelId ||
    event.pixels?.meta ||
    event.pixels?.ga4 ||
    event.pixels?.snap ||
    event.pixels?.tiktok;
  
  if (!hasPixel) {
    return;
  }

  track(
    "free_ticket_select",
    {
      event_id: event.id,
      event_name: event.title,
      ticket_id: ticket.id,
      ticket_name: ticket.name,
      action: "attending",
    },
    event,
    user
  );
};

export const trackFreeEventTicketSelect = (
  event: EventData,
  ticket: TicketData,
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  const hasPixel =
    event.organizerPixelId ||
    event.pixels?.meta ||
    event.pixels?.ga4 ||
    event.pixels?.snap ||
    event.pixels?.tiktok;
  
  if (!hasPixel) {
    return;
  }

  track(
    "free_event_ticket_select",
    {
      event_id: event.id,
      event_name: event.title,
      ticket_id: ticket.id,
      ticket_name: ticket.name,
      action: "attending",
    },
    event,
    user
  );
};

export const trackFreeTicketDeselect = (
  event: EventData,
  ticket: TicketData,
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  const hasPixel =
    event.organizerPixelId ||
    event.pixels?.meta ||
    event.pixels?.ga4 ||
    event.pixels?.snap ||
    event.pixels?.tiktok;
  
  if (!hasPixel) {
    return;
  }

  track(
    "free_ticket_deselect",
    {
      event_id: event.id,
      event_name: event.title,
      ticket_id: ticket.id,
      ticket_name: ticket.name,
      action: "not_attending",
    },
    event,
    user
  );
};

export const trackFreeEventTicketDeselect = (
  event: EventData,
  ticket: TicketData,
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  const hasPixel =
    event.organizerPixelId ||
    event.pixels?.meta ||
    event.pixels?.ga4 ||
    event.pixels?.snap ||
    event.pixels?.tiktok;
  
  if (!hasPixel) {
    return;
  }

  track(
    "free_event_ticket_deselect",
    {
      event_id: event.id,
      event_name: event.title,
      ticket_id: ticket.id,
      ticket_name: ticket.name,
      action: "not_attending",
    },
    event,
    user
  );
};

export const trackAddToCart = (
  event: EventData,
  tickets: TicketData[],
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  const hasPixel =
    event.pixels?.meta ||
    event.pixels?.ga4 ||
    event.pixels?.snap ||
    event.pixels?.tiktok;
  
  if (!hasPixel) {
    return;
  }

  const value = tickets.reduce(
    (sum, ticket) => sum + ticket.price * (ticket.quantity || 1),
    0
  );

  track(
    "add_to_cart",
    {
      content_type: "event_tickets",
      content_ids: tickets.map((t) => t.id),
      content_name: event.title,
      value: value.toFixed(2),
      currency: "USD",
      num_items: tickets.reduce((sum, t) => sum + (t.quantity || 1), 0),
      tickets: tickets.map((t) => ({
        id: t.id,
        name: t.name,
        quantity: t.quantity || 1,
        price: t.price.toFixed(2),
      })),
    },
    event,
    user
  );
};

export const trackInitiateCheckout = (
  event: EventData,
  tickets: TicketData[],
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  const hasPixel =
    event.organizerPixelId ||
    event.pixels?.meta ||
    event.pixels?.ga4 ||
    event.pixels?.snap ||
    event.pixels?.tiktok;
  
  if (!hasPixel) {
    return;
  }

  const value = tickets.reduce(
    (sum, ticket) => sum + ticket.price * (ticket.quantity || 1),
    0
  );

  track(
    "initiate_checkout",
    {
      content_type: "event_tickets",
      content_ids: tickets.map((t) => t.id),
      content_name: event.title,
      value: value.toFixed(2),
      currency: "USD",
      num_items: tickets.reduce((sum, t) => sum + (t.quantity || 1), 0),
      tickets: tickets.map((t) => ({
        id: t.id,
        name: t.name,
        quantity: t.quantity || 1,
        price: t.price.toFixed(2),
      })),
    },
    event,
    user
  );
};

export const trackOrganizerProfileClick = (
  event: EventData,
  user?: Partial<UserData>
): void => {
  if (isDevelopment) return;
  const hasPixel =
    event.pixels?.meta ||
    event.pixels?.ga4 ||
    event.pixels?.snap ||
    event.pixels?.tiktok;
  
  if (!hasPixel) {
    return;
  }

  track(
    "organizer_profile_click",
    {
      source_event_id: event.id,
      source_event_name: event.title,
    },
    event,
    user
  );
};

export const trackPurchaseComplete = (
  event: EventData,
  tickets: TicketData[],
  transactionId: string,
  user?: Partial<UserData>,
  pixels?: {
    meta?: string;
    ga4?: string;
    snap?: string;
    tiktok?: string;
  }
): void => {
  if (isDevelopment) return;
  const hasPixel =
    pixels?.meta ||
    pixels?.ga4 ||
    pixels?.snap ||
    pixels?.tiktok;
  
  if (!hasPixel) {
    return;
  }

  const value = tickets.reduce(
    (sum, ticket) => sum + ticket.price * (ticket.quantity || 1),
    0
  );

  track(
    "purchase_complete",
    {
      content_type: "event_tickets",
      content_ids: tickets.map((t) => t.id),
      content_name: event.title,
      value: value.toFixed(2),
      currency: "USD",
      num_items: tickets.reduce((sum, t) => sum + (t.quantity || 1), 0),
      transaction_id: transactionId,
      tickets: tickets.map((t) => ({
        id: t.id,
        name: t.name,
        quantity: t.quantity || 1,
        price: t.price.toFixed(2),
      })),
    },
    event,
    user
  );
};
