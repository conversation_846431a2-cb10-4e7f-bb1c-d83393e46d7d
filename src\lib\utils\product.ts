import { Product } from "@/app/events/types";

/**
 * Returns the first non-zero variant price from a product
 * If no non-zero variant price is found, returns 0
 * @param product The product to get the price from
 * @returns The first non-zero variant price or 0
 */
export const getFirstNonZeroVariantPrice = (product: Product): number => {
  try {
  // First check if there's a default variant price
  if (product?.defaultVariant?.price) {
    const price = Number(product.defaultVariant.price);
    if (price > 0) return price;
  }

  // Then check through variants
  if (product?.variants && product?.variants?.length > 0) {
    const nonZeroVariant = product?.variants?.find(
      (variant) => Number(variant.price) > 0
    );
    if (nonZeroVariant) {
      return Number(nonZeroVariant.price);
    }
  }
    return 0;
  } catch (error) {
    console.error(error);
    return 0;
  }
};

