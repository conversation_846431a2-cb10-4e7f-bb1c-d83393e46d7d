import { isRejectedWithValue, Middleware, MiddlewareAPI } from '@reduxjs/toolkit';
import {  RestrictionError } from '@/lib/types/restriction';
import { getRestrictionReason, isRestrictionError } from './handleRestriction';

/**
 * Middleware that detects restricted access API responses and redirects to the restriction page
 */
export const restrictionMiddleware: Middleware = 
  (api: MiddlewareAPI) => (next) => (action) => {
    // Check if this is a rejected API call with an error response
    if (isRejectedWithValue(action)) {
      const { payload } = action;
      
      // Check if this is a restriction error
      if (isRestrictionError(payload)) {
        const errorData = (payload as any)?.data || (payload as any)?.error?.data || payload;
        const error = errorData as RestrictionError;
        
        // Set cookies with restriction information
        document.cookie = `restricted=true; path=/`;
        document.cookie = `restriction_code=${error.code}; path=/`;
        document.cookie = `restriction_reason=${encodeURIComponent(getRestrictionReason(error.code))}; path=/`;
        document.cookie = `restriction_detail=${encodeURIComponent(error.detail)}; path=/`;
        
        // Redirect to the restricted page if not already there
        if (!window.location.pathname.startsWith('/restricted')) {
          window.location.href = '/restricted';
        }
      }
    }

    return next(action);
  }; 