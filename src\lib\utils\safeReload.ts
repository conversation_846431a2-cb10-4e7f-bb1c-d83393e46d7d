// lib/utils/safeReload.ts

interface ReloadProtection {
  lastReloadTime: number;
  reloadCounts: Map<string, { count: number; lastReset: number }>;
}

class SafeReloadManager {
  private static readonly MIN_RELOAD_INTERVAL = 10000; // 10 seconds
  private static readonly MAX_RELOADS_PER_MINUTE = 3;
  private static readonly RESET_INTERVAL = 60000; // 1 minute
  
  private static protection: ReloadProtection = {
    lastReloadTime: 0,
    reloadCounts: new Map(),
  };

  /**
   * Check if a reload is safe based on timing and frequency
   */
  private static isReloadSafe(source: string): boolean {
    const now = Date.now();
    
    // Check minimum interval since last reload
    const timeSinceLastReload = now - this.protection.lastReloadTime;
    if (timeSinceLastReload < this.MIN_RELOAD_INTERVAL) {
      console.warn(`Reload blocked: ${timeSinceLastReload}ms < ${this.MIN_RELOAD_INTERVAL}ms since last reload`);
      return false;
    }

    // Check frequency for this specific source
    const sourceData = this.protection.reloadCounts.get(source);
    if (sourceData) {
      const timeSinceReset = now - sourceData.lastReset;
      
      // Reset counter if enough time has passed
      if (timeSinceReset >= this.RESET_INTERVAL) {
        this.protection.reloadCounts.set(source, { count: 0, lastReset: now });
      } else if (sourceData.count >= this.MAX_RELOADS_PER_MINUTE) {
        console.warn(`Reload blocked: ${source} exceeded ${this.MAX_RELOADS_PER_MINUTE} reloads per minute`);
        return false;
      }
    }

    return true;
  }

  /**
   * Safely trigger a page reload with comprehensive loop protection
   */
  static safeReload(source: string, fallbackUrl?: string): boolean {
    if (!this.isReloadSafe(source)) {
      if (fallbackUrl) {
        console.log(`Reload blocked, redirecting to fallback: ${fallbackUrl}`);
        try {
          window.location.href = fallbackUrl;
          return true;
        } catch (error) {
          console.error("Failed to redirect to fallback URL:", error);
        }
      }
      return false;
    }

    // Update tracking
    this.protection.lastReloadTime = Date.now();
    
    const sourceData = this.protection.reloadCounts.get(source) || { count: 0, lastReset: Date.now() };
    sourceData.count++;
    this.protection.reloadCounts.set(source, sourceData);

    console.log(`Safe reload triggered by: ${source} (count: ${sourceData.count})`);
    
    try {
      window.location.reload();
      return true;
    } catch (error) {
      console.error("Failed to reload page:", error);
      
      // Try fallback URL if reload fails
      if (fallbackUrl) {
        try {
          window.location.href = fallbackUrl;
          return true;
        } catch (redirectError) {
          console.error("Failed to redirect to fallback URL:", redirectError);
        }
      }
      
      return false;
    }
  }

  /**
   * Get reload protection statistics for debugging
   */
  static getProtectionStats(): ReloadProtection {
    return {
      lastReloadTime: this.protection.lastReloadTime,
      reloadCounts: new Map(this.protection.reloadCounts),
    };
  }

  /**
   * Reset protection state (useful for testing or manual intervention)
   */
  static resetProtection(): void {
    this.protection = {
      lastReloadTime: 0,
      reloadCounts: new Map(),
    };
  }
}

/**
 * Convenience function for safe page reloads
 * @param source - Identifier for the source triggering the reload
 * @param fallbackUrl - Optional URL to redirect to if reload is blocked
 * @returns true if reload/redirect was successful, false otherwise
 */
export const safeReload = (source: string, fallbackUrl?: string): boolean => {
  return SafeReloadManager.safeReload(source, fallbackUrl);
};

/**
 * Get protection statistics for debugging
 */
export const getReloadStats = (): ReloadProtection => {
  return SafeReloadManager.getProtectionStats();
};

/**
 * Reset reload protection (use with caution)
 */
export const resetReloadProtection = (): void => {
  SafeReloadManager.resetProtection();
};

export default SafeReloadManager;
