// lib/utils/safeStorage.ts

interface StorageResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Safe localStorage wrapper with error handling for quota exceeded and access denied scenarios
 */
export class SafeStorage {
  /**
   * Safely get an item from localStorage
   */
  static getItem(key: string): StorageResult<string> {
    try {
      const data = localStorage.getItem(key);
      return { success: true, data: data || undefined };
    } catch (error) {
      console.warn(`Failed to get localStorage item "${key}":`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Safely set an item in localStorage with quota checking
   */
  static setItem(key: string, value: string): StorageResult<void> {
    try {
      // Check if we can access localStorage first
      const testKey = '__storage_test__';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      
      // Proceed with actual operation
      localStorage.setItem(key, value);
      return { success: true };
    } catch (error) {
      // Handle specific storage errors
      if (error instanceof DOMException) {
        if (error.name === 'QuotaExceededError' || error.name === 'QUOTA_EXCEEDED_ERR') {
          console.warn(`localStorage quota exceeded when setting "${key}". Attempting cleanup...`);
          this.cleanupOldEntries();
          
          // Try again after cleanup
          try {
            localStorage.setItem(key, value);
            return { success: true };
          } catch (retryError) {
            console.error(`Failed to set localStorage item "${key}" even after cleanup:`, retryError);
            return { 
              success: false, 
              error: 'Storage quota exceeded and cleanup failed' 
            };
          }
        } else if (error.name === 'SecurityError') {
          console.warn(`localStorage access denied for "${key}" (private browsing mode?):`, error);
          return { 
            success: false, 
            error: 'Storage access denied (private browsing mode?)' 
          };
        }
      }
      
      console.warn(`Failed to set localStorage item "${key}":`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Safely remove an item from localStorage
   */
  static removeItem(key: string): StorageResult<void> {
    try {
      localStorage.removeItem(key);
      return { success: true };
    } catch (error) {
      console.warn(`Failed to remove localStorage item "${key}":`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Safely get and parse JSON from localStorage
   */
  static getJSON<T>(key: string, fallback?: T): StorageResult<T> {
    const result = this.getItem(key);
    if (!result.success || !result.data) {
      return { success: false, data: fallback, error: result.error };
    }

    try {
      const parsed = JSON.parse(result.data) as T;
      return { success: true, data: parsed };
    } catch (error) {
      console.warn(`Failed to parse JSON from localStorage item "${key}":`, error);
      return { 
        success: false, 
        data: fallback,
        error: 'Invalid JSON data' 
      };
    }
  }

  /**
   * Safely set JSON data in localStorage
   */
  static setJSON(key: string, value: any): StorageResult<void> {
    try {
      const jsonString = JSON.stringify(value);
      return this.setItem(key, jsonString);
    } catch (error) {
      console.warn(`Failed to stringify data for localStorage item "${key}":`, error);
      return { 
        success: false, 
        error: 'Failed to serialize data' 
      };
    }
  }

  /**
   * Get localStorage usage information
   */
  static getUsageInfo() {
    try {
      let totalSize = 0;
      let itemCount = 0;
      const itemSizes: Record<string, number> = {};

      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          const value = localStorage.getItem(key) || '';
          const size = (key.length + value.length) * 2; // Rough estimate in bytes
          itemSizes[key] = size;
          totalSize += size;
          itemCount++;
        }
      }

      return {
        success: true,
        data: {
          totalSize,
          itemCount,
          itemSizes,
          formattedSize: this.formatBytes(totalSize)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Clean up old or large entries to free up space
   */
  private static cleanupOldEntries() {
    try {
      const usage = this.getUsageInfo();
      if (!usage.success || !usage.data) return;

      // Remove large items first (over 100KB)
      const LARGE_ITEM_THRESHOLD = 100 * 1024; // 100KB
      for (const [key, size] of Object.entries(usage.data.itemSizes)) {
        if (size > LARGE_ITEM_THRESHOLD) {
          console.log(`Removing large localStorage item: ${key} (${this.formatBytes(size)})`);
          localStorage.removeItem(key);
        }
      }

      // If still having issues, remove items with specific patterns (temp, cache, etc.)
      const tempPatterns = ['temp_', 'cache_', '_temp', '_cache', 'tmp_'];
      for (let key in localStorage) {
        if (tempPatterns.some(pattern => key.includes(pattern))) {
          console.log(`Removing temporary localStorage item: ${key}`);
          localStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup localStorage:', error);
    }
  }

  /**
   * Format bytes to human readable string
   */
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

/**
 * Safe sessionStorage wrapper (similar to localStorage but for session data)
 */
export class SafeSessionStorage {
  /**
   * Safely get an item from sessionStorage
   */
  static getItem(key: string): StorageResult<string> {
    try {
      const data = sessionStorage.getItem(key);
      return { success: true, data: data || undefined };
    } catch (error) {
      console.warn(`Failed to get sessionStorage item "${key}":`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Safely set an item in sessionStorage
   */
  static setItem(key: string, value: string): StorageResult<void> {
    try {
      sessionStorage.setItem(key, value);
      return { success: true };
    } catch (error) {
      if (error instanceof DOMException && 
          (error.name === 'QuotaExceededError' || error.name === 'QUOTA_EXCEEDED_ERR')) {
        console.warn(`sessionStorage quota exceeded when setting "${key}"`);
        return { 
          success: false, 
          error: 'Session storage quota exceeded' 
        };
      }
      
      console.warn(`Failed to set sessionStorage item "${key}":`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Safely remove an item from sessionStorage
   */
  static removeItem(key: string): StorageResult<void> {
    try {
      sessionStorage.removeItem(key);
      return { success: true };
    } catch (error) {
      console.warn(`Failed to remove sessionStorage item "${key}":`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Safely get and parse JSON from sessionStorage
   */
  static getJSON<T>(key: string, fallback?: T): StorageResult<T> {
    const result = this.getItem(key);
    if (!result.success || !result.data) {
      return { success: false, data: fallback, error: result.error };
    }

    try {
      const parsed = JSON.parse(result.data) as T;
      return { success: true, data: parsed };
    } catch (error) {
      console.warn(`Failed to parse JSON from sessionStorage item "${key}":`, error);
      return { 
        success: false, 
        data: fallback,
        error: 'Invalid JSON data' 
      };
    }
  }

  /**
   * Safely set JSON data in sessionStorage
   */
  static setJSON(key: string, value: any): StorageResult<void> {
    try {
      const jsonString = JSON.stringify(value);
      return this.setItem(key, jsonString);
    } catch (error) {
      console.warn(`Failed to stringify data for sessionStorage item "${key}":`, error);
      return { 
        success: false, 
        error: 'Failed to serialize data' 
      };
    }
  }
}

// Convenience functions for backward compatibility
export const safeLocalStorage = SafeStorage;
export const safeSessionStorage = SafeSessionStorage;
