import * as Sentry from "@sentry/nextjs";

interface LogFunctionalErrorParams {
  userMessage: string;
  error?: unknown;
  context?: Record<string, any>;
  tags?: Record<string, string>;
}

export function logFunctionalError({
  userMessage,
  error,
  context = {},
  tags = {},
}: LogFunctionalErrorParams): void {
  const err = error instanceof Error ? error : new Error(String(error) || userMessage);

  Sentry.captureException(err, {
    level: "warning",
    contexts: {
      ui: { message: userMessage, ...context },
    },
    tags: {
      type: "functional_error",
      ...tags,
    },
  });
}

/**
 * Utility function to log a functional error to Sentry with just a user message.
 *
 * @param userMessage - The message describing the functional error.
 * @param additionalTags - Optional additional tags for Sentry.
 */
export function reportError(userMessage: string, additionalTags?: Record<string, string>): void {
  logFunctionalError({ userMessage, tags: additionalTags });
} 