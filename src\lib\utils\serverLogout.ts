export type LogoutError = {
  message: string;
  status?: number;
};

export async function serverSideLogout(
  accessToken: string,
  refreshToken: string,
  csrfToken: string
): Promise<void> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}auth/sign-out/`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Cookie": `refreshToken=${refreshToken}`,
          Authorization: `Bearer ${accessToken}`,
          "X-CSRFToken": csrfToken,
        },
        credentials: "include",
      }
    );

    if (!response.ok) {
      const error: LogoutError = {
        message: "Failed to sign out on the server",
        status: response.status,
      };
      throw error;
    }
  } catch (error) {
    const logoutError: LogoutError = {
      message: error instanceof Error ? error.message : "Unknown error during logout",
    };
    throw logoutError;
  }
}
