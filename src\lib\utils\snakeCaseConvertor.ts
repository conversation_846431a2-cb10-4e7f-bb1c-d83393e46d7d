import { camelCase } from "camel-case";
import { snakeCase } from "snake-case";

export function keysToCamel(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((v) => keysToCamel(v));
  } else if (obj.constructor === Object) {
    return Object.keys(obj).reduce((result, key) => {
      result[camelCase(key)] = keysToCamel(obj[key]);
      return result;
    }, {});
  }
  return obj;
}

export function keysToSnake(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((v) => keysToSnake(v));
  } else if (obj.constructor === Object) {
    return Object.keys(obj).reduce((result, key) => {
      result[snakeCase(key)] = keysToSnake(obj[key]);
      return result;
    }, {});
  }
  return obj;
}