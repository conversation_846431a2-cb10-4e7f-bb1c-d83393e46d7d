export const getFirstName = (name: string): string => {
  const names = name.split(" ");
  return names[0];
};

export const splitFullName = (
  name: string
): { firstName: string; lastName: string } => {
  if (!name) {
    return {
      firstName: "",
      lastName: "",
    };
  }
  const names = name.split(" ");
  return {
    firstName: names[0],
    lastName: names.slice(1).join(" "),
  };
};

export const getInitials = (name: string): string => {
  const names = name.split(" ");
  return names[0][0];
};

export const capitalize = (str: string): string =>
  str.charAt(0).toUpperCase() + str.slice(1);

export const getStringBeforeHyphen = (str: string): string => {
  const hyphenIndex = str.indexOf("-");
  return hyphenIndex === -1 ? str : str.substring(0, hyphenIndex);
};

export const capitalizeAndSplitSnakeCase = (str: string): string => {
  if (str.includes("_")) {
    return str
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const isValidEmail = (email: string | null | undefined): boolean => {
  try {
    if (!email) return false;
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email.trim());
  } catch (error) {
    console.log("Error in isValidEmail", error);
    return false;
  }
};

export const getName = (user: any): string => {
  const firstName = user?.firstName || "" + " ";
  const lastName = user?.lastName || "";
  if (firstName?.trim() && lastName?.trim()) {
    return `${firstName} ${lastName}`;
  }
  return user?.name || "";
};

export const usernamePattern = /^[a-z0-9_.]*$/;

// Helper function to check if username format is valid
export const isUsernameFormatValid = (username: string): boolean => {
  if (!username) return false;
  return username ? usernamePattern.test(username) : false;
};


export const isEmailValid = (email: string): boolean => {
  if (!email) return false;
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email.trim());
};
