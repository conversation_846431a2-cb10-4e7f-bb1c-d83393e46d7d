/**
 * Subdomain utilities for whitelabel functionality
 */

export interface SubdomainInfo {
  isSubdomain: boolean;
  organizerSlug: string | null;
  hostname: string;
}

/**
 * Extract subdomain from hostname
 * @param hostname - The hostname to analyze
 * @returns SubdomainInfo object with subdomain details
 */
export function getSubdomainInfo(hostname: string): SubdomainInfo {
  if (!hostname) {
    return {
      isSubdomain: false,
      organizerSlug: null,
      hostname: "",
    };
  }

  // Remove www. if present
  const cleanHostname = hostname.replace(/^www\./, "");
  
  // Split by dots
  const parts = cleanHostname.split(".");
  
  // Handle localhost development - check for subdomain.localhost pattern
  if (cleanHostname.includes("localhost")) {
    // For localhost, we expect: subdomain.localhost or subdomain.localhost:port
    // Remove port if present
    const hostWithoutPort = cleanHostname.split(':')[0];
    const localhostParts = hostWithoutPort.split('.');
    
    // If we have subdomain.localhost (2 parts), treat the first part as organizer slug
    if (localhostParts.length === 2 && localhostParts[1] === 'localhost') {
      const subdomain = localhostParts[0];
      const invalidSubdomains = ["www", "api", "admin", "app", "mail", "ftp", "dev"];
      
      if (subdomain && !invalidSubdomains.includes(subdomain.toLowerCase())) {
        return {
          isSubdomain: true,
          organizerSlug: subdomain,
          hostname: cleanHostname,
        };
      }
    }
    
    // If it's just localhost or localhost:port, not a subdomain
    if (localhostParts.length === 1 || (localhostParts.length === 1 && cleanHostname.includes(':'))) {
      return {
        isSubdomain: false,
        organizerSlug: null,
        hostname: cleanHostname,
      };
    }
  }

  // Handle 127.0.0.1 - not treating as subdomain for development
  if (cleanHostname.includes("127.0.0.1")) {
    return {
      isSubdomain: false,
      organizerSlug: null,
      hostname: cleanHostname,
    };
  }

  // For production - expecting format: subdomain.domain.com
  // We need at least 3 parts for a subdomain (subdomain.domain.com)
  if (parts.length >= 3) {
    const subdomain = parts[0];
    const mainDomain = parts.slice(1).join(".");
    
    // Check if this is a valid subdomain (not empty and not common prefixes)
    const invalidSubdomains = ["www", "api", "admin", "app", "mail", "ftp", "dev"];
    
    if (subdomain && !invalidSubdomains.includes(subdomain.toLowerCase())) {
      return {
        isSubdomain: true,
        organizerSlug: subdomain,
        hostname: cleanHostname,
      };
    }
  }

  return {
    isSubdomain: false,
    organizerSlug: null,
    hostname: cleanHostname,
  };
}

/**
 * Check if a path is allowed for whitelabel access
 * @param pathname - The path to check
 * @returns boolean indicating if the path is allowed
 */
export function isWhitelabelAllowedPath(pathname: string): boolean {
  const allowedPaths = [
    "/events", // Allow events listing page (will redirect to /event)
    "/events/", // Allow events/* paths (will rewrite to /event/*)
    "/event", // Allow whitelabel event listing page  
    "/event/", // Allow whitelabel event detail pages
    "/resend", // Allow password reset functionality
    "/api/", // Allow API calls
    "/_next/", // Allow Next.js assets
    "/favicon.ico",
    "/robots.txt",
    "/sitemap.xml",
    // Static assets
    "/landing-page/",
    "/testimonials/",
    "/font/",
    // Add other static assets as needed
  ];

  // Also allow direct static file extensions
  const allowedExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot'];
  const hasAllowedExtension = allowedExtensions.some(ext => pathname.endsWith(ext));

  // Check for exact match or startsWith for paths ending with /
  const isAllowed = allowedPaths.some(path => {
    if (path.endsWith('/')) {
      return pathname.startsWith(path);
    } else {
      return pathname === path;
    }
  });

  return isAllowed || hasAllowedExtension;
}

/**
 * Check if the current request is from a whitelabel subdomain
 * @param request - NextRequest object
 * @returns SubdomainInfo
 */
export function getRequestSubdomainInfo(hostname: string): SubdomainInfo {
  return getSubdomainInfo(hostname);
} 