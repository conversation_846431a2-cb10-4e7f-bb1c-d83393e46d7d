import { Mutex } from 'async-mutex';
import { getCookie, removeC<PERSON>ie } from './cookieUtils';
import { refreshTokenAction } from '@/lib/actions/auth/refreshTokenAction';
import {
  COOKIE_NAMES,
  TOKEN_REFRESH_ERRORS,
  HTTP_STATUS,
} from '@/lib/utils/constants';

interface TokenRefreshResult {
  success: boolean;
  accessToken?: string;
  error?: string;
  shouldLogout?: boolean;
}

class TokenRefreshManager {
  private mutex = new Mutex();
  private refreshPromise: Promise<TokenRefreshResult> | null = null;

  async refreshToken(): Promise<TokenRefreshResult> {
    // If there's already a refresh in progress, wait for it
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    // Create a new refresh promise
    this.refreshPromise = this.performRefresh();
    
    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      // Clear the promise after completion
      this.refreshPromise = null;
    }
  }

  private async performRefresh(): Promise<TokenRefreshResult> {
    const release = await this.mutex.acquire();
    
    try {
      const refreshToken = getCookie(COOKIE_NAMES.REFRESH_TOKEN);
      const csrfToken = getCookie(COOKIE_NAMES.CSRF_TOKEN);

      if (!refreshToken || !csrfToken) {
        return {
          success: false,
          error: 'Missing refresh or CSRF token',
          shouldLogout: true
        };
      }

      const result = await refreshTokenAction(refreshToken, csrfToken);
      
      if ('error' in result) {
        const shouldLogout = result.error === TOKEN_REFRESH_ERRORS.INVALID_REFRESH_TOKEN || result.status === HTTP_STATUS.UNAUTHORIZED;
        
        if (shouldLogout) {
          // Clean up cookies if refresh token is invalid
          removeCookie(COOKIE_NAMES.REFRESH_TOKEN);
          removeCookie(COOKIE_NAMES.CSRF_TOKEN);
        }
        
        return {
          success: false,
          error: result.message || 'Token refresh failed',
          shouldLogout
        };
      }

      return {
        success: true,
        accessToken: result.accessToken
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Unexpected error during token refresh',
        shouldLogout: true
      };
    } finally {
      release();
    }
  }

  // Method to check if a refresh is currently in progress
  isRefreshing(): boolean {
    return this.refreshPromise !== null;
  }
}

// Export a singleton instance
export const tokenRefreshManager = new TokenRefreshManager(); 