export   const validateZipCode = (zipCode: string, country: string): boolean => {
    const zipCodePatterns: { [key: string]: RegExp } = {
      "United States": /^\d{5}(-\d{4})?$/,
      "United Kingdom": /^[A-Z]{1,2}\d[A-Z\d]? ?\d[A-Z]{2}$/i,
      "Canada": /^[A-Z]\d[A-Z] ?\d[A-Z]\d$/i,
      "Australia": /^\d{4}$/,
      "Germany": /^\d{5}$/,
    };

    const pattern = zipCodePatterns[country];
    if (!pattern) {
      // For countries without specific validation, ensure minimum length
      return zipCode.length >= 3;
    }

    return pattern.test(zipCode);
  };