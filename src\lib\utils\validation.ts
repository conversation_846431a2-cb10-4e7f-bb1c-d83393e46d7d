import { z } from "zod";

export const signInSchema = z.object({
  value: z
    .string()
    .min(1, { message: "Email or Username is required" })
    .transform((value) => value.trim()),
  password: z
    .string()
    .min(1, { message: "Password is required" })
    .min(6, { message: "Password must be at least 6 characters" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Password cannot contain only whitespace",
    }),
});

export const signUpSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email" }),
  password: z
    .string()
    .min(1, { message: "Password is required" })
    .min(6, { message: "Password must be at least 6 characters" })
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])(?=.*[0-9]).{6,}$/, {
      message:
        "Password must contain at least 6 characters, 1 uppercase letter, 1 lowercase letter, and 1 special character",
    })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Password cannot contain only whitespace",
    }),
  username: z
    .string()
    .regex(/^[a-z0-9._]+$/, {
      message:
        "Username can only contain lowercase letters, numbers, underscores, and dots",
    })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Username cannot contain only whitespace",
    }),
  name: z
    .string()
    .min(1, { message: "Name is required" })
    .min(3, { message: "Name must be at least 3 characters" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Name cannot contain only whitespace",
    })
    .refine((value) => /^[a-zA-Z\s]+$/.test(value), {
      message: "Name can only contain letters, and spaces",
    }),
});

export const forgotEmailSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email" }),
  // verifyOTP: z.string(),
});

export const verficationSchema = z.object({
  verifyOTP: z
    .string()
    .min(1, { message: "Verification code is required" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "OTP cannot contain only whitespace",
    }),
});

export const resetPasswordSchema = z
  .object({
    newPassword: z
      .string()
      .min(1, { message: "Password is required" })
      .min(8, { message: "Password must be at least 8 characters" })
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*])(?=.*[0-9]).{8,}$/, {
        message:
          "Password must contain at least 8 characters, 1 uppercase letter, 1 lowercase letter, and 1 special character",
      })
      .transform((value) => value.trim())
      .refine((value) => value.length > 0, {
        message: "New password cannot contain only whitespace",
      }),
    confirmPassword: z
      .string()
      .min(1, { message: "Confirm password is required" })
      .transform((value) => value.trim())
      .refine((value) => value.length > 0, {
        message: "Confirm password cannot contain only whitespace",
      }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export const userNameSchema = z.object({
  username: z
    .string()
    .regex(/^[a-z0-9._]+$/, {
      message:
        "Username can only contain lowercase letters, numbers, underscores, and dots",
    })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Username cannot contain only whitespace",
    }),
});

export const nameFormSchema = z.object({
  firstName: z
    .string()
    .min(1, { message: "First name is required" })
    .min(3, { message: "First name must be at least 3 characters" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "First Name cannot contain only whitespace",
    }),
  lastName: z
    .string()
    .min(1, { message: "Last name is required" })
    .min(3, { message: "Last name must be at least 3 characters" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Last Name cannot contain only whitespace",
    }),
});

export const onBoardingFormSchema = z.object({
  firstName: z
    .string()
    .min(1, { message: "First name is required" })
    .min(3, { message: "First name must be at least 3 characters" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "First Name cannot contain only whitespace",
    }),
  lastName: z
    .string()
    .min(1, { message: "Last name is required" })
    .min(3, { message: "Last name must be at least 3 characters" })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Last Name cannot contain only whitespace",
    }),
  username: z
    .string()
    .regex(/^[a-z0-9._]+$/, {
      message:
        "Username can only contain lowercase letters, numbers, underscores, and dots",
    })
    .transform((value) => value.trim())
    .refine((value) => value.length > 0, {
      message: "Username cannot contain only whitespace",
    }),
  userProfilePicture: z
    .any()
    .refine((file) => file instanceof File || file instanceof Blob, {
      message: "Profile picture is required",
    }),
});
