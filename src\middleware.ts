import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getRequestSubdomainInfo, isWhitelabelAllowedPath } from "@/lib/utils/subdomainUtils";

export async function middleware(request: NextRequest) {
  const hostname = request.headers.get("host") || "";
  const pathname = request.nextUrl.pathname;
  const subdomainInfo = getRequestSubdomainInfo(hostname);

  // Handle subdomain (whitelabel) logic
  if (subdomainInfo.isSubdomain) {
    // For whitelabel subdomains, only allow specific routes
    if (!isWhitelabelAllowedPath(pathname)) {
      // Redirect to the whitelabel events page
      return NextResponse.redirect(new URL(`/event`, request.url));
    }

    // Redirect /events to /event for whitelabel subdomains (events listing)
    if (pathname === '/events') {
      return NextResponse.redirect(new URL('/event', request.url));
    }

    // Rewrite /events/{slug} to /event/{slug} for whitelabel subdomains
    if (pathname.startsWith('/events/') && pathname !== '/events') {
      const eventSlug = pathname.split('/events/')[1];
      if (eventSlug) {
        return NextResponse.rewrite(new URL(`/event/${eventSlug}`, request.url));
      }
    }

    // Add subdomain info to headers for use in the application
    const response = NextResponse.next();
    response.headers.set("x-subdomain-info", JSON.stringify(subdomainInfo));
    response.headers.set("x-organizer-slug", subdomainInfo.organizerSlug || "");
    
    return response;
  }

  // Original restricted cookie logic (for main domain)
  if (
    request.cookies.get("restricted")?.value === "true" &&
    !request.nextUrl.pathname.startsWith("/restricted")
  ) {
    return NextResponse.redirect(new URL("/restricted", request.url));
  }

  if (
    !request.cookies.get("restricted") &&
    request.nextUrl.pathname.startsWith("/restricted")
  ) {
    return NextResponse.redirect(new URL("/", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
