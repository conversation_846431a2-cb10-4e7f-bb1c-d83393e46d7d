/* Base styles for markdown content */
.markdown {
  @apply text-base leading-relaxed;
}

/* Headings */
.markdown h1 {
  @apply text-4xl font-bold mt-8 mb-4;
}

.markdown h2 {
  @apply text-3xl font-bold mt-8 mb-4;
}

.markdown h3 {
  @apply text-2xl font-semibold mt-6 mb-4;
}

.markdown h4 {
  @apply text-xl font-semibold mt-6 mb-4;
}

.markdown h5 {
  @apply text-lg font-semibold mt-6 mb-4;
}

.markdown h6 {
  @apply text-base font-semibold mt-6 mb-4;
}

/* Paragraphs and spacing */
.markdown p {
  @apply mb-4 whitespace-pre-wrap;
}

/* Lists */
.markdown ul {
  @apply list-disc list-inside mb-4 pl-4;
}

.markdown ol {
  @apply list-decimal list-inside mb-4 pl-4;
}

.markdown li {
  @apply mb-2;
}

.markdown li > ul,
.markdown li > ol {
  @apply ml-4 mt-2;
}

/* Links */
.markdown a {
  @apply text-primary hover:text-primary-500 underline;
}

/* Blockquotes */
.markdown blockquote {
  @apply pl-4 border-l-4 border-gray-300 italic my-4;
}

/* Code blocks */
.markdown pre {
  @apply bg-gray-100 rounded-lg p-4 mb-4 overflow-x-auto;
}

.markdown code {
  @apply bg-gray-100 rounded px-1 py-0.5 font-mono text-sm;
}

.markdown pre code {
  @apply bg-transparent p-0;
}

/* Tables */
.markdown table {
  @apply w-full border-collapse mb-4;
}

.markdown th {
  @apply bg-gray-100 border border-gray-300 px-4 py-2 text-left;
}

.markdown td {
  @apply border border-gray-300 px-4 py-2;
}

/* Images */
.markdown img {
  @apply max-w-full h-auto rounded-xl my-4;
}

/* Horizontal rule */
.markdown hr {
  @apply my-8 border-t border-gray-300;
}

/* Definition lists */
.markdown dl {
  @apply mb-4;
}

.markdown dt {
  @apply font-bold mt-4;
}

.markdown dd {
  @apply ml-4 mb-4;
}

/* Task lists */
.markdown input[type="checkbox"] {
  @apply mr-2;
}

/* Footnotes */
.markdown .footnotes {
  @apply mt-8 pt-8 border-t border-gray-300;
}

/* Custom container */
.markdown .custom-block {
  @apply p-4 mb-4 rounded-lg;
}

.markdown .custom-block.info {
  @apply bg-blue-50 text-blue-900;
}

.markdown .custom-block.warning {
  @apply bg-yellow-50 text-yellow-900;
}

.markdown .custom-block.danger {
  @apply bg-red-50 text-red-900;
} 