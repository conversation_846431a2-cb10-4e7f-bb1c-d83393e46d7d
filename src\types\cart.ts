export interface Vehicle {
  type: string;
  year: string;
  make: string;
  model: string;
  model_name: string;
  modification_text: string;
  vehicle_images: Array<{ image: string; type: string; tag: string }>;
}

export interface VehicleFormData {
  vehicle: Vehicle;
  team_name: string;
  add_vehicle_to_profile?: boolean;
  email: string;
  first_name: string;
  last_name: string;
  social_media: { instagram: string };
}

export interface CartItem {
  eventId: string;
  ticket: {
    ticketId: string;
    quantity: number;
  };
  vehicleFormData?: VehicleFormData;
}

export interface CheckoutPayload {
  checkoutToken: string;
} 