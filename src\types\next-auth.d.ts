import "next-auth";
import { DefaultSession, DefaultUser } from 'next-auth';

// Extend the default User type
interface I<PERSON><PERSON> extends DefaultUser {
  // Add properties returned by your /api/users/me endpoint (camelCased)
  id: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  isActive: boolean;
  isBot: boolean;
  isPhoneVerified: boolean;
  userTimezone: string;
  profile?: any; // Add specific profile type if known
}

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    // Properties added in the session callback
    accessToken?: string;
    accessTokenExpiration?: string;
    refreshToken?: string;
    csrfToken?: string;
    error?: string;
    
    // Extend the default user property with our custom fields
    user?: IUser & DefaultSession['user'];
  }

  // Extend the token object if you are adding custom properties in the jwt callback
  interface JWT {
    accessToken?: string;
    accessTokenExpiration?: string;
    refreshToken?: string;
    csrfToken?: string;
    error?: string;
    user?: IUser;
  }

  interface User {
    id: string;
    name: string;
    email: string;
    image?: string;
  }
} 